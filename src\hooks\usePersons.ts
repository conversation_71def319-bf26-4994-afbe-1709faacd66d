import { UmsPerson_ } from "@/apis";
import { umsPersonAll, umsPersonAllPersonList } from "@/apis/umsPersonController";

// persons hooks
export const usePersons = (withDisabled = false) => {
    /**
     * 所有人员列表(不包含已被禁用的人员)
     */
    const allPersonList = ref<UmsPerson_[]>([]);

    /**
     * 所有人员列表(包含已被禁用的人员)
     */
    const allPersonListWithDisabled = ref<UmsPerson_[]>([]);

    /**
     * 获取所有人员列表(不包含已被禁用的人员)
     */
    const getPersonList = async () => {
        const res = await umsPersonAllPersonList({});
        allPersonList.value = res.data;
    };

    /**
     * 获取所有人员列表(包含已被禁用的人员)
     */
    const getPersonListWithDisabled = async () => {
        const res = await umsPersonAll({});
        allPersonListWithDisabled.value = res.data;
    };

    onMounted(() => {
        if (withDisabled) {
            getPersonListWithDisabled();
        } else {
            getPersonList();
        }
    });

    return {
        allPersonList,
        getPersonList,
        allPersonListWithDisabled,
        getPersonListWithDisabled,
    };
};
