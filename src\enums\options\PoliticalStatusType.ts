/**
 * 政治面貌：中共党员、中共预备党员、共青团员、民革党员、民盟盟员、民建会员、民进会员、农工党党员、致公党党员、九三学社社员、台盟盟员、无党派人士、群众
 */
export enum PoliticalStatusType {
    /** 中共党员 */
    CPC_MEMBER = 0,
    /** 中共预备党员 */
    CPC_PROBATIONARY = 1,
    /** 共青团员 */
    LEAGUE_MEMBER = 2,
    /** 民革党员 */
    REVOLUTIONARY_COMMITTEE = 3,
    /** 民盟盟员 */
    DEMOCRATIC_LEAGUE = 4,
    /** 民建会员 */
    DEMOCRATIC_CONSTRUCTION = 5,
    /** 民进会员 */
    DEMOCRATIC_PROMOTION = 6,
    /** 农工党党员 */
    PEASANTS_WORKERS_PARTY = 7,
    /** 致公党党员 */
    ZRIGONG_PARTY = 8,
    /** 九三学社社员 */
    JIUSAN_SOCIETY = 9,
    /** 台盟盟员 */
    TAIWAN_ALLIANCE = 10,
    /** 无党派人士 */
    NON_PARTISAN = 11,
    /** 群众 */
    MASSES = 12,
}

// 生成对应Map
export const PoliticalStatusTypeMap: Record<PoliticalStatusType, string> = {
    [PoliticalStatusType.CPC_MEMBER]: "中共党员",
    [PoliticalStatusType.CPC_PROBATIONARY]: "中共预备党员",
    [PoliticalStatusType.LEAGUE_MEMBER]: "共青团员",
    [PoliticalStatusType.REVOLUTIONARY_COMMITTEE]: "民革党员",
    [PoliticalStatusType.DEMOCRATIC_LEAGUE]: "民盟盟员",
    [PoliticalStatusType.DEMOCRATIC_CONSTRUCTION]: "民建会员",
    [PoliticalStatusType.DEMOCRATIC_PROMOTION]: "民进会员",
    [PoliticalStatusType.PEASANTS_WORKERS_PARTY]: "农工党党员",
    [PoliticalStatusType.ZRIGONG_PARTY]: "致公党党员",
    [PoliticalStatusType.JIUSAN_SOCIETY]: "九三学社社员",
    [PoliticalStatusType.TAIWAN_ALLIANCE]: "台盟盟员",
    [PoliticalStatusType.NON_PARTISAN]: "无党派人士",
    [PoliticalStatusType.MASSES]: "群众",
} 