/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 根据大类ID获取标签实体列表 GET /cms/cmsTagCategoryEntity/getTagListByEntityId */
export async function cmsTagCategoryEntityGetTagListByEntityId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsTagCategoryEntityGetTagListByEntityIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsTag_>(
    '/cms/cmsTagCategoryEntity/getTagListByEntityId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据大类ID获取标签列表 GET /cms/cmsTagCategoryEntity/getTagsByEntityId */
export async function cmsTagCategoryEntityGetTagsByEntityId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsTagCategoryEntityGetTagsByEntityIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsTagCategoryEntity_>(
    '/cms/cmsTagCategoryEntity/getTagsByEntityId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据标签id获取大类列表 GET /cms/cmsTagCategoryEntity/getTagsByTagId */
export async function cmsTagCategoryEntityGetTagsByTagId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsTagCategoryEntityGetTagsByTagIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsTagCategoryEntity_>(
    '/cms/cmsTagCategoryEntity/getTagsByTagId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据大类ID获取关联的标签树列表 GET /cms/cmsTagCategoryEntity/getTagTreeListByEntityId */
export async function cmsTagCategoryEntityGetTagTreeListByEntityId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsTagCategoryEntityGetTagTreeListByEntityIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsTagNode_>(
    '/cms/cmsTagCategoryEntity/getTagTreeListByEntityId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}
