import type { Component, VNode } from "vue";

// el-button 按钮属性接口
export interface ButtonProps {
    type?: 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger' | 'text';
    size?: 'large' | 'default' | 'small';
    plain?: boolean;
    text?: boolean;
    bg?: boolean;
    link?: boolean;
    round?: boolean;
    circle?: boolean;
    loading?: boolean;
    disabled?: boolean;
    autofocus?: boolean;
    icon?: Component;
    loadingIcon?: Component;
    color?: string;
    dark?: boolean;
    autoInsertSpace?: boolean;
}

export interface CustomDialogOptions {
    title?: string;
    message?: string | VNode;
    dangerouslyUseHTMLString?: boolean;
    icon?: Component | string;
    iconUrl?: string;
    iconSize?: number;
    iconColor?: string;
    width?: string | number;
    showClose?: boolean;
    showButtons?: boolean;
    showCancelButton?: boolean;
    showConfirmButton?: boolean;
    cancelButtonText?: string;
    confirmButtonText?: string;
    cancelButtonProps?: ButtonProps;
    confirmButtonProps?: ButtonProps;
    buttonSize?: "large" | "default" | "small";
    closeOnClickModal?: boolean;
    closeOnPressEscape?: boolean;
    alignCenter?: boolean;
    dialogClass?: string;
    beforeClose?: (done: () => void) => void;
    onConfirm?: () => void | Promise<void> | Promise<boolean>;
    onCancel?: () => void | Promise<void> | Promise<boolean>;
    onClose?: () => void | Promise<void> | Promise<boolean>;
    type?: 'success' | 'error' | 'warning' | 'info' | 'confirm';
}

export interface CustomDialogInstance {
    close: () => void;
    confirm: () => void;
    cancel: () => void;
}

export type CustomDialogAction = "confirm" | "cancel" | "close";
