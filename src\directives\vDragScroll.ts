import type { Directive, DirectiveBinding } from 'vue'

interface DragScrollOptions {
  speed?: number
  checkOnDataChange?: boolean
}

export const vDragScroll: Directive<HTMLElement, DragScrollOptions> = {
  mounted(el, binding) {
    const options = binding.value || {}
    const speed = options.speed || 1.5
    
    let isDragging = false
    let startX = 0
    let scrollLeft = 0
    let isScrollable = false

    // 检查是否需要横向滚动
    const checkScrollable = () => {
      isScrollable = el.scrollWidth > el.clientWidth
      // 根据是否可滚动设置光标样式
      el.style.cursor = isScrollable ? 'grab' : 'default'
    }

    // 开始拖拽
    const startDrag = (e: MouseEvent) => {
      if (!isScrollable) return
      isDragging = true
      startX = e.pageX - el.offsetLeft
      scrollLeft = el.scrollLeft
      el.style.cursor = 'grabbing'
    }

    // 结束拖拽
    const endDrag = () => {
      if (!isScrollable) return
      isDragging = false
      el.style.cursor = 'grab'
    }

    // 拖拽过程
    const drag = (e: MouseEvent) => {
      if (!isDragging) return
      e.preventDefault()
      const x = e.pageX - el.offsetLeft
      const walk = (x - startX) * speed // 滚动速度系数
      el.scrollLeft = scrollLeft - walk
    }

    // 添加事件监听
    el.addEventListener('mousedown', startDrag)
    el.addEventListener('mouseleave', endDrag)
    el.addEventListener('mouseup', endDrag)
    el.addEventListener('mousemove', drag)
    window.addEventListener('resize', checkScrollable)

    // 初始检查
    checkScrollable()

    // 保存清理函数到元素上，以便在 unmounted 时调用
    ;(el as any)._dragScrollCleanup = () => {
      el.removeEventListener('mousedown', startDrag)
      el.removeEventListener('mouseleave', endDrag)
      el.removeEventListener('mouseup', endDrag)
      el.removeEventListener('mousemove', drag)
      window.removeEventListener('resize', checkScrollable)
    }

    // 公开检查方法，以便外部调用
    ;(el as any)._checkDragScrollable = checkScrollable
  },

  updated(el) {
    // 元素内容可能变化，重新检查是否可滚动
    setTimeout(() => {
      if ((el as any)._checkDragScrollable) {
        (el as any)._checkDragScrollable()
      }
    }, 0)
  },

  unmounted(el) {
    // 移除事件监听
    if ((el as any)._dragScrollCleanup) {
      (el as any)._dragScrollCleanup()
    }
  }
}

export default vDragScroll 