<template>
    <div class="wrapper">
        <GeneralDataDisplay>
            <template #top>
                <div class="top">
                    <CustomSearch v-model="searchKeyword" placeholder="输入人员名称或教职工号搜索" />
                </div>
            </template>

            <template #content>
                <div class="content">
                    <DataTable
                        :search-keyword="searchKeyword"
                        :is-local-search="true"
                        :search-fields="['employeeName', 'employeeId']"
                        :show-all="true"
                        ref="dataTableRef"
                        :is-load-first="true"
                        @itemClick="loadDetail"
                        :load-more-func="loadMoreFunc">
                        <template #title="{ item }">
                            <span class="name-text">{{ item.employeeName }}</span>
                            <span class="dual-teacher" v-if="item.isDualTeacher">双师</span>
                        </template>
                        <template #desc="{ item }"> 教职工号：{{ item.employeeId }} </template>
                        <template #info="{ item }">
                            <div class="positionList">
                                <div
                                    v-for="position in getPositionName(item.position)"
                                    :key="position"
                                    :style="{ backgroundColor: getPositionColorById(position) }">
                                    {{ position }}
                                </div>
                            </div>
                        </template>
                        <template #detail="{ selectedItem }">
                            <Detail
                                :position-list="positionList"
                                :get-position-name="getPositionName"
                                ref="detailRef"
                                @refresh="refreshDetail"
                                @refresh-all="refreshAll"
                                :selected-item="detailData"
                                :detail-loading="detailLoading" />
                        </template>
                    </DataTable>
                </div>
            </template>
        </GeneralDataDisplay>
    </div>
</template>

<script setup lang="ts">
import GeneralDataDisplay from "@/components/GeneralDataDisplay/index.vue";
import DataTable from "@/components/DataTable/index.vue";
import Detail from "./detail.vue";
import { Search } from "@element-plus/icons-vue";
import { umsPersonAllPersonList, umsPersonDetail } from "@/apis/umsPersonController";
import { UmsPerson1Res, UmsPerson_ } from "@/apis";
import { usePositionList } from "@/hooks/usePosition";
import CustomSearch from "@/components/CustomSearch/index.vue";

const { positionList, getPositionName, getPositionColorById } = usePositionList();

const personList = ref<UmsPerson_[]>([]);

// 存储当前选中人员详情
const detailData = ref<UmsPerson1Res | null>(null);

// 存储detail组件实例
const detailRef = ref();

// 搜索关键字
const searchKeyword = ref("");

// 存储dataTable组件实例
const dataTableRef = ref();

// 详情加载中
const detailLoading = ref(false);

// 获取详情
const loadDetail = (item: UmsPerson_) => {
    // 如果 当前选中项和之前选中项相同，则不重新加载
    if (item.id === detailData.value?.id) return;
    detailData.value = null; // 清空之前的详情数据
    detailLoading.value = true;

    umsPersonDetail({ params: { id: item.id } }).then((res) => {
        detailData.value = res.data;
        detailLoading.value = false;
    });
};

// 刷新人员详情
const refreshDetail = () => {
    if (detailData.value?.id) {
        // 直接调用API重新获取数据，绕过loadDetail中的相同ID检查
        detailLoading.value = true;
        umsPersonDetail({ params: { id: detailData.value.id } }).then((res) => {
            detailData.value = res.data;
            detailLoading.value = false;
        });
    }
};

// 刷新人员列表
const refreshAll = () => {
    if (dataTableRef.value) {
        dataTableRef.value.refreshAll();
        detailData.value = null;
        searchKeyword.value = "";
        nextTick(() => {
            if (personList.value.length > 0) {
                loadDetail(personList.value[0]);
            }
        });
    }
};

const loadMoreFunc = (pageNum: number, pageSize: number) => {
    return umsPersonAllPersonList({});
};
</script>

<style scoped lang="scss">
// 清除标题部分默认的高度
:deep(.item-title) {
    display: flex;
    align-items: center;
    gap: 4px;
}

:deep(.tab-list) {
    width: 324px !important;
}

.wrapper {
    height: 100%;
    .top {
        padding: 16px 32px;
        background: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .content {
        height: 100%;
    }

    .dual-teacher {
        background: #fff1f0;
        border: 1px solid #ffa39e;
        border-radius: 4px;
        color: #cf1322;
        font-size: 12px;
        font-weight: 400;
        line-height: 20px;
        display: flex;
        padding: 1px 7px;
        align-items: center;
    }

    .positionList {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        align-items: center;
        div {
            display: flex;
            padding: 1px 7px;
            gap: 3px;
            align-items: center;
            color: #fff;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 400;
            line-height: 20px;
        }
    }
}
</style>
