<template>
    <div class="entry-home" v-if="!isEntrying">
        <div class="left">
            <div class="classify-list">
                <el-anchor type="underline" :container="containerRef" @click="handleClick">
                    <el-anchor-link v-for="item in classifyList" :href="`#${item.name}`">
                        {{ item.name }}
                    </el-anchor-link>
                </el-anchor>
            </div>
        </div>
        <div class="right" ref="containerRef">
            <div class="right-list">
                <!-- 搜索区域 -->
                <div>
                    <el-input style="width: 400px" size="large" v-model="searchKeyword" placeholder="输入关键字检索">
                        <template #append>
                            <el-button :icon="Search" />
                        </template>
                    </el-input>
                </div>

                <!-- 收藏区域 -->
                <div class="favorite-section">
                    <FavoriteTemplateList
                        :favoriteTemplates="favoriteTemplates"
                        :getStarImage="getStarImage"
                        @choose-entry="handleChooseEntry"
                        @collect="handleCollect" />
                </div>

                <!-- 分类区域 -->
                <div class="template-section">
                    <TemplateList
                        :classifyList="classifyList"
                        :templateList="templateList"
                        :getStarImage="getStarImage"
                        @choose-entry="handleChooseEntry"
                        @collect="handleCollect" />
                </div>
            </div>
        </div>
    </div>
    <div v-else class="entry-home" :style="selectedTemplate == null ? { 'overflow-y': 'auto' } : {}">
        <DataEntry
            :selected-template="selectedTemplate"
            @back="handleBack"
            @show-performance-rules="showPerformanceRules"
            @refresh="refresh" />
        <!-- =========================== 查看已关联绩效规则弹窗 =========================== -->
        <PerformanceRulesDialog
            v-model="performanceRulesDialogVisible"
            :performance-rules-list="performanceRulesList"
            :show-change-rules-button="false" />
        <!-- =========================== 查看已关联绩效规则弹窗 =========================== -->
    </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import { ElMessageBox } from "element-plus";
import { cmsClassifyList } from "@/apis/cmsClassifyController";
import { cmsCategoryTemplateList } from "@/apis/cmsCategoryTemplateController";
import { useDataEntryStore } from "@/store/modules/dataEntry";
import type { CmsCategoryTemplate0, CmsCategoryTemplate_, CmsClassify_, CmsTagNode_ } from "@/apis/types";
import DataEntry from "@/components/DataEntry/index.vue";
import { useRoute } from "vue-router";
import { cmsTagCategoryEntityGetTagTreeListByEntityId } from "@/apis/cmsTagCategoryEntityController";
import { useFavoriteTemplate } from "@/hooks/useFavoriteTemplate";
import { useTemplateHeight } from "@/hooks/useTemplateHeight";
import FavoriteTemplateList from "@/components/FavoriteTemplateList/index.vue";
import TemplateList from "@/components/TemplateList/index.vue";
import { cmsCategoryRowGetOwnerByStatus } from "@/apis/cmsCategoryRowController";
import { Search } from "@element-plus/icons-vue";
import PerformanceRulesDialog from "@/components/PerformanceRulesDialog/index.vue";

const route = useRoute();

// 是否正在录入
const isEntrying = ref(false);

// 搜索关键字
const searchKeyword = ref("");

// 数据录入状态
const dataEntryStore = useDataEntryStore();

// 大类归类列表
const classifyList = ref<CmsClassify_[]>([]);

// 数据表列表
const templateList = ref<CmsCategoryTemplate0[]>([]);

// 锚点容器
const containerRef = ref<HTMLElement | null>(null);

const selectedTemplate = ref<CmsCategoryTemplate_ | null>(null);

// 使用收藏功能hook
const { favoriteTemplates, getStarImage, handleCollect, loadFavoriteList } = useFavoriteTemplate(templateList);

// 使用模板高度调整hook
const { adjustTemplateHeights, setupLifecycle } = useTemplateHeight(classifyList, templateList);

// 刷新
const refresh = () => {
    // 刷新数据表列表
    cmsCategoryTemplateList({}).then((res) => {
        templateList.value = res.data;
    });
};

// 选择数据表
const handleChooseEntry = async (template: CmsCategoryTemplate_) => {
    // 查询当前用户数据表某个状态是否有数据
    const res = await cmsCategoryRowGetOwnerByStatus({
        params: {
            approvalStatus: [0, 4],
            rowTypes: ["IMPORT", "INSERT"],
            templateId: template.id,
        },
    });
    if (res.data === true) {
        ElMessageBox.confirm(
            `您的「待补充」中仍有未处理的「${template.templateName}」数据。为避免重复录入，请先确认您当前要录入的内容是否跟「待补充」中的数据重复。如您已经确认，可继续录入操作。`,
            "提示",
            {
                confirmButtonText: "继续录入",
                cancelButtonText: "取消",
                type: "info",
            },
        ).then(() => {
            isEntrying.value = true;
            dataEntryStore.setEnteringData(true);
            selectedTemplate.value = template;
        });
    } else {
        // 弹窗提示用户是否开始录入
        ElMessageBox.confirm(`前往录入 ${template.templateName} ？`, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "info",
        }).then(() => {
            isEntrying.value = true;
            dataEntryStore.setEnteringData(true);
            selectedTemplate.value = template;
        });
    }
};

// 返回
const handleBack = () => {
    isEntrying.value = false;
    dataEntryStore.setEnteringData(false);
    selectedTemplate.value = null;
};

// 阻止锚点跳转
const handleClick = (e: Event) => {
    e.preventDefault();
};

// 查看已关联绩效规则弹窗
const performanceRulesDialogVisible = ref(false);

// 已关联绩效规则列表
const performanceRulesList = ref<CmsTagNode_[]>([]);

// 显示绩效规则弹窗
function showPerformanceRules(templateId: number) {
    performanceRulesDialogVisible.value = true;
    // 获取标签树结构
    cmsTagCategoryEntityGetTagTreeListByEntityId({ params: { categoryEntityId: templateId } }).then((res) => {
        performanceRulesList.value = res.data;
    });
}

// 监听窗口大小变化，重新调整高度
onMounted(() => {
    // 获取数据表列表
    cmsCategoryTemplateList({}).then((res) => {
        templateList.value = res.data;
        adjustTemplateHeights();
    });

    // 获取大类归类列表
    cmsClassifyList({}).then((res) => {
        classifyList.value = res.data;
    });

    // 加载收藏列表
    loadFavoriteList();

    // 设置生命周期钩子
    setupLifecycle();
});

// 监听路由参数t的变化，刷新页面
watch(
    () => route.query.t,
    () => {
        // 执行刷新逻辑
        isEntrying.value = false;
        refresh();
    },
);
</script>

<style scoped lang="scss">
@use "../../../../assets/styles/mixins.scss" as mix;
@use "../../../../assets/styles/favorite.scss";

:deep(.el-anchor__marker) {
    // display: none;
    height: 20px !important;
    width: 3px !important;
}
:deep(.el-anchor__link.is-active) {
    color: #1677ff !important;
}
:deep(.el-anchor__link) {
    color: rgba(0, 0, 0, 0.88) !important;
}

:deep(.el-anchor__item) {
    margin: 10px 0;
}

.entry-home {
    /* margin-top: 20px; */
    background-color: #fff;
    height: 100%;
    /* overflow: scroll; */
    /* height: calc(100vh - 220px); */

    display: flex;
    .left {
        margin-left: 10px;
        width: 188px;
        height: 100%;
        overflow: scroll;
    }
    .right {
        height: 100%;
        flex: 1;
        overflow: scroll;

        .right-list {
            padding: 40px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        // 收藏区域样式
        .favorite-section {
            background: #ebf1ff;
            border-radius: 8px;
            padding: 10px;
        }

        // 分类区域样式
        .template-section {
            background: #f9fbff;
            border-radius: 8px;
            padding: 10px 10px 20px 10px;
        }
    }
    .choose-entry-item {
        width: 140px;
        color: #fff;
        background-color: #7b8cb0;
        height: 150px;
        padding: 20px 10px;
        border-radius: 5px;
        cursor: pointer;
        .choose-entry-item-title {
            font-size: 15px;
            font-weight: bold;
            border-bottom: 1px solid #fff;
            padding-bottom: 10px;
        }
        .choose-entry-item-content {
            font-size: 12px;
            margin-top: 20px;
            letter-spacing: 0.1rem;
        }
    }
}
</style>
