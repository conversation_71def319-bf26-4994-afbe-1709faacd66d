<template>
    <div class="overflowable-progress" :style="{ width: width }">
        <div
            class="progress-bar"
            :class="{ 'progress-bar--with-text': textInside }"
            :style="{ height: `${strokeWidth}px` }">
            <!-- 溢出颜色背景层 - 仅在有溢出时显示 -->
            <div
                v-if="isOverflow"
                class="progress-background"
                :style="{
                    backgroundColor: overflowColor,
                }"></div>
            <!-- 正常进度条部分 -->
            <div
                class="progress-normal progress-normal--rounded-right"
                :style="{
                    width: `${normalPercentage}%`,
                    backgroundColor: normalColor,
                }"></div>
            <!-- 溢出部分 -->
            <div
                v-if="isOverflow"
                class="progress-overflow"
                :style="{
                    width: `${overflowPercentage}% `,
                    backgroundColor: overflowColor,
                }"></div>
            <!-- 内置文本插槽 - 移到进度条外层 -->
            <span v-if="textInside" class="progress-text-inside" :class="{ 'text-white': isTextWhite }">
                <slot name="default">
                    {{ formattedPercentage }}
                </slot>
            </span>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";

interface Props {
    /** 百分比 */
    percentage: number;
    /** 最大值 */
    maxValue?: number;
    /** 正常颜色 */
    normalColor?: string;
    /** 溢出颜色 */
    overflowColor?: string;
    /** 是否显示文本 */
    showText?: boolean;
    /** 精度 */
    precision?: number;
    /** 文本是否在进度条内部 */
    textInside?: boolean;
    /** 进度条宽度 */
    strokeWidth?: number;
    /** 进度条长度 */
    width?: string;
}

const props = withDefaults(defineProps<Props>(), {
    maxValue: 100,
    normalColor: "#1677FF",
    overflowColor: "#FF4D4F",
    showText: true,
    precision: 0,
    textInside: false,
    strokeWidth: 6,
    width: "100%",
});

// 格式化百分比显示
const formattedPercentage = computed(() => {
    if (isNaN(props.percentage)) {
        return "0%";
    }
    return `${props.percentage.toFixed(props.precision)}%`;
});

// 计算是否溢出
const isOverflow = computed(() => {
    if (isNaN(props.percentage)) return false;
    return props.percentage > props.maxValue;
});

// 计算正常进度条百分比
const normalPercentage = computed(() => {
    if (isNaN(props.percentage)) return 0;
    if (isOverflow.value) {
        return 100 * (props.maxValue / props.percentage);
    }
    return props.percentage;
});

// 计算溢出部分百分比
const overflowPercentage = computed(() => {
    if (isNaN(props.percentage)) return 0;
    if (isOverflow.value) {
        return 100 * ((props.percentage - props.maxValue) / props.percentage);
    }
    return 0;
});

// 添加文字颜色计算属性
const isTextWhite = computed(() => {
    if (isNaN(props.percentage)) return false;
    return props.percentage >= 50;
});
</script>

<style scoped>
.overflowable-progress {
    position: relative;
    display: flex;
    align-items: center;
}

.progress-bar {
    flex: 1;
    background-color: #e5e9f2;
    border-radius: 100px;
    overflow: hidden;
    display: flex;
    position: relative;
}

.progress-bar--with-text {
    min-width: 100px;
}

.progress-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 100px;
    z-index: 0;
}

.progress-normal,
.progress-overflow {
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.progress-normal--rounded-right {
    border-radius: 100px;
}

.progress-overflow {
    border-top-right-radius: 100px;
    border-bottom-right-radius: 100px;
}

.progress-text-inside {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    color: #1677ff;
    line-height: 1;
    white-space: nowrap;
    z-index: 1;
}

.progress-text-inside.text-white {
    color: #ffffff;
}

.progress-text {
    margin-left: 5px;
    font-size: 14px;
    color: #606266;
    min-width: 40px;
    text-align: right;
}

/* 当进度较小时隐藏内部文本 */
.progress-normal:not([style*="width: 0"]):not([style*="width:0"]) {
    min-width: 40px;
}
</style>
