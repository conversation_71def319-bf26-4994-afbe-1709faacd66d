<template>
    <div v-if="selectedItem" v-loading="loading" class="detail-content">
        <div class="title">{{ selectedItem.projectName }}</div>
        <div class="content">
            <el-tabs type="card">
                <el-tab-pane label="项目信息">
                    <div class="project-info">
                        <!-- 基本信息 -->
                        <div class="basic-info-section">
                            <div class="section-title">
                                <div class="title-indicator"></div>
                                <span>基本信息</span>
                            </div>
                            <div class="info-table">
                                <div class="info-row">
                                    <div class="info-label">团队负责人</div>
                                    <div class="info-content">{{ selectedItem.projectLeaderName }}</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">项目状态</div>
                                    <div class="info-content project-status-content">
                                        <div class="status-item">
                                            立项时间：{{ selectedItem.projectInitiationDate || "暂无" }}
                                        </div>
                                        <div class="status-item">
                                            预计结项时间：{{ selectedItem.projectExpectedEndDate || "暂无" }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 团队成员 -->
                        <div class="team-members-section">
                            <div class="section-title">
                                <div class="title-indicator"></div>
                                <span>团队成员</span>
                            </div>
                            <div class="members-grid">
                                <div
                                    class="member-card"
                                    v-for="item in selectedItem.memberList"
                                    :key="'member-' + item.id">
                                    <div class="member-card-inner">
                                        <div class="member-avatar-new" :style="{ background: getAvatarColor(item.id) }">
                                            <span v-if="!item.avatar" class="avatar-text">{{
                                                item.employeeName.charAt(0)
                                            }}</span>
                                            <img v-else :src="filesBaseUrl + item.avatar" />
                                        </div>
                                        <div class="member-name">{{ item.employeeName }}</div>
                                        <div class="member-status joined">已加入</div>
                                    </div>
                                </div>
                            </div>
                            <div v-if="selectedItem.memberList.length === 0" style="margin-left: 30px; color: #6b7995">
                                暂无成员
                            </div>
                        </div>

                        <!-- 申请共享资料 -->
                        <div class="shared-request-section" v-if="chosenItem.dataType === 1">
                            <div class="section-title">
                                <div class="title-indicator"></div>
                                <span>申请共享资料</span>
                            </div>
                            <div class="info-table">
                                <div class="info-row">
                                    <div class="info-label">资料名称</div>
                                    <div class="info-content">
                                        <span>{{ rowData?.projectName }}</span>
                                        <el-button
                                            class="SystemStytle-custom-round-button"
                                            color="#E5F0FD"
                                            size="small"
                                            style="width: 80px; margin-left: 10px"
                                            >查看详情</el-button
                                        >
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="action-buttons">
                            <div
                                v-if="chosenItem.dataType === 0"
                                style="display: flex; gap: 30px; justify-content: center">
                                <el-button
                                    size="large"
                                    style="color: #fff"
                                    type="primary"
                                    @click="handleAcceptInvitation"
                                    >接受邀请</el-button
                                >
                                <el-button size="large" type="danger" plain @click="handleRejectInvitation"
                                    >拒绝邀请</el-button
                                >
                            </div>
                            <div v-else style="display: flex; gap: 30px; justify-content: center">
                                <el-button
                                    size="large"
                                    color="#89BB58"
                                    style="color: #fff"
                                    type="primary"
                                    @click="handleAcceptShared"
                                    >确认共享</el-button
                                >
                                <el-button
                                    size="large"
                                    color="#DE7A5C"
                                    style="color: #fff"
                                    type="danger"
                                    @click="handleRejectShared"
                                    >拒绝共享</el-button
                                >
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
    <div v-else class="detail-content">
        <el-empty description="请从左侧选择一个项目查看详情。" />
    </div>
</template>

<script setup lang="ts">
import { filesBaseUrl } from "@/utils/filesBaseUrl";
import {
    CmsCategoryRow_,
    cmsCategoryRowGetById,
    TmsProject0,
    tmsProjectRequestAcceptAndReject,
    TmsProjectRequestDto,
} from "@/apis";
import defaultAvatar from "@/assets/defaultAvatar.png";
import { tmsProjectInvitationAcceptAndReject } from "@/apis/tmsProjectInvitationController";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refresh"]);

const loading = ref(false);
const { selectedItem, chosenItem } = defineProps<{
    selectedItem: TmsProject0;
    chosenItem: TmsProjectRequestDto;
}>();

const rowData = ref<CmsCategoryRow_>();

// 监听页面事件
watch(
    () => selectedItem,
    (newVal) => {
        if (newVal) {
            // 仅当dataType为资料申请数据的时候，才查询对应行数据
            if (chosenItem.dataType === 1) {
                loading.value = true;
                cmsCategoryRowGetById({
                    params: {
                        id: chosenItem.groupId,
                    },
                }).then((res) => {
                    if (res.code === 200) {
                        rowData.value = res.data;
                        loading.value = false;
                    }
                });
            }
        }
    },
);

// 根据用户ID生成头像背景颜色
const getAvatarColor = (userId: number) => {
    const colors = ["#45B7D1", "#54A0FF", "#FF9F43", "#9AD572", "#FD79A8", "#FDCB6E", "#6C5CE7", "#A29BFE"];
    return colors[userId % colors.length];
};

// 接受邀请
const handleAcceptInvitation = () => {
    tmsProjectInvitationAcceptAndReject({
        body: {
            invitationId: chosenItem.id,
            invitationStatus: 2,
        },
    }).then((res) => {
        if (res.code === 200) {
            ElMessage.success("接受邀请成功");
            emit("refresh");
        }
    });
};

// 拒绝邀请
const handleRejectInvitation = () => {
    tmsProjectInvitationAcceptAndReject({
        body: {
            invitationId: chosenItem.id,
            invitationStatus: 3,
        },
    }).then((res) => {
        if (res.code === 200) {
            ElMessage.success("拒绝邀请成功");
            emit("refresh");
        }
    });
};

// 确认共享
const handleAcceptShared = () => {
    tmsProjectRequestAcceptAndReject({
        body: {
            id: chosenItem.id,
            status: 1,
        },
    }).then((res) => {
        if (res.code === 200) {
            ElMessage.success("确认共享成功");
            emit("refresh");
        }
    });
};

// 拒绝共享
const handleRejectShared = () => {
    tmsProjectRequestAcceptAndReject({
        body: {
            id: chosenItem.id,
            status: 2,
        },
    }).then((res) => {
        if (res.code === 200) {
            ElMessage.success("拒绝共享成功");
            emit("refresh");
        }
    });
};
</script>

<style scoped lang="scss">
@use "../../../../assets/styles/mixins.scss" as mix;

.bold {
    font-weight: bold;
}
.pagination {
    @extend .flex-end;
    margin-top: 10px;
    margin-right: 20px;
}

.flex-end {
    display: flex;
    justify-content: end;
}

.project-info {
    height: 100%;
    /* height: calc(100vh - 410px); */
    overflow-y: auto;
}

// 基本信息区域样式
.basic-info-section {
    background: #ffffff;
    padding-top: 5.5px;
}

.section-title {
    display: flex;
    align-items: center;
    min-height: 21px;
    margin: 21.5px 0;

    .title-indicator {
        border-left: 3px solid #1677ff;
        padding-left: 8px;
        margin-right: 8px;
        height: 20px;
        display: flex;
        align-items: center;
    }

    span {
        color: #000000;
        line-height: 20px;
        width: 100%;
        min-height: 20px;
        font-size: 14px;
    }
}

.info-table {
    display: flex;
    flex-direction: column;
}

.info-row {
    display: flex;
    flex-direction: row;

    &:first-child .info-label {
        border-top: 1px solid rgba(0, 0, 0, 0.06);
        border-top-left-radius: 8px;
    }

    &:first-child .info-content {
        border-top: 1px solid rgba(0, 0, 0, 0.06);
        border-top-right-radius: 8px;
    }

    &:last-child .info-label {
        border-bottom-left-radius: 8px;
    }

    &:last-child .info-content {
        border-bottom-right-radius: 8px;
    }
}

.info-label {
    background: #f9fbff;
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-right: none;
    width: 160px;
    padding: 16px 24px;
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.88);
    font-weight: 600;
    line-height: 22px;
    flex-shrink: 0;
}

.info-content {
    background: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.06);
    padding: 16px 24px;
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.88);
    line-height: 22px;
    flex-grow: 1;

    &.project-status-content {
        display: flex;
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
        padding: 16px 24px;

        .status-item {
            min-height: 22px;
        }
    }
}

// 团队成员区域样式
.team-members-section {
    margin-top: 0.5px;
}

.members-grid {
    margin-top: 0.5px;
    gap: 16px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.member-card {
    width: 160px;
    background: #ffffff;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    position: relative;
    transition: box-shadow 0.3s ease;

    &:hover {
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.05);
        border: 1px solid #dddddd;
    }
}

.member-card-inner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 32px 48px 16px 48px;
    position: relative;
}

.member-avatar-new {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    overflow: hidden;
    background: #45b7d1;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .avatar-text {
        color: #ffffff;
        font-size: 24px;
        font-weight: 700;
        line-height: 37.7px;
        text-align: center;
    }
}

.member-name {
    color: #000000;
    line-height: 22px;
    text-align: center;
    font-size: 14px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    max-width: 100%;
}

.member-status {
    border-radius: 4px;
    padding: 0 12px;
    font-size: 12px;
    line-height: 20px;
    text-align: center;
    min-height: 20px;

    &.joined {
        background: #f6ffed;
        color: #52c41a;
    }

    &.invited {
        background: #e6f4ff;
        color: #1677ff;
    }
}

// 申请共享资料区域样式
.shared-request-section {
    margin-top: 0.5px;
}

// 操作按钮区域样式
.action-buttons {
    margin: 20px auto;
    width: 100%;
    display: flex;
    justify-content: center;
}

.detail-content {
    padding: 20px;
    .title {
        height: 30px;
        font-size: 24px;
        font-weight: 700;
        letter-spacing: 0.1em;
        color: #23346d;
        margin-bottom: 10px;
    }
}
</style>
