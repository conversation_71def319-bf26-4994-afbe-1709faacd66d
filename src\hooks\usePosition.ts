import { postListAll, UmsPost_ } from "@/apis";
import { ref, computed, onMounted } from "vue";
import { PositionColors } from "@/enums/Position/PositionEnum";
import { cs } from "element-plus/es/locale";

export function usePositionList() {
    const positionList = ref<UmsPost_[]>([]);

    /**
     * 获取岗位列表
     */
    const fetchPositionList = async () => {
        try {
            const res = await postListAll({});
            positionList.value = res.data;
        } catch (error) {
            console.error("加载岗位列表失败:", error);
        }
    };

    /**
     * 根据岗位id获取岗位对应颜色
     */
    const getPositionColorById = function (id: number | string) {
        return PositionColors[id];
    };

    /**
     * 根据岗位id获取岗位名称
     * @param positionIds 岗位id
     * @returns 岗位名称
     */
    const getPositionName = function (positionIds: string | Array<string | number>) {
        if (!positionIds) return "";
        const positionIdsArray = Array.isArray(positionIds) ? positionIds : positionIds.split(",");
        if (positionIdsArray.length === 0) return "";
        
        const positionNames = positionIdsArray.map((id) => {
            const position = positionList.value.find((item) => item.id === Number(id));
            return position?.name;
        });

        if (positionNames.length === 0) return "";

        return positionNames;
    };

    // 自动加载数据
    onMounted(fetchPositionList);

    return {
        positionList,
        fetchPositionList,
        getPositionColorById,
        getPositionName,
    };
}
