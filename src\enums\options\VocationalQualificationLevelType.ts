/**
 * 职业资格认证书级别：高级技师、技师、高级工、中级工、初级工、高级、中级、初级
 */
export enum VocationalQualificationLevelType {
    /** 高级技师 */
    SENIOR_TECHNICIAN = 0,
    /** 技师 */
    TECHNICIAN = 1,
    /** 高级工 */
    SENIOR_WORKER = 2,
    /** 中级工 */
    INTERMEDIATE_WORKER = 3,
    /** 初级工 */
    JUNIOR_WORKER = 4,
    /** 高级 */
    SENIOR_LEVEL = 5,
    /** 中级 */
    INTERMEDIATE_LEVEL = 6,
    /** 初级 */
    JUNIOR_LEVEL = 7,
}

// 生成对应Map
export const VocationalQualificationLevelTypeMap: Record<VocationalQualificationLevelType, string> = {
    [VocationalQualificationLevelType.SENIOR_TECHNICIAN]: "高级技师",
    [VocationalQualificationLevelType.TECHNICIAN]: "技师",
    [VocationalQualificationLevelType.SENIOR_WORKER]: "高级工",
    [VocationalQualificationLevelType.INTERMEDIATE_WORKER]: "中级工",
    [VocationalQualificationLevelType.JUNIOR_WORKER]: "初级工",
    [VocationalQualificationLevelType.SENIOR_LEVEL]: "高级",
    [VocationalQualificationLevelType.INTERMEDIATE_LEVEL]: "中级",
    [VocationalQualificationLevelType.JUNIOR_LEVEL]: "初级",
} 