<template>
    <div class="wrapper">
        <div class="tab-list" v-loading="loading">
            <template v-if="props.useTabStyle">
                <el-tabs v-model="activeTab" class="custom-tabs" :stretch="true">
                    <el-tab-pane v-for="tab in props.tabConfig" :key="tab.key" :label="tab.label" :name="tab.key">
                        <el-scrollbar :height="calculatedHeight">
                            <div
                                v-if="!props.showAll"
                                v-infinite-scroll="() => loadMore(tab.key)"
                                infinite-scroll-distance="10"
                                infinite-scroll-immediate="false"
                                :infinite-scroll-disabled="
                                    !tabsData[tab.key]?.canLoadMore || hasLoadedEmptyData(tab.key)
                                ">
                                <div
                                    class="list-item"
                                    v-for="(item, index) in getFilteredDataList(tab.key)"
                                    :key="item.id || index"
                                    :class="{ selected: selectedTab === tab.key && selectedIndex === index }"
                                    @click="selectItem(item, index, tab.key)">
                                    <div class="item-main">
                                        <div class="item-left">
                                            <div class="item-title">
                                                <slot name="title" :item="item"></slot>
                                            </div>
                                            <div class="item-desc">
                                                <slot name="desc" :item="item"></slot>
                                            </div>
                                        </div>
                                        <div class="item-right">
                                            <slot name="right" :item="item"></slot>
                                        </div>
                                    </div>

                                    <div class="info">
                                        <slot name="info" :item="item"></slot>
                                    </div>
                                </div>
                                <div class="bottom">
                                    <div v-if="isTabLoading(tab.key)">
                                        <el-icon class="is-loading">
                                            <Loading />
                                        </el-icon>
                                    </div>
                                    <div v-else-if="getFilteredDataList(tab.key).length === 0">
                                        <img style="width: 30px" src="../../assets/empty.svg" />
                                        &nbsp;&nbsp;暂无数据
                                    </div>
                                    <div v-else>
                                        <img style="width: 30px" src="../../assets/empty.svg" />
                                        &nbsp;&nbsp;没有更多数据了
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <div
                                    class="list-item"
                                    v-for="(item, index) in getFilteredDataList(tab.key)"
                                    :key="item.id || index"
                                    :class="{ selected: selectedTab === tab.key && selectedIndex === index }"
                                    @click="selectItem(item, index, tab.key)">
                                    <div class="item-main">
                                        <div class="item-left">
                                            <div class="item-title">
                                                <slot name="title" :item="item"></slot>
                                            </div>
                                            <div class="item-desc">
                                                <slot name="desc" :item="item"></slot>
                                            </div>
                                        </div>
                                        <div class="item-right">
                                            <slot name="right" :item="item"></slot>
                                        </div>
                                    </div>

                                    <div class="info">
                                        <slot name="info" :item="item"></slot>
                                    </div>
                                </div>
                                <div v-if="!getFilteredDataList(tab.key).length" class="bottom">
                                    <div>
                                        <img style="width: 30px" src="../../assets/empty.svg" />
                                        &nbsp;&nbsp;暂无数据
                                    </div>
                                </div>
                            </div>
                        </el-scrollbar>
                    </el-tab-pane>
                </el-tabs>
            </template>
            <template v-else>
                <el-scrollbar ref="scrollbarRef" :height="calculatedHeight">
                    <div
                        v-if="!props.showAll"
                        v-infinite-scroll="loadMore"
                        infinite-scroll-distance="10"
                        infinite-scroll-immediate="true"
                        :infinite-scroll-disabled="!canLoadMore">
                        <div
                            class="list-item"
                            v-for="(item, index) in filteredDataList"
                            :key="item.id || index"
                            :class="{ selected: selectedIndex === index }"
                            @click="selectItem(item, index)">
                            <div class="item-main">
                                <div class="item-left">
                                    <div class="item-title">
                                        <slot name="title" :item="item"></slot>
                                    </div>
                                    <div class="item-desc">
                                        <slot name="desc" :item="item"></slot>
                                    </div>
                                </div>
                                <div class="item-right">
                                    <slot name="right" :item="item"></slot>
                                </div>
                            </div>

                            <div class="info">
                                <slot name="info" :item="item"></slot>
                            </div>
                        </div>
                        <div class="bottom">
                            <div v-if="canLoadMore">
                                <el-icon class="is-loading">
                                    <Loading />
                                </el-icon>
                            </div>
                            <div v-else>
                                <img style="width: 30px" src="../../assets/empty.svg" />
                                &nbsp;&nbsp;没有更多数据了
                            </div>
                        </div>
                    </div>
                    <div v-else>
                        <div
                            class="list-item"
                            v-for="(item, index) in filteredDataList"
                            :key="item.id || index"
                            :class="{ selected: selectedIndex === index }"
                            @click="selectItem(item, index)">
                            <div class="item-main">
                                <div class="item-left">
                                    <div class="item-title">
                                        <slot name="title" :item="item"></slot>
                                    </div>
                                    <div class="item-desc">
                                        <slot name="desc" :item="item"></slot>
                                    </div>
                                </div>
                                <div class="item-right">
                                    <slot name="right" :item="item"></slot>
                                </div>
                            </div>

                            <div class="info">
                                <slot name="info" :item="item"></slot>
                            </div>
                        </div>
                        <div v-if="!filteredDataList.length" class="bottom">
                            <div>
                                <img style="width: 30px" src="../../assets/empty.svg" />
                                &nbsp;&nbsp;暂无数据
                            </div>
                        </div>
                    </div>
                </el-scrollbar>
            </template>
        </div>

        <div class="detail">
            <slot name="detail" :selected-item="selectedItem"></slot>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useWindowSize } from "@vueuse/core";
import { ScrollbarInstance } from "element-plus";
import { ref, watch, reactive, computed, onMounted, onUnmounted } from "vue";
import { PropType } from "vue";

interface TabConfig {
    key: string;
    label: string;
    loadMoreFunc: (current: number, size: number, searchKeyword?: string) => Promise<any>;
}

interface TabData {
    dataList: any[];
    canLoadMore: boolean;
    pagination: {
        current: number;
        size: number;
        total: number;
    };
}

defineSlots<{
    title: { item: any };
    desc: { item: any };
    detail: { selectedItem: any };
    right: { item: any };
    info: { item: any };
}>();

const props = defineProps({
    loadMoreFunc: {
        type: Function,
        required: false,
        default: () => {},
    },
    isLoadFirst: {
        type: Boolean,
        required: false,
        default: true,
    },
    searchKeyword: {
        type: String,
        required: false,
        default: "",
    },
    useTabStyle: {
        type: Boolean,
        required: false,
        default: false,
    },
    tabConfig: {
        type: Array as () => TabConfig[],
        required: false,
        default: () => [],
    },
    showAll: {
        type: Boolean,
        required: false,
        default: false,
    },
    getAllDataFunc: {
        type: Function,
        required: false,
        default: null,
    },
    isLocalSearch: {
        type: Boolean,
        default: false,
    },
    searchFields: {
        type: Array as PropType<string[]>,
        default: () => [],
    },
});

const scrollbarRef = ref<ScrollbarInstance>();

// 是否保持选中状态
const keepSelected = ref(false);

// 加载
const loading = ref(false);

// 是否可以加载更多
const canLoadMore = ref(true);

// 数据列表
const dataList = ref([]);

// 事件
const emit = defineEmits(["itemClick", "update:modelValue", "loadComplete"]);

// 选中的索引
const selectedIndex = ref(-1);

// 选中的项目
const selectedItem = ref(null);

// 分页
const pagination = ref({
    current: 1,
    size: 10,
    total: 0,
});

// 标签页相关状态
const activeTab = ref("");
const selectedTab = ref("");
const tabsData = reactive<Record<string, TabData>>({});

// 计算属性：本地搜索时过滤数据
const filteredDataList = computed(() => {
    if (!props.isLocalSearch || !props.searchKeyword) {
        return dataList.value;
    }

    return dataList.value.filter((item) => {
        return props.searchFields.some((field) => {
            const value = getNestedValue(item, field);
            return value && value.toString().toLowerCase().includes(props.searchKeyword.toLowerCase());
        });
    });
});

// 获取嵌套对象值的辅助函数
function getNestedValue(obj, path) {
    return path.split(".").reduce((prev, curr) => {
        return prev ? prev[curr] : null;
    }, obj);
}

// 标签页模式下获取过滤后的数据
function getFilteredDataList(tabKey) {
    if (!props.isLocalSearch || !props.searchKeyword) {
        return tabsData[tabKey]?.dataList || [];
    }

    return (tabsData[tabKey]?.dataList || []).filter((item) => {
        return props.searchFields.some((field) => {
            const value = getNestedValue(item, field);
            return value && value.toString().toLowerCase().includes(props.searchKeyword.toLowerCase());
        });
    });
}

// 监听标签页切换
watch(
    () => activeTab.value,
    (newTab) => {
        // 发送标签页变化事件
        emit("update:modelValue", newTab);

        // 只在使用标签页模式且新标签页有效时处理
        if (props.useTabStyle && newTab) {
            // 如果新标签页没有数据且canLoadMore为true，才加载数据
            if (
                (!tabsData[newTab]?.dataList.length || tabsData[newTab]?.dataList.length === 0) &&
                tabsData[newTab]?.canLoadMore
            ) {
                loadMore(newTab);
            }
        }
    },
);

// 修改标签页配置监听
watch(
    () => props.tabConfig,
    (newConfig) => {
        if (props.useTabStyle && newConfig.length > 0) {
            // 只有在activeTab未设置时才初始化为第一个tab
            if (!activeTab.value) {
                activeTab.value = newConfig[0].key;
                selectedTab.value = newConfig[0].key;
            }

            newConfig.forEach((tab) => {
                if (!tabsData[tab.key]) {
                    tabsData[tab.key] = {
                        dataList: [],
                        canLoadMore: true,
                        pagination: {
                            current: 1,
                            size: 10,
                            total: 0,
                        },
                    };
                }
            });

            // 移除这里的初始数据加载，避免与onMounted中的加载冲突
            // 数据加载统一在onMounted中处理
        }
    },
    { immediate: true },
);

// 选择项目
function selectItem(item, index, tabKey?: string) {
    if (props.useTabStyle && tabKey) {
        selectedTab.value = tabKey;
    }
    selectedIndex.value = index;
    selectedItem.value = item;
    emit("itemClick", item);
}

// 添加一个标志变量，用于跟踪是否已执行初始滚动
const hasPerformedInitialScroll = ref(false);

// 修改simulateClick函数
function simulateClick(index: number, tabKey?: string, itemId?: number) {
    // 处理标签页模式
    if (props.useTabStyle) {
        // 使用传入的tabKey或当前活动标签页
        const currentTabKey = tabKey || activeTab.value;
        activeTab.value = currentTabKey;
        const tabData = tabsData[currentTabKey];
        // 检查索引是否在当前标签页数据范围内
        if (tabData && index >= 0 && index < tabData.dataList.length) {
            selectItem(tabData.dataList[index], index, currentTabKey);
            // 仅在首次调用时执行滚动
            if (!hasPerformedInitialScroll.value) {
                scrollToIndex(index);
                hasPerformedInitialScroll.value = true;
            }
        } else {
            // 索引值与实际dataList长度不匹配，这种情况是标签页情况，需要查找列表来选中
            const gotItem = tabsData[currentTabKey].dataList.find((item) => Number(item.id) == itemId);
            const gotIndex = tabsData[currentTabKey].dataList.findIndex((item) => Number(item.id) == itemId);
            if (gotItem) {
                selectItem(gotItem, gotIndex, currentTabKey);
                // 仅在首次调用时执行滚动
                if (!hasPerformedInitialScroll.value) {
                    scrollToIndex(gotIndex);
                    hasPerformedInitialScroll.value = true;
                }
            }
        }
    } else {
        // 非标签页模式保持原有逻辑
        if (index >= 0 && index <= dataList.value.length) {
            selectItem(dataList.value[index], index);
            // 仅在首次调用时执行滚动
            if (!hasPerformedInitialScroll.value) {
                scrollToIndex(index);
                hasPerformedInitialScroll.value = true;
            }
        }
    }
}

// 滚动到指定索引位置的方法
function scrollToIndex(index: number) {
    if (scrollbarRef.value) {
        // 假设每行高度为固定值，可以根据实际情况调整
        const rowHeight = 109; // 行高
        const scrollPosition = index * rowHeight;

        // 考虑可视区域的位置，使选中项在可视区域的中间位置
        if (scrollbarRef.value.$el) {
            const finalPosition = Math.max(0, scrollPosition);
            scrollbarRef.value.setScrollTop(finalPosition);
        } else {
            // 如果无法获取可视区域高度，直接滚动到计算位置
            scrollbarRef.value.setScrollTop(scrollPosition);
        }
    }
}

// 刷新dataList
function refreshAll(tabKey?: string) {
    selectedIndex.value = -1;
    // 如果没有传入tabKey但使用标签页样式，则使用当前活动标签页
    const currentTabKey = tabKey || (props.useTabStyle ? activeTab.value : undefined);

    if (props.showAll) {
        // 全量数据模式
        loading.value = true;
        if (props.useTabStyle && currentTabKey) {
            // 使用标签页的情况，需要获取当前标签页的数据
            const currentTab = props.tabConfig.find((tab) => tab.key === currentTabKey);
            if (!currentTab) return;

            const loadFunc = props.getAllDataFunc || currentTab.loadMoreFunc;
            loadFunc()
                .then((res) => {
                    if (props.useTabStyle && currentTabKey) {
                        tabsData[currentTabKey] = {
                            dataList: res.data,
                            canLoadMore: false,
                            pagination: {
                                current: 1,
                                size: res.data.length,
                                total: res.data.length,
                            },
                        };
                    }
                    loading.value = false;
                })
                .catch(() => {
                    loading.value = false;
                });
        } else {
            // 不使用标签页的情况
            const loadFunc = props.getAllDataFunc || props.loadMoreFunc;
            loadFunc()
                .then((res) => {
                    dataList.value = res.data;
                    canLoadMore.value = false;
                    loading.value = false;
                })
                .catch(() => {
                    loading.value = false;
                });
        }
    } else {
        // 原有的分页模式
        if (props.useTabStyle && currentTabKey) {
            tabsData[currentTabKey] = {
                dataList: [],
                canLoadMore: true,
                pagination: {
                    current: 1,
                    size: 10,
                    total: 0,
                },
            };
            keepSelected.value = true;
            loadMore(currentTabKey);
        } else {
            dataList.value = [];
            keepSelected.value = true;
            pagination.value = {
                current: 1,
                size: 10,
                total: 0,
            };
            canLoadMore.value = true;
            loadMore();
        }
    }
}

// 搜索
function search() {
    if (props.isLocalSearch) {
        // 本地搜索不需要重新请求
        return;
    }

    // 远程搜索：重置分页并重新加载
    if (props.useTabStyle) {
        // 标签页模式
        const currentTabKey = activeTab.value;
        if (tabsData[currentTabKey]) {
            tabsData[currentTabKey].pagination.current = 1;
            tabsData[currentTabKey].dataList = [];
            tabsData[currentTabKey].canLoadMore = true;
        }
        loadMore(currentTabKey);
    } else {
        // 非标签页模式
        pagination.value.current = 1;
        dataList.value = [];
        canLoadMore.value = true;
        loadMore();
    }
}

// 暴露方法
defineExpose({
    refreshAll,
    simulateClick, // 添加新方法到暴露列表
    activeTab, // 暴露当前活动的标签页
    search,
    filteredDataList, // 暴露过滤后的数据
    dataList, // 暴露数据列表
});

// 添加一个函数来发送加载完成事件
function emitLoadComplete(tabKey?: string) {
    if (props.useTabStyle && tabKey) {
        // 标签页模式：发送当前标签页的加载状态
        const tabData = tabsData[tabKey];
        emit("loadComplete", {
            tabKey,
            isComplete: !tabData.canLoadMore,
            total: tabData.pagination.total,
            currentCount: tabData.dataList.length,
        });
    } else {
        // 非标签页模式：发送整体加载状态
        emit("loadComplete", {
            isComplete: !canLoadMore.value,
            total: pagination.value.total,
            currentCount: dataList.value.length,
        });
    }
}

function loadMore(tabKey?: string) {
    if (loading.value) return;
    loading.value = true;

    // 如果是显示全部数据模式，调用refreshAll
    if (props.showAll) {
        refreshAll(tabKey);
        return;
    }

    if (props.useTabStyle && tabKey) {
        const currentTab = props.tabConfig.find((tab) => tab.key === tabKey);
        if (!currentTab) {
            loading.value = false;
            return;
        }

        const tabData = tabsData[tabKey];

        // 检查是否已经确定没有更多数据
        if (!tabData.canLoadMore) {
            loading.value = false;
            emitLoadComplete(tabKey);
            return;
        }

        currentTab
            .loadMoreFunc(
                tabData.pagination.current,
                tabData.pagination.size,
                props.isLocalSearch ? undefined : props.searchKeyword,
            )
            .then((res) => {
                // 确保res.data存在
                if (!res.data) {
                    loading.value = false;
                    tabData.canLoadMore = false;
                    return;
                }

                tabData.pagination.current = res.data.current;
                tabData.pagination.size = res.data.size;
                tabData.pagination.total = res.data.total;
                if (res.data.records) {
                    // 初始加载时直接替换数据而不是追加
                    if (tabData.pagination.current <= 1) {
                        tabData.dataList = [...res.data.records];
                    } else {
                        tabData.dataList = [...tabData.dataList, ...res.data.records];
                    }
                }

                // 更新分页参数
                tabData.pagination.current++;

                // 检查是否已加载所有数据或返回为空
                const hasReachedEnd = res.data.size * res.data.current >= res.data.total;
                const hasEmptyResponse = !res.data.records || res.data.records.length === 0;
                const hasEmptyTotal = res.data.total === 0;

                // 只要有一个条件满足，就停止加载更多
                if (hasReachedEnd || hasEmptyResponse || hasEmptyTotal) {
                    tabData.canLoadMore = false;
                }

                // 发送加载完成事件
                emitLoadComplete(tabKey);

                // 最后设置loading状态为false
                loading.value = false;
            })
            .catch((err) => {
                loading.value = false;
                // 出错时也应该停止加载更多
                tabData.canLoadMore = false;
                // 发送加载完成事件
                emitLoadComplete(tabKey);
            });
    } else {
        props
            .loadMoreFunc(
                pagination.value.current,
                pagination.value.size,
                props.isLocalSearch ? undefined : props.searchKeyword,
            )
            .then((res) => {
                pagination.value.current = res.data.current;
                pagination.value.size = res.data.size;
                pagination.value.total = res.data.total;

                if (res.data.records) {
                    dataList.value = [...dataList.value, ...res.data.records];
                }

                loading.value = false;
                pagination.value.current++;

                if (res.data.size * res.data.current >= res.data.total) {
                    canLoadMore.value = false;
                }

                // 发送加载完成事件
                emitLoadComplete();
            })
            .catch(() => {
                loading.value = false;
                // 发送加载完成事件，表示加载出错
                emitLoadComplete();
            });
    }
}

// 监听dataList的变化，来更新selectedIndex
watch(
    () => dataList.value,
    (newVal, oldVal) => {
        // 获取用户当前视口高度
        const { height } = useWindowSize();

        if (newVal?.length > 0 && props.isLoadFirst) {
            // 自动选中第一项并触发itemClick事件
            selectItem(newVal[0], 0);
        } else if (keepSelected.value || oldVal?.length > 0) {
            selectedIndex.value = selectedIndex.value;
        }
        // else {
        //     selectedIndex.value = -1;
        //     暂时注释掉影响新功能的代码
        // }

        // 如果数据量小于5条，或者数据量乘以135小于视口高度，则显示加载更多
        if (!newVal.length) return;
        if (newVal.length < 5 || newVal.length * 135 < height.value) {
            canLoadMore.value = false;
        } else if (newVal.length < 10) {
            canLoadMore.value = false;
        }
        keepSelected.value = false;
    },
    { immediate: true },
);

// 监听标签页数据变化
watch(
    tabsData,
    (newVal) => {
        if (!props.useTabStyle || !props.isLoadFirst) return;
        // 获取当前活动标签页的数据
        const currentTabKey = activeTab.value;
        const currentTabData = newVal[currentTabKey];

        if (currentTabData?.dataList?.length > 0) {
            // 自动选中第一项并触发itemClick事件
            selectItem(currentTabData.dataList[0], 0, currentTabKey);
        }
    },
    { deep: true },
);

const calculatedHeight = ref<string>(""); // 存储动态高度

function updateHeight() {
    const headerHeight = 64; // 顶部导航栏高度
    const tabsHeight = 45; // 滚动内分页标签页高度
    const navBarHeight = 48 + 64; // 顶部导航栏 + margin-top高度
    const searchBoxHeight = 48 + 24; // 搜索栏 + margin-top高度
    const extraMargin = 24 + 12; // 组件间的间距
    if (props.useTabStyle) {
        calculatedHeight.value = `calc(100vh - ${headerHeight + navBarHeight + tabsHeight + searchBoxHeight + extraMargin}px)`;
    } else {
        calculatedHeight.value = `calc(100vh - ${headerHeight + navBarHeight + searchBoxHeight + extraMargin}px)`;
    }
}

// 初次挂载和窗口大小改变时更新高度
onMounted(() => {
    updateHeight();
    window.addEventListener("resize", updateHeight);

    // 如果设置了showAll或isLoadFirst，初始加载数据
    if (props.showAll || props.isLoadFirst) {
        if (props.useTabStyle && props.tabConfig.length > 0) {
            // 对于标签页模式，确保使用直接loadMore而不是refreshAll
            // refreshAll会重置数据，可能导致显示问题
            if (activeTab.value) {
                loadMore(activeTab.value);
            } else if (props.tabConfig.length > 0) {
                loadMore(props.tabConfig[0].key);
            }
        } else {
            refreshAll();
        }
    }
});

// 组件卸载时移除事件监听
onUnmounted(() => {
    window.removeEventListener("resize", updateHeight);
});

// 监听showAll属性变化
watch(
    () => props.showAll,
    () => {
        // 当showAll属性变化时，重新加载数据
        refreshAll(props.useTabStyle ? activeTab.value : undefined);
    },
);

// 判断是否加载过空数据
function hasLoadedEmptyData(tabKey) {
    const tabData = tabsData[tabKey];
    return tabData.dataList.length === 0 && !tabData.canLoadMore;
}

// 判断是否加载过空数据
function isTabLoading(tabKey) {
    const tabData = tabsData[tabKey];
    return loading.value && tabData.canLoadMore;
}
</script>

<style scoped lang="scss">
@use "../../assets/styles/mixins.scss" as mix;
// 标题颜色
$titleColor: #23346d;

// 标题的高度和行高
$titleHeight: 28px;

// item 内部元素的内边距
$itemPadding: 16px;

// 左侧列表的宽度
$ListWidth: 256px;

// 右侧详情面板宽度
$detailWidth: calc(100%);

.wrapper {
    display: flex;
    height: 100%;
    gap: 10px; // 使用gap替代margin-left

    .tab-list {
        min-width: 256px; // 最小宽度
        height: 100%;
        /* @include mix.custom-scrollBar-height; */
        width: $ListWidth; // 两侧各减去间隙的一半
        border-radius: 5px;
        background: #fff;
        flex-shrink: 0; // 防止被压缩
        flex-grow: 0; // 不自动增长

        .bottom {
            div {
                height: 50px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #5e5e5e;
                font-size: 13px;
            }
        }

        .list-item {
            cursor: pointer;
            padding: $itemPadding;
            transition: background 0.3s;
            border-bottom: solid #a2abbe 1px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            gap: 8px;

            &.selected {
                background: #ebf1ff;
                position: relative;

                &::before {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 4px; // 左侧线条宽度
                    height: 100%;
                    background-color: #1677ff;
                }
            }

            .item-main {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .item-left {
                    width: 100%;
                    display: flex;
                    flex-direction: column;
                    gap: 8px;
                }

                .item-title {
                    color: $titleColor;
                    height: $titleHeight;
                    line-height: $titleHeight;
                    font-size: 18px;
                    font-style: normal;
                    font-weight: 700;
                    letter-spacing: 0.9px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .item-desc {
                    color: #6c7cb2;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 20px; /* 1.429 */
                    letter-spacing: 0.7px;
                }
            }
        }
    }

    .detail {
        /* height: 100%; */
        background: #fff;
        min-width: 900px; // 最小宽度
        flex: 1; // 占据剩余空间
        border-radius: 5px;
        display: flex; // 让表格等内容根据容器尺寸自动缩放
        flex-direction: column;
        overflow: hidden; // 防止内容溢出撑开布局
    }
}

.custom-tabs {
    :deep(.el-tabs__header) {
        margin: 0;
        padding: 5px 0px 0;
        background: #f5f7fa;
    }

    :deep(.el-tabs__item) {
        color: #666;
        font-size: 14px;
        height: 40px;
        line-height: 40px;

        &.is-active {
            color: $titleColor;
            font-weight: bold;
        }
    }
}
</style>
