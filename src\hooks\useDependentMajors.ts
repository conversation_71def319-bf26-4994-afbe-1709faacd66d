import { UmsDept_ } from "@/apis";
import { umsDeptGetAllDependentMajor, umsDeptGetDependentMajor } from "@/apis/umsDeptController";
import { ref, onMounted } from "vue";

// 依托专业hooks
export const useDependentMajors = (withDisabled = false) => {
    /**
     * 已启用的依托专业列表
     */
    const activeDependentMajors = ref<UmsDept_[]>([]);

    /**
     * 所有依托专业列表(包含已被禁用的)
     */
    const allDependentMajors = ref<UmsDept_[]>([]);

    /**
     * 获取已启用的依托专业列表
     */
    const getActiveDependentMajors = async () => {
        const res = await umsDeptGetDependentMajor({});
        activeDependentMajors.value = res.data;
    };

    /**
     * 获取所有依托专业列表(包含已被禁用的)
     */
    const getAllDependentMajors = async () => {
        const res = await umsDeptGetAllDependentMajor({});
        allDependentMajors.value = res.data;
    };

    onMounted(() => {
        if (withDisabled) {
            getAllDependentMajors();
        } else {
            getActiveDependentMajors();
        }
    });

    return {
        activeDependentMajors,
        getActiveDependentMajors,
        allDependentMajors,
        getAllDependentMajors,
    };
}; 