<template>
    <div v-if="selectedItem" class="detail-content" v-loading="detailLoading">
        <DataEntry
            :record-list="[]"
            :is-read-only="true"
            :selected-template="selectedTemplate"
            @show-performance-rules="showPerformanceRules"
            @refresh="handleRefresh"
            :is-show-back-button="false"
            :row-id="selectedItem.id"
            :is-auto-fetch-data="true"
            :is-show-import-history="true"
            :is-show-performance-rating="true">
        </DataEntry>
    </div>
    <div v-else class="detail-content">
        <el-empty description="请从左侧选择录入记录查看详情。" />
    </div>

    <!-- =========================== 查看已关联绩效规则弹窗 =========================== -->
    <el-dialog v-model="performanceRulesDialogVisible" width="700">
        <template #header="{ titleId, titleClass }">
            <div style="border-bottom: 1px solid #ccc; padding-bottom: 10px">
                <h4 :id="titleId" :class="titleClass" style="color: #23346d">
                    {{ "已关联绩效规则 (" + getLeafNodesCount(performanceRulesList) + "条）" }}
                </h4>
            </div>
        </template>
        <div>
            <div v-for="v in performanceRulesList" :key="v.id" style="background-color: #fffcf9; padding: 10px">
                <div v-for="(node, index) in traverseTree(v, '')" :key="node.id" style="margin-bottom: 20px">
                    <div
                        style="font-size: 14px; font-weight: bold; color: #23346d; margin-bottom: 5px"
                        v-if="node.isLeaf">
                        {{ index + 1 }}.{{ node.parentPath }}
                    </div>
                    <div style="font-size: 13px; letter-spacing: 0.1em" v-if="node.isLeaf">{{ node.content }}</div>
                </div>
            </div>
        </div>
    </el-dialog>
    <!-- =========================== 查看已关联绩效规则弹窗 =========================== -->
</template>

<script setup lang="ts">
import { CmsCategoryTemplate_, CmsCheck_, CmsRowValueResult, CmsTag_, UmsPerson_ } from "@/apis/types";
import { ElMessage, ElMessageBox } from "element-plus";
import DataEntry from "@/components/DataEntry/index.vue";
import { ref, watch } from "vue";
import { cmsTagCategoryEntityGetTagTreeListByEntityId } from "@/apis/cmsTagCategoryEntityController";
import { getLeafNodesCount } from "@/utils/tree";
import { traverseTree } from "@/utils/tree";
import { cmsCategoryTemplateGetById } from "@/apis/cmsCategoryTemplateController";

const emit = defineEmits(["refresh"]);

// 选中的项目
const props = defineProps<{
    selectedItem: CmsRowValueResult;
    detailLoading: boolean;
    allPersonList: UmsPerson_[];
}>();

const selectedTemplate = ref<CmsCategoryTemplate_ | null>(null);

// 已关联绩效规则弹窗可见性
const performanceRulesDialogVisible = ref(false);

// 已关联绩效规则列表
const performanceRulesList = ref<CmsTag_[]>([]);

// 刷新
function handleRefresh() {
    try {
        // 触发父组件的刷新事件
        emit("refresh");
    } catch (error) {
        console.error("刷新过程发生错误:", error);
        ElMessage.error("刷新失败，请手动刷新页面");
    }
}

// 显示绩效规则弹窗
function showPerformanceRules(templateId: number) {
    performanceRulesDialogVisible.value = true;
    // 获取标签树结构
    cmsTagCategoryEntityGetTagTreeListByEntityId({ params: { categoryEntityId: templateId } }).then((res) => {
        performanceRulesList.value = res.data;
    });
}

// 当选中的项目变化时，更新模板信息
watch(
    () => props.selectedItem,
    (newItem) => {
        if (newItem && newItem.categoryTemplateId) {
            // 通过categoryTemplateId获取模板详情
            cmsCategoryTemplateGetById({ params: { id: newItem.categoryTemplateId } }).then((res) => {
                if (res.code === 200) {
                    selectedTemplate.value = res.data;
                }
            });
        }
    },
);
</script>

<style scoped lang="scss">
$padding: 0 30px;

// 每行最低高度
$min-height: 70px;

.approval-history {
    padding-left: 60px;
    .history-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .history-label {
            color: #23346d;
            font-size: 15px;
            font-weight: bold;
        }

        .history-value {
            color: #23346d;
            font-size: 14px;
        }
    }
}

.con-title {
    padding: $padding;

    span {
        border-left: 3px solid #8ba0c9;
        padding-left: 5px;
        font-size: 20px;
        font-weight: 700;
        letter-spacing: 0.1em;
        color: #23346d;
    }

    height: $min-height;
    display: flex;
    align-items: center;
}

.bold {
    font-weight: bold;
}
.pagination {
    display: flex;
    justify-content: end;
    margin-top: 10px;
    margin-right: 20px;
}

.flex-end {
    display: flex;
    justify-content: end;
}

.edit-box {
    margin-top: 10px;
    border: 1px solid #bec5d7;
    border-radius: 10px;
    .meta-info {
        background-color: #fff;
        border-bottom: 1px solid #bec5d7;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        gap: 10px;
        padding: 20px 10px;
    }
}

.detail-content {
    /* height: calc(100vh - 220px); */
    overflow-y: auto;
}
</style>
