/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 删除分组 POST /cms/cmsTemplateGroups/delete */
export async function cmsTemplateGroupsDelete({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsTemplateGroupsDeleteParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/cms/cmsTemplateGroups/delete', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据ID查询分组 GET /cms/cmsTemplateGroups/getById */
export async function cmsTemplateGroupsGetById({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsTemplateGroupsGetByIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCmsTemplateGroups_>(
    '/cms/cmsTemplateGroups/getById',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据大类ID查询分组 GET /cms/cmsTemplateGroups/getByTemplateId */
export async function cmsTemplateGroupsGetByTemplateId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsTemplateGroupsGetByTemplateIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsTemplateGroups_>(
    '/cms/cmsTemplateGroups/getByTemplateId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取分组列表 GET /cms/cmsTemplateGroups/list */
export async function cmsTemplateGroupsList({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsTemplateGroups_>(
    '/cms/cmsTemplateGroups/list',
    {
      method: 'GET',
      ...(options || {}),
    }
  );
}

/** 添加或修改分组信息 POST /cms/cmsTemplateGroups/saveOrUpdate */
export async function cmsTemplateGroupsSaveOrUpdate({
  body,
  options,
}: {
  body: API.CmsTemplateGroups_;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/cms/cmsTemplateGroups/saveOrUpdate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
