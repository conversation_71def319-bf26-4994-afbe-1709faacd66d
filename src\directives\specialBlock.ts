import type { Directive, DirectiveBinding } from "vue";

// 扩展HTMLElement类型，添加自定义属性
declare global {
    interface HTMLElement {
        _originalContent?: string;
        _processSpecialBlock?: () => void;
        _lastProcessedContent?: string;
    }
}

interface SpecialBlockOptions {
    className?: string;
    replaceTag?: string;
    optionsMap?: Record<string, string>;
}

export const vSpecialBlock: Directive<HTMLElement, SpecialBlockOptions> = {
    mounted(el, binding) {
        // 保存原始内容以便重置
        el._originalContent = el.innerHTML;
        el._lastProcessedContent = '';
        processContent(el, binding.value);
    },

    updated(el, binding) {
        // 如果内容没有变化，不重新处理
        if (el._originalContent === el.innerHTML && el._lastProcessedContent) {
            return;
        }
        
        // 保存新的原始内容
        el._originalContent = el.innerHTML;
        
        // 使用requestAnimationFrame确保DOM操作在下一帧进行，减少闪烁
        requestAnimationFrame(() => {
        processContent(el, binding.value);
        });
    },

    beforeUnmount(el) {
        // 还原原始内容
        el.innerHTML = el._originalContent || el.innerHTML;
        delete el._originalContent;
        delete el._processSpecialBlock;
        delete el._lastProcessedContent;
    },
};

function processContent(el: HTMLElement, options: SpecialBlockOptions = {}) {
    const config = {
        className: options.className || "special-block",
        replaceTag: options.replaceTag || "span",
        optionsMap: options.optionsMap || {},
    };

    let content = el.innerHTML;
    
    // 检查内容是否已经处理过，避免重复处理
    const processedKey = JSON.stringify(options) + content;
    if (el._lastProcessedContent === processedKey) {
        return;
    }

    // 创建替换正则表达式
    const openTagRegex = /<specialBlock\s*>/gi;
    const closeTagRegex = /<\/specialBlock\s*>/gi;

    // 执行替换
    content = content
        .replace(openTagRegex, `<${config.replaceTag} class="${config.className}">`)
        .replace(closeTagRegex, `</${config.replaceTag}>`);

    el.innerHTML = content;
    el._lastProcessedContent = processedKey;

    // 立即处理ID替换，不使用setTimeout避免闪烁
        const specialBlocks = el.querySelectorAll(`.${config.className}`);
        specialBlocks.forEach((block) => {
            const id = block.textContent;
            if (id && config.optionsMap[id]) {
                block.textContent = config.optionsMap[id];
            }
        });
}

export default vSpecialBlock;
