/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 查询所有人员 给所有录入数据回显使用 GET /ums/umsPerson/all */
export async function umsPersonAll({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListUmsPerson_>('/ums/umsPerson/all', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 查询所有人员 给人员管理页面，所有录入时候选择人员查询使用 GET /ums/umsPerson/allPersonList */
export async function umsPersonAllPersonList({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListUmsPerson_>(
    '/ums/umsPerson/allPersonList',
    {
      method: 'GET',
      ...(options || {}),
    }
  );
}

/** 根据权限卡id查询person列表 GET /ums/umsPerson/audit/list */
export async function umsPersonAuditList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsPersonAuditListParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListUmsPerson_>('/ums/umsPerson/audit/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 添加人员并注册 POST /ums/umsPerson/create */
export async function umsPersonCreate({
  body,
  options,
}: {
  body: API.UmsPerson0Req;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsPerson/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 添加身份证信息 POST /ums/umsPerson/createSensitive */
export async function umsPersonCreateSensitive({
  body,
  options,
}: {
  body: API.UmsPersonSensitive_;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsPerson/createSensitive', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据id删除学术/社会组织兼职数据 POST /ums/umsPerson/deleteAcademicRoles */
export async function umsPersonDeleteAcademicRoles({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsPersonDeleteAcademicRolesParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsPerson/deleteAcademicRoles', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据id删除证书 职称、证书 POST /ums/umsPerson/deleteCertificates */
export async function umsPersonDeleteCertificates({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsPersonDeleteCertificatesParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsPerson/deleteCertificates', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据人员id和部门id删除人员和部门关系 POST /ums/umsPerson/deletePersonDeptById */
export async function umsPersonDeletePersonDeptById({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsPersonDeletePersonDeptByIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsPerson/deletePersonDeptById', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据id获取人员信息 GET /ums/umsPerson/detail */
export async function umsPersonDetail({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsPersonDetailParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultUmsPerson_>('/ums/umsPerson/detail', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据部门查询人员 GET /ums/umsPerson/listByDeptIds */
export async function umsPersonListByDeptIds({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsPersonListByDeptIdsParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListUmsPerson_>(
    '/ums/umsPerson/listByDeptIds',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据角色查询人员 GET /ums/umsPerson/listByRoleIds */
export async function umsPersonListByRoleIds({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsPersonListByRoleIdsParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListUmsPerson_>(
    '/ums/umsPerson/listByRoleIds',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 多条件分页查询人员 person为必要传入的对象,不需要查的值直接将那一行数据删除或者将值设为''都可以 POST /ums/umsPerson/listPerson */
export async function umsPersonListPerson({
  params,
  body,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsPersonListPersonParams;
  body: API.UmsPerson_;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageUmsPerson_>(
    '/ums/umsPerson/listPerson',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      params: {
        // pageNum has a default value: 1
        pageNum: '1',
        // pageSize has a default value: 5
        pageSize: '5',
        ...params,
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 新增或修改学术/社会组织兼职数据 POST /ums/umsPerson/saveAndUpdateAcademicRoles */
export async function umsPersonSaveAndUpdateAcademicRoles({
  body,
  options,
}: {
  body: API.UmsAcademicRoles_;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>(
    '/ums/umsPerson/saveAndUpdateAcademicRoles',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 新增或修改证书 证书，新增必须传证书类型（0：资格证书，1：职称，2：教师资格证） POST /ums/umsPerson/saveAndUpdateCertificates */
export async function umsPersonSaveAndUpdateCertificates({
  body,
  options,
}: {
  body: API.UmsCertificate_;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>(
    '/ums/umsPerson/saveAndUpdateCertificates',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 根据人员id获取人员身份证信息 GET /ums/umsPerson/sensitiveDetails */
export async function umsPersonSensitiveDetails({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsPersonSensitiveDetailsParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultUmsPersonSensitive_>(
    '/ums/umsPerson/sensitiveDetails',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 更新人员 POST /ums/umsPerson/update */
export async function umsPersonUpdate({
  body,
  options,
}: {
  body: API.UmsPersonUpdateParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsPerson/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新身份证信息 POST /ums/umsPerson/updateSensitive */
export async function umsPersonUpdateSensitive({
  body,
  options,
}: {
  body: API.UmsPersonSensitive_;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsPerson/updateSensitive', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
