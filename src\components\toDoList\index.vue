<template>
    <div class="todo-container">
        <!-- 大标题 -->
        <div class="todo-title">
            <div>
                <img style="width: 24px; height: 24px" src="@/assets/fileIcon.png" />
            </div>
            &nbsp;
            {{ title }}
        </div>
        <!-- 待办事项列表 -->
        <div class="todo-list">
            <!-- 分组显示不同type的待办事项 -->
            <div v-for="group in todoGroups" :key="group.type" class="todo-group">
                <!-- 分组标题行 -->
                <div class="group-header" @click="toggleGroup(group.type)">
                    <div class="group-header-content">
                        <el-icon color="#8BA0C9" size="16" :class="{ 'arrow-rotated': !group.collapsed }">
                            <ArrowRight />
                        </el-icon>
                        <h3 class="group-title">
                            {{ group.title }}<span class="group-count">{{ group.items.length }}</span>
                        </h3>
                    </div>
                </div>

                <!-- 分割线 -->
                <div class="group-divider"></div>

                <!-- 分组内容 - 可折叠 -->
                <div v-if="!group.collapsed" class="group-content">
                    <div
                        v-for="(item, index) in group.items"
                        :key="item.id"
                        class="todo-item"
                        @click="handleClick(item, index)">
                        <div class="item-content-wrapper">
                            <!-- 代录入标识 -->
                            <div v-if="item.isProxy" class="proxy-badge">代</div>
                            <p class="item-content" :class="{ 'with-proxy': item.isProxy }">{{ item.content }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 查看更多 -->
            <div v-if="hasMore" class="view-more" @click="handleViewMore">查看更多</div>

            <!-- 空状态 -->
            <div v-if="todoGroups.length === 0">
                <el-empty description="暂无内容" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { TodoResult } from "@/apis/types";
import { computed, ref } from "vue";
import { useRouter } from "vue-router";
import auth from "@/plugins/auth";
import { PermissionEnum } from "@/enums/roles/authorCards";

const router = useRouter();

// Props
const props = defineProps({
    type: {
        type: String,
        required: true,
    },
    todos: {
        type: Array as PropType<TodoResult[]>,
        required: false,
        default: () => [],
    },
    count: {
        type: Number,
        required: false,
        default: 0,
    },
});

// 通过解构获取 type, todos, count
const { type, todos, count } = toRefs(props);

// 折叠状态管理
const collapsedGroups = ref<Set<number>>(new Set());

// 动态标题
const title = computed(() => {
    const types = {
        approval: "待审核事项",
        entry: "录入待办事项",
        team: "项目团队待办事项",
    };
    return types[type.value] || "未知待办类型";
});

// 获取type对应的标题
const getTypeTitle = (todoType: number) => {
    const typeTitles = {
        0: "你有待审核的[业绩录入]",
        1: "你有新的[批量导入]需补充信息",
        2: "你有[业绩录入]未通过审核",
        3: "你有新的[项目团队邀请]",
        4: "你有待审核的[新闻]",
    };
    return typeTitles[todoType] || "未知类型";
};

// 根据当前组件type过滤应该显示的todo类型
const getValidTypes = () => {
    if (type.value === "approval") {
        return [0, 4]; // 待审核事项：业绩录入和新闻
    } else if (type.value === "entry") {
        return [1, 2]; // 录入待办事项：批量导入和审核退回
    } else if (type.value === "team") {
        return [3]; // 项目团队待办事项
    }
    return [];
};

// 分组待办事项
const todoGroups = computed(() => {
    const validTypes = getValidTypes();
    const groups = [];

    // 按type分组
    const groupedTodos = {};
    todos.value.forEach((todo) => {
        if (validTypes.includes(todo.type)) {
            if (!groupedTodos[todo.type]) {
                groupedTodos[todo.type] = [];
            }
            groupedTodos[todo.type].push(todo);
        }
    });

    // 构建分组数据
    validTypes.forEach((todoType) => {
        const items = groupedTodos[todoType] || [];
        if (items.length > 0) {
            groups.push({
                type: todoType,
                title: getTypeTitle(todoType),
                items: items,
                collapsed: collapsedGroups.value.has(todoType),
            });
        }
    });

    return groups;
});

// 是否有更多数据
const hasMore = computed(() => {
    return count.value > todos.value.length;
});

// 切换分组折叠状态
const toggleGroup = (groupType: number) => {
    if (collapsedGroups.value.has(groupType)) {
        collapsedGroups.value.delete(groupType);
    } else {
        collapsedGroups.value.add(groupType);
    }
};

// 点击待办事项
const handleClick = (item: TodoResult, index: number) => {
    // 根据type类型进行跳转
    if (item.type == 0) {
        router.push(
            `/workspace/adminApproval/pendingApproval?queryListItemId=${item.id}&queryIndex=${index}&queryType=0`
        );
    } else if (item.type == 1) {
        // 待补充
        router.push(`/workspace/teacherEntry/toBeUpdate?queryListItemId=${item.id}&queryIndex=${index}&queryType=1`);
    } else if (item.type == 2) {
        // 审核退回，根据用户拥有的权限进行跳转
        if (auth.hasPermi(PermissionEnum.TEACHER_ENTRY)) {
            router.push(
                `/workspace/teacherEntry/toBeUpdate?queryListItemId=${item.id}&queryIndex=${index}&queryType=2`
            );
        } else if (auth.hasPermi(PermissionEnum.ADMIN_ENTRY)) {
            router.push(`/workspace/adminEntry/adminToBeUpdate?queryListItemId=${item.id}&queryIndex=${index}&queryType=2`);
        }
    } else if (item.type == 3) {
        // 团队待确认申请
        router.push(
            `/workspace/teamManagement/beVerifiedTeam?queryListItemId=${item.id}&queryIndex=${index}&queryType=3`
        );
    } else if (item.type == 4) {
        // 新闻审核
        router.push(`/workspace/newsManagement/pendingNews?queryListItemId=${item.id}&queryIndex=${index}&queryType=4`);
    }
};

// 查看更多
const handleViewMore = () => {
    // 根据type类型进行跳转
    if (type.value == "approval") {
        router.push("/workspace/adminApproval/pendingApproval");
    } else if (type.value == "entry") {
        router.push("/workspace/teacherEntry/toBeUpdate");
    } else if (type.value == "team") {
        router.push("/workspace/teamManagement/beVerifiedTeam");
    }
};
</script>

<style scoped lang="scss">
// 修改滚动条样式
::-webkit-scrollbar {
    width: 4px;
}

::-webkit-scrollbar-thumb {
    border-radius: 2px;
    width: 4px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #ccc;
}

::-webkit-scrollbar-track {
    width: 5px;
}

.todo-container {
    width: 100%;
    height: 424px;
    border: 1px solid #dcdcdc;
    border-radius: 8px;
    background-color: #ffffff;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;

    .todo-title {
        border-radius: 8px 8px 0 0;
        font-size: 20px;
        font-weight: bold;
        color: #ffffff;
        padding-left: 40px;
        background: #6e88ac;
        height: 72px;
        display: flex;
        align-items: center;
        text-shadow: 0px 4px 4px rgba(95, 109, 134, 0.25);
        letter-spacing: 1.2px;
    }

    .todo-list {
        flex: 1;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding-bottom: 3px;

        .todo-group {
            .group-header {
                margin-top: 7px;
                padding: 0 32px;
                cursor: pointer;

                .group-header-content {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .el-icon {
                        transition: transform 0.3s ease;

                        &.arrow-rotated {
                            transform: rotate(90deg);
                        }
                    }

                    .group-title {
                        color: #23346d;
                        font-size: 16px;
                        font-weight: 500;
                        line-height: 28px;
                        letter-spacing: 0.9px;
                        flex-grow: 1;
                        margin: 0;
                    }

                    .group-count {
                        width: 20px;
                        height: 20px;
                        background: #ff4d4f;
                        border-radius: 10px;
                        color: #ffffff;
                        font-size: 14px;
                        text-align: center;
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        padding: 0 6px;
                        margin-left: 10px;
                    }
                }
            }

            .group-divider {
                background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTI4IiBoZWlnaHQ9IjEiIHZpZXdCb3g9IjAgMCA1MjggMSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGxpbmUgeTE9IjAuNSIgeDI9IjUyOCIgeTI9IjAuNSIgc3Ryb2tlPSIjRDFERkU5Ii8+Cjwvc3ZnPgo=");
                height: 1px;
                margin-top: 8px;
                background-size: cover;
            }

            .group-content {
                .todo-item {
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    border-bottom: 1px solid #dcdcdc;
                    min-height: 44px;
                    .item-content-wrapper {
                        display: flex;
                        align-items: center;
                        min-height: 24px;
                        padding-left: 17px;
                        padding-right: 32px;
                        gap: 17px;

                        .proxy-badge {
                            // margin-top: 10px;
                            background: #afb694;
                            box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.05);
                            border-radius: 12px;
                            color: #ffffff;
                            font-size: 14px;
                            line-height: 20px;
                            width: 30px;
                            height: 24px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            flex-shrink: 0;
                        }

                        .item-content {
                            color: #6c7cb3;
                            font-size: 14px;
                            letter-spacing: 0.7px;
                            flex-grow: 1;
                            margin: 0;
                            line-height: 24px;

                            &.with-proxy {
                                // 代录入项不需要额外的左边距
                            }

                            &:not(.with-proxy) {
                                // 普通项需要额外的左边距来对齐
                                margin-left: 32px; // 30px(代标识宽度) + 17px(间距) - 17px(gap) = 30px，但考虑到已有gap，这里用32px
                            }
                        }
                    }
                }
            }
        }

        .view-more {
            text-align: center;
            padding: 10px 0;
            color: #6c7cb2;
            cursor: pointer;
            margin-top: 8px;
        }
    }
}
</style>
