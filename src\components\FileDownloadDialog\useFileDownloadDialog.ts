import { ref } from 'vue';

export interface UseFileDownloadDialogOptions {
    fileListString?: string;
}

export function useFileDownloadDialog(options: UseFileDownloadDialogOptions = {}) {
    const visible = ref(false);
    const fileListString = ref(options.fileListString || '');

    const openDialog = (files?: string) => {
        if (files) {
            fileListString.value = files;
        }
        visible.value = true;
    };

    const closeDialog = () => {
        visible.value = false;
    };

    const updateFileList = (files: string) => {
        fileListString.value = files;
    };

    return {
        visible,
        fileListString,
        openDialog,
        closeDialog,
        updateFileList
    };
} 