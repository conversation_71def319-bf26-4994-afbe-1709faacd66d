# 使用官方 NGINX 镜像
FROM nginx:1.28-alpine

# 设置工作目录
WORKDIR /usr/share/nginx/html

# 删除默认配置和内容
RUN rm /etc/nginx/conf.d/default.conf && \
    rm -rf /usr/share/nginx/html/*

# 复制自定义配置文件
COPY nginx.conf /etc/nginx/conf.d/

# 拷贝 Vite 构建后的静态资源
COPY dist/ /usr/share/nginx/html/

# 创建非root用户（如果需要）
RUN addgroup -g 1001 -S nginx-user && \
    adduser -S nginx-user -u 1001 -G nginx-user

# 声明端口
EXPOSE 80

# 设置维护者信息
LABEL maintainer="binuo"

# 默认命令（nginx镜像已包含）
CMD ["nginx", "-g", "daemon off;"]
