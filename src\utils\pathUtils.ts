/**
 * 截取路径到指定层级
 * @param path 原始路径字符串
 * @param index 要保留的层级数（从1开始计数）
 * @returns 截取后的路径字符串
 */
export function truncatePathToLevel(path: string, index: number): string {
    // 拆分路径并过滤空段（处理开头/结尾的斜杠）
    const segments = path.split('/').filter(segment => segment !== '');
    
    // 处理索引超出范围的情况
    const safeIndex = Math.min(Math.max(index, 1), segments.length);
    
    // 截取前N段并重新拼接
    return '/' + segments.slice(0, safeIndex).join('/');
} 