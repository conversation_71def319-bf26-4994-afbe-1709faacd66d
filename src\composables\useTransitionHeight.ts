import { type TransitionProps } from "vue";

/**
 * 高度自适应展开收起动画
 * @returns transition动画钩子函数
 */
export function useTransitionHeight() {
    // 进入动画开始
    const onEnter: TransitionProps["onEnter"] = (el: Element, done) => {
        const htmlEl = el as HTMLElement;
        // 设置元素的高度为0
        htmlEl.style.height = "0";
        // 强制浏览器重绘
        void htmlEl.offsetHeight;
        // 设置元素高度为实际内容的高度
        htmlEl.style.height = (htmlEl.children[0] as HTMLElement).offsetHeight + "px";

        // 监听过渡结束事件
        const onTransitionEnd = () => {
            done();
            htmlEl.removeEventListener("transitionend", onTransitionEnd);
        };
        htmlEl.addEventListener("transitionend", onTransitionEnd);
    };

    // 进入动画结束
    const onAfterEnter: TransitionProps["onAfterEnter"] = (el: Element) => {
        const htmlEl = el as HTMLElement;
        // 清除高度限制
        htmlEl.style.height = "";
    };

    // 离开动画
    const onLeave: TransitionProps["onLeave"] = (el: Element, done) => {
        const htmlEl = el as HTMLElement;
        // 设置元素当前高度
        htmlEl.style.height = htmlEl.offsetHeight + "px";
        // 强制浏览器重绘
        void htmlEl.offsetHeight;
        // 设置高度为0触发动画
        htmlEl.style.height = "0";

        // 监听过渡结束事件
        const onTransitionEnd = () => {
            done();
            htmlEl.removeEventListener("transitionend", onTransitionEnd);
        };
        htmlEl.addEventListener("transitionend", onTransitionEnd);
    };

    return {
        onEnter,
        onAfterEnter,
        onLeave,
    };
}
