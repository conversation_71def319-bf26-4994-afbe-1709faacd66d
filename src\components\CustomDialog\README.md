# CustomDialog 自定义对话框组件

基于设计图重新设计的指令式弹窗组件，支持多种状态类型和自定义配置。

## 特性

- 🎨 基于最新设计图的现代化UI风格
- 🔧 指令式调用，使用简单
- 📱 响应式设计，支持不同设备
- 🎭 多种状态类型：success、error、warning、info、confirm
- 🖼️ 使用本地图标资源，加载更快
- ⚙️ 高度可定制化
- 🔄 支持异步操作和回调

## 设计更新

根据最新设计图进行了以下更新：

1. **布局调整**：图标和标题并排显示，图标位于左侧
2. **图标系统**：使用本地 `dialog_icons` 文件夹中的PNG图标
3. **样式优化**：按钮样式、间距、字体大小等完全符合设计规范
4. **confirm类型**：右上角显示关闭按钮，内容区域无图标

## 安装和引入

```typescript
import { useCustomDialog } from '@/components/CustomDialog';

const { success, error, warning, info, confirm } = useCustomDialog();
```

## 基础用法

### 成功提示

```typescript
// 简单文本
success('保存成功');

// 完整配置
success({
    title: '保存成功',
    message: '如该表涉及绩效，请到绩效设置页面进行配置。',
    onConfirm: () => {
        console.log('用户点击了确定');
    }
});
```

### 错误提示

```typescript
error({
    title: '保存失败',
    message: '当前保存未成功，请重试。'
});
```

### 警告提示

```typescript
warning({
    title: '请添加批注',
    message: '如确定审核不予通过，请至少填写一条批注，以便提交人进行相应修改。',
    showCancelButton: true
});
```

### 信息提示

```typescript
info({
    title: '审核未通过',
    message: '当前审核未通过，提交人将收到通知。'
});
```

### 确认对话框

```typescript
confirm({
    title: '确认此表不在您的审核范围内吗？',
    message: '点击"确定"可将此表返回负责人处，负责人将重新为该表分配审核人。',
    onConfirm: async () => {
        // 确认操作
        await api.reassignForm();
    },
    onCancel: () => {
        // 取消操作
        console.log('用户取消了操作');
    }
});
```

### 按钮属性自定义

```typescript
// 危险操作确认，使用红色按钮
confirm({
    title: '确认删除',
    message: '此操作将永久删除数据，是否继续？',
    confirmButtonProps: {
        type: 'danger',
        plain: true
    },
    cancelButtonProps: {
        type: 'info'
    }
});

// 加载状态按钮
success({
    title: '保存中...',
    message: '请等待数据保存完成',
    confirmButtonProps: {
        loading: true,
        disabled: true
    }
});

// 文本按钮样式
warning({
    title: '警告提示',
    message: '检测到潜在风险，建议谨慎操作',
    confirmButtonProps: {
        text: true,
        type: 'warning'
    },
    cancelButtonProps: {
        text: true,
        type: 'info'
    },
    showCancelButton: true
});

// 带图标的按钮
info({
    title: '操作提示',
    message: '点击确定继续操作',
    confirmButtonProps: {
        type: 'primary',
        round: true,
        icon: markRaw(Check) // 需要使用 markRaw 包装图标组件
    }
});
```

## 按钮属性接口

```typescript
interface ButtonProps {
    type?: 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger' | 'text';
    size?: 'large' | 'default' | 'small';
    plain?: boolean;               // 朴素按钮
    text?: boolean;                // 文字按钮
    bg?: boolean;                  // 背景色
    link?: boolean;                // 链接按钮
    round?: boolean;               // 圆角按钮
    circle?: boolean;              // 圆形按钮
    loading?: boolean;             // 加载状态
    disabled?: boolean;            // 禁用状态
    autofocus?: boolean;           // 自动聚焦
    icon?: Component;              // 图标组件
    loadingIcon?: Component;       // 加载图标
    color?: string;                // 自定义颜色
    dark?: boolean;                // 深色模式
    autoInsertSpace?: boolean;     // 自动插入空格
}
```

### HTML 内容解析

```typescript
// 启用 HTML 解析功能
success({
    title: '富文本内容',
    message: '保存成功！请查看 <strong>详细信息</strong> 或 <a href="#" style="color: #1677ff;">点击这里</a> 进行下一步操作。',
    dangerouslyUseHTMLString: true,
    onConfirm: () => {
        console.log('用户确认了操作');
    }
});

// 也可以包含更复杂的HTML结构
warning({
    title: '注意事项',
    message: `
        <div style="line-height: 1.6;">
            <p>请注意以下事项：</p>
            <ul style="margin: 8px 0; padding-left: 20px;">
                <li>确保数据的准确性</li>
                <li>检查必填字段</li>
                <li style="color: #ff4d4f; font-weight: bold;">操作不可撤销</li>
            </ul>
        </div>
    `,
    dangerouslyUseHTMLString: true,
    showCancelButton: true
});
```

## 配置选项

```typescript
interface CustomDialogOptions {
    title?: string;                    // 对话框标题
    message?: string | VNode;          // 对话框内容
    dangerouslyUseHTMLString?: boolean; // 是否将 message 作为 HTML 解析，默认 false
    type?: 'success' | 'error' | 'warning' | 'info' | 'confirm'; // 对话框类型
    icon?: Component | string;         // 自定义图标组件
    iconUrl?: string;                  // 自定义图标URL（会覆盖默认图标）
    iconSize?: number;                 // 图标大小，默认24px
    iconColor?: string;                // 图标颜色
    width?: string | number;           // 对话框宽度，默认384px
    showClose?: boolean;               // 是否显示关闭按钮（confirm类型自动为true）
    showButtons?: boolean;             // 是否显示按钮区域，默认true
    showCancelButton?: boolean;        // 是否显示取消按钮
    showConfirmButton?: boolean;       // 是否显示确认按钮，默认true
    cancelButtonText?: string;         // 取消按钮文本，默认"取 消"
    confirmButtonText?: string;        // 确认按钮文本，默认"确 定"
    cancelButtonProps?: ButtonProps;   // 取消按钮属性
    confirmButtonProps?: ButtonProps;  // 确认按钮属性
    buttonSize?: 'large' | 'default' | 'small'; // 按钮尺寸（会被按钮属性中的size覆盖）
    closeOnClickModal?: boolean;       // 点击遮罩层是否关闭
    closeOnPressEscape?: boolean;      // 按Esc键是否关闭
    alignCenter?: boolean;             // 是否居中显示
    dialogClass?: string;              // 自定义CSS类名
    beforeClose?: (done: () => void) => void; // 关闭前回调
    onConfirm?: () => void | Promise<void>;   // 确认回调
    onCancel?: () => void | Promise<void>;    // 取消回调
    onClose?: () => void | Promise<void>;     // 关闭回调
}
```

## 图标系统

组件使用本地图标文件，位于 `src/assets/dialog_icons/` 目录：

- `success.png` - 成功状态（绿色勾号）
- `error.png` - 错误状态（红色错误）
- `warning.png` - 警告状态（黄色警告）
- `info.png` - 信息状态（蓝色信息）

### 自定义图标

如果需要使用自定义图标，可以通过 `iconUrl` 属性传入：

```typescript
success({
    title: '自定义成功',
    message: '使用自定义图标',
    iconUrl: '/path/to/custom-icon.png'
});
```

## 样式说明

### 设计规范

- **对话框宽度**：421px
- **圆角**：8px
- **阴影**：多层阴影效果
- **图标尺寸**：24x24px
- **字体**：Alibaba PuHuiTi 3.0
- **主色调**：#1677FF

### 类型样式

1. **操作结果类型**（success、error、warning、info）
   - 图标和标题在同一行
   - 图标位于左侧，标题右对齐
   - 内容文本左对齐，与图标保持36px间距

2. **确认操作类型**（confirm）
   - 无图标显示
   - 右上角显示关闭按钮
   - 内容文本左对齐，无缩进

## 返回值

所有方法都返回 Promise，resolve 的值为用户的操作类型：

```typescript
const action = await confirm('确认删除吗？');
// action 可能的值：'confirm' | 'cancel' | 'close'

if (action === 'confirm') {
    // 用户点击了确认
} else if (action === 'cancel') {
    // 用户点击了取消
} else if (action === 'close') {
    // 用户点击了关闭或按了ESC
}
```

## 注意事项

1. 组件会自动挂载到 `document.body`
2. 支持同时显示多个对话框
3. 每个对话框都有独立的生命周期
4. confirm 类型会自动显示关闭按钮，其他类型默认不显示
5. 保留了外部传入图标的功能，通过 `iconUrl` 或 `icon` 属性实现

### 按钮属性优先级

- `confirmButtonProps` 和 `cancelButtonProps` 中的属性会覆盖默认设置
- `buttonSize` 属性会被按钮属性中的 `size` 覆盖
- 确认按钮默认 `type="primary"`，取消按钮默认 `type="default"`

```typescript
// 例如：buttonSize 会被 confirmButtonProps.size 覆盖
confirm({
    buttonSize: 'small',           // 应用到两个按钮
    confirmButtonProps: {
        size: 'large',             // 只影响确认按钮，覆盖 buttonSize
        type: 'danger'
    }
    // 结果：确认按钮是 large + danger，取消按钮是 small + default
});
```

### HTML 解析安全提示

⚠️ **安全警告**：当启用 `dangerouslyUseHTMLString` 时，请确保：

1. **只解析可信的 HTML 内容**，避免 XSS 攻击
2. **不要直接解析用户输入的内容**
3. **对动态内容进行适当的转义处理**
4. **优先使用纯文本，仅在必要时使用 HTML**

```typescript
// ❌ 危险：直接使用用户输入
const userInput = getUserInput(); // 可能包含恶意脚本
success({
    message: userInput,
    dangerouslyUseHTMLString: true // 有安全风险
});

// ✅ 安全：使用预定义的HTML模板
const safeHtmlTemplate = `
    <div>
        <p>操作完成！</p>
        <p>结果：<span style="color: #52c41a; font-weight: bold;">${escapeHtml(result)}</span></p>
    </div>
`;
success({
    message: safeHtmlTemplate,
    dangerouslyUseHTMLString: true
});
```

## 更新历史

### v2.2.0 (最新)
- 🎨 新增 `confirmButtonProps` 和 `cancelButtonProps`，支持自定义按钮属性
- ⚡ 支持 el-button 的所有常用属性：type、plain、text、round、loading 等
- 🎯 保持向后兼容，原有的 `buttonSize` 仍然有效
- 📖 新增按钮自定义使用示例和完整的属性说明

### v2.1.0
- ✨ 新增 `dangerouslyUseHTMLString` 选项，支持HTML内容解析
- 🔒 添加HTML解析安全提示和最佳实践
- 📚 更新文档，包含HTML用法示例

### v2.0.0 
- 🎨 基于最新设计图重构UI
- 🖼️ 使用本地PNG图标替代Element Plus图标
- 📐 调整布局：图标和标题并排显示
- 🎯 confirm类型显示关闭按钮
- 💄 优化按钮样式和间距
- 🔧 新增 `type` 属性用于区分对话框类型 