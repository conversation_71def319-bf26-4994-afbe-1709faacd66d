// 弹窗样式集合
// 用于统一管理项目中所有弹窗的样式

// 绩效规则弹窗样式
.performance-rules-dialog {
    // 自定义头部样式
    .dialog-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 4px 0;

        .header-title {
            color: rgba(0, 0, 0, 0.88);
            font-size: 16px;
            line-height: 24px;
            font-weight: 400;
        }

        .close-icon {
            width: 16px;
            height: 16px;
            cursor: pointer;
            opacity: 0.45;
            transition: opacity 0.3s;

            &:hover {
                opacity: 0.75;
            }
        }
    }

    // 规则路径样式
    .rule-path {
        color: #697b94;
        line-height: 22px;
        font-size: 14px;
        margin-top: 16px;
        margin-bottom: 8px;
    }

    // 规则内容容器
    .rule-content-wrapper {
        background: #f5f7fa;
        border-radius: 6px;
        padding: 16px;
        margin-bottom: 16px;

        .rule-content {
            color: rgba(0, 0, 0, 0.8);
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            min-height: 132px;
            word-break: break-word;
        }
    }

    // 空状态样式
    .empty-state {
        text-align: center;
        color: #c0c4cc;
        font-size: 14px;
        padding: 40px 0;
    }

    // 底部按钮样式
    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        margin-top: 12px;

        .change-rule-btn {
            background: #1677ff;
            border: 1px solid transparent;
            border-radius: 6px;
            box-shadow: 0px 2px 0px 0px rgba(5, 145, 255, 0.1);
            color: #ffffff;
            font-size: 14px;
            line-height: 22px;
            padding: 5px 16px;
            min-width: 116px;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
                background: #4096ff;
                box-shadow: 0px 2px 0px 0px rgba(5, 145, 255, 0.15);
            }

            &:active {
                background: #0958d9;
            }
        }
    }
}
