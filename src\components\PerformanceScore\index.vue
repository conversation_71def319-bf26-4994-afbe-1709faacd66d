<template>
    <div class="performance-score" v-if="detailAndScore.tagId !== null">
        <el-collapse v-model="activeNames">
            <el-collapse-item name="1">
                <template #title>
                    <div class="performance-score-title">
                        <div class="title-left">
                            <div class="title-icon">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                    width="16"
                                    height="16"
                                    viewBox="0 0 16 16">
                                    <defs>
                                        <clipPath id="clipPath7652368251">
                                            <path
                                                d="M0 0L16 0L16 16L0 16L0 0Z"
                                                fill-rule="nonzero"
                                                transform="matrix(1 0 0 1 0 0)" />
                                        </clipPath>
                                    </defs>
                                    <g clip-path="url(#clipPath7652368251)">
                                        <path
                                            d="M8 0L10.5 5L16 5.75L12 9.5L13 15L8 12.5L3 15L4 9.5L0 5.75L5.5 5L8 0Z"
                                            fill-rule="nonzero"
                                            transform="matrix(1 0 0 1 0 1)"
                                            fill="rgb(22, 119, 255)" />
                                    </g>
                                </svg>
                            </div>
                            <div class="title-text">评分详情</div>
                        </div>
                        <div class="title-right">
                            <span class="collapse-title" v-if="activeNames.includes('1')">收起详情</span>
                            <span class="collapse-title" v-else>展开详情</span>
                            <div class="arrow-icon" :class="{ expanded: activeNames.includes('1') }"></div>
                        </div>
                    </div>
                </template>
                <!-- 置空以清除默认图标 -->
                <template #icon="{ isActive }">
                    <span></span>
                </template>
                <div class="performance-score-detail">
                    <div class="detail-item">
                        <div class="label-text">
                            数据表名称：<span class="normal-weight">{{ detailAndScore.templateName }}</span>
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="label-with-tooltip">
                            <span class="label-text">数据表类型：</span>
                            <el-tooltip
                                popper-class="description-tooltip"
                                effect="customized"
                                :content="
                                    detailAndScore.templateType == categoryTemplateType['成果数据']
                                        ? '成果数据：指教师个人在教学、科研、社会服务等方面取得的具体成果。如：国家级教学成果奖、国家精品在线课程、发明专利等。'
                                        : detailAndScore.templateType == categoryTemplateType['工作数据']
                                          ? '工作数据：指教师在日常教学、日常工作、学院事务等活动中的行为与过程记录。如：参加会议、联系合作企业、指导学生社团、推荐学生就业等。'
                                          : '院情数据：指反映学院整体运行情况、资源配置与行政记录的综合性数据。如：年度考核数据、实训室资产数据、在校生数据等。'
                                "
                                placement="right">
                                <div class="question-mark">?</div>
                            </el-tooltip>
                        </div>
                        <div class="option-cards">
                            <template v-for="item in categoryTemplateType">
                                <div
                                    v-if="typeof item === 'number'"
                                    class="option-card"
                                    :class="{ active: detailAndScore.templateType === item }">
                                    {{ categoryTemplateType[item] }}
                                </div>
                            </template>
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="label-with-tooltip">
                            <span class="label-text">绩效分配模式：</span>
                            <el-tooltip
                                popper-class="description-tooltip"
                                effect="customized"
                                :content="
                                    detailAndScore.allocateType == tagAllocateType.PROJECT_SCORE
                                        ? `项目制计分：该表中的数据将以项目的形式来计算绩效总分数，选择该模式后，绩效分将以“每个参与人按比例分得该项目总绩效”的形式分配。若是单人项目，则项目负责人默认获得100%绩效分。若是多人项目，由项目负责人按比例分配给所有参与人。<br>
                                            例如标准研制业绩，根据该标准的类型和参与状态，产生了10分的绩效分，项目负责人在录入时，需要手动填写所有参与人应当获得比例（例如：三人参与，三人分别获得50%、25%、25%的绩效分，即5分、2.5分、2.5分）。`
                                        : `人头制计分：该表中的数据将以人员的形式来计算绩效分数，选择该模式后，绩效分将以“每个参与人独立获得评价标准中规定的绩效分数”的形式分配。<br>
                                            例如学院派员活动业绩，绩效评价标准规定参与即获得1分。在某项学院派员活动业绩审核通过后，所有参与人员都将获得1分。`
                                "
                                placement="right"
                                raw-content>
                                <div class="question-mark">?</div>
                            </el-tooltip>
                        </div>
                        <div class="option-cards">
                            <template v-for="item in tagAllocateType">
                                <div
                                    v-if="typeof item === 'number'"
                                    class="option-card"
                                    :class="{ active: detailAndScore.allocateType === item }">
                                    {{ tagAllocateTypeMap[item] }}
                                </div>
                            </template>
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="label-with-tooltip">
                            <span class="label-text">绩效赋分方式：</span>
                            <el-tooltip
                                popper-class="description-tooltip"
                                effect="customized"
                                :content="
                                    detailAndScore.scoreMethod == tagScoringMethod.AUTO
                                        ? '平台计算：由平台根据绩效规则自动计算出该项业绩所获得的绩效分数。'
                                        : '手动输入：由审核人员，在审核的时候根据具体的绩效评价标准手动输入该项业绩所获得的绩效分数。'
                                "
                                placement="right">
                                <div class="question-mark">?</div>
                            </el-tooltip>
                        </div>
                        <div class="option-cards">
                            <template v-for="item in tagScoringMethod">
                                <div
                                    v-if="typeof item === 'number'"
                                    class="option-card"
                                    :class="{ active: detailAndScore.scoreMethod === item }">
                                    {{ tagScoringMethodMap[item] }}
                                </div>
                            </template>
                        </div>
                    </div>
                    <div
                        class="detail-item"
                        v-if="
                            detailAndScore.scoreMethod != tagScoringMethod.MANUAL &&
                            detailAndScore.allocateType != tagAllocateType.HEAD_SCORE
                        ">
                        <div class="label-with-tooltip">
                            <span class="label-text">绩效计算方式：</span>
                            <el-tooltip
                                popper-class="description-tooltip"
                                effect="customized"
                                :content="
                                    detailAndScore.scoreSource == tagCalculationMethod.DATA_ITEM_COMBINATION
                                        ? `数据项组合计算分值：平台根据该表的某些数据项的内容，通过组合计算后，赋予与绩效评价标准相符的绩效总分数。<br>
                                            例如：某数据表相关的绩效评价标准内容中包括“主持国家级项目10分；参与国家级项目8分；主持省级项目8分；参与国家级项目6分”，此时，数据表中也会存在“级别：国家级、省级”和“参与状态：主持、参与”这两个数据项作为条件。平台通过组合数据项的内容后自动计算结果，判断并赋予该项业绩应得的绩效总分数。`
                                        : detailAndScore.scoreSource == tagCalculationMethod.FIXED_SCORE
                                          ? `固定分值：平台根据绩效规则设置中的分值，无需其他条件判断，直接赋予该项业绩固定的绩效分数。<br>
                                            例如：学校规定的国家级重大成果奖励项目，20分/项，只要相关的数据表有业绩录入，平台就会赋予其20分绩效分并按照参与人分配比例（由项目负责人填写）进行分数分配。`
                                          : '数据项等值分值：平台根据该表的某个数据项的值直接赋予绩效分数。例如，某教师在数据项“教师工作量完成度分数”一栏中的数值为70，该教师因此项获得的绩效分将直接被系统计算为70分。'
                                "
                                placement="right">
                                <div class="question-mark">?</div>
                            </el-tooltip>
                        </div>
                        <div class="option-cards">
                            <template v-for="item in tagCalculationMethod">
                                <div
                                    v-if="typeof item === 'number'"
                                    class="option-card"
                                    :class="{ active: detailAndScore.scoreSource === item }">
                                    {{ tagCalculationMethodMap[item] }}
                                    <span v-if="detailAndScore.scoreSource === item">
                                        {{
                                            (detailAndScore.combinationName === null
                                                ? ""
                                                : "- " + detailAndScore.combinationName) +
                                            "：" +
                                            detailAndScore.totalScore +
                                            "分"
                                        }}
                                    </span>
                                </div>
                            </template>
                        </div>
                    </div>
                    <div class="detail-item" v-if="detailAndScore.combinationList?.length > 0">
                        <div class="label-with-tooltip">
                            <span class="label-text">可计分数据项：</span>
                            <el-tooltip
                                popper-class="description-tooltip"
                                effect="customized"
                                :content="`可计分数据项展示了该数据表中决定最终绩效分的数据项，审核人员可对照“已关联绩效规则”检查是否有误。`"
                                placement="right">
                                <div class="question-mark">?</div>
                            </el-tooltip>
                        </div>
                        <div class="combination-list">
                            <template v-for="item in detailAndScore.combinationList">
                                <div class="combination-item">{{ item.combinationName }}:{{ item.score }}分</div>
                            </template>
                        </div>
                    </div>
                </div>
            </el-collapse-item>
        </el-collapse>

        <div class="total-score-section">
            <div class="label-text" style="color: #1677ff; font-weight: 600">项目总分：</div>
            <div
                class="score-input-wrapper"
                v-if="detailAndScore.scoreMethod === tagScoringMethod.AUTO || isAlreadyPassed">
                <div class="score-value">{{ detailAndScore.totalScore }}</div>
                <div class="score-unit">分</div>
            </div>
            <InputWithScore
                v-else
                :model-value="reAssignScore"
                @update:modelValue="(val) => emit('update:reAssignScore', val)" />
        </div>

        <div class="participants-section">
            <div class="label-with-tooltip">
                <span class="label-text">参与人得分情况：</span>
                <el-tooltip
                    popper-class="description-tooltip"
                    effect="customized"
                    :content="`参与人得分情况的说明信息`"
                    placement="right">
                    <div class="question-mark">?</div>
                </el-tooltip>
            </div>
        </div>

        <div class="custom-table">
            <div class="table-header">
                <div class="header-cell role-cell">角色</div>
                <div class="header-cell name-cell">姓名</div>
                <div
                    class="header-cell proportion-cell"
                    v-if="detailAndScore.allocateType != tagAllocateType.HEAD_SCORE">
                    分配得分比例
                </div>
                <div class="header-cell score-cell">实际得分</div>
            </div>
            <div class="table-body">
                <div class="table-row" v-for="(item, index) in detailAndScore.finalScoreList" :key="index">
                    <div class="body-cell role-cell">{{ item.templateValueName }}</div>
                    <div class="body-cell name-cell">{{ item.personName }}</div>
                    <div
                        class="body-cell proportion-cell"
                        v-if="detailAndScore.allocateType != tagAllocateType.HEAD_SCORE">
                        {{ item.finalScoreProportion }}%
                    </div>
                    <div
                        class="body-cell score-cell score-value"
                        v-if="
                            detailAndScore.allocateType === tagAllocateType.HEAD_SCORE ||
                            detailAndScore.scoreMethod === tagScoringMethod.AUTO
                        ">
                        {{ item.finalScore }}
                    </div>
                    <div
                        class="body-cell score-cell score-value"
                        v-if="detailAndScore.scoreMethod === tagScoringMethod.MANUAL">
                        {{ calculateActualScore(item.finalScoreProportion).toFixed(2) }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import {
    tagScoringMethod,
    tagAllocateType,
    tagCalculationMethod,
    tagAllocateTypeMap,
    tagScoringMethodMap,
    tagCalculationMethodMap,
} from "@/enums/tag/tagConfig";
import { categoryTemplateType } from "@/enums/categoryTemplate/categoryTemplateType";
import { ArrowUp, ArrowDown } from "@element-plus/icons-vue";
import { CmsTagDetailAndScoreDto } from "@/apis/types";
import InputWithScore from "@/components/InputWithScore/index.vue";

const props = defineProps<{
    /** 绩效评分详情 */
    detailAndScore: CmsTagDetailAndScoreDto;
    /** 重新赋分分值 */
    reAssignScore?: number | null;
    /** 是否已通过审核 */
    isAlreadyPassed?: boolean;
}>();

const emit = defineEmits<{
    (e: "update:reAssignScore", value: number | string | null): void;
}>();

// 平台计算得分折叠面板
const activeNames = ref(["0"]);

// 表格样式相关的方法
const headerCellStyle = {
    background: "rgba(211, 211, 211, 0.4)",
    color: "#23346D",
    fontWeight: "bold",
    border: "1px solid #dcdfe6",
};

const cellStyle = {
    color: "#23346D",
    border: "1px solid #ebeef5",
};

// 计算实际得分
const calculateActualScore = (proportion: number | string) => {
    const numProportion = Number(proportion || 0);
    // 如果为已通过审核的，则直接返回总分
    if (props.isAlreadyPassed) {
        return (props.detailAndScore.totalScore * numProportion) / 100;
    }
    // 如果未通过，则返回0
    if (!props.reAssignScore || isNaN(Number(props.reAssignScore))) {
        return 0;
    }
    return (numProportion / 100) * Number(props.reAssignScore);
};
</script>

<style scoped lang="scss">
// 覆盖el-tooltip样式
:global(.el-popper.is-customized.description-tooltip) {
    padding: 6px 12px;
    background: #f2f2f2;
    color: #818da4;
    font-weight: 400;
    max-width: 600px;
    box-shadow: rgba(0, 0, 0, 0.24) 0 3px 8px;
}

:global(.el-popper.is-customized.description-tooltip .el-popper__arrow::before) {
    background: #f2f2f2;
    right: 0;
}

.performance-score {
    padding: 24px 16px 32px 16px;
    background: #f9fbff;
    border-radius: 8px;
    color: #23346d;
    margin-bottom: 20px;

    font-size: 14px;
    :deep(.el-collapse) {
        border: none;

        .el-collapse-item__header {
            background-color: transparent;
            border: none;
            height: auto;
            padding: 0;
        }

        .el-collapse-item__wrap {
            background-color: transparent;
            border: none;
        }

        .el-collapse-item__content {
            padding: 17px 0 0 0;
        }
    }

    .performance-score-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .title-left {
            display: flex;
            align-items: center;
            gap: 8px;

            .title-icon {
                padding-bottom: 2px;
                display: flex;
                align-items: center;
            }

            .title-text {
                color: #1677ff;
                font-size: 16px;
                font-weight: 600;
                line-height: 24px;
            }
        }

        .title-right {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 1.5px 0;

            .collapse-title {
                color: #262626;
                font-size: 14px;
                line-height: 21px;
            }

            .arrow-icon {
                width: 14px;
                height: 21px;
                background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAxNCAyMSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTcgMTQuNUwxIDguNUg2SDhIMTNMNyAxNC41WiIgZmlsbD0iIzI2MjYyNiIvPgo8L3N2Zz4K");
                background-size: cover;
                transition: transform 0.3s ease;

                &.expanded {
                    transform: rotate(180deg);
                }
            }
        }
    }

    .performance-score-detail {
        margin-left: 0;
    }

    .detail-item {
        margin-top: 17px;

        &:first-child {
            margin-top: 0;
        }

        .label-text {
            color: #23346d;
            font-weight: 500;
            line-height: 20px;
            display: flex;
            align-items: center;

            .normal-weight {
                font-weight: normal;
            }
        }

        .label-with-tooltip {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 17px;

            .label-text {
                color: #23346d;
                font-weight: 500;
                line-height: 20px;
            }

            .question-mark {
                background: #b9bed1;
                border-radius: 9px;
                width: 18px;
                height: 18px;
                display: flex;
                justify-content: center;
                align-items: center;
                color: #ffffff;
                font-size: 12px;
                font-weight: 700;
                cursor: pointer;
            }
        }

        .option-cards {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;

            .option-card {
                background: #f5f7fa;
                border: 1px solid #e5e9f2;
                border-radius: 4px;
                padding: 6px 16px;
                color: #b1b7cb;
                line-height: 20px;
                cursor: pointer;
                transition: all 0.3s ease;
                min-height: 34px;
                display: flex;
                align-items: center;
                justify-content: center;

                &.active {
                    background: #ebf1ff;
                    border-color: #a8c0f0;
                    color: #23346d;
                }
            }
        }

        .combination-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: flex-start;

            .combination-item {
                font-weight: bold;
                color: #23346d;
            }
        }
    }

    .total-score-section {
        background: #ebf1ff;
        border-radius: 6px;
        display: flex;
        gap: 8px;
        align-items: center;
        margin-top: 16px;
        padding: 12px 16px;

        .score-input-wrapper {
            background: #ffffff;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            display: flex;
            align-items: center;
            padding: 6px 13px;
            gap: 8px;

            .score-value {
                color: rgba(0, 0, 0, 0.88);
                line-height: 21px;
                font-weight: 600;
            }

            .score-unit {
                color: #6b7995;
                line-height: 21px;
            }

            .manual-input {
                border: none;

                :deep(.el-input__wrapper) {
                    box-shadow: none;
                    padding: 0;
                }
            }
        }
    }

    .participants-section {
        margin-top: 25px;

        .label-with-tooltip {
            display: flex;
            align-items: center;
            gap: 8px;

            .label-text {
                color: #23346d;
                font-weight: 500;
                line-height: 20px;
            }

            .question-mark {
                background: #b9bed1;
                border-radius: 9px;
                width: 18px;
                height: 18px;
                display: flex;
                justify-content: center;
                align-items: center;
                color: #ffffff;
                font-size: 12px;
                font-weight: 700;
                cursor: pointer;
            }
        }
    }

    .custom-table {
        margin-top: 17px;
        border-radius: 8px;
        overflow: hidden;

        .table-header {
            display: flex;
            background: #f6f6f6;

            .header-cell {
                padding: 13px;
                color: rgba(0, 0, 0, 0.88);
                font-weight: 500;
                line-height: 21px;
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;

                &:last-child {
                    border-right: none;
                }

                &.role-cell {
                    flex: 1;
                    border-radius: 8px 0 0 0;
                }

                &.name-cell {
                    flex: 1;
                }

                &.proportion-cell {
                    flex: 1;
                }

                &.score-cell {
                    flex: 1;
                    border-radius: 0 8px 0 0;
                }
            }
        }

        .table-body {
            .table-row {
                display: flex;
                background: #ffffff;

                .body-cell {
                    padding: 13.25px 13px;
                    color: rgba(0, 0, 0, 0.88);
                    line-height: 21px;
                    text-align: center;
                    border-bottom: 1px solid #f0f0f0;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    &:last-child {
                        border-right: none;
                    }

                    &.role-cell {
                        flex: 1;
                    }

                    &.name-cell {
                        flex: 1;
                    }

                    &.proportion-cell {
                        flex: 1;
                    }

                    &.score-cell {
                        flex: 1;

                        &.score-value {
                            color: #1677ff;
                            font-weight: 600;
                        }
                    }
                }

                &:last-child .body-cell {
                    border-bottom: none;
                }
            }
        }
    }
}
</style>
