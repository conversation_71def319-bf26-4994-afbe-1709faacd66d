# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
```bash
# Start development server
npm run dev

# Build for development environment
npm run build:dev

# Build for production environment
npm run build:prod

# Preview production build
npm run preview

# Generate API client from OpenAPI spec
npm run openapi
```

### Environment Setup
Before running the application, you need to:
1. Update `src/utils/request.ts` to set the correct `baseURL` 
2. Update `src/utils/filesBaseUrl.ts` to set the correct file server URL
3. Ensure environment variables are set: `VITE_BASEURL` and `VITE_FILES_BASEURL`

## Project Architecture

### Technology Stack
- **Framework**: Vue 3 with Composition API + TypeScript
- **Build Tool**: Vite with modern plugins (auto-imports, code inspector, dev tools)
- **UI Libraries**:Element Plus 3
- **State Management**: Pinia with modular stores
- **Router**: Vue Router 4 with hash-based routing
- **HTTP Client**: Axios with comprehensive interceptors
- **Rich Text**: Quill Editor + WangEditor with custom plugins

### Key Architecture Patterns

#### 1. Modular API Architecture
- APIs are auto-generated from OpenAPI spec using `openapi-ts-request`
- Each controller module (cms*, dms*, etc.) represents a business domain
- Custom function naming converts API paths to camelCase methods
- All API modules are exported from `src/apis/index.ts`

#### 2. Permission-Based Routing System
- **Dynamic Routes**: Routes are fetched from backend via `umsAdminMenuTree()`
- **Role-Based Access**: Users have roles with specific authority cards
- **Route Guards**: `src/permission.js` handles authentication and route access
- **Auto-Login**: Supports token-based login via URL parameters
- **Dynamic Redirects**: Parent routes automatically redirect to first accessible child

#### 3. State Management Architecture
- **User Store** (`src/store/modules/user.ts`): Authentication, user info, roles
- **Permission Store** (`src/store/modules/permission.ts`): Dynamic route generation
- **App Store** (`src/store/modules/app.ts`): UI state management
- **Data Entry Store** (`src/store/modules/dataEntry.ts`): Form state management

#### 4. Component Architecture
- **Layout Components**: Fixed sidebar + main content area
- **Business Components**: Reusable components for specific domains (data entry, file management, etc.)
- **Utility Components**: Generic UI components (dialogs, selectors, displays)
- **Higher-Order Components**: `ParentView` for nested routing

### Critical Configuration Files

#### API Configuration
- `openapi-ts-request.config.ts`: API generation configuration
- `src/utils/request.ts`: Axios configuration with interceptors
- Base URL configuration uses environment variables

#### Permission System
- `src/permission.js`: Route guards and authentication logic
- `src/store/modules/permission.ts`: Dynamic route generation
- `src/utils/auth.ts`: Token management utilities

#### Build Configuration
- `vite.config.ts`: Build configuration with path aliases (`@/` → `src/`)
- `tsconfig.json`: TypeScript configuration with strict mode (some exceptions)
- Auto-imports enabled for Vue, Vue Router, and Pinia

### Business Domain Organization

The codebase is organized by business domains with consistent prefixes:
- **cms**: Content Management System
- **dms**: Dictionary Management System  
- **fms**: File Management System
- **mms**: Message Management System
- **nms**: News Management System
- **oms**: Organization Management System
- **pms**: Performance Management System
- **tms**: Task/Team Management System
- **ums**: User Management System

### Development Patterns

#### Component Registration
- Vue components use `<script setup>` syntax
- Auto-imports configured for common Vue APIs
- Use Element Plus components 

#### API Usage
```typescript
// Import specific API functions
import { umsAdminLogin, umsAdminInfo } from '@/apis'

// Use in components or stores
const result = await umsAdminLogin({ body: { username, password } })
```

#### Permission Checking
```typescript
// In components - use auth plugin
import auth from '@/plugins/auth'
const hasPermission = auth.hasPermi('system:user:add')

// In stores - direct role/permission checking
const roles = useUserStore().roles
const permissions = useUserStore().permissions
```

#### File Handling
- File uploads use multipart/form-data with extended timeouts
- File downloads use blob response type with header parsing
- File URLs are prefixed with configured base URL from environment

### Important Notes

#### UI Library Usage
The project uses Element Plus. When adding new components:
- Check existing usage patterns in the codebase
- Prefer consistency with existing components in the same area
- Element Plus is used for main UI components
- Compatible with different devices and resolutions as much as possible
- Use modern code design specifications and responsive design layouts as much as possible, and use padding as much as possible to replace the emergence of margins.
- Use "Sass" nested css syntax to make css more readable
- Don't care about the styles of those components in ElementPlus. If I don't explicitly mention (such as button, select, input), we can finally modify those preset css variables globally.
- If chats contains "miaoduo" or url with "miaoduo",use miaoduo MCP to get full html tree and css styles

#### Custom Quill Configuration
- Custom Quill formats are registered globally in `main.ts`
- Quill select data is managed globally to avoid re-initialization
- Custom CSS overrides are in `src/views/backgroundManagement/centralSettings/newsletterManagement/components/quill-custom.css`

#### Environment-Specific Builds
- `build:dev` and `build:prod` use different Vite modes
- API endpoints and file server URLs are environment-specific
- The README mentions needing to update URLs in `request.ts` and `filesBaseUrl.ts`

## Others
- Leverage VueUse functions where applicable to enhance reactivity and performance.
- No need to create test files or create sample file.
- Always response in Chinese.