<template>
    <div class="search-container">
        <el-input
            @clear="handleClear"
            v-model="searchKeyword"
            :placeholder="placeholder"
            :style="{ width: inputWidth }"
            @keyup.enter="handleSearch"
            @input="handleInput"
            clearable />
        <el-button type="primary" @click="handleSearch" class="search-button">
            <el-icon><Search /></el-icon>
        </el-button>
    </div>
</template>

<script setup lang="ts">
import { Search } from '@element-plus/icons-vue';
import { ref, watch, nextTick } from 'vue';

interface Props {
    placeholder?: string;
    inputWidth?: string;
    modelValue?: string;
}

interface Emits {
    (e: 'update:modelValue', value: string): void;
    (e: 'search', keyword: string): void;
    (e: 'clear'): void;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: '输入关键字检索',
    inputWidth: '206px',
    modelValue: ''
});

const emit = defineEmits<Emits>();

const searchKeyword = ref(props.modelValue);

// 监听外部传入的值变化
watch(() => props.modelValue, (newVal) => {
    searchKeyword.value = newVal;
});

// 处理搜索
const handleSearch = () => {
    emit('update:modelValue', searchKeyword.value);
    emit('search', searchKeyword.value);
};

// 处理输入变化
const handleInput = (value: string) => {
    emit('update:modelValue', value);
};

// 清空事件
const handleClear = () => {
    emit('update:modelValue', '');
    nextTick(() => {
        emit('clear');
    });
};


</script>

<style scoped lang="scss">
.search-container {
    display: flex;
    align-items: center;
    width: 250px;
    :deep(.el-input) {
        .el-input__wrapper {
            border-radius: 6px 0 0 6px;
            border-right: none;
            box-shadow: 0 0 0 1px #d9d9d9 inset;

            &:hover {
                box-shadow: 0 0 0 1px #4c9bff inset;
            }

            &.is-focus {
                box-shadow: 0 0 0 1px #1677ff inset;
            }
        }
    }

    .search-button {
        height: 32px;
        border-radius: 0 6px 6px 0;
        background: #1677ff;
        border: 1px solid #1677ff;
        box-shadow: 0px 2px 0px rgba(5, 145, 255, 0.1);
        margin-left: 0;

        &:hover {
            background: #4c9bff;
            border-color: #4c9bff;
        }
    }
}
</style>
