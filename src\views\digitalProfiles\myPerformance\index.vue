<template>
    <MyPerformanceScore 
        :person-id="Number(route.query.personId)" 
        :custom-back="() => router.back()" 
        @back="router.back()" 
    />
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import MyPerformanceScore from "@/components/MyPerformanceScore/index.vue";

const route = useRoute();
const router = useRouter();
</script>

<style scoped lang="scss">

</style>
