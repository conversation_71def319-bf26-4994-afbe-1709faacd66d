<template>
    <div class="score-input-wrapper">
        <el-input
            :style="{ width: inputWidth }"
            :model-value="modelValue"
            @update:modelValue="(val) => emit('update:modelValue', val)"
            class="manual-input"
            v-bind="{ ...inputProps, ...$attrs }" />
        <div class="score-unit">{{ unit }}</div>
    </div>
</template>

<script setup lang="ts">
import { ElInput, type InputProps } from "element-plus";
interface Props {
    /** 输入框的值（只读） */
    modelValue?: number | string | null;
    /** 单位文字 */
    unit?: string;
    /** 输入框宽度 */
    inputWidth?: string;
}
const inputProps = withDefaults(defineProps<Partial<InputProps> & Props>(), {
    unit: "分",
    inputWidth: "25px",
});

const emit = defineEmits<{
    (e: "update:modelValue", value: number | string | null): void;
}>();

// 继承 el-input 的属性
defineOptions({
    inheritAttrs: false,
});
</script>

<style scoped lang="scss">
.score-input-wrapper {
    background: #ffffff;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    display: flex;
    align-items: center;
    padding: 6px 13px;
    gap: 8px;

    .score-unit {
        color: #6b7995;
        line-height: 21px;
    }

    .manual-input {
        border: none;

        :deep(.el-input__wrapper) {
            box-shadow: none;
            padding: 0;
        }
    }
}
</style>
