<template>
    <div class="project-application">
        <div class="top">绩效数据检索</div>

        <div class="project-application-content">
            <!-- 顶部标题 -->
            <div class="title-section">
                <h2 class="page-title">搜索绩效数据</h2>
            </div>

            <!-- 搜索条件区域 -->
            <div class="search-section">
                <!-- 搜索条件表单 -->
                <div class="search-form-template">
                    <div class="simple-text">添加搜索数据表：</div>
                    <el-tree-select
                        clearable
                        collapse-tags
                        collapse-tags-tooltip
                        :max-collapse-tags="4"
                        value-key="id"
                        popper-class="template-tree-select-popper"
                        ref="treeSelectRef"
                        v-model="selectedTemplates"
                        :data="treeTemplateList"
                        :render-after-expand="false"
                        multiple
                        :props="{
                            label: getLabelName,
                            value: 'id',
                            children: 'templates',
                        }"
                        show-checkbox
                        style="width: 400px" />
                </div>
                <div class="search-form">
                    <div class="search-form-left">
                        <div class="simple-text">添加搜索条件：</div>
                        <div>
                            <div class="search-condition-row" v-for="(item, index) in searchConditions" :key="index">
                                <div class="condition-item">
                                    <el-select
                                        v-model="item.typeValue"
                                        class="condition-label"
                                        @change="handleConditionTypeChange(index)">
                                        <el-option
                                            v-for="option in getAvailableConditionTypes(index)"
                                            :key="option.value"
                                            :label="option.label"
                                            :value="option.value" />
                                    </el-select>

                                    <!-- 根据条件类型显示不同的输入控件 -->
                                    <!-- 项目状态下拉菜单 -->
                                    <el-select
                                        v-if="item.typeValue === publicPerformanceType.PROJECT_STATUS"
                                        v-model="item.value"
                                        class="condition-input"
                                        placeholder="请选择项目状态">
                                        <el-option
                                            v-for="option in projectStatusList"
                                            :key="option.value"
                                            :label="option.text"
                                            :value="option.text" />
                                    </el-select>

                                    <!-- 级别下拉菜单 -->
                                    <el-select
                                        v-else-if="item.typeValue === publicPerformanceType.GRADE"
                                        v-model="item.value"
                                        class="condition-input"
                                        placeholder="请选择级别">
                                        <el-option
                                            v-for="option in gradeFilterList"
                                            :key="option.value"
                                            :label="option.text"
                                            :value="option.text" />
                                    </el-select>

                                    <!-- 负责人/参与人下拉菜单 -->
                                    <el-select
                                        clearable
                                        filterable
                                        v-else-if="
                                            item.typeValue === publicPerformanceType.PROJECT_MAIN ||
                                            item.typeValue === publicPerformanceType.PROJECT_PARTICIPATE
                                        "
                                        v-model="item.value"
                                        class="condition-input"
                                        placeholder="请选择负责人/参与人">
                                        <el-option
                                            v-for="option in allPersonList"
                                            :key="option.id"
                                            :label="option.employeeName"
                                            :value="option.id" />
                                    </el-select>

                                    <!-- 其他条件类型使用文本输入框 -->
                                    <el-input
                                        v-else
                                        v-model="item.value"
                                        placeholder="请输入"
                                        class="condition-input" />

                                    <div class="operation-buttons">
                                        <el-button
                                            type="primary"
                                            color="#586fbb"
                                            circle
                                            @click="addCondition"
                                            v-if="hasAvailableConditionTypes"
                                            >+</el-button
                                        >
                                        <el-button
                                            type="primary"
                                            color="#586fbb"
                                            circle
                                            @click="removeCondition(index)"
                                            v-if="searchConditions.length > 1"
                                            >-</el-button
                                        >
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="search-form-right">
                        <!-- 时间范围限定 -->
                        <div class="time-range-section">
                            <span class="simple-text">限定时间范围：</span>
                            <div class="time-range-container">
                                <div
                                    class="time-range-item"
                                    :class="{ active: timeRangeType === 'ONE_YEAR' }"
                                    @click="
                                        timeRangeType === 'ONE_YEAR'
                                            ? selectTimeRange('NULL')
                                            : selectTimeRange('ONE_YEAR')
                                    ">
                                    近一年
                                </div>
                                <div
                                    class="time-range-item"
                                    :class="{ active: timeRangeType === 'THREE_YEARS' }"
                                    @click="
                                        timeRangeType === 'THREE_YEARS'
                                            ? selectTimeRange('NULL')
                                            : selectTimeRange('THREE_YEARS')
                                    ">
                                    近三年
                                </div>
                                <div
                                    class="time-range-item"
                                    :class="{ active: timeRangeType === 'CUSTOM' }"
                                    @click="
                                        timeRangeType === 'CUSTOM' ? selectTimeRange('NULL') : selectTimeRange('CUSTOM')
                                    ">
                                    自定义时间
                                </div>
                            </div>

                            <!-- 自定义时间日期选择器 -->
                            <div v-if="timeRangeType === 'CUSTOM'" class="custom-date-picker">
                                <el-date-picker
                                    v-model="customDateRange"
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    format="YYYY/MM/DD"
                                    value-format="YYYY-MM-DD"
                                    :disabled-date="disableFutureDates"
                                    :default-time="defaultTimes" />
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 搜索按钮 -->
                <div class="search-button-container">
                    <el-button type="primary" color="#586fbb" class="search-btn" @click="handleSearch">搜索</el-button>
                </div>
            </div>

            <el-divider />

            <!-- 搜索结果表格区域 -->
            <div class="search-result-section" v-if="showSearchResult" v-loading="loading">
                <el-table :data="searchResultData" border style="width: 100%">
                    <el-table-column
                        prop="owner"
                        label="资料所属"
                        align="center"
                        :filters="personFilterList"
                        :filter-method="personFilterHandler">
                        <template #default="scope">
                            {{ getPersonName(allPersonListWithDisabled, String(scope.row.owner)) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="templateName" label="数据表" align="center" />
                    <el-table-column prop="projectName" label="项目名称" align="center" />
                    <el-table-column
                        prop="projectStatus"
                        :filters="projectStatusList"
                        label="项目状态"
                        :filter-method="projectStatusFilterHandler"
                        width="100"
                        align="center">
                        <template #default="scope">
                            <div>
                                {{ getFieldName(dictList, scope.row.projectStatus) }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="grade"
                        label="级别"
                        align="center"
                        :filters="gradeFilterList"
                        :filter-method="gradeFilterHandler">
                        <template #default="scope">
                            <div>
                                {{ getFieldName(dictList, scope.row.grade) }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="projectTime" label="项目时间" align="center" sortable />
                    <el-table-column prop="proofMaterial" label="佐证材料" align="center">
                        <template #default="scope">
                            <FileIconDisplay v-if="scope.row.proofMaterial" :fileUrls="scope.row.proofMaterial || ''" />
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" fixed="right" align="center" width="250">
                        <template #default="scope">
                            <el-button
                                type="primary"
                                style="padding: 0 15px"
                                class="SystemStytle-custom-round-button"
                                color="#E5F0FD"
                                >查看详情</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
                <div class="pagination">
                    <el-pagination
                        size="small"
                        background
                        layout="prev, pager, next"
                        prev-text="上一页"
                        next-text="下一页"
                        :total="totalItems"
                        :current-page="currentPage"
                        @current-change="handlePageChange"
                        @size-change="handleSizeChange" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { publicPerformanceType, publicPerformanceTypeMap } from "@/models/publicPerformanceType";
import { ProjectStatus, ProjectStatusMap } from "@/enums/project/projectStatus";
import { cmsCategoryRowFullTextSearch } from "@/apis/cmsCategoryRowController";
import { ElMessage } from "element-plus";
import type { CmsClassify_, FullTextSearch, PublicItemParam } from "@/apis/types";
import { usePersons } from "@/hooks/usePersons";
import { getPersonName, getFieldName } from "@/utils/getNames";
import FileIconDisplay from "@/components/FileIconDisplay/index.vue";
import { useDict } from "@/hooks/useDict";
import { useProjectStatusFilter, useGradeFilter, usePersonFilter } from "@/utils/filterUtils";
import { cmsCategoryTemplateTreeTemplate } from "@/apis/cmsCategoryTemplateController";

const { allPersonListWithDisabled, allPersonList, getPersonList } = usePersons(true);

const { dictList } = useDict();

const treeTemplateList = ref<CmsClassify_[]>([]);
const selectedTemplates = ref<number[]>([]);
const treeSelectRef = ref<any>(null);

// u4f7fu7528u63d0u53d6u51fau7684u7b5bu9009u529fu80fd
const { projectStatusList, projectStatusFilterHandler } = useProjectStatusFilter(dictList);
const { gradeFilterList, gradeFilterHandler } = useGradeFilter(dictList);

// 定义搜索条件接口
interface SearchCondition {
    typeValue: number;
    typeName: string;
    value: string | number;
}

// 获取所有公共标识符枚举（除了时间）
const allPerformanceTypes = computed(() => {
    return Object.values(publicPerformanceType)
        .filter((type) => typeof type === "number") // 过滤掉枚举的反向映射
        .filter((type) => type !== publicPerformanceType.DATE) // 过滤掉时间项
        .map((type) => ({
            value: type as number,
            label: publicPerformanceTypeMap[type as publicPerformanceType],
        }));
});

// 已选择的条件类型IDs
const selectedTypeIds = computed(() => searchConditions.map((item) => item.typeValue));

// 获取未被选择的条件类型选项
const getAvailableConditionTypes = (currentIndex: number) => {
    const currentValue = searchConditions[currentIndex]?.typeValue;
    return allPerformanceTypes.value.filter(
        (option) => !selectedTypeIds.value.includes(option.value) || option.value === currentValue,
    );
};

// 检查是否还有可用的条件类型
const hasAvailableConditionTypes = computed(() => {
    return allPerformanceTypes.value.length > searchConditions.length;
});

// 默认只显示项目负责人
const searchConditions = reactive<SearchCondition[]>([
    {
        typeValue: publicPerformanceType.PROJECT_MAIN,
        typeName: publicPerformanceTypeMap[publicPerformanceType.PROJECT_MAIN],
        value: "",
    },
]);

// 添加新条件
const addCondition = () => {
    // 获取第一个未被选择的条件类型
    const availableType = allPerformanceTypes.value.find((option) => !selectedTypeIds.value.includes(option.value));

    if (availableType) {
        searchConditions.push({
            typeValue: availableType.value,
            typeName: availableType.label,
            value: "",
        });
    }
};

// 移除条件
const removeCondition = (index: number) => {
    searchConditions.splice(index, 1);
};

// 处理条件类型变化
const handleConditionTypeChange = (index: number) => {
    const typeId = searchConditions[index].typeValue;
    const typeName = publicPerformanceTypeMap[typeId as publicPerformanceType] || "";
    searchConditions[index].typeName = typeName;

    // 重置当前条件的值
    searchConditions[index].value = "";
};

// 时间范围类型
type TimeRangeType = "ONE_YEAR" | "THREE_YEARS" | "CUSTOM" | "NULL";
const timeRangeType = ref<TimeRangeType>("NULL");

// 自定义日期范围
const customDateRange = ref<[string, string] | null>(null);

// 默认时间 [00:00:00, 23:59:59]
const defaultTimes = [new Date(2000, 0, 1, 0, 0, 0), new Date(2000, 0, 1, 23, 59, 59)];

// 禁用未来日期
const disableFutureDates = (date: Date) => {
    return date > new Date();
};

// 选择时间范围类型
const selectTimeRange = (type: TimeRangeType) => {
    timeRangeType.value = type;

    // 根据类型计算时间范围
    const today = new Date();

    if (type === "ONE_YEAR") {
        // 近一年（今天往前算一年）
        const oneYearAgo = new Date();
        oneYearAgo.setFullYear(today.getFullYear() - 1);
        customDateRange.value = [formatDate(oneYearAgo), formatDate(today)];
    } else if (type === "THREE_YEARS") {
        // 近三年（今天往前算三年）
        const threeYearsAgo = new Date();
        threeYearsAgo.setFullYear(today.getFullYear() - 3);
        customDateRange.value = [formatDate(threeYearsAgo), formatDate(today)];
    } else if (type === "CUSTOM" && !customDateRange.value) {
        // 自定义时间，如果未设置，默认设为今天
        customDateRange.value = [formatDate(today), formatDate(today)];
    }
};

// 格式化日期为YYYY-MM-DD
const formatDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
};

// 加载状态
const loading = ref(false);
const setLoading = (status: boolean) => {
    loading.value = status;
};

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);

// 分页变化处理
const handlePageChange = (page: number) => {
    currentPage.value = page;
    handleSearch();
};

// 每页条数变化处理
const handleSizeChange = (size: number) => {
    pageSize.value = size;
    currentPage.value = 1; // 切换每页条数时重置为第一页
    handleSearch();
};

// u6a21u62dfu6570u636e
const searchResultData = ref<any[]>([]);

// u4f7fu7528u4ebau5458u7b5bu9009u529fu80fd
const { personFilterList, personFilterHandler } = usePersonFilter(searchResultData, allPersonListWithDisabled);

// 处理搜索按钮点击
const handleSearch = async () => {
    // 构建查询项数组
    const searchItems: PublicItemParam[] = [];

    // 添加普通查询条件
    searchConditions.forEach((condition) => {
        if (condition.value) {
            // 检查是否是项目负责人或项目参与人
            if (
                condition.typeValue === publicPerformanceType.PROJECT_MAIN ||
                condition.typeValue === publicPerformanceType.PROJECT_PARTICIPATE
            ) {
                // 获取人员ID对应的人名
                const personId = String(condition.value);
                const person = allPersonListWithDisabled.value.find((p) => String(p.id) === personId);
                if (person) {
                    searchItems.push({
                        code: condition.typeValue.toString(),
                        value: person.employeeName, // 使用人名替代ID
                    });
                }
            } else {
                // 其他条件直接使用原值
                searchItems.push({
                    code: condition.typeValue.toString(), // API要求code为字符串
                    value: condition.value.toString(),
                });
            }
        }
    });

    // 添加时间范围条件
    if (timeRangeType.value !== "NULL" && customDateRange.value) {
        searchItems.push({
            code: publicPerformanceType.DATE.toString(), // 时间的code为"6"
            startTime: customDateRange.value[0],
            endTime: customDateRange.value[1],
        });
    }

    // 构建完整查询参数
    const searchParams: FullTextSearch = {
        items: searchItems,
        pageParam: {
            pageSize: pageSize.value,
            pageNum: currentPage.value,
        },
        templates: selectedTemplates.value,
    };

    // 调用搜索接口
    try {
        setLoading(true);
        const result = await cmsCategoryRowFullTextSearch({
            body: searchParams,
        });

        if (result.code === 200) {
            // 更新表格数据和分页信息
            searchResultData.value = result.data.records;
            totalItems.value = result.data.total || 0;
        } else {
            // 处理错误
            ElMessage.error(result.message || "搜索失败");
        }
    } catch (error) {
        console.error("搜索请求出错:", error);
        ElMessage.error("搜索请求失败，请稍后重试");
    } finally {
        setLoading(false);
    }
};

// 搜索结果是否显示
const showSearchResult = ref(true);

// 获取label名称
const getLabelName = (node: any) => {
    if (node.name) {
        return node.name;
    } else {
        return node.templateName;
    }
};

onMounted(() => {
    cmsCategoryTemplateTreeTemplate({}).then((res: any) => {
        treeTemplateList.value = res.data;
    });
    getPersonList();
});
</script>

<style scoped lang="scss">
:global(.template-tree-select-popper) {
    height: 450px;
}

:global(.template-tree-select-popper .el-select-dropdown__wrap) {
    max-height: 450px;
}

.simple-text {
    font-size: 15px;
    color: #61636a;
    margin-right: 15px;
}

.pagination {
    display: flex;
    justify-content: end;
    margin-top: 10px;
    margin-right: 20px;
}

.project-application {
    height: 100%;
    display: flex;
    flex-direction: column;
    .top {
        font-size: 20px;
        font-weight: 600;
        color: #23346d;
        text-align: center;
        background: #c8d3e9;
        color: #6b7995;
        height: 60px;
        line-height: 60px;
    }

    .project-application-content {
        padding: 20px;
        background-color: #fff;
        border-radius: 4px;
        flex: 1;
        height: 0;
        overflow-y: auto;

        .title-section {
            margin-bottom: 20px;

            .page-title {
                font-size: 18px;
                font-weight: 400;
                color: #23346d;
                margin: 0;
                padding: 10px 0;
            }
        }

        .search-section {
            margin-bottom: 30px;

            .search-form-template {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 20px;
            }

            .search-form {
                display: flex;
                align-items: start;
                justify-content: space-between;
                gap: 50px;
                .search-form-left {
                    flex: 10;
                    display: flex;
                }

                .search-form-right {
                    flex: 3;
                }
                .search-condition-row {
                    margin-bottom: 15px;
                }

                .condition-item {
                    display: flex;
                    align-items: center;

                    .condition-label {
                        width: 150px;
                        margin-right: 10px;
                    }

                    .condition-input {
                        flex: 1;
                        width: 15vw;
                    }

                    .operation-buttons {
                        margin-left: 15px;
                        display: flex;

                        .circle-btn {
                            width: 30px;
                            height: 30px;
                            padding: 0;
                            border-radius: 50%;
                            margin-right: 10px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                    }
                }

                .time-range-section {
                    width: 400px;

                    .time-range-container {
                        width: 150px;
                        display: inline-flex;
                        flex-wrap: wrap;
                        align-items: center;
                        gap: 10px;
                        margin-bottom: 10px;

                        .time-range-item {
                            font-size: 14px;
                            padding: 5px 10px;
                            border: 1px solid #d0d0d0;
                            border-radius: 5px;
                            text-align: center;
                            cursor: pointer;
                            transition: all 0.3s;

                            &:nth-child(1),
                            &:nth-child(2) {
                                flex-grow: 1;
                            }

                            &:nth-child(3) {
                                flex-grow: 2;
                            }

                            &.active {
                                background-color: #d2e7ff;
                                border-color: #6c90bf;
                                color: #38587d;
                                font-weight: bold;
                            }
                        }
                    }

                    .custom-date-picker {
                        margin-top: 10px;
                        width: 100%;
                    }
                }
            }
        }
        .search-button-container {
            margin-top: 30px;
            text-align: center;

            .search-btn {
                width: 80px;
            }
        }

        .search-result-section {
            margin-top: 20px;

            .operation-buttons-container {
                display: flex;
                justify-content: center;

                .detail-btn,
                .apply-btn {
                    margin: 0 5px;
                    background-color: #d2e7ff;
                    border-color: #6c90bf;
                    color: #38587d;
                }
            }
        }
    }
}
</style>
