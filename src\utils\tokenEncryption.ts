import CryptoJS from 'crypto-js'

/**
 * Token加密解密工具类
 * 与后端Java UrlTokenEncryptor保持一致
 */
export class TokenEncryptor {
    // 密钥必须为16字节（128位），与后端保持一致
    private static readonly KEY = 'asvujk3uysmrlpz2'

    /**
     * 加密token
     * @param plainText 明文token
     * @returns 加密后的token
     */
    static encrypt(plainText: string): string {
        try {
            const key = CryptoJS.enc.Utf8.parse(this.KEY)
            const encrypted = CryptoJS.AES.encrypt(plainText, key, {
                mode: CryptoJS.mode.ECB,
                padding: CryptoJS.pad.Pkcs7
            })
            
            // 转换为URL安全的Base64编码（无填充）
            const base64 = encrypted.toString()
            return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '')
        } catch (error) {
            console.error('❌ Token加密失败:', error)
            throw new Error('Token加密失败')
        }
    }

    /**
     * 解密token
     * @param cipherText 加密后的token
     * @returns 解密后的明文token
     */
    static decrypt(cipherText: string): string {
        try {
            // 还原标准Base64格式
            let base64 = cipherText.replace(/-/g, '+').replace(/_/g, '/')
            
            // 添加必要的填充
            const padding = 4 - (base64.length % 4)
            if (padding !== 4) {
                base64 += '='.repeat(padding)
            }

            const key = CryptoJS.enc.Utf8.parse(this.KEY)
            const decrypted = CryptoJS.AES.decrypt(base64, key, {
                mode: CryptoJS.mode.ECB,
                padding: CryptoJS.pad.Pkcs7
            })
            
            const decryptedText = decrypted.toString(CryptoJS.enc.Utf8)
            
            if (!decryptedText) {
                throw new Error('解密结果为空')
            }
            
            return decryptedText
        } catch (error) {
            console.error('❌ Token解密失败:', error)
            throw new Error('Token解密失败，请检查token是否有效')
        }
    }

    /**
     * 检测token是否为加密格式
     * 通过检查token是否为JWT格式来判断（JWT格式通常包含两个点）
     * @param token 
     * @returns 如果是加密格式返回true，否则返回false
     */
    static isEncrypted(token: string): boolean {
        // JWT token通常有三部分，用两个点分隔：header.payload.signature
        // 如果没有点或只有一个点，很可能是加密的token
        const parts = token.split('.')
        return parts.length !== 3
    }
}

/**
 * 便捷的解密函数
 * @param token 可能是加密的token
 * @returns 解密后的token
 */
export function decryptTokenIfNeeded(token: string): string {
    console.log('🔍 检测token格式...')
    
    if (TokenEncryptor.isEncrypted(token)) {
        console.log('🔐 检测到加密token，开始解密...')
        const decryptedToken = TokenEncryptor.decrypt(token)
        console.log('✅ Token解密成功')
        return decryptedToken
    } else {
        console.log('📝 检测到明文token，无需解密')
        return token
    }
}