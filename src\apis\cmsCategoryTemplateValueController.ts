/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 新增字段 POST /cms/cmsCategoryTemplateValue/create */
export async function cmsCategoryTemplateValueCreate({
  body,
  options,
}: {
  body: API.CmsCategoryTemplateValue_;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>(
    '/cms/cmsCategoryTemplateValue/create',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 删除字段 POST /cms/cmsCategoryTemplateValue/delete */
export async function cmsCategoryTemplateValueDelete({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryTemplateValueDeleteParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>(
    '/cms/cmsCategoryTemplateValue/delete',
    {
      method: 'POST',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 查询指定数据项的所有下拉值 项目状态：4，级别：5 GET /cms/cmsCategoryTemplateValue/getAllDropdownValues */
export async function cmsCategoryTemplateValueGetAllDropdownValues({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryTemplateValueGetAllDropdownValuesParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListString_>(
    '/cms/cmsCategoryTemplateValue/getAllDropdownValues',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据id获取字段详情 GET /cms/cmsCategoryTemplateValue/getById */
export async function cmsCategoryTemplateValueGetById({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryTemplateValueGetByIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCmsCategoryTemplateValue_>(
    '/cms/cmsCategoryTemplateValue/getById',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取当前数据表指定自定义下拉值 项目状态：4，级别：5 GET /cms/cmsCategoryTemplateValue/getDropdownValues */
export async function cmsCategoryTemplateValueGetDropdownValues({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryTemplateValueGetDropdownValuesParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListDmsDict_>(
    '/cms/cmsCategoryTemplateValue/getDropdownValues',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据大类id获取树的子节点 GET /cms/cmsCategoryTemplateValue/getValueByTemplateId */
export async function cmsCategoryTemplateValueGetValueByTemplateId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryTemplateValueGetValueByTemplateIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsCategoryTemplateValue_>(
    '/cms/cmsCategoryTemplateValue/getValueByTemplateId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据大类id获取字段树 GET /cms/cmsCategoryTemplateValue/getValueTreeByTemplateId */
export async function cmsCategoryTemplateValueGetValueTreeByTemplateId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryTemplateValueGetValueTreeByTemplateIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCmsCategoryTemplateValue_>(
    '/cms/cmsCategoryTemplateValue/getValueTreeByTemplateId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 修改字段 POST /cms/cmsCategoryTemplateValue/update */
export async function cmsCategoryTemplateValueUpdate({
  body,
  options,
}: {
  body: API.CmsCategoryTemplateValue_;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>(
    '/cms/cmsCategoryTemplateValue/update',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 修改字段集合 POST /cms/cmsCategoryTemplateValue/updateList */
export async function cmsCategoryTemplateValueUpdateList({
  body,
  options,
}: {
  body: API.CmsCategoryTemplateValue11[];
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>(
    '/cms/cmsCategoryTemplateValue/updateList',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
