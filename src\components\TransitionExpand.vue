<template>
    <transition
        name="expand"
        @enter="transitionHeight.onEnter"
        @after-enter="transitionHeight.onAfterEnter"
        @leave="transitionHeight.onLeave">
        <slot></slot>
    </transition>
</template>

<script setup lang="ts">
import { useTransitionHeight } from "@/composables/useTransitionHeight";

const transitionHeight = useTransitionHeight();
</script>

<style scoped lang="scss">
.expand-enter-active,
.expand-leave-active {
    transition: height 0.3s ease-in-out;
    overflow: hidden;
}
</style>
