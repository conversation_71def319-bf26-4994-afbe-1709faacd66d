/* eslint-disable */
// @ts-ignore

export type BatchRelateCategoryParam = {
  /** 关联的数据表id列表 */
  categoryIdList?: number[];
  /** 绩效标签id */
  tagId?: number;
};

export type CategoryValuePublicItemParam = {
  /** 公共字段的code */
  code?: string;
  /** 结束时间 */
  endTime?: string;
  /** 模糊查询值 */
  likeValue?: string;
  /** 开始时间 */
  startTime?: string;
  /** 精确查询值 */
  value?: string;
};

export type CmsAchievement = {
  /** 级别 */
  grade?: string;
  /** 是否接受共享 */
  isExist?: number;
  /** 归属者 */
  owner?: number;
  /** 项目名称 */
  projectName?: string;
  /** 项目状态 */
  projectStatus?: string;
  /** 项目时间 */
  projectTime?: string;
  /** 作证材料id */
  proofMaterial?: string;
  /** 行数据id */
  rowId?: number;
  /** 大类id */
  templateId?: number;
  /** 大类名称 */
  templateName?: string;
};

export type CmsCategoryPublicDataParam = {
  /** 批次号 */
  batchNumber?: string;
  /** 所属人员(多个人员使用(,)分割) */
  owners?: string;
  /** 页码，从 1 开始 */
  pageNum?: number;
  /** 每页条数，最大值为 100 */
  pageSize?: number;
  /** 大类ID */
  templateId?: number;
};

export type CmsCategoryPublicPerformanceParam = {
  /** 页码，从 1 开始 */
  pageNum?: number;
  /** 每页条数，最大值为 100 */
  pageSize?: number;
  /** 标识符 */
  publicPerformances?: CategoryValuePublicItemParam[];
  /** 大类ID(当有多个大类时，请使用(,)分隔) */
  templateIds?: string;
};

export type CmsCategoryRow_ = {
  /** 审核时间 */
  approvalAt?: string;
  /** 审核人 */
  approvalBy?: number;
  /** 审核状态（0：草稿，1：待处理，2：不需要审核，3：审核通过，4：审核不通过） */
  approvalStatus?: number;
  /** 批次号 */
  batchNumber?: string;
  /** 所属大类 */
  categoryTemplateId?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 申报时间 */
  declaredAt?: string;
  id?: number;
  /** 归属人 */
  owner?: number;
  /** 结项时间 */
  projectEndedAt?: string;
  /** 项目名称 */
  projectName?: string;
  /** 立项时间 */
  projectStartedAt?: string;
  /** 项目状态 */
  projectStatus?: string;
  /** 项目时间 */
  projectTime?: string;
  /** 行数据总分 */
  rowScore?: number;
  /** 来源 */
  rowType?: string;
  /** 提交审核人 */
  submitBy?: number;
  /** 提交审核时间 */
  submitDate?: string;
  /** 标签id */
  tagId?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type CmsCategoryRow0 = {
  /** 审核时间 */
  approvalAt?: string;
  /** 审核人 */
  approvalBy?: number;
  /** 审核状态（0：草稿，1：待处理，2：不需要审核，3：审核通过，4：审核不通过） */
  approvalStatus?: number;
  /** 批次号 */
  batchNumber?: string;
  /** 所属大类 */
  categoryTemplateId?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 申报时间 */
  declaredAt?: string;
  id?: number;
  /** 归属人 */
  owner?: number;
  /** 结项时间 */
  projectEndedAt?: string;
  /** 项目名称 */
  projectName?: string;
  /** 立项时间 */
  projectStartedAt?: string;
  /** 项目状态 */
  projectStatus?: string;
  /** 项目时间 */
  projectTime?: string;
  /** 行数据总分 */
  rowScore?: number;
  /** 来源 */
  rowType?: string;
  /** 得分 */
  scores?: PmsScore_[];
  /** 提交审核人 */
  submitBy?: number;
  /** 提交审核时间 */
  submitDate?: string;
  /** 标签id */
  tagId?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
  /** 大类列数据 */
  values?: CmsCategoryValue0[];
};

export type cmsCategoryRowCheckDataParams = {
  /** batchNumber */
  batchNumber?: string;
  /** id */
  id: number;
};

export type cmsCategoryRowCheckPerformanceParams = {
  /** id */
  id: number;
};

export type cmsCategoryRowDataListByPersonIdParams = {
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
  /** personId */
  personId: number;
  /** status */
  status: number[];
};

export type cmsCategoryRowDeleteParams = {
  /** id */
  id: number;
};

export type cmsCategoryRowGetAllBatchNumberParams = {
  /** status */
  status?: number[];
  /** templateId */
  templateId: number;
};

export type cmsCategoryRowGetByApprovalAndRowTypeParams = {
  /** approvalStatus */
  approvalStatus?: number[];
  /** name */
  name?: string;
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
  /** rowTypes */
  rowTypes?: string[];
};

export type cmsCategoryRowGetByIdParams = {
  /** id */
  id: number;
};

export type cmsCategoryRowGetByProjectIdParams = {
  /** name */
  name?: string;
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
  /** projectId */
  projectId: number;
};

export type cmsCategoryRowGetOwnerByStatusParams = {
  /** approvalStatus */
  approvalStatus?: number[];
  /** rowTypes */
  rowTypes?: string[];
  /** templateId */
  templateId: number;
};

export type cmsCategoryRowGetRepeatParams = {
  /** rowId */
  rowId: number;
};

export type cmsCategoryRowGetTodoParams = {
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
  /** type */
  type?: number;
};

export type cmsCategoryRowImportDataFileParams = {
  /** batchNumber */
  batchNumber?: string;
  /** id */
  id: number;
};

export type cmsCategoryRowImportPerformanceFileParams = {
  /** id */
  id: number;
};

export type cmsCategoryRowListByCategoryIdParams = {
  /** batchNumber */
  batchNumber?: string;
  /** categoryId */
  categoryId: number;
  /** status */
  status?: number[];
};

export type cmsCategoryRowOwnerCreateGetByApprovalAndTypeParams = {
  /** approvalStatus */
  approvalStatus?: number[];
  /** isSelectOwner */
  isSelectOwner?: boolean;
  /** name */
  name?: string;
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
  /** rowTypes */
  rowTypes?: string[];
};

export type cmsCategoryRowPageByCategoryIdParams = {
  /** batchNumber */
  batchNumber?: string;
  /** categoryId */
  categoryId: number;
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
  /** status */
  status?: number[];
};

export type cmsCategoryRowPerformanceListByPersonIdParams = {
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
  /** personId */
  personId: number;
  /** status */
  status: number[];
};

export type CmsCategoryTemplate_ = {
  /** 审批配置状态 */
  approvalConfigurationStatus?: number;
  /** 是否有批次（0：否，1：是） */
  batchType?: number;
  /** 负责检查人id */
  checkId?: number;
  /** 大类归类 */
  classify?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 数据项配置状态 */
  dataItemConfigurationStatus?: number;
  /** 备注 */
  description?: string;
  /** 主键 */
  id?: number;
  /** 批量导入后是否需要审核（0：不需要，1：需要） */
  importCheck?: number;
  /** 是否仅秘书录入 */
  isSecretary?: number;
  /** 大类实体名 */
  templateName?: string;
  /** 大类实体类型（0:成果、1：院情、 2: 工作） */
  templateType?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type CmsCategoryTemplate0 = {
  /** 审批配置状态 */
  approvalConfigurationStatus?: number;
  /** 是否有批次（0：否，1：是） */
  batchType?: number;
  /** 负责检查人id */
  checkId?: number;
  /** 大类归类 */
  classify?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 数据项配置状态 */
  dataItemConfigurationStatus?: number;
  /** 备注 */
  description?: string;
  enabled?: number;
  /** 主键 */
  id?: number;
  /** 批量导入后是否需要审核（0：不需要，1：需要） */
  importCheck?: number;
  /** 是否仅秘书录入 */
  isSecretary?: number;
  /** 大类实体名 */
  templateName?: string;
  /** 大类实体类型（0:成果、1：院情、 2: 工作） */
  templateType?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type CmsCategoryTemplate1 = {
  /** 审批配置状态 */
  approvalConfigurationStatus?: number;
  /** 是否有批次（0：否，1：是） */
  batchType?: number;
  /** 负责检查人id */
  checkId?: number;
  /** 大类归类 */
  classify?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 数据项配置状态 */
  dataItemConfigurationStatus?: number;
  /** 备注 */
  description?: string;
  /** 主键 */
  id?: number;
  /** 批量导入后是否需要审核（0：不需要，1：需要） */
  importCheck?: number;
  /** 是否仅秘书录入 */
  isSecretary?: number;
  /** 大类实体名 */
  templateName?: string;
  /** 大类实体类型（0:成果、1：院情、 2: 工作） */
  templateType?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type cmsCategoryTemplateCheckHeadParams = {
  /** id */
  id: number;
};

export type cmsCategoryTemplateDeleteParams = {
  /** id */
  id: number;
};

export type cmsCategoryTemplateExportFileParams = {
  /** id */
  id: number;
};

export type cmsCategoryTemplateFavoriteDeleteidParams = {
  /** 数据表id */
  id: number;
};

export type cmsCategoryTemplateFavoriteSaveidParams = {
  /** 数据表id */
  id: number;
};

export type cmsCategoryTemplateGetByIdParams = {
  /** id */
  id: number;
};

export type cmsCategoryTemplateListByGroupIdidParams = {
  /** id */
  id: number;
};

export type cmsCategoryTemplateListByIdParams = {
  /** id */
  id: number;
  /** pageNum */
  pageNum?: number;
  /** pageSize */
  pageSize?: number;
};

export type cmsCategoryTemplateQueryByOwnerAndYearParams = {
  /** owner */
  owner: number;
  /** years */
  years?: string[];
};

export type CmsCategoryTemplateValue_ = {
  /** 公共字段是否不可修改（0：否，1：是） */
  categoryPublicIsNoChange?: number;
  /** 公共字段类型 */
  categoryPublicType?: number;
  /** 所属大类 */
  categoryTemplateId?: number;
  children?: CmsCategoryTemplateValue_[];
  /** 描述 */
  description?: string;
  /** 主键 */
  id?: number;
  /** 是否必填 */
  isRequired?: number;
  /** 父类id */
  parentId?: number;
  /** 是否可搜索 */
  searchable?: number;
  /** 排序 */
  sortOrder?: number;
  /** 补充值 */
  tag?: string;
  /** 补充值2 */
  tag2?: string;
  /** 字段类型 */
  type?: number;
  /** 字段名 */
  value?: string;
};

export type CmsCategoryTemplateValue0 = {
  /** 公共字段是否不可修改（0：否，1：是） */
  categoryPublicIsNoChange?: number;
  /** 公共字段类型 */
  categoryPublicType?: number;
  /** 所属大类 */
  categoryTemplateId?: number;
  children?: CmsCategoryTemplateValue_[];
  /** 描述 */
  description?: string;
  dictList?: DmsDict_[];
  /** 主键 */
  id?: number;
  /** 是否必填 */
  isRequired?: number;
  /** 父类id */
  parentId?: number;
  /** 是否可搜索 */
  searchable?: number;
  /** 排序 */
  sortOrder?: number;
  /** 补充值 */
  tag?: string;
  /** 补充值2 */
  tag2?: string;
  /** 字段类型 */
  type?: number;
  /** 字段名 */
  value?: string;
};

export type CmsCategoryTemplateValue10 = {
  /** 公共字段是否不可修改（0：否，1：是） */
  categoryPublicIsNoChange?: number;
  /** 公共字段类型 */
  categoryPublicType?: number;
  /** 所属大类 */
  categoryTemplateId?: number;
  children?: CmsCategoryTemplateValue0[];
  /** 描述 */
  description?: string;
  dictList?: DmsDict_[];
  /** 主键 */
  id?: number;
  /** 是否必填 */
  isRequired?: number;
  /** 父类id */
  parentId?: number;
  /** 是否可搜索 */
  searchable?: number;
  /** 排序 */
  sortOrder?: number;
  /** 补充值 */
  tag?: string;
  /** 补充值2 */
  tag2?: string;
  /** 字段类型 */
  type?: number;
  /** 字段名 */
  value?: string;
};

export type CmsCategoryTemplateValue11 = {
  /** 公共字段是否不可修改（0：否，1：是） */
  categoryPublicIsNoChange?: number;
  /** 公共字段类型 */
  categoryPublicType?: number;
  /** 所属大类 */
  categoryTemplateId?: number;
  children?: CmsCategoryTemplateValue11[];
  /** 描述 */
  description?: string;
  /** 主键 */
  id?: number;
  /** 是否必填 */
  isRequired?: number;
  /** 父类id */
  parentId?: number;
  /** 是否可搜索 */
  searchable?: number;
  /** 排序 */
  sortOrder?: number;
  /** 补充值 */
  tag?: string;
  /** 补充值2 */
  tag2?: string;
  /** 字段类型 */
  type?: number;
  /** 字段名 */
  value?: string;
};

export type cmsCategoryTemplateValueDeleteParams = {
  /** id */
  id: number;
};

export type cmsCategoryTemplateValueGetAllDropdownValuesParams = {
  /** type */
  type: number;
};

export type cmsCategoryTemplateValueGetByIdParams = {
  /** id */
  id: number;
};

export type cmsCategoryTemplateValueGetDropdownValuesParams = {
  /** templateId */
  templateId: number;
  /** type */
  type: number;
};

export type cmsCategoryTemplateValueGetValueByTemplateIdParams = {
  /** id */
  id: number;
};

export type cmsCategoryTemplateValueGetValueTreeByTemplateIdParams = {
  /** id */
  id: number;
};

export type CmsCategoryValue_ = {
  /** 对应表头字段id */
  categoryTemplateValueId?: number;
  /** 公共字段code，用于查询 */
  code?: string;
  id?: number;
  /** 行id */
  rowId?: number;
  /** 补充值 */
  tag?: string;
  /** 值 */
  value?: string;
};

export type CmsCategoryValue0 = {
  /** 公共字段code，用于查询 */
  categoryPublicType?: number;
  /** 对应表头字段id */
  categoryTemplateValueId?: number;
  /** 对应表头字段名 */
  categoryTemplateValueName?: string;
  id?: number;
  /** 行id */
  rowId?: number;
  /** 补充值 */
  tag?: string;
  /** 值 */
  value?: string;
};

export type CmsCheck_ = {
  /** 数据表ID */
  categoryId?: number;
  /** 审核人id */
  checkId?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 描述 */
  description?: string;
  /**  主键 */
  id?: number;
  /** 是否负责人(1：是，0：否) */
  isPrincipal?: number;
  /** 是否已读 */
  isRead?: number;
  /** 项目名称 */
  projectName?: string;
  /** 审核行id */
  rowId?: number;
  /** 分配人id */
  sendId?: number;
  /** 状态（0：待处理，1：已分发，2：已退回，3：已通过，4：不通过） */
  status?: number;
  /** 类型（0：正常审核，1：带重新赋分） */
  type?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type CmsCheck0 = {
  /** 数据表ID */
  categoryId?: number;
  /** 审核人id */
  checkId?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 描述 */
  description?: string;
  /**  主键 */
  id?: number;
  /** 是否负责人(1：是，0：否) */
  isPrincipal?: number;
  /** 是否已读 */
  isRead?: number;
  /** 是否有重复 */
  isRepeat?: boolean;
  /** 项目名称 */
  projectName?: string;
  row?: CmsCategoryRow_;
  /** 审核行id */
  rowId?: number;
  /** 分配人id */
  sendId?: number;
  /** 状态（0：待处理，1：已分发，2：已退回，3：已通过，4：不通过） */
  status?: number;
  /** 行数据项 */
  templateValues?: CmsCategoryTemplateValue_[];
  /** 类型（0：正常审核，1：带重新赋分） */
  type?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
  /** 行数据 */
  values?: CmsCategoryValue_[];
};

export type CmsCheck1 = {
  /** 数据表ID */
  categoryId?: number;
  /** 审核人id */
  checkId?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 描述 */
  description?: string;
  /**  主键 */
  id?: number;
  /** 是否负责人(1：是，0：否) */
  isPrincipal?: number;
  /** 是否已读 */
  isRead?: number;
  /** 项目名称 */
  projectName?: string;
  /** 审核行id */
  rowId?: number;
  /** 分配人id */
  sendId?: number;
  /** 状态（0：待处理，1：已分发，2：已退回，3：已通过，4：不通过） */
  status?: number;
  /** 类型（0：正常审核，1：带重新赋分） */
  type?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type CmsCheckAgreeParam = {
  /** 行id */
  id?: number;
  /** 得分 */
  score?: number;
};

export type cmsCheckGetByStatusParams = {
  /** 搜索值 */
  name?: string;
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
  /** 状态（0：待处理，1：已分发，2：已退回，3：已通过，4：不通过） */
  status?: number[];
  /** 类型（0：正常审核，1：带重新赋分） */
  types: number[];
};

export type cmsCheckGetRecordParams = {
  /** rowId */
  rowId: number;
};

export type cmsCheckGetToBeReassignedListParams = {
  /** 查询关键字 */
  key?: string;
  /** 页码 */
  pageNum: number;
  /** 每页数量 */
  pageSize: number;
};

export type cmsCheckReadParams = {
  /** rowId */
  rowId: number;
};

export type CmsClassify_ = {
  /** 描述 */
  description?: string;
  /** 主键 */
  id?: number;
  /** 昵称 */
  name?: string;
  /** 数据表集合 */
  templates?: CmsCategoryTemplate_[];
};

export type CmsConfigAmountComputeCondition = {
  /** 金额达到指定阈值，阈值配置项（万元） */
  amountThreshold?: number;
  /** 达到指定金额后获得的绩效分 */
  performanceScore?: number;
};

export type CmsConfigDetailDto = {
  /** （金额累计制计分）金额计分条件列表 */
  amountComputeConditions?: CmsConfigAmountComputeCondition[];
  /** （金额累计制计分）金额计分数据项列表 */
  amountComputeFieldIds?: number[];
  /** 分值组合列表（数据项组合和人头制计分时使用） */
  combinationList?: CmsTagConfigCombination_[];
  /** （计算方式为数据项组合计算分值时，字段组合type，多个type使用,分隔，字段为下拉类型时有tyep，type为字典里的type） */
  combinationType?: string[];
  /** 数据项等值分值填写 选择的字段的id */
  dataFieldId?: number;
  /** 固定分值时填写的分值 */
  fixedScore?: number;
  /** 是否在审核页输入分值（0-否 1-是） */
  isInputScore?: number;
  /** 是否每人的分值一致（0-否 1-是） */
  isSameScore?: number;
};

export type CmsFinalScoreDto = {
  /** 最终得分 */
  finalScore?: number;
  /** 最终得分占比 */
  finalScoreProportion?: number;
  /** 人员id */
  personId?: number;
  /** 人员名称 */
  personName?: string;
  /** 角色数据项名称 */
  templateValueName?: string;
};

export type CmsRowValueResult = {
  /** 审核时间 */
  approvalAt?: string;
  /** 审核人 */
  approvalBy?: number;
  /** 审核状态（0：草稿，1：待处理，2：不需要审核，3：审核通过，4：审核不通过） */
  approvalStatus?: number;
  /** 批次号 */
  batchNumber?: string;
  /** 所属大类 */
  categoryTemplateId?: number;
  /** 审批历史 */
  checkHistory?: CmsCheck_[];
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 申报时间 */
  declaredAt?: string;
  /** 行id */
  id?: number;
  /** 归属人 */
  owner?: number;
  /** 结项时间 */
  projectEndedAt?: string;
  /** 项目名称 */
  projectName?: string;
  /** 立项时间 */
  projectStartedAt?: string;
  /** 项目状态 */
  projectStatus?: string;
  /** 项目时间 */
  projectTime?: string;
  /** 行数据总分 */
  rowScore?: number;
  /** 来源 */
  rowType?: string;
  /** 提交审核人 */
  submitBy?: number;
  /** 提交审核时间 */
  submitDate?: string;
  /** 标签id */
  tagId?: number;
  /** 数据表名称 */
  templateName?: string;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
  /** 列数据 */
  values?: CmsCategoryValue_[];
};

export type CmsTag_ = {
  id?: number;
  /** 分数上线，级别配置有效，评价标准配置无效 */
  maxScore?: number;
  /** 旧标签名称 */
  oldTagName?: string;
  /** 父标签id，用于构建标签树 */
  parentTagId?: number;
  /** 标签等级 */
  tagLevel?: number;
  /** 标签名称 */
  tagName?: string;
  /** 标签类型，0-等级指标，1-评价标准 */
  tagType?: number;
};

export type CmsTagCategoryEntity_ = {
  /** 大类实体id */
  categoryEntityId?: number;
  id?: number;
  /** 标签id */
  tagId?: number;
};

export type cmsTagCategoryEntityGetTagListByEntityIdParams = {
  /** categoryEntityId */
  categoryEntityId: number;
  /** tagType */
  tagType?: number;
};

export type cmsTagCategoryEntityGetTagsByEntityIdParams = {
  /** categoryEntityId */
  categoryEntityId: number;
};

export type cmsTagCategoryEntityGetTagsByTagIdParams = {
  /** tagId */
  tagId: number;
};

export type cmsTagCategoryEntityGetTagTreeListByEntityIdParams = {
  /** categoryEntityId */
  categoryEntityId: number;
};

export type CmsTagConfig_ = {
  /** 分配方式 (0 - 项目制计分， 1 - 人头制计分, 2 - 金额累计制计分) */
  allocateType?: number;
  /** 关联大类id */
  categoryId?: number;
  configDetail?: CmsConfigDetailDto;
  id?: number;
  /** 分值来源（0-数据项组合计算分值，1-固定分值，2-数据项等值分值） */
  scoreSource?: number;
  /** 赋分方式 (0-手动，1-自动) */
  scoringMethod?: number;
  /** 标签id */
  tagId?: number;
};

export type CmsTagConfigCombination_ = {
  /** 组合名称 */
  combinationName?: string;
  /** 关联的数据表表头字段id */
  dataFieldId?: number;
  /** 分值 */
  score?: number;
};

export type CmsTagConfigCombinationResp = {
  /** 组合名称 */
  combinationName?: string;
  /** 分值 */
  score?: number;
};

export type cmsTagConfigGetCombinationListByTypeParams = {
  /** 字段组合type，多个type使用,分隔，type为字典里的type，大类实体和字典的type关联 */
  combinationType: string;
};

export type cmsTagConfigGetDetailAndScoreDataidParams = {
  /** id */
  id: number;
};

export type cmsTagConfigGetDetailAndScoreidParams = {
  /** id */
  id: number;
};

export type cmsTagConfigGetTagConfigByCategoryIdParams = {
  /** categoryId */
  categoryId?: number;
};

export type cmsTagConfigGetTagConfigParams = {
  /** categoryId */
  categoryId?: number;
  /** tagId */
  tagId?: number;
};

export type CmsTagConfigParam = {
  /** 分配方式 (0 - 项目制计分， 1 - 人头制计分，2 - 金额累计制计分) */
  allocateType?: number;
  /** 关联大类id */
  categoryId?: number;
  configDetail?: CmsConfigDetailDto;
  /** 计算方式（0-数据项组合计算分值，1-固定分值，2-数据项等值分值） */
  scoreSource?: number;
  /** 赋分方式 (0-手动，1-自动) */
  scoringMethod?: number;
  /** 标签id */
  tagId?: number;
};

export type CmsTagConfigResp = {
  /** 分配方式 (0-单人，1-团体，2-人次) */
  allocateType?: number;
  cmsTagConfigSelectResp?: CmsTagConfigSelectResp;
  id?: number;
  /** 赋分方式 (0-手动，1-自动) */
  scoringMethod?: number;
  /** 标签id */
  tagId?: number;
};

export type CmsTagConfigSelectResp = {
  /** 关联大类id */
  categoryId?: number;
  /** 分值组合列表 */
  cmsTagConfigCombinationResponseList?: CmsTagConfigCombination_[];
  /** 字段组合type，多个type使用,分隔，type为字典里的type，大类实体和字典的type关联 */
  combinationType?: string;
  id?: number;
  /** 是否在审核页输入分值（0-否 1-是） */
  isInputScore?: number;
  /** 是否每人的分值一致（0-否 1-是） */
  isSameScore?: number;
  /** 分值来源（0-字段组合，1-固定，2-指定分值字段） */
  scoreSource?: number;
  /** 标签配置选择id */
  tagConfigSelectId?: number;
};

export type cmsTagDeleteidParams = {
  /** 标签id */
  id: number;
};

export type CmsTagDetailAndScoreDto = {
  /** 分配方式（0：项目制计分，人头制计分） */
  allocateType?: number;
  /** 人头值-绩效计算方式 */
  combinationList?: CmsTagConfigCombination_[];
  /** 组合名称 */
  combinationName?: string;
  /** 得分情况 */
  finalScoreList?: CmsFinalScoreDto[];
  /** 行id */
  rowId?: number;
  /** 赋分方式（0：手动，1：自动） */
  scoreMethod?: number;
  /** 项目制-绩效计算方式（0-数据项组合计算分值，1-固定分值，2-数据项等值分值） */
  scoreSource?: number;
  /** 标签id */
  tagId?: number;
  /** 数据表id */
  templateId?: number;
  /** 表名称 */
  templateName?: string;
  /** 数据表类型（0:成果、1：院情、 2: 工作） */
  templateType?: number;
  /** 总分 */
  totalScore?: number;
};

export type cmsTagGetByIdParams = {
  /** 标签id */
  id: number;
};

export type cmsTagGetTagTreeByIdParams = {
  /** 数据表id */
  id: number;
};

export type CmsTagNode_ = {
  /** 实际得分（不会溢出的得分） */
  actualScore?: number;
  /** 关联数据表id列表，指标此字段为null */
  categoryTemplateIds?: number[];
  /** 子标签 */
  children?: CmsTagNode_[];
  /** 当前标签及所有子孙标签绩效得分之和（可能会溢出的得分） */
  countScore?: number;
  id?: number;
  /** 分数上线，级别配置有效，评价标准配置无效 */
  maxScore?: number;
  /** 旧标签名称 */
  oldTagName?: string;
  /** 父标签id，用于构建标签树 */
  parentTagId?: number;
  /** 标签绩效得分 */
  score?: number;
  /** 标签等级 */
  tagLevel?: number;
  /** 标签名称 */
  tagName?: string;
  /** 该指标下的绩效规则 */
  tagRules?: string[];
  /** 标签类型，0-等级指标，1-评价标准 */
  tagType?: number;
};

export type CmsTagParam = {
  /** 关联实体id列表 */
  categoryTemplateIds?: number[];
  /** 分数上线，级别配置有效，评价标准配置无效 */
  maxScore?: number;
  /** 父标签id，用于构建标签树， 0表示没有父标签 */
  parentTagId?: number;
  /** 标签名称 */
  tagName?: string;
  /** 标签类型，0-等级指标，1-评价标准 */
  tagType?: number;
};

export type CmsTagProportion_ = {
  id?: number;
  /** 用户id */
  personId?: number;
  /** 人员类型（1：项目负责人，7：参与人员） */
  personType?: number;
  /** 占比 */
  proportion?: number;
  /** 行id */
  rowId?: number;
};

export type cmsTagProportionGetProportionByRowIdParams = {
  /** rowId */
  rowId: number;
};

export type CmsTagResp = {
  /** 大类id列表 */
  categoryTemplateIds?: number[];
  id?: number;
  /** 分数上线，级别配置有效，评价标准配置无效 */
  maxScore?: number;
  /** 旧标签名称 */
  oldTagName?: string;
  /** 父标签id，用于构建标签树 */
  parentTagId?: number;
  /** 标签等级 */
  tagLevel?: number;
  /** 标签名称 */
  tagName?: string;
  /** 标签类型，0-等级指标，1-评价标准 */
  tagType?: number;
};

export type CmsTagUpdateParam = {
  /** 关联实体id列表 */
  categoryTemplateIds?: number[];
  id: number;
  /** 分数上线，级别配置有效，评价标准配置无效 */
  maxScore?: number;
  /** 标签名称 */
  tagName?: string;
};

export type CmsTemplateGroups_ = {
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: string;
  /** 实体分组描述 */
  description?: string;
  /** 实体分组名称 */
  groupName?: string;
  /** 实体分组ID */
  id?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: string;
};

export type cmsTemplateGroupsDeleteParams = {
  /** groupId */
  groupId: number;
};

export type cmsTemplateGroupsGetByIdParams = {
  /** groupId */
  groupId: number;
};

export type cmsTemplateGroupsGetByTemplateIdParams = {
  /** templateId */
  templateId: number;
};

export type CmsToBeReassignedDto = {
  id?: number;
  rows?: CmsCategoryRow0;
  /** 绩效规则id */
  tagId?: number;
};

export type CommonDeleteDto = {
  id: number;
};

export type CommonPageCmsAchievement_ = {
  current?: number;
  pages?: number;
  records?: CmsAchievement[];
  size?: number;
  total?: number;
};

export type CommonPageCmsCategoryRow_ = {
  current?: number;
  pages?: number;
  records?: CmsCategoryRow0[];
  size?: number;
  total?: number;
};

export type CommonPageCmsCheck_ = {
  current?: number;
  pages?: number;
  records?: CmsCheck0[];
  size?: number;
  total?: number;
};

export type CommonPageCmsRowValueResult_ = {
  current?: number;
  pages?: number;
  records?: CmsRowValueResult[];
  size?: number;
  total?: number;
};

export type CommonPageCmsToBeReassignedDto_ = {
  current?: number;
  pages?: number;
  records?: CmsToBeReassignedDto[];
  size?: number;
  total?: number;
};

export type CommonPageFmsFiles_ = {
  current?: number;
  pages?: number;
  records?: FmsFiles_[];
  size?: number;
  total?: number;
};

export type CommonPageMmsBacklog_ = {
  current?: number;
  pages?: number;
  records?: MmsBacklog_[];
  size?: number;
  total?: number;
};

export type CommonPageMmsMessage_ = {
  current?: number;
  pages?: number;
  records?: MmsMessage_[];
  size?: number;
  total?: number;
};

export type CommonPageNmsNews_ = {
  current?: number;
  pages?: number;
  records?: NmsNews_[];
  size?: number;
  total?: number;
};

export type CommonPageNmsNewsTemplate_ = {
  current?: number;
  pages?: number;
  records?: NmsNewsTemplate_[];
  size?: number;
  total?: number;
};

export type CommonPageOmsOrganizationHonor_ = {
  current?: number;
  pages?: number;
  records?: OmsOrganizationHonor_[];
  size?: number;
  total?: number;
};

export type CommonPagePmsScoreDTO_ = {
  current?: number;
  pages?: number;
  records?: PmsScoreDTO[];
  size?: number;
  total?: number;
};

export type CommonPageTmsProject_ = {
  current?: number;
  pages?: number;
  records?: TmsProject_[];
  size?: number;
  total?: number;
};

export type CommonPageTmsProjectRequestDto_ = {
  current?: number;
  pages?: number;
  records?: TmsProjectRequestDto[];
  size?: number;
  total?: number;
};

export type CommonPageTodoResult_ = {
  current?: number;
  pages?: number;
  records?: TodoResult[];
  size?: number;
  total?: number;
};

export type CommonPageUmsMenu_ = {
  current?: number;
  pages?: number;
  records?: UmsMenu0[];
  size?: number;
  total?: number;
};

export type CommonPageUmsPerson_ = {
  current?: number;
  pages?: number;
  records?: UmsPerson_[];
  size?: number;
  total?: number;
};

export type CommonPageUmsPost_ = {
  current?: number;
  pages?: number;
  records?: UmsPost_[];
  size?: number;
  total?: number;
};

export type CommonPageUmsResource_ = {
  current?: number;
  pages?: number;
  records?: UmsResource_[];
  size?: number;
  total?: number;
};

export type CommonResultBoolean_ = {
  code?: number;
  data?: boolean;
  message?: string;
};

export type CommonResultCmsCategoryRow_ = {
  code?: number;
  data?: CmsCategoryRow0;
  message?: string;
};

export type CommonResultCmsCategoryTemplate_ = {
  code?: number;
  data?: CmsCategoryTemplate0;
  message?: string;
};

export type CommonResultCmsCategoryTemplateValue_ = {
  code?: number;
  data?: CmsCategoryTemplateValue10;
  message?: string;
};

export type CommonResultCmsTagConfig_ = {
  code?: number;
  data?: CmsTagConfig_;
  message?: string;
};

export type CommonResultCmsTagDetailAndScoreDto_ = {
  code?: number;
  data?: CmsTagDetailAndScoreDto;
  message?: string;
};

export type CommonResultCmsTagNode_ = {
  code?: number;
  data?: CmsTagNode_;
  message?: string;
};

export type CommonResultCmsTagResp_ = {
  code?: number;
  data?: CmsTagResp;
  message?: string;
};

export type CommonResultCmsTemplateGroups_ = {
  code?: number;
  data?: CmsTemplateGroups_;
  message?: string;
};

export type CommonResultCommonPageCmsAchievement_ = {
  code?: number;
  data?: CommonPageCmsAchievement_;
  message?: string;
};

export type CommonResultCommonPageCmsCategoryRow_ = {
  code?: number;
  data?: CommonPageCmsCategoryRow_;
  message?: string;
};

export type CommonResultCommonPageCmsCheck_ = {
  code?: number;
  data?: CommonPageCmsCheck_;
  message?: string;
};

export type CommonResultCommonPageCmsRowValueResult_ = {
  code?: number;
  data?: CommonPageCmsRowValueResult_;
  message?: string;
};

export type CommonResultCommonPageCmsToBeReassignedDto_ = {
  code?: number;
  data?: CommonPageCmsToBeReassignedDto_;
  message?: string;
};

export type CommonResultCommonPageFmsFiles_ = {
  code?: number;
  data?: CommonPageFmsFiles_;
  message?: string;
};

export type CommonResultCommonPageMmsBacklog_ = {
  code?: number;
  data?: CommonPageMmsBacklog_;
  message?: string;
};

export type CommonResultCommonPageMmsMessage_ = {
  code?: number;
  data?: CommonPageMmsMessage_;
  message?: string;
};

export type CommonResultCommonPageNmsNews_ = {
  code?: number;
  data?: CommonPageNmsNews_;
  message?: string;
};

export type CommonResultCommonPageNmsNewsTemplate_ = {
  code?: number;
  data?: CommonPageNmsNewsTemplate_;
  message?: string;
};

export type CommonResultCommonPageOmsOrganizationHonor_ = {
  code?: number;
  data?: CommonPageOmsOrganizationHonor_;
  message?: string;
};

export type CommonResultCommonPagePmsScoreDTO_ = {
  code?: number;
  data?: CommonPagePmsScoreDTO_;
  message?: string;
};

export type CommonResultCommonPageTmsProject_ = {
  code?: number;
  data?: CommonPageTmsProject_;
  message?: string;
};

export type CommonResultCommonPageTmsProjectRequestDto_ = {
  code?: number;
  data?: CommonPageTmsProjectRequestDto_;
  message?: string;
};

export type CommonResultCommonPageTodoResult_ = {
  code?: number;
  data?: CommonPageTodoResult_;
  message?: string;
};

export type CommonResultCommonPageUmsMenu_ = {
  code?: number;
  data?: CommonPageUmsMenu_;
  message?: string;
};

export type CommonResultCommonPageUmsPerson_ = {
  code?: number;
  data?: CommonPageUmsPerson_;
  message?: string;
};

export type CommonResultCommonPageUmsPost_ = {
  code?: number;
  data?: CommonPageUmsPost_;
  message?: string;
};

export type CommonResultCommonPageUmsResource_ = {
  code?: number;
  data?: CommonPageUmsResource_;
  message?: string;
};

export type CommonResultListCmsCategoryRow_ = {
  code?: number;
  data?: CmsCategoryRow_[];
  message?: string;
};

export type CommonResultListCmsCategoryTemplate_ = {
  code?: number;
  data?: CmsCategoryTemplate_[];
  message?: string;
};

export type CommonResultListCmsCategoryTemplateValue_ = {
  code?: number;
  data?: CmsCategoryTemplateValue0[];
  message?: string;
};

export type CommonResultListCmsCheck_ = {
  code?: number;
  data?: CmsCheck1[];
  message?: string;
};

export type CommonResultListCmsClassify_ = {
  code?: number;
  data?: CmsClassify_[];
  message?: string;
};

export type CommonResultListCmsTag_ = {
  code?: number;
  data?: CmsTag_[];
  message?: string;
};

export type CommonResultListCmsTagCategoryEntity_ = {
  code?: number;
  data?: CmsTagCategoryEntity_[];
  message?: string;
};

export type CommonResultListCmsTagConfigCombinationResp_ = {
  code?: number;
  data?: CmsTagConfigCombinationResp[];
  message?: string;
};

export type CommonResultListCmsTagConfigResp_ = {
  code?: number;
  data?: CmsTagConfigResp[];
  message?: string;
};

export type CommonResultListCmsTagNode_ = {
  code?: number;
  data?: CmsTagNode_[];
  message?: string;
};

export type CommonResultListCmsTagProportion_ = {
  code?: number;
  data?: CmsTagProportion_[];
  message?: string;
};

export type CommonResultListCmsTemplateGroups_ = {
  code?: number;
  data?: CmsTemplateGroups_[];
  message?: string;
};

export type CommonResultListDmsDict_ = {
  code?: number;
  data?: DmsDict_[];
  message?: string;
};

export type CommonResultListFileInfoDto_ = {
  code?: number;
  data?: FileInfoDto[];
  message?: string;
};

export type CommonResultListLong_ = {
  code?: number;
  data?: number[];
  message?: string;
};

export type CommonResultListMinioUploadDto_ = {
  code?: number;
  data?: MinioUploadDto[];
  message?: string;
};

export type CommonResultListMmsMessage_ = {
  code?: number;
  data?: MmsMessage_[];
  message?: string;
};

export type CommonResultListNmsNewsTemplate_ = {
  code?: number;
  data?: NmsNewsTemplate_[];
  message?: string;
};

export type CommonResultListOmsLandscapeOrientationResearchPerformanceDto_ = {
  code?: number;
  data?: OmsLandscapeOrientationResearchPerformanceDto[];
  message?: string;
};

export type CommonResultListOmsLast3YearProjectListResult_ = {
  code?: number;
  data?: OmsLast3YearProjectListResult[];
  message?: string;
};

export type CommonResultListOmsOrganization_ = {
  code?: number;
  data?: OmsOrganization0[];
  message?: string;
};

export type CommonResultListOmsOrganizationOccurrenceDto_ = {
  code?: number;
  data?: OmsOrganizationOccurrenceDto[];
  message?: string;
};

export type CommonResultListOmsPicChartDataResult_ = {
  code?: number;
  data?: OmsPicChartDataResult[];
  message?: string;
};

export type CommonResultListOmsPublicStaticsResultDto_ = {
  code?: number;
  data?: OmsPublicStaticsResultDto[];
  message?: string;
};

export type CommonResultListPmsScore_ = {
  code?: number;
  data?: PmsScore_[];
  message?: string;
};

export type CommonResultListPmsScoreDTO_ = {
  code?: number;
  data?: PmsScoreDTO[];
  message?: string;
};

export type CommonResultListPmsScoreTotalDto_ = {
  code?: number;
  data?: PmsScoreTotalDto[];
  message?: string;
};

export type CommonResultListString_ = {
  code?: number;
  data?: string[];
  message?: string;
};

export type CommonResultListUmsAdmin_ = {
  code?: number;
  data?: UmsAdmin_[];
  message?: string;
};

export type CommonResultListUmsAuthorityCard_ = {
  code?: number;
  data?: UmsAuthorityCard_[];
  message?: string;
};

export type CommonResultListUmsDept_ = {
  code?: number;
  data?: UmsDept_[];
  message?: string;
};

export type CommonResultListUmsMenu_ = {
  code?: number;
  data?: UmsMenu_[];
  message?: string;
};

export type CommonResultListUmsPerson_ = {
  code?: number;
  data?: UmsPerson_[];
  message?: string;
};

export type CommonResultListUmsPost_ = {
  code?: number;
  data?: UmsPost_[];
  message?: string;
};

export type CommonResultListUmsResource_ = {
  code?: number;
  data?: UmsResource_[];
  message?: string;
};

export type CommonResultListUmsResourceCategory_ = {
  code?: number;
  data?: UmsResourceCategory_[];
  message?: string;
};

export type CommonResultListUmsRole_ = {
  code?: number;
  data?: UmsRole_[];
  message?: string;
};

export type CommonResultLong_ = {
  code?: number;
  data?: number;
  message?: string;
};

export type CommonResultMapStringInt_ = {
  code?: number;
  data?: Record<string, unknown>;
  message?: string;
};

export type CommonResultMinioUploadDto_ = {
  code?: number;
  data?: MinioUploadDto;
  message?: string;
};

export type CommonResultNmsNews_ = {
  code?: number;
  data?: NmsNews_;
  message?: string;
};

export type CommonResultNmsNewsTemplate_ = {
  code?: number;
  data?: NmsNewsTemplate_;
  message?: string;
};

export type CommonResultObject_ = {
  code?: number;
  data?: Record<string, unknown>;
  message?: string;
};

export type CommonResultOmsOrganization_ = {
  code?: number;
  data?: OmsOrganization_;
  message?: string;
};

export type CommonResultOmsPicChartDataCostResult_ = {
  code?: number;
  data?: OmsPicChartDataCostResult;
  message?: string;
};

export type CommonResultOmsPicChartDataResult_ = {
  code?: number;
  data?: OmsPicChartDataResult;
  message?: string;
};

export type CommonResultOmsTeacherStudentFloatResult_ = {
  code?: number;
  data?: OmsTeacherStudentFloatResult;
  message?: string;
};

export type CommonResultPageCmsCategoryRow_ = {
  code?: number;
  data?: PageCmsCategoryRow_;
  message?: string;
};

export type CommonResultRowImportErrorMessage_ = {
  code?: number;
  data?: RowImportErrorMessage;
  message?: string;
};

export type CommonResultString_ = {
  code?: number;
  data?: string;
  message?: string;
};

export type CommonResultTmsProject_ = {
  code?: number;
  data?: TmsProject0;
  message?: string;
};

export type CommonResultUmsMenu_ = {
  code?: number;
  data?: UmsMenu0;
  message?: string;
};

export type CommonResultUmsPerson_ = {
  code?: number;
  data?: UmsPerson1Res;
  message?: string;
};

export type CommonResultUmsPersonSensitive_ = {
  code?: number;
  data?: UmsPersonSensitive_;
  message?: string;
};

export type CommonResultUmsPost_ = {
  code?: number;
  data?: UmsPost_;
  message?: string;
};

export type CommonResultUmsResource_ = {
  code?: number;
  data?: UmsResource_;
  message?: string;
};

export type CommonResultUmsRole_ = {
  code?: number;
  data?: UmsRole0;
  message?: string;
};

export type CommonResultVoid_ = {
  code?: number;
  message?: string;
};

export type DmsDict_ = {
  /** 创建时间 */
  createTime?: string;
  id?: number;
  /** 字典键值标签 */
  name?: string;
  /** 字典类型 */
  type?: string;
  /** 使用次数 */
  useCount?: number;
};

export type ExportScoreListDTO = {
  personIds?: number[];
  year?: number;
};

export type FileInfoDto = {
  contentType?: string;
  fileName?: string;
  lastModified?: string;
  objectName?: string;
  size?: number;
};

export type FmsFiles_ = {
  /** 业务ID */
  businessId?: string;
  /** 业务类型 */
  businessType?: string;
  /** 业务类型名 */
  businessTypeName?: string;
  /** 文件描述 */
  fileDescription?: string;
  /** 文件ID */
  fileId?: number;
  /** 文件名 */
  fileName?: string;
  /** 文件路径 */
  filePath?: string;
  /** 文件类型 */
  fileType?: string;
  /** 文件类型名 */
  fileTypeName?: string;
  /** 上传日期和时间 */
  uploadDate?: string;
};

export type fmsFilesDeleteParams = {
  /** 文件ID */
  fileId: number;
};

export type fmsFilesListParams = {
  /** 文件名 */
  fileName?: string;
  /** 页码 */
  pageNum: number;
  /** 每页数量 */
  pageSize: number;
};

export type FullTextSearch = {
  /** 查询参数 */
  items?: PublicItemParam[];
  pageParam?: PageParam;
  /** 数据表id */
  templates?: number[];
};

export type ImportErrorInfo = {
  /** 重复文件值 */
  repetitionFile?: ImportRowValueResult[];
  /** 重复资料库值 */
  repetitionOwe?: ImportRowValueResult[];
  /** 文件重复行 */
  repetitionRow?: ImportRowValueResult[];
};

export type ImportRowValueResult = {
  rowId?: number;
  rowInfo?: CmsCategoryRow_;
  values?: CmsCategoryValue_[];
};

export type InputStream = object;

export type InsertValueParam = {
  /** 所属大类 */
  categoryTemplateId?: number;
  /** 行id */
  id?: number;
  /** 占比 */
  properties?: CmsTagProportion_[];
  /** 大类列数据 */
  values?: CmsCategoryValue_[];
};

export type minioDeleteParams = {
  /** 文件Url */
  fileUrl: string;
};

export type minioDownloadByFileUrlParams = {
  /** 文件地址 */
  fileUrl: string;
};

export type minioDownloadByImageUrlParams = {
  /** 图片地址 */
  fileUrl: string;
};

export type MinioUploadDto = {
  /** 文件名称 */
  name?: string;
  /** 文件全名称 */
  objectName?: string;
  /** 文件访问URL */
  url?: string;
};

export type MmsBacklog_ = {
  /** 代录入人 */
  agency?: number;
  /** 内容 */
  content?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  id?: number;
  /** 是否删除 */
  isDeleted?: number;
  /** 接收人 */
  receiver?: number;
  /** 业务id */
  rowId?: number;
  /** 已读未读 */
  status?: number;
  /** 标题 */
  title?: string;
  /** 类型 */
  type?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type mmsBacklogBacklogCompleteidParams = {
  /** id */
  id: number;
};

export type mmsBacklogListParams = {
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
  /** 0 未完成， 1已完成 */
  status?: number;
  /**  类型在 BacklogEnum 类查看 */
  types?: number[];
};

export type MmsMessage_ = {
  /** 通知内容 */
  content?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 消息id */
  id?: number;
  /** 是否删除（0：未删除，1：已删除） */
  isDeleted?: number;
  /** 消息接受人 */
  receiver?: number;
  /** 行id */
  rowId?: number;
  /** 已读未读状态 0-未读，1-已读，2-撤回 */
  status?: number;
  /** 消息标题 */
  title?: string;
  /** 消息类型(0：草稿消息；1：待审批消息；2：审批失败消息；3：组队消息；4：共享申请消息) */
  type?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type mmsMessageListMessageParams = {
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
  /** 0 未读， 1已读 */
  status?: number;
  /**  类型在 MessageTypeEnum 类查看 */
  types?: number[];
};

export type mmsMessageMessageReadidParams = {
  /** id */
  id: number;
};

export type MultifileDto = {
  /** 文件地址列表 */
  fileUrls?: string[];
};

export type NmsNews_ = {
  /** 作者id */
  authorId?: number;
  /** 作者名称 */
  authorName?: string;
  /** 数据表id */
  categoryId?: number;
  /** 内容 */
  content?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 新闻ID */
  id?: number;
  /** 发布时间 */
  publishedAt?: string;
  /** 备注 */
  remark?: string;
  /** 行数据id */
  rowId?: number;
  /** 状态（1：草稿、2：审批中、3：审批拒绝、4：审批通过、5：已发布、6：已归档） */
  status?: number;
  /** 标题 */
  title?: string;
  /** 新闻类型（1：默认类型、2：简讯） */
  type?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type nmsNewsGetDraftByIdParams = {
  /** id */
  id: number;
};

export type nmsNewsGetPublishByIdParams = {
  /** id */
  id: number;
};

export type nmsNewsGetSubmitByIdParams = {
  /** id */
  id: number;
};

export type nmsNewsListBriefParams = {
  /** 内容 */
  content?: string;
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
  /** 标题 */
  title?: string;
};

export type nmsNewsListDraftParams = {
  /** 内容 */
  content?: string;
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
  /** 标题 */
  title?: string;
};

export type nmsNewsListPublishParams = {
  /** 内容 */
  content?: string;
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
  /** 标题 */
  title?: string;
};

export type nmsNewsListSubmitParams = {
  /** 内容 */
  content?: string;
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
  /** 标题 */
  title?: string;
};

export type NmsNewsSaveParam = {
  /** 内容 */
  content?: string;
  /** 标题 */
  title?: string;
};

export type NmsNewsSubmitParam = {
  /** 内容 */
  content?: string;
  /** 新闻草稿的id */
  id?: number;
  /** 标题 */
  title?: string;
};

export type NmsNewsTemplate_ = {
  /** 模板内容 */
  content?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 大类ID */
  entityId?: number;
  /** 大类名称 */
  entityName?: string;
  id?: number;
  /** 模板名称 */
  name?: string;
  /** 模板占位符 */
  placeholders?: string;
  /** 模板规则(符合条件的规则) */
  rule?: number[];
  /** 模板类型：1大类模板 */
  type?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type nmsNewsTemplateDetailParams = {
  /** id */
  id: number;
};

export type nmsNewsTemplateGetCategoryFieldListParams = {
  /** categoryId */
  categoryId?: number;
};

export type nmsNewsTemplateGetTemplatesByEntityIdParams = {
  /** entityId */
  entityId: number;
};

export type NmsNewsTemplatePageParam = {
  /** 模板内容 */
  content?: string;
  /** 大类ID */
  entityId?: number;
  /** 大类名称 */
  entityName?: string;
  /** 模板名称 */
  name?: string;
  /** 页码，从 1 开始 */
  pageNum?: number;
  /** 每页条数，最大值为 100 */
  pageSize?: number;
  /** 模板类型：1大类模板 */
  type?: number;
};

export type NmsNewsTemplateSaveParam = {
  /** 模板内容 */
  content?: string;
  /** 大类ID */
  entityId?: number;
  /** 大类名称 */
  entityName?: string;
  /** 模板名称 */
  name?: string;
  /** 模板占位符 */
  placeholders?: string;
  /** 模板规则 */
  rule?: number[];
  /** 模板类型：1大类模板 */
  type?: number;
};

export type NmsNewsTemplateUpdateParam = {
  /** 模板内容 */
  content?: string;
  /** 大类ID */
  entityId?: number;
  /** 大类名称 */
  entityName?: string;
  id: number;
  /** 模板名称 */
  name?: string;
  /** 模板占位符 */
  placeholders?: string;
  /** 模板规则 */
  rule?: number[];
  /** 模板类型：1大类模板 */
  type?: number;
};

export type NmsNewsUpdateParam = {
  /** 内容 */
  content?: string;
  /** 新闻草稿的id */
  id?: number;
  /** 标题 */
  title?: string;
};

export type NmsNewsUpdateSubmitParam = {
  /** 内容 */
  content?: string;
  /** 待审核新闻的id */
  id?: number;
  /** 标题 */
  title?: string;
};

export type NmsPublishSubmitDto = {
  /** 发布新闻的id */
  id?: number;
};

export type OmsChangePositionDto = {
  /** 组织团队id */
  organizationId: number;
  /** personId */
  personId: number;
  /** 岗位编号(枚举值) 0负责人 1普通成员 2执行人 */
  position: number;
};

export type OmsCountSectionStaticsDto = {
  /** 计费字段 */
  costTemplateValueIds?: number[];
  /** 最高费用 */
  maxCost?: number;
  /** 最低费用 */
  minCost?: number;
  /** 返回名称 */
  returnName?: string;
  /** 大类ID */
  templateId?: number;
  /** 字段ID */
  templateValueIds?: number[];
  /** 时间ID */
  timeId?: number;
  /** 时间类型 */
  timeType?: number;
  /** 值 */
  values?: string[];
  /** 年 */
  year?: string;
};

export type OmsCountStaticsDto = {
  /** 大类ID */
  templateId?: number;
  /** 字段ID */
  templateValueIds?: number[];
  /** 时间ID */
  timeId?: number;
  /** 时间类型 */
  timeType?: number;
  /** 值 */
  values?: string[];
  /** 年 */
  year?: string;
};

export type OmsCountStaticsParamDto = {
  list?: OmsCountStaticsDto[];
  returnName?: string;
};

export type OmsLandscapeOrientationResearchPerformanceDto = {
  /** 花费 */
  expenditure?: number;
  /** 立项日期 */
  initiationTime?: string;
  /** 负责人 */
  principal?: string;
  /** 项目名称 */
  projectName?: string;
};

export type OmsLast3YearProjectListResult = {
  /** 项目级别 */
  level?: string;
  /** 负责人 */
  principal?: string;
  /** 项目名称 */
  projectName?: string;
  /** 立项时间 */
  startDate?: string;
};

export type OmsOrganization_ = {
  /** 部门id */
  deptId?: number;
  id?: number;
  /** 团队成员列表 */
  memberList?: UmsPerson_[];
  /** 实际执行人id */
  organizationActualExecutorId?: number;
  /** 基本信息 */
  organizationInfo?: string;
  /** 组织负责人 */
  organizationLeaderId?: number;
  /** 组织名称 */
  organizationName?: string;
};

export type OmsOrganization0 = {
  /** 部门id */
  deptId?: number;
  id?: number;
  /** 基本信息 */
  organizationInfo?: string;
  /** 组织负责人 */
  organizationLeaderId?: number;
  /** 组织名称 */
  organizationName?: string;
};

export type OmsOrganizationDto = {
  /** 部门id */
  deptId?: number;
  /** 基本信息 */
  organizationInfo?: string;
  /** 组织负责人 */
  organizationLeaderId?: number;
  /** 组织名称 */
  organizationName?: string;
};

export type omsOrganizationGetOrgByIdParams = {
  /** orgId */
  orgId: number;
};

export type OmsOrganizationHonor_ = {
  /** 荣誉名称 */
  honorName?: string;
  /** id */
  id?: number;
  /** 组织id */
  organizationId?: number;
};

export type omsOrganizationHonorDeleteidParams = {
  /** id */
  id: number;
};

export type OmsOrganizationHonorDto = {
  /** 荣誉名称 */
  honorName?: string;
  /** 组织id */
  organizationId?: number;
};

export type omsOrganizationHonorListParams = {
  /** honorName */
  honorName?: string;
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
};

export type OmsOrganizationHonorUpdateDto = {
  /** 荣誉名称 */
  honorName?: string;
};

export type omsOrganizationHonorUpdateidParams = {
  /** id */
  id: number;
};

export type OmsOrganizationMemberUpdateDto = {
  /** 成员id */
  memberId?: number;
  /** 组织id */
  organizationId?: number;
  /** 职位 */
  position?: string;
};

export type OmsOrganizationOccurrenceDto = {
  /** 名称 */
  name?: string;
  /** 值List */
  valueList?: OmsPublicStaticsResultDto[];
};

export type omsOrganizationPersonListParams = {
  /** orgId */
  orgId: number;
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
};

export type omsOrganizationPersonMembersParams = {
  /** memberName */
  memberName: string;
  /** orgId */
  orgId: number;
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
};

export type OmsOrganizationUpdateDto = {
  id: number;
  /** 基本信息 */
  organizationInfo?: string;
  /** 组织负责人 */
  organizationLeaderId?: number;
  /** 组织名称 */
  organizationName?: string;
};

export type OmsPersonDeleteDto = {
  memberId?: number;
  orgId?: number;
};

export type OmsPicChartDataCostResult = {
  /** 数据名称 */
  name?: string;
  /** 数据值 */
  total?: number;
};

export type OmsPicChartDataResult = {
  /** 数据值 */
  count?: number;
  /** 数据名称 */
  name?: string;
};

export type OmsPublicStaticsDto = {
  /** 结束时间 */
  endTime?: string;
  /** 结束时间字段ID */
  endTimeId?: number;
  /** 级别字段ID */
  gradeId?: number;
  /** 查询的级别值 */
  grades?: string[];
  /** 开始时间 */
  startTime?: string;
  /** 开始时间字段ID */
  startTimeId?: number;
  /** 大类ID */
  templateId?: number;
  /** 所属字段ID */
  templateValues?: number[];
  /** 值 */
  values?: string[];
};

export type OmsPublicStaticsResultDto = {
  /** 名称 */
  name?: string;
  /** 数量 */
  number?: number;
};

export type OmsTeacherStudentFloatResult = {
  /** 专任教师总数 */
  fullTeacherSum?: number;
  /** 专业负责人 */
  majorCharge?: string;
  /** 专业名称 */
  majorName?: string;
  /** 学生总数 */
  studentSum?: number;
};

export type OrderItem = {
  asc?: boolean;
  column?: string;
};

export type overViewEmployeeTypeNumberPieChartParams = {
  /** 大类ID */
  categoryId: number;
  /** 员工类型字段id */
  employeeTypeColumnId: number;
};

export type overViewFullOrPartTeacherPieChartParams = {
  /** 员工大类ID */
  employeeCategoryId: number;
  /** 员工类型字段id */
  employeeTypeColumnId: number;
  /** 专任教师教师类型 */
  fullTeacherType: string;
  /** 兼职教师大类ID */
  partTeacherCategoryId: number;
  /** 兼职教师聘期结束时间字段id */
  partTeacherEndTimeColumnId: number;
  /** 兼职教师聘期开始时间字段id */
  partTeacherStartTimeColumnId: number;
};

export type overViewGetCountByConditionParams = {
  /** returnName */
  returnName: string;
};

export type overViewGetDataCostParams = {
  /** returnName */
  returnName: string;
  /** templateId */
  templateId: number;
  /** templateValueIds */
  templateValueIds: number[];
  /** timeId */
  timeId?: number;
};

export type overViewGetDataCountDistinctParams = {
  /** returnName */
  returnName: string;
  /** templateId */
  templateId: number;
  /** templateValueIds */
  templateValueIds: number[];
  /** timeId */
  timeId?: number;
};

export type overViewGetDataCountParams = {
  /** returnName */
  returnName: string;
  /** templateId */
  templateId: number;
  /** templateValueIds */
  templateValueIds: number[];
  /** timeId */
  timeId?: number;
};

export type overViewGetLandscapeOrientationResearchPerformanceThreeYearsProjectListParams =
  {
    /** 横向科研项目业绩 大类id */
    categoryId?: number;
    /** 合同名称 字段id */
    contractColumnId?: number;
    /** 花费 字段id */
    expenditureColumnId?: number;
    /** 负责人 字段id */
    principalColumnId?: number;
    /** 签订日期 字段id */
    sinningDateColumnId?: number;
  };

export type overViewGetLast3YearProjectListCollegeParams = {
  /** 纵向科研项目（校级以上）业绩大类id */
  categoryId: number;
  /** 级别字段id */
  levelColumnId: number;
  /** 负责人字段id */
  principalColumnId: number;
  /** 项目名称字段id */
  projectNameColumnId: number;
  /** 立项日期字段id */
  startDateColumnId: number;
};

export type overViewGetLast3YearProjectListMajorParams = {
  /** 纵向科研项目（校级以上）业绩大类id */
  categoryId: number;
  /** 级别字段id */
  levelColumnId: number;
  /** 专业 */
  major: string;
  /** 所属专业字段id */
  majorColumnId: number;
  /** 负责人字段id */
  principalColumnId: number;
  /** 项目名称字段id */
  projectNameColumnId: number;
  /** 立项日期字段id */
  startDateColumnId: number;
};

export type overViewGetLast3YearProjectListPersonParams = {
  /** 纵向科研项目（校级以上）业绩大类id */
  categoryId: number;
  /** 级别字段id */
  levelColumnId: number;
  /** 所属人字段id */
  personColumnId: number;
  /** 负责人字段id */
  principalColumnId: number;
  /** 项目名称字段id */
  projectNameColumnId: number;
  /** 立项日期字段id */
  startDateColumnId: number;
};

export type overViewGetMajorLandscapeOrientationResearchPerformanceThreeYearsProjectListParams =
  {
    /** 横向科研项目业绩 大类id */
    categoryId?: number;
    /** 合同名称 字段id */
    contractColumnId?: number;
    /** 花费 字段id */
    expenditureColumnId?: number;
    /** 专业 字段值 */
    major?: string[];
    /** 专业 字段id */
    majorColumnId?: number;
    /** 负责人 字段id */
    principalColumnId?: number;
    /** 签订日期 字段id */
    sinningDateColumnId?: number;
  };

export type overViewGetOrganizationLevelOccurrenceParams = {
  /** 级别字段id */
  levelValueId: number;
  /** 级别字段需要统计的值 */
  levelValueList: string[];
  /** 团队字段id */
  organizationValueId: number;
  /** 团队字段需要查询的值 */
  organizationValueList: string[];
  /** 大类id */
  templateId: number;
};

export type overViewGetPersonLandscapeOrientationResearchPerformanceThreeYearsProjectListParams =
  {
    /** 横向科研项目业绩 大类id */
    categoryId?: number;
    /** 合同名称 字段id */
    contractColumnId?: number;
    /** 花费 字段id */
    expenditureColumnId?: number;
    /** 负责人 字段id */
    principalColumnId?: number;
    /** 签订日期 字段id */
    sinningDateColumnId?: number;
  };

export type overViewGetRowCountParams = {
  /** templateId */
  templateId: number;
  /** timeId */
  timeId?: number;
};

export type overViewGetStudentResearchPieCharParams = {
  /** 著作权（版权） 大类ID */
  copyRightCategoryId: number;
  /** 著作权 '合作情况' 表头字段id */
  copyRightCooperationColumnId: number;
  /** 著作权 '出版日期' 表头字段id */
  copyRightPublishedTimeColumnId: number;
  /** 著作权 '类型' 表头字段id */
  copyRightTypeColumnId: number;
  /** 指导学生主持项目 大类ID */
  instructStudentProjectCategoryId: number;
  /** 指导学生主持项目 '立项日期' 表头字段id */
  instructStudentProjectInitiationTimeColumnId: number;
  /** 专利业绩 '申请日期' 表头字段id */
  patentPerformanceAppliedDateColumnId: number;
  /** 专利业绩 大类ID */
  patentPerformanceCategoryId: number;
  /** 专利业绩 '合作情况' 表头字段id */
  patentPerformanceCooperationColumnId: number;
  /** 纵向科研项目（校级以上）业绩 大类ID */
  researchProjectCategoryId: number;
  /** 纵向科研项目（校级以上）业绩 '立项日期' 表头字段id */
  researchProjectInitiationTimeColumnId: number;
  /** 纵向科研项目（校级以上）业绩 '项目类型' 表头字段id */
  researchProjectTypeColumnId: number;
  /** 论文业绩 '合作情况' 表头字段id */
  thesisCooperationColumnId: number;
  /** 论文业绩 大类ID */
  thesisPerformanceCategoryId: number;
  /** 论文业绩 '发表/出版时间' 表头字段id */
  thesisPublishedTimeColumnId: number;
  /** 纵向科研项目（校级）业绩 大类ID */
  universityResearchProjectCategoryId: number;
  /** 纵向科研项目（校级）业绩 '立项日期' 表头字段id */
  universityResearchProjectInitiationTimeColumnId: number;
  /** 纵向科研项目（校级）业绩 '项目类型' 表头字段id */
  universityResearchProjectTypeColumnId: number;
};

export type overViewGetTemplateValueOccurrenceParams = {
  /** 大类id */
  templateId: number;
  /** 字段id */
  templateValueId: number;
  /** 字段需要统计次数的值 */
  templateValueList: string[];
};

export type overViewGetTop5AttendeesParams = {
  /** personId */
  personId: number;
  /** templateId */
  templateId: number;
  /** timeId */
  timeId: number;
};

export type overViewLongitudinalResearchPerformanceLevelCollegeParams = {
  /** 纵向科研项目（校级以上）业绩大类id */
  categoryId?: number;
  /** 级别字段id */
  levelColumnId?: number;
  /** 年份 */
  year?: string;
  /** 判断当前年份字段id */
  yearColumnId?: number;
};

export type overViewProfessionalPieChartParams = {
  /** 学制字段id */
  academicColumnId: number;
  /** 大类ID */
  categoryId: number;
  /** 专业字段id */
  professionalColumnId: number;
};

export type overViewTeacherStudentFloatDataParams = {
  /** 教研室主任角色id */
  deptChargeRoleId: number;
  /** 该专业的部门id */
  deptId: number;
  /** 在校生大类id */
  studentCategoryId: number;
  /** 在校生大类专业 */
  studentMajor: string;
  /** 在校生大类专业字段id */
  studentMajorColumnId: number;
};

export type PageCmsCategoryRow_ = {
  countId?: string;
  current?: number;
  maxLimit?: number;
  optimizeCountSql?: boolean;
  orders?: OrderItem[];
  pages?: number;
  records?: CmsCategoryRow0[];
  searchCount?: boolean;
  size?: number;
  total?: number;
};

export type PageParam = {
  /** 页码，从 1 开始 */
  pageNum?: number;
  /** 每页条数，最大值为 100 */
  pageSize?: number;
};

export type PmsScore_ = {
  /** 审批状态（0-待审批，1-审批通过，2-审批拒绝） */
  approvalStatus?: number;
  /** 大类id */
  categoryId?: number;
  /** 大类行数据id */
  categoryRowId?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  deleted?: number;
  id?: number;
  /** 是否为最新的绩效得分数据（0否 1是） */
  isNewScore?: number;
  /** 用户id */
  personId?: number;
  /** 行数据总分 */
  rowScore?: number;
  /** 得分（经过计算后当前用户的得分） */
  score?: number;
  /** 组合分数名称 */
  scoreCombinationName?: string;
  /** 得分时间 */
  scoreDate?: string;
  /** 标签id */
  tagId?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type pmsScoreDeleteidParams = {
  /** id */
  id: number;
};

export type PmsScoreDTO = {
  personId?: number;
  personName?: string;
  totalScore?: number;
};

export type pmsScoreGetAllScoreListByYearPageParams = {
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
  /** year */
  year?: number;
};

export type pmsScoreGetAllScoreListByYearParams = {
  /** year */
  year?: number;
};

export type pmsScoreGetMyScoreTotalParams = {
  /** year */
  year: number;
};

export type pmsScoreGetScoreListByYearAndPersonIdParams = {
  /** personId */
  personId?: number;
  /** year */
  year?: number;
};

export type pmsScoreGetScoreListByYearParams = {
  /** year */
  year?: number;
};

export type pmsScoreGetScoreTotalByPersonIdParams = {
  /** personId */
  personId: number;
  /** year */
  year: number;
};

export type pmsScoreGetScoreTreeByYearAndPersonIdParams = {
  /** personId */
  personId?: number;
  /** year */
  year?: number;
};

export type pmsScoreGetScoreTreeByYearParams = {
  /** year */
  year?: number;
};

export type pmsScoreMajorGetAllScoreListByYearParams = {
  /** year */
  year: number;
};

export type pmsScoreMajorGetScoreTotalByPersonIdAndDeptParams = {
  /** personId */
  personId: number;
  /** year */
  year: number;
};

export type pmsScoreMajorGetScoreTreeByYearAndPersonIdParams = {
  /** personId */
  personId?: number;
  /** year */
  year?: number;
};

export type PmsScoreParam = {
  /** 审批状态（0-待审批，1-审批通过，2-审批拒绝） */
  approvalStatus: number;
  /** 大类id */
  categoryId: number;
  /** 大类行数据id */
  categoryRowId: number;
  id?: number;
  /** 用户id */
  personId: number;
  /** 行数据总分 */
  rowScore: number;
  /** 得分（经过计算后当前用户的得分） */
  score: number;
  /** 组合分数名称 */
  scoreCombinationName?: string;
  /** 标签id */
  tagId: number;
};

export type PmsScoreTotalDto = {
  name?: string;
  score?: number;
};

export type postDeleteidParams = {
  /** id */
  id: number;
};

export type postidParams = {
  /** id */
  id: number;
};

export type postListParams = {
  /** 岗位名称 */
  keyword?: string;
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
};

export type postUpdateStatusidParams = {
  /** id */
  id: number;
  /** status */
  status: number;
};

export type PublicItemParam = {
  /** 公共标识符号，1：项目负责人，2：项目名称，4：项目状态，5：级别，6：重要日期，7：参与人 */
  code?: string;
  /** 结束时间 */
  endTime?: string;
  /** 是否模糊搜索，默认模糊搜索 */
  isLike?: boolean;
  /** 开始时间 */
  startTime?: string;
  /** 查询值 */
  value?: string;
};

export type Resource = {
  description?: string;
  file?: string;
  filename?: string;
  inputStream?: InputStream;
  open?: boolean;
  readable?: boolean;
  uri?: string;
  url?: string;
};

export type RowImportErrorMessage = {
  /** 操作状态：0：检验成功，1：表头校验失败，2：数据项校验失败（有重复），3：校验失败，数据类型错误，缺少归属人，缺少项目名称，4：自定义值不存在，5：其他错误 */
  code?: number;
  /** 错误列表 */
  errorList?: ImportErrorInfo[];
  template?: CmsCategoryTemplate_;
  /** 数据项列表 */
  templateValueList?: CmsCategoryTemplateValue_[];
};

export type SearchProjectInitiationMaterialsParam = {
  /** 查询参数 */
  items?: PublicItemParam[];
  pageParam?: PageParam;
  /** 项目id */
  projectId?: number;
};

export type TmsAcceptRejectParam = {
  /** 邀请id */
  invitationId?: number;
  /** 邀请状态（2-接受、3-拒绝） */
  invitationStatus?: number;
};

export type TmsDeletePersonDto = {
  /** 项目成员id */
  memberId?: number;
  /** 项目团队id */
  projectId?: number;
};

export type TmsProject_ = {
  /** 创建时间 */
  createdAt?: string;
  id?: number;
  /** 是否删除（0-否、1-是） */
  isDeleted?: number;
  /** 项目负责人 */
  leaderId?: number;
  /** 项目简介 */
  projectBrief: string;
  /** 预计结项时间 */
  projectExpectedEndDate?: string;
  /** 立项时间 */
  projectInitiationDate: string;
  /** 项目名称 */
  projectName: string;
  /** 项目状态（1-申报、2-立项、3-结项、4-获奖） */
  projectStatus: number;
};

export type TmsProject0 = {
  /** 待加入成员列表 */
  beingProcessedList?: UmsPerson_[];
  /** 创建时间 */
  createdAt?: string;
  /** 管理员列表 */
  executorList?: UmsPerson_[];
  id?: number;
  /** 当前用户是否为管理员 */
  isAdmin?: boolean;
  /** 是否删除（0-否、1-是） */
  isDeleted?: number;
  /** 项目负责人 */
  leaderId?: number;
  /** 团队成员列表 */
  memberList?: UmsPerson_[];
  /** 项目简介 */
  projectBrief: string;
  /** 预计结项时间 */
  projectExpectedEndDate?: string;
  /** 立项时间 */
  projectInitiationDate: string;
  /** 项目负责人 */
  projectLeaderId: number;
  /** 项目负责人名称 */
  projectLeaderName: string;
  /** 项目名称 */
  projectName: string;
  /** 项目状态（1-申报、2-立项、3-结项、4-获奖） */
  projectStatus: number;
};

export type TmsProjectDto = {
  /** 项目负责人 */
  leaderId?: number;
  /** 项目简介 */
  projectBrief?: string;
  /** 预计结项时间 */
  projectExpectedEndDate?: string;
  /** 立项时间 */
  projectInitiationDate?: string;
  /** 项目名称 */
  projectName: string;
};

export type tmsProjectGetProjectListParams = {
  /** keyword */
  keyword?: string;
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
};

export type TmsProjectInvitationDto = {
  /** 其他相关信息 */
  info?: string;
  /** 参与人 */
  participant?: number;
  /** 项目ID */
  projectId?: number;
};

export type TmsProjectRequestDto = {
  /** 创建时间 */
  creationTime?: string;
  /** 数据类型（0- 邀请数据， 1-资料申请数据） */
  dataType?: number;
  /** 行id */
  groupId?: number;
  /** ID */
  id?: number;
  /** 其他相关信息 */
  info?: string;
  /** 发起人 */
  initiator?: number;
  /** 发起人姓名 */
  initiatorName?: string;
  /** 参与人 */
  participant?: number;
  /** 参与人姓名 */
  participantName?: string;
  /** 项目ID */
  projectId?: number;
  /** 项目名称 */
  projectName?: string;
  /** 申请状态（1-处理中、2-接受、3-拒绝） */
  status?: number;
  /** 资料类型：0资料，1成果 */
  type?: number;
  /** 最后更新时间 */
  updateTime?: string;
};

export type tmsProjectRequestGetRowNameParams = {
  /** id */
  id?: number;
};

export type tmsProjectRequestGetSharedListParams = {
  /** 搜索关键字 */
  keyword?: string;
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
  /** 状态（1-处理中、2-接受、3-拒绝） */
  status: number;
};

export type TmsProjectRequestParam = {
  /** 资料类型（0-资料、1-成果） */
  dataType?: number;
  /** 行id */
  groupId?: number;
  /** 其他相关信息 */
  info?: string;
  /** 参与人 */
  participant?: number;
  /** 项目ID */
  projectId?: number;
};

export type tmsProjectSelectProjectByIdParams = {
  /** 项目团队id */
  id: number;
};

export type TmsProjectSharedQueryParam = {
  /** 页码，从 1 开始 */
  pageNum?: number;
  /** 每页条数，最大值为 100 */
  pageSize?: number;
  /** 项目团队id */
  projectId?: number;
  /** 共享状态（0：未共享；1：已共享；2：待处理） */
  status?: number;
  /** 资料类型：0立项资料，1建设成果 */
  type?: number;
};

export type TmsProjectUpdateDto = {
  /** id */
  id: number;
  /** 项目负责人id */
  leaderId?: number;
  /** 项目简介 */
  projectBrief?: string;
  /** 预计结项时间 */
  projectExpectedEndDate?: string;
  /** 立项时间 */
  projectInitiationDate?: string;
  /** 项目名称 */
  projectName?: string;
};

export type TmsRequestAcceptParam = {
  /** id */
  id: number;
  /** 状态（1：同意，2：拒绝） */
  status: number;
};

export type TmsRevokeInviteDto = {
  /** 人员ID */
  personId?: number;
  /** 项目ID */
  projectId?: number;
};

export type TmsUpdateAdministratorDto = {
  administrators?: number[];
  projectId: number;
};

export type TodoResult = {
  /** 待办内容 */
  content?: string;
  /** 待办ID */
  id?: number;
  /** 是否代录入 */
  isProxy?: boolean;
  /** 待办标题 */
  title?: string;
  /** 待办类型 审核待办：0，录入待办-待补充：1，录入待办-审核退回：2，项目团队待办：3，新闻审核代办：4 */
  type?: number;
};

export type UmsAcademicRoles_ = {
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 聘任结束时间（YYYY-MM-DD） */
  endDate?: string;
  /** 主键 */
  id?: number;
  /** 组织级别 */
  orgLevel?: number;
  /** 学术/社会组织名称 */
  orgName?: string;
  /** 所属人员id */
  personId?: number;
  /** 所属人员名称 */
  personName?: string;
  /** 担任职务 */
  position?: string;
  /** 聘任开始时间（YYYY-MM-DD） */
  startDate?: string;
  /** 佐证材料（多文件id） */
  supportingMaterialsIds?: string;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type UmsAddAdminDto = {
  /** 教职工号 */
  employeeId?: string;
  /** 姓名 */
  employeeName?: string;
  /** 权限身份，角色id */
  roleId?: number;
};

export type UmsAdmin_ = {
  /** 创建时间 */
  createTime?: string;
  id?: number;
  /** 删除标志 */
  isDeleted?: number;
  /** 登录时间 */
  loginTime?: string;
  /** 密码 */
  password?: string;
  /** 权限身份 */
  roles?: UmsRole_[];
  /** 状态 */
  status?: number;
  /** 修改时间 */
  updateTime?: string;
  /** 姓名 */
  userNick?: string;
  /** 登录名（教职工号） */
  username?: string;
};

export type umsAdminDeleteByIdParams = {
  /** id */
  id: number;
};

export type umsAdminEnableOrDisAbleParams = {
  /** id */
  id: number;
};

export type UmsAdminLoginParam = {
  /** 登录方式 */
  loginType: number;
  /** 密码 */
  password: string;
  /** 用户名 */
  username: string;
};

export type umsAdminResetPasswordidParams = {
  /** id */
  id: number;
};

export type umsAdminRoleUpdateParams = {
  /** adminId */
  adminId: number;
  /** roleIds */
  roleIds: number[];
};

export type UmsAuthorityCard_ = {
  /** 描述 */
  description?: string;
  /** 主键 */
  id?: number;
  /** 卡名称 */
  name?: string;
  /** 排序 */
  sort?: number;
};

export type UmsCertificate_ = {
  /** 发证机构 */
  certificateAuthority?: string;
  /** 证书编号 */
  certificateNumber?: string;
  /** 证书类型（0：资格证书，1：职称，2：教师资格证） */
  certificateType?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 主键 */
  id?: number;
  /** 发证时间 */
  issueDate?: string;
  /** 种类 */
  kind?: string;
  /** 等级 */
  level?: number;
  /** 专业 */
  major?: string;
  /** 职业、岗位、资格种类 */
  name?: string;
  /** 所属人员 */
  personId?: number;
  /** 所属人员名称 */
  personName?: string;
  /** 职务 */
  post?: number;
  /** 佐证材料 */
  supportingMaterialsIds?: string;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type UmsDept_ = {
  /** 子部门 */
  children?: UmsDept_[];
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  deleted?: number;
  /** 排序 */
  deptSort?: number;
  /** ID */
  id?: number;
  /** 名称 */
  name?: string;
  /** 上级部门 */
  pid?: number;
  /** 状态0不启用1启用 */
  status?: number;
  /** 子部门数目 */
  subCount?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type umsDeptDeleteByIdParams = {
  /** id */
  id: number;
};

export type UmsMenu_ = {
  /** 子级菜单 */
  children?: UmsMenu_[];
  /** 组件路径 */
  component?: string;
  /** 创建时间 */
  createTime?: string;
  /** 前端隐藏 */
  hidden?: number;
  /** 前端图标 */
  icon?: string;
  id?: number;
  /** 菜单级数 */
  level?: number;
  /** 前端名称 */
  name?: string;
  /** 父级ID */
  parentId?: number;
  /** 前端路径 */
  path?: string;
  /** 菜单排序 */
  sort?: number;
  /** 菜单名称 */
  title?: string;
};

export type UmsMenu0 = {
  /** 组件路径 */
  component?: string;
  /** 创建时间 */
  createTime?: string;
  /** 前端隐藏 */
  hidden?: number;
  /** 前端图标 */
  icon?: string;
  id?: number;
  /** 菜单级数 */
  level?: number;
  /** 前端名称 */
  name?: string;
  /** 父级ID */
  parentId?: number;
  /** 前端路径 */
  path?: string;
  /** 菜单排序 */
  sort?: number;
  /** 菜单名称 */
  title?: string;
};

export type UmsMenu1 = {
  /** 组件路径 */
  component?: string;
  /** 创建时间 */
  createTime?: string;
  /** 前端隐藏 */
  hidden?: number;
  /** 前端图标 */
  icon?: string;
  id?: number;
  /** 菜单级数 */
  level?: number;
  /** 前端名称 */
  name?: string;
  /** 父级ID */
  parentId?: number;
  /** 前端路径 */
  path?: string;
  /** 菜单排序 */
  sort?: number;
  /** 菜单名称 */
  title?: string;
};

export type umsMenuDeleteidParams = {
  /** id */
  id: number;
};

export type umsMenuidParams = {
  /** id */
  id: number;
};

export type umsMenuListparentIdParams = {
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
  /** parentId */
  parentId: number;
};

export type umsMenuUpdateHiddenidParams = {
  /** hidden */
  hidden: number;
  /** id */
  id: number;
};

export type umsMenuUpdateidParams = {
  /** id */
  id: number;
};

export type UmsPerson_ = {
  /** 来校时间 */
  arriveDate?: string;
  /** 头像 */
  avatar?: string;
  /** 生活照 */
  avatarLife?: string;
  /** 出生日期 */
  birthDate?: string;
  /** 出生地 */
  birthPlace?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 学位名称 */
  degreeName?: string;
  /** 学位院校 */
  degreeSchool?: string;
  /** 描述 */
  description?: string;
  /** 是否双师佐证材料 */
  dualTeacherSupporting?: string;
  /** 教职工号 */
  employeeId?: string;
  /** 姓名 */
  employeeName?: string;
  /** 员工类型 */
  employeeType?: number;
  /** 民族 */
  ethnicity?: number;
  /** 性别 */
  gender?: number;
  /** 毕业院校 */
  graduationSchool?: string;
  /** 所学专业 */
  graduationSchoolMajor?: string;
  /** 最高学位 */
  highestDegree?: number;
  /** 最高学历 */
  highestEducation?: number;
  /** 学位所学专业 */
  highestMajor?: string;
  /** 主键 */
  id?: number;
  /** 删除 */
  isDeleted?: number;
  /** 是否双师 */
  isDualTeacher?: number;
  /** 所在党支部 */
  partyBranch?: number;
  /** 党内职务 */
  partyPosition?: string;
  /** 党内职务结束时间 */
  partyPositionEndTime?: string;
  /** 党内职务开始时间 */
  partyPositionStartTime?: string;
  /** 政治面貌 */
  political?: number;
  /** 岗位名称 */
  position?: string;
  /** 专业技能 */
  professional?: string;
  /** 状态：0禁用 1启用 */
  status?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type UmsPerson0Req = {
  /** 来校时间 */
  arriveDate?: string;
  /** 头像 */
  avatar?: string;
  /** 生活照 */
  avatarLife?: string;
  /** 出生日期 */
  birthDate?: string;
  /** 出生地 */
  birthPlace?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 学位名称 */
  degreeName?: string;
  /** 学位院校 */
  degreeSchool?: string;
  /** 部门ids */
  deptIds?: number[];
  /** 描述 */
  description?: string;
  /** 是否双师佐证材料 */
  dualTeacherSupporting?: string;
  /** 教职工号 */
  employeeId?: string;
  /** 姓名 */
  employeeName?: string;
  /** 员工类型 */
  employeeType?: number;
  /** 民族 */
  ethnicity?: number;
  /** 性别 */
  gender?: number;
  /** 毕业院校 */
  graduationSchool?: string;
  /** 所学专业 */
  graduationSchoolMajor?: string;
  /** 最高学位 */
  highestDegree?: number;
  /** 最高学历 */
  highestEducation?: number;
  /** 学位所学专业 */
  highestMajor?: string;
  /** 主键 */
  id?: number;
  /** 删除 */
  isDeleted?: number;
  /** 是否双师 */
  isDualTeacher?: number;
  /** 所在党支部 */
  partyBranch?: number;
  /** 党内职务 */
  partyPosition?: string;
  /** 党内职务结束时间 */
  partyPositionEndTime?: string;
  /** 党内职务开始时间 */
  partyPositionStartTime?: string;
  /** 政治面貌 */
  political?: number;
  /** 岗位名称 */
  position?: string;
  /** 专业技能 */
  professional?: string;
  /** 状态：0禁用 1启用 */
  status?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type UmsPerson1Res = {
  /** 拥有学术/社会组织兼职列表 */
  academicRolesList?: UmsAcademicRoles_[];
  /** 来校时间 */
  arriveDate?: string;
  /** 头像 */
  avatar?: string;
  /** 生活照 */
  avatarLife?: string;
  /** 出生日期 */
  birthDate?: string;
  /** 出生地 */
  birthPlace?: string;
  /** 拥有证书列表 */
  certificateList?: UmsCertificate_[];
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 学位名称 */
  degreeName?: string;
  /** 学位院校 */
  degreeSchool?: string;
  /** 部门 */
  deptId?: number;
  /** 描述 */
  description?: string;
  /** 是否双师佐证材料 */
  dualTeacherSupporting?: string;
  /** 教职工号 */
  employeeId?: string;
  /** 姓名 */
  employeeName?: string;
  /** 员工类型 */
  employeeType?: number;
  /** 民族 */
  ethnicity?: number;
  /** 性别 */
  gender?: number;
  /** 毕业院校 */
  graduationSchool?: string;
  /** 所学专业 */
  graduationSchoolMajor?: string;
  /** 最高学位 */
  highestDegree?: number;
  /** 最高学历 */
  highestEducation?: number;
  /** 学位所学专业 */
  highestMajor?: string;
  /** 主键 */
  id?: number;
  /** 删除 */
  isDeleted?: number;
  /** 是否双师 */
  isDualTeacher?: number;
  /** 所在党支部 */
  partyBranch?: number;
  /** 党内职务 */
  partyPosition?: string;
  /** 党内职务结束时间 */
  partyPositionEndTime?: string;
  /** 党内职务开始时间 */
  partyPositionStartTime?: string;
  /** 政治面貌 */
  political?: number;
  /** 拥有职称列表 */
  portList?: UmsCertificate_[];
  /** 岗位名称 */
  position?: string;
  /** 专业技能 */
  professional?: string;
  /** 状态：0禁用 1启用 */
  status?: number;
  /** 拥有教师资格证书列表 */
  teacherList?: UmsCertificate_[];
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type umsPersonAuditListParams = {
  /** cards */
  cards: number[];
};

export type umsPersonDeleteAcademicRolesParams = {
  /** id */
  id: number;
};

export type umsPersonDeleteCertificatesParams = {
  /** id */
  id: number;
};

export type umsPersonDeletePersonDeptByIdParams = {
  /** deptId */
  deptId: number;
  /** personId */
  personId: number;
};

export type umsPersonDetailParams = {
  /** id */
  id: number;
};

export type umsPersonListByDeptIdsParams = {
  /** deptIds */
  deptIds: number[];
};

export type umsPersonListByRoleIdsParams = {
  /** roleIds */
  roleIds: number[];
};

export type umsPersonListPersonParams = {
  /** pageNum */
  pageNum?: number;
  /** pageSize */
  pageSize?: number;
};

export type UmsPersonSensitive_ = {
  /** 主键 */
  id?: number;
  /** 身份证号 */
  idCard?: string;
  /** 身份证国徽面 */
  idCardBack?: string;
  /** 身份证头像面 */
  idCardFront?: string;
  /** 用户id */
  personId?: number;
};

export type umsPersonSensitiveDetailsParams = {
  /** id */
  id: number;
};

export type UmsPersonUpdateParam = {
  /** 来校时间 */
  arriveDate?: string;
  /** 头像 */
  avatar?: string;
  /** 生活照 */
  avatarLife?: string;
  /** 出生日期 */
  birthDate?: string;
  /** 出生地 */
  birthPlace?: string;
  /** 学位名称 */
  degreeName?: string;
  /** 学位院校 */
  degreeSchool?: string;
  /** 所属部门 */
  deptId?: number;
  /** 描述 */
  description?: string;
  /** 是否双师佐证材料 */
  dualTeacherSupporting?: string;
  /** 姓名 */
  employeeName?: string;
  /** 员工类型 */
  employeeType?: number;
  /** 民族 */
  ethnicity?: number;
  /** 性别 */
  gender?: number;
  /** 毕业院校 */
  graduationSchool?: string;
  /** 所学专业 */
  graduationSchoolMajor?: string;
  /** 最高学位 */
  highestDegree?: number;
  /** 最高学历 */
  highestEducation?: number;
  /** 学位所学专业 */
  highestMajor?: string;
  /** 用户id */
  id?: number;
  /** 身份证号 */
  idCard?: string;
  /** 是否双师 */
  isDualTeacher?: number;
  /** 政治面貌 */
  political?: number;
  /** 岗位名称 */
  position?: string;
  /** 专业技能 */
  professional?: string;
};

export type UmsPost_ = {
  /** 岗位编码 */
  code?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  id?: number;
  /** 岗位名 */
  name?: string;
  /** 排序 */
  sort?: number;
  /** 状态 */
  status?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type UmsPostParam = {
  /** 岗位编码 */
  code?: string;
  /** 岗位ID */
  id?: number;
  /** 岗位名称 */
  name?: string;
  /** 显示顺序 */
  sort?: number;
  /** 状态：0->禁用；1->启用 */
  status?: number;
};

export type UmsResource_ = {
  /** 资源分类ID */
  categoryId?: number;
  /** 创建时间 */
  createTime?: string;
  /** 描述 */
  description?: string;
  id?: number;
  /** 资源名称 */
  name?: string;
  /** 资源URL */
  url?: string;
};

export type UmsResourceCategory_ = {
  /** 创建时间 */
  createTime?: string;
  id?: number;
  /** 分类名称 */
  name?: string;
  /** 排序 */
  sort?: number;
};

export type umsResourceCategoryDeleteidParams = {
  /** id */
  id: number;
};

export type umsResourceCategoryUpdateidParams = {
  /** id */
  id: number;
};

export type umsResourceDeleteidParams = {
  /** id */
  id: number;
};

export type umsResourceidParams = {
  /** id */
  id: number;
};

export type umsResourceListParams = {
  /** categoryId */
  categoryId?: number;
  /** nameKeyword */
  nameKeyword?: string;
  /** 页码，从 1 开始 */
  pageNum: number;
  /** 每页条数，最大值为 100 */
  pageSize: number;
  /** urlKeyword */
  urlKeyword?: string;
};

export type umsResourceUpdateidParams = {
  /** id */
  id: number;
};

export type UmsRole_ = {
  authorityCardList?: UmsAuthorityCard_[];
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 描述 */
  description?: string;
  /** 主键 */
  id?: number;
  /** 是否删除 */
  isDeleted?: number;
  /** 角色名称 */
  name?: string;
  /** 角色层级 */
  stair?: number;
  /** 状态（0：禁用，1：启用） */
  status?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type UmsRole0 = {
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 描述 */
  description?: string;
  /** 主键 */
  id?: number;
  /** 是否删除 */
  isDeleted?: number;
  /** 角色名称 */
  name?: string;
  /** 角色层级 */
  stair?: number;
  /** 状态（0：禁用，1：启用） */
  status?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type UmsRole1 = {
  /** 创建时间 */
  createdAt?: string;
  /** 创建人 */
  createdBy?: number;
  /** 描述 */
  description?: string;
  /** 主键 */
  id?: number;
  /** 是否删除 */
  isDeleted?: number;
  /** 角色名称 */
  name?: string;
  /** 角色层级 */
  stair?: number;
  /** 状态（0：禁用，1：启用） */
  status?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 更新人 */
  updatedBy?: number;
};

export type umsRoleAllocCardParams = {
  /** cardIds */
  cardIds?: number[];
  /** roleId */
  roleId: number;
};

export type umsRoleDeleteParams = {
  /** id */
  id: number;
};

export type umsRoleEnableOrDisableParams = {
  /** id */
  id: number;
};

export type umsRoleGetByIdParams = {
  /** id */
  id: number;
};

export type umsRoleGetByIdsParams = {
  /** ids */
  ids: number[];
};

export type UmsUpdateAdminDto = {
  /** 姓名 */
  employeeName?: string;
  id?: number;
  /** 权限身份，角色id */
  roleId?: number;
};

export type UpdateAdminPasswordParam = {
  /** 新密码 */
  newPassword: string;
  /** 旧密码 */
  oldPassword: string;
};
