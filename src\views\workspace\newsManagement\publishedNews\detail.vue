<template>
    <div v-if="selectedItem" class="detail-content">
        <div class="content">
            <div class="news-detail" v-if="selectedItem">
                <div class="news-header">
                    <h1 class="news-title">{{ selectedItem.title }}</h1>
                    <div class="news-meta">
                        <span>发布时间：{{ formatDate(selectedItem.publishedAt) }}</span>
                        <span>作者：{{ selectedItem.authorName || 0 }}</span>
                    </div>
                </div>
                <div class="news-content" v-html="selectedItem.content"></div>
                <div class="delete-btn">
                    <el-button type="danger" plain @click="handleDelete">删除</el-button>
                </div>
            </div>
        </div>
    </div>
    <div v-else class="detail-content">
        <el-empty description="请从左侧选择一个新闻草稿查看详情。" />
    </div>
</template>

<script setup lang="ts">
import "@wangeditor/editor/dist/css/style.css";
import { NmsNews_ } from "@/apis/types";
import { nmsNewsDeletePublish } from "@/apis/nmsNewsController";
import { ElMessage, ElMessageBox } from "element-plus";

const emit = defineEmits(["refresh"]);

// 选中的项目
const { selectedItem, detailLoading } = defineProps<{ selectedItem: NmsNews_; detailLoading: boolean }>();

function formatDate(dateString: string | null) {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
}

// 删除
const handleDelete = () => {
    // 弹窗确认是否删除
    ElMessageBox.confirm("确定要删除该新闻吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    }).then(() => {
        nmsNewsDeletePublish({
            body: {
                id: selectedItem.id,
            },
        }).then((res) => {
            if (res.code === 200) {
                ElMessage.success("删除成功");
                emit("refresh");
            } else {
                ElMessage.error("删除失败");
            }
        });
    });
};
</script>

<style scoped lang="scss">
@use "../../../../assets/styles/mixins.scss" as mix;

.bold {
    font-weight: bold;
}
.pagination {
    @extend .flex-end;
    margin-top: 10px;
    margin-right: 20px;
}

.flex-end {
    display: flex;
    justify-content: end;
}

.edit-box {
    margin-top: 10px;
    border: 1px solid #bec5d7;
    border-radius: 10px;
    .meta-info {
        background-color: #fff;
        border-bottom: 1px solid #bec5d7;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        gap: 10px;
        padding: 20px 10px;
    }
}

.detail-content {
    padding: 20px;

    .content {
        background: #fff;
        padding: 20px 40px;
        min-height: 60vh;
        height: calc(100vh - 305px);
        overflow-y: auto;

        .delete-btn {
            display: flex;
            justify-content: end;
        }

        .news-detail {
            .news-header {
                border-bottom: 1px solid #d2dbec;
                padding-bottom: 15px;
                margin-bottom: 20px;

                .news-title {
                    font-size: 24px;
                    font-weight: 700;
                    color: #23346d;
                    margin-bottom: 15px;
                    letter-spacing: 0.1em;
                    text-align: center;
                }

                .news-meta {
                    display: flex;
                    justify-content: center;
                    color: #6c7cb2;
                    font-size: 14px;
                    gap: 30px;
                }
            }

            .news-content {
                padding: 20px 0;
                color: #23346d;
                font-size: 16px;
                line-height: 1.8;
                letter-spacing: 0.05em;
                min-height: 300px;

                :deep(img) {
                    max-width: 100%;
                    height: auto;
                    margin: 10px 0;
                }

                :deep(p) {
                    margin-bottom: 15px;
                }
            }
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 400px;
        }
    }
}
</style>
