/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 根据行id获取占比 GET /cms/cmsTagProportion/getProportionByRowId */
export async function cmsTagProportionGetProportionByRowId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsTagProportionGetProportionByRowIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsTagProportion_>(
    '/cms/cmsTagProportion/getProportionByRowId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}
