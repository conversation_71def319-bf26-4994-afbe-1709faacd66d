/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 新增组织团队荣誉 POST /oms/omsOrganizationHonor/create */
export async function omsOrganizationHonorCreate({
  body,
  options,
}: {
  body: API.OmsOrganizationHonorDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/oms/omsOrganizationHonor/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除组织团队荣誉 POST /oms/omsOrganizationHonor/delete/${param0} */
export async function omsOrganizationHonorDeleteId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.omsOrganizationHonorDeleteidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultVoid_>(
    `/oms/omsOrganizationHonor/delete/${param0}`,
    {
      method: 'POST',
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}

/** 分页查询组织团队荣誉 GET /oms/omsOrganizationHonor/list */
export async function omsOrganizationHonorList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.omsOrganizationHonorListParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageOmsOrganizationHonor_>(
    '/oms/omsOrganizationHonor/list',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 修改组织团队荣誉 POST /oms/omsOrganizationHonor/update/${param0} */
export async function omsOrganizationHonorUpdateId({
  params,
  body,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.omsOrganizationHonorUpdateidParams;
  body: API.OmsOrganizationHonorUpdateDto;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultVoid_>(
    `/oms/omsOrganizationHonor/update/${param0}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      params: { ...queryParams },
      data: body,
      ...(options || {}),
    }
  );
}
