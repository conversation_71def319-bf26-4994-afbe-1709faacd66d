import type { CmsTagNode_ } from '@/apis/types';

export interface PerformanceRulesDialogOptions {
    performanceRulesList: CmsTagNode_[];
    title?: string;
    emptyText?: string;
    showChangeRulesButton?: boolean;
    changeRulesButtonText?: string;
    changeRulesPath?: string;
    showCloseButton?: boolean;
    closeButtonText?: string;
    closeOnClickModal?: boolean;
    closeOnPressEscape?: boolean;
    onChangeRules?: () => void | Promise<void>;
    onClose?: () => void | Promise<void>;
}

export interface PerformanceRulesDialogInstance {
    close: () => void;
    changeRules: () => void;
}

export type PerformanceRulesDialogAction = "change-rules" | "close"; 