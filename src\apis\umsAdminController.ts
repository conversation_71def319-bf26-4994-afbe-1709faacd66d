/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 添加人员并注册 POST /ums/umsAdmin/create */
export async function umsAdminCreate({
  body,
  options,
}: {
  body: API.UmsAddAdminDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsAdmin/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除人员（软删） POST /ums/umsAdmin/deleteById */
export async function umsAdminDeleteById({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsAdminDeleteByIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsAdmin/deleteById', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 启用/禁用 POST /ums/umsAdmin/enableOrDisAble */
export async function umsAdminEnableOrDisAble({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsAdminEnableOrDisAbleParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsAdmin/enableOrDisAble', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取当前登录用户信息 GET /ums/umsAdmin/info */
export async function umsAdminInfo({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultObject_>('/ums/umsAdmin/info', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取所有账户 GET /ums/umsAdmin/list */
export async function umsAdminList({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListUmsAdmin_>('/ums/umsAdmin/list', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 登录以后返回token POST /ums/umsAdmin/login */
export async function umsAdminLogin({
  body,
  options,
}: {
  body: API.UmsAdminLoginParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultObject_>('/ums/umsAdmin/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 登出功能 POST /ums/umsAdmin/logout */
export async function umsAdminLogout({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsAdmin/logout', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 获取当前用户的菜单树 GET /ums/umsAdmin/menuTree */
export async function umsAdminMenuTree({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListUmsMenu_>('/ums/umsAdmin/menuTree', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 重置指定用户密码 POST /ums/umsAdmin/resetPassword/${param0} */
export async function umsAdminResetPasswordId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsAdminResetPasswordidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultVoid_>(
    `/ums/umsAdmin/resetPassword/${param0}`,
    {
      method: 'POST',
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}

/** 给用户分配角色 POST /ums/umsAdmin/role/update */
export async function umsAdminRoleUpdate({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsAdminRoleUpdateParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultObject_>('/ums/umsAdmin/role/update', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改账号信息 POST /ums/umsAdmin/update */
export async function umsAdminUpdate({
  body,
  options,
}: {
  body: API.UmsUpdateAdminDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsAdmin/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改用户密码 POST /ums/umsAdmin/updatePassword */
export async function umsAdminUpdatePassword({
  body,
  options,
}: {
  body: API.UpdateAdminPasswordParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsAdmin/updatePassword', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
