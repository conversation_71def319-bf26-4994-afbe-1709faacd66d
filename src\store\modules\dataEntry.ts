import { defineStore } from "pinia";
import { watch } from "vue";

export const useDataEntryStore = defineStore("dataEntry", {
    state: () => ({
        isEnteringData: false,
    }),
    actions: {
        /**
         * 设置是否正在录入数据
         * @param status 
         */
        setEnteringData(status: boolean) {
            this.isEnteringData = status;
        },

        /**
         * 处理页面关闭事件
         * @param e BeforeUnloadEvent
         */
        handleBeforeUnload(e: BeforeUnloadEvent) {
            // 设置提示信息
            const message = '您正在输入数据，关闭页面将丢失所有未保存的内容！';
            e.preventDefault();
            // Chrome需要同时设置returnValue和return值
            e.returnValue = message;
            return message;
        },
    },
});

// 监听isEnteringData的变化
export const setupDataEntryWatcher = () => {
    const store = useDataEntryStore();
    watch(
        () => store.isEnteringData,
        (newValue) => {
            if (newValue) {
                window.addEventListener('beforeunload', store.handleBeforeUnload);
            } else {
                window.removeEventListener('beforeunload', store.handleBeforeUnload);
            }
        }
    );
};
