# DataTable 数据表格组件

这是一个功能强大的数据表格组件，支持分页加载、标签页模式、无限滚动、本地搜索和远程搜索等特性。组件设计灵活，可用于展示列表数据和详情的布局场景。

## 功能特点

- 支持分页加载和无限滚动
- 支持标签页模式展示多个数据列表
- 支持本地搜索和远程搜索
- 自适应高度计算
- 支持全量数据加载模式
- 响应式布局设计
- 自定义内容插槽
- 选中项高亮显示

## 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| loadMoreFunc | Function | () => {} | 加载更多数据的函数，需返回Promise |
| isLoadFirst | Boolean | true | 是否在初始化时自动加载第一页数据 |
| searchKeyword | String | '' | 搜索关键字 |
| useTabStyle | Boolean | false | 是否使用标签页模式 |
| tabConfig | Array | [] | 标签页配置数组 |
| showAll | Boolean | false | 是否一次性加载所有数据 |
| getAllDataFunc | Function | null | 获取全部数据的函数 |
| isLocalSearch | Boolean | false | 是否使用本地搜索 |
| searchFields | Array | [] | 本地搜索时的搜索字段数组 |

### tabConfig 配置项

```typescript
interface TabConfig {
    key: string;      // 标签页唯一标识
    label: string;    // 标签页显示文本
    loadMoreFunc: (current: number, size: number, searchKeyword?: string) => Promise<any>; // 加载数据的函数
}
```

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| itemClick | item: any | 点击列表项时触发 |
| update:modelValue | tabKey: string | 标签页切换时触发（仅在useTabStyle为true时） |

## 插槽

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| title | { item: any } | 列表项标题内容 |
| desc | { item: any } | 列表项描述内容 |
| right | { item: any } | 列表项右侧内容 |
| detail | { selectedItem: any } | 详情面板内容 |

## 暴露的方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| refreshAll | (tabKey?: string) | 刷新数据列表 |
| simulateClick | (index: number) | 模拟点击指定索引的列表项 |
| search | - | 触发搜索操作 |

## 基础用法

```vue
<template>
  <DataTable
    :loadMoreFunc="loadData"
    :isLoadFirst="true"
    :searchKeyword="keyword"
    @itemClick="handleItemClick"
  >
    <template #title="{ item }">
      {{ item.title }}
    </template>
    <template #desc="{ item }">
      {{ item.description }}
    </template>
    <template #detail="{ selectedItem }">
      <div v-if="selectedItem">
        <!-- 详情内容 -->
      </div>
    </template>
  </DataTable>
</template>

<script setup lang="ts">
const loadData = async (current: number, size: number, keyword?: string) => {
  // 实现数据加载逻辑
  return {
    data: {
      records: [],
      current: 1,
      size: 10,
      total: 0
    }
  }
}

const handleItemClick = (item: any) => {
  // 处理项目点击
}
</script>
```

## 标签页模式用法

```vue
<template>
  <DataTable
    :useTabStyle="true"
    :tabConfig="tabConfig"
    :isLoadFirst="true"
    :searchKeyword="keyword"
    @itemClick="handleItemClick"
  >
    <!-- 插槽内容同基础用法 -->
  </DataTable>
</template>

<script setup lang="ts">
const tabConfig = [
  {
    key: 'tab1',
    label: '标签1',
    loadMoreFunc: async (current, size, keyword) => {
      // 实现数据加载逻辑
    }
  },
  {
    key: 'tab2',
    label: '标签2',
    loadMoreFunc: async (current, size, keyword) => {
      // 实现数据加载逻辑
    }
  }
]
</script>
```

## 本地搜索模式

```vue
<template>
  <DataTable
    :loadMoreFunc="loadData"
    :isLocalSearch="true"
    :searchFields="['title', 'description']"
    :searchKeyword="keyword"
  >
    <!-- 插槽内容 -->
  </DataTable>
</template>
```

## 样式定制

组件使用 SCSS 进行样式定义，主要样式变量包括：

```scss
$titleColor: #23346d;      // 标题颜色
$titleHeight: 55px;        // 标题高度
$itemPadding: 20px;       // 列表项内边距
$ListWidth: calc(25% - 5px);    // 列表宽度
$detailWidth: calc(75% - 5px);  // 详情面板宽度
```

## 注意事项

1. 组件会自动计算高度以适应视口，确保父容器具有明确的高度。
2. 在使用远程搜索时，需要在 `loadMoreFunc` 中处理搜索逻辑。
3. 本地搜索模式下，确保提供正确的 `searchFields` 配置。
4. 标签页模式下，每个标签页的数据是独立管理的。
5. 组件的最小宽度限制：列表区域 350px，详情区域 900px。 