# CustomDrawer 使用示例

## 替换前（原有代码）

```vue
<template>
  <!-- 基本信息抽屉 -->
  <el-drawer v-model="basicInfoDialogVisible" :show-close="false" :close-on-click-modal="false">
    <template #header="{ close }">
      <div class="custom-drawer-header-container">
        <div class="drawer-title-icon" @click="close">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
            <!-- 长篇的SVG关闭图标代码 -->
          </svg>
        </div>
        <div class="drawer-title">编辑基本信息</div>
        <div>
          <el-button @click="basicInfoDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitBasicInfoForm(basicInfoFormRef)">确定</el-button>
        </div>
      </div>
    </template>
    <div class="custom-drawer-content">
      <el-form
        :model="basicInfoForm"
        :rules="basicInfoRules"
        ref="basicInfoFormRef"
        label-width="120px"
        label-position="top">
        <!-- 表单内容 -->
      </el-form>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
// 大量的逻辑代码...
const basicInfoDialogVisible = ref(false)

const submitBasicInfoForm = (formRef) => {
  // 表单提交逻辑...
}
</script>
```

## 替换后（使用CustomDrawer）

### 方法1: 基础用法

```vue
<template>
  <!-- 基本信息抽屉 -->
  <CustomDrawer 
    v-model="basicInfoDialogVisible" 
    title="编辑基本信息"
    @confirm="submitBasicInfoForm(basicInfoFormRef)">
    
    <el-form
      :model="basicInfoForm"
      :rules="basicInfoRules"
      ref="basicInfoFormRef"
      label-width="120px"
      label-position="top">
      <!-- 表单内容 -->
    </el-form>
  </CustomDrawer>
</template>

<script setup lang="ts">
import CustomDrawer from '@/components/CustomDrawer'

const basicInfoDialogVisible = ref(false)

const submitBasicInfoForm = (formRef) => {
  // 表单提交逻辑...
}
</script>
```

### 方法2: 使用 Composable (推荐)

```vue
<template>
  <!-- 基本信息抽屉 -->
  <CustomDrawer 
    v-model="basicInfoDrawer.visible.value"
    v-bind="basicInfoDrawer.drawerProps"
    @confirm="basicInfoDrawer.confirm"
    @close="basicInfoDrawer.close">
    
    <el-form
      :model="basicInfoForm"
      :rules="basicInfoRules"
      ref="basicInfoFormRef"
      label-width="120px"
      label-position="top">
      <!-- 表单内容 -->
    </el-form>
  </CustomDrawer>
</template>

<script setup lang="ts">
import CustomDrawer from '@/components/CustomDrawer'
import { useCustomDrawer } from '@/components/CustomDrawer/useCustomDrawer'

// 创建抽屉实例
const basicInfoDrawer = useCustomDrawer({
  title: '编辑基本信息',
  onConfirm: async () => {
    // 表单验证和提交
    if (basicInfoFormRef.value) {
      await basicInfoFormRef.value.validate()
      await submitBasicInfoForm()
      // 如果提交成功，抽屉会自动关闭
      // 如果提交失败，抛出错误，抽屉保持打开状态
    }
  }
})

const submitBasicInfoForm = async () => {
  // 表单提交逻辑...
  try {
    // API调用
    await updateBasicInfo(basicInfoForm)
    // 可以在这里添加成功提示
    ElMessage.success('保存成功')
  } catch (error) {
    // 错误会被抛出，抽屉不会关闭
    throw error
  }
}

// 打开抽屉的方法
const openBasicInfoDrawer = () => {
  basicInfoDrawer.open()
}
</script>
```


## 优势总结

1. **代码简洁**: 大幅减少模板代码
2. **统一性**: 所有抽屉使用相同的视觉样式和交互逻辑  
3. **可维护性**: 样式和行为集中管理，易于维护
4. **类型安全**: 完整的TypeScript支持
5. **灵活性**: 丰富的插槽和配置选项
6. **错误处理**: 内置的异步操作错误处理机制

## 批量替换建议

如果项目中有多个类似的抽屉，建议按以下步骤进行替换：

1. 搜索项目中所有使用 `custom-drawer-header-container` 的文件
2. 逐一替换为 CustomDrawer 组件
3. 提取公共的确认逻辑到 composable 中
4. 统一错误处理和用户反馈机制 