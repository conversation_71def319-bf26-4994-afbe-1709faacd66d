<template>
    <div class="data-base-container">
        <div class="data-base-header">
            {{ checkFailInfoStore.checkFailInfo == null ? "资料库" : "重复数据详情" }}
        </div>
        
        <!-- 进入详情的时候隐藏滚动条，交由子组件控制滚动条 -->
        <div
            class="data-base-content"
            ref="tableContainerRef"
            :style="{ overflowY: isShowDetail || checkFailInfoStore.checkFailInfo ? 'hidden' : 'auto', height: isShowDetail || checkFailInfoStore.checkFailInfo ? 'auto' : '100%' }">
            
            <Transition name="fade" mode="out-in">
                <!-- 重复数据展示 -->
                <DuplicateDataView
                    v-if="checkFailInfoStore.checkFailInfo"
                    key="duplicate-data"
                    :check-fail-info="checkFailInfoStore.checkFailInfo"
                    :duplicate-import-loading="duplicateImportLoading"
                    :show-more-buttons="showMoreButtons"
                    :error-item-states="errorItemStates"
                    :all-person-list-with-disabled="allPersonListWithDisabled"
                    :dict-list="dictList"
                    :all-dependent-majors="allDependentMajors"
                    :is-from-audit="!!$router.currentRoute.value.query.rowId"
                    :has-backend-permission="hasBackendPermission"
                    :has-admin-entry-permission="hasAdminEntryPermission"
                    @toggle-error-item="toggleErrorItem"
                    @clear-fail-info="handleClearFailInfo"
                    @continue-import="handleContinueImport"
                    @back-to-audit="backToAudit" />
                
                <!-- 数据详情查看 -->
                <DataDetailView
                    v-else-if="isShowDetail"
                    key="detail"
                    :selected-item="selectedItem"
                    :selected-table-detail="selectedTableDetail"
                    :field-list="fieldList"
                    :record-list="recordList"
                    :detail-and-score="detailAndScore"
                    :all-person-list-with-disabled="allPersonListWithDisabled"
                    :all-dependent-majors="allDependentMajors"
                    :has-backend-permission="hasBackendPermission"
                    :has-admin-entry-permission="hasAdminEntryPermission"
                    @close-detail="isShowDetail = false" />
                
                <!-- 主列表视图 -->
                <div v-else class="con-box" key="list">
                    <!-- 数据表选择器 -->
                    <DataTableSelector
                        v-model:selected-table="selectedTable"
                        v-model:selected-batch="selectedBatch"
                        v-model:is-export="isExport"
                        :selected-table-detail="selectedTableDetail"
                        :available-batches="availableBatches"
                        :table-options="tableOptions"
                        :is-show-delete="isShowDelete"
                        :table-data="tableData"
                        :multiple-selection="multipleSelection"
                        @table-change="handleTableChange"
                        @delete-batch="deleteBatch"
                        @export-start="switchExport"
                        @export-cancel="switchExport"
                        @export-selected="exportSelected" />
                    
                    <!-- 主数据表格 -->
                    <DataTableList
                        ref="dataTableListRef"
                        :loading="loading"
                        :table-data="tableData"
                        :field-list="fieldList"
                        :selected-table-detail="selectedTableDetail"
                        :available-batches="availableBatches"
                        :is-export="isExport"
                        :has-backend-permission="hasBackendPermission"
                        :all-person-list-with-disabled="allPersonListWithDisabled"
                        :dict-list="dictList"
                        :all-dependent-majors="allDependentMajors"
                        :filter-configs="filterConfigs"
                        @selection-change="handleSelectionChange"
                        @sort-change="sortChange"
                        @table-scroll="throttledHandleTableScroll"
                        @download-material="handleDownload"
                        @view-detail="handleDetail"
                        @toggle-selection="toggleSelection"
                        @clear-selection="clearSelection" />
                </div>
            </Transition>
            
            <!-- 分页组件 -->
            <Transition name="pagination">
                <div class="pagination-container" v-show="showPagination && !isShowDetail">
                    <el-pagination
                        :current-page="pagination.currentPage"
                        :page-size="pagination.pageSize"
                        layout=" prev, pager, next, jumper"
                        :total="pagination.total"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange" />
                </div>
            </Transition>
        </div>
        
        <!-- 文件下载弹窗 -->
        <FileDownloadDialog v-model="fileDownloadDialogVisible" :file-list-string="fileListString" />
    </div>
</template>

<script setup lang="ts">
// 导入API
import {
    cmsCategoryRowDeleteByBatchNumber,
    cmsCategoryRowExportRowInfoSingle,
    cmsCategoryRowGetAllBatchNumber,
    cmsCategoryRowGetRepeat,
    cmsCategoryRowImportPerformanceFile,
    cmsCategoryRowPageByCategoryId,
} from "@/apis/cmsCategoryRowController";
import { cmsCategoryTemplateValueGetValueByTemplateId } from "@/apis/cmsCategoryTemplateValueController";
import { cmsTagConfigGetDetailAndScoreDataId } from "@/apis/cmsTagConfigController";
import { cmsCheckGetRecord } from "@/apis/cmsCheckController";

// 导入类型
import { CmsCategoryTemplate_, CmsCategoryTemplateValue10, CmsCheck_, CmsTagDetailAndScoreDto } from "@/apis/types";
import { categoryTemplateType } from "@/enums/categoryTemplate/categoryTemplateType";
import type { TableInstance } from "element-plus";

// 导入组件
import DataTableSelector from "./components/DataTableSelector.vue";
import DuplicateDataView from "./components/DuplicateDataView.vue";
import DataDetailView from "./components/DataDetailView.vue";
import DataTableList from "./components/DataTableList.vue";
import FileDownloadDialog from "@/components/FileDownloadDialog/index.vue";

// 导入hooks
import { useCategoryTemplateList } from "@/hooks/useCategoryTemplate";
import { useClassify } from "@/hooks/useClassify";
import { useDict } from "@/hooks/useDict";
import { usePersons } from "@/hooks/usePersons";
import { useDependentMajors } from "@/hooks/useDependentMajors";
import { useFileDownload } from "@/hooks/useFileDownload";
import { useCustomDialog } from "@/components/CustomDialog";

// 导入工具函数
import { 
    handleTableSort,
    createErrorItemStates, 
    toggleErrorItemState,
    createPaginationState,
    resetPagination as resetPaginationUtil,
    createScrollPaginationState,
    handleTableScrollPagination,
    clearScrollTimers
} from "./utils/tableUtils";
import { 
    DATA_CONSTANTS, 
    filterConfigs, 
    isCollegeDataType, 
    buildTableOptions as buildTableOptionsUtil
} from "./utils/constants";
import { createDataWatchers, createDuplicateDataWatchers, createDetailWatchers } from "./utils/watchers";

// 导入其他依赖
import { PermissionEnum } from "@/enums/roles/authorCards";
import auth from "@/plugins/auth";
import { ElMessage, ElLoading, ElMessageBox, dayjs } from "element-plus";
import { useCheckFailInfoStore } from "@/store/modules/checkFailInfo";

// 初始化stores和hooks
const route = useRoute();
const router = useRouter();
const customDialog = useCustomDialog();
const checkFailInfoStore = useCheckFailInfoStore();

const { templateList } = useCategoryTemplateList();
const { classifyList } = useClassify();
const { allPersonListWithDisabled } = usePersons(true);
const { allDependentMajors } = useDependentMajors(true);
const { dictList } = useDict();
const { fileDownloadDialogVisible, fileListString, handleDownload: handleFileDownload } = useFileDownload();

// 响应式状态
const pagination = ref(createPaginationState());
const scrollState = ref(createScrollPaginationState());
const selectedTable = ref<[number, number] | []>([]);
const selectedTableDetail = ref<CmsCategoryTemplate_ | undefined>(undefined);
const selectedBatch = ref<string>("");
const availableBatches = ref<string[]>([]);
const selectedItem = ref<any>({});
const isShowDetail = ref(false);
const isExport = ref(false);
const loading = ref(false);
const duplicateImportLoading = ref(false);
const showMoreButtons = ref(true);

// 数据相关状态
const fieldList = ref<CmsCategoryTemplateValue10[]>([]);
const tableData = ref<any[]>([]);
const tableOptions = ref<any[]>([]);
const multipleSelection = ref<any[]>([]);
const errorItemStates = ref<{ [key: number]: number }>({});

// 详情相关状态
const detailAndScore = ref<CmsTagDetailAndScoreDto>({});
const recordList = ref<CmsCheck_[]>([]);

// refs
const tableContainerRef = ref<HTMLElement>();
const dataTableListRef = ref<InstanceType<typeof DataTableList>>();

// 计算属性
const hasBackendPermission = computed(() => {
    return auth.hasPermi(PermissionEnum.BACKEND_MANAGEMENT);
});

const hasAdminEntryPermission = computed(() => {
    return auth.hasPermi(PermissionEnum.ADMIN_ENTRY);
});

const isShowDelete = computed(() => {
    if (
        selectedTableDetail.value?.templateType === categoryTemplateType["院情数据"] &&
        availableBatches.value.length > 1 &&
        hasBackendPermission.value
    ) {
        return true;
    }
    return false;
});

const showPagination = computed(() => scrollState.value.showPagination);

// 核心业务方法
const loadTableData = async (templateId: number, batchNumber?: string, pageNum: number = 1) => {
    loading.value = true;
    try {
        const [fieldRes, rowRes] = await Promise.all([
            cmsCategoryTemplateValueGetValueByTemplateId({
                params: { id: templateId },
            }),
            cmsCategoryRowPageByCategoryId({
                params: {
                    categoryId: templateId,
                    batchNumber,
                    pageNum: pageNum,
                    pageSize: pagination.value.pageSize,
                },
            }),
        ]);

        fieldList.value = fieldRes.data;
        tableData.value = rowRes.data.records;
        pagination.value.total = rowRes.data.total;
        pagination.value.currentPage = rowRes.data.current;

        if (tableData.value.length > 0) {
            scrollState.value.showPagination = true;
        }
        scrollState.value.lastScrollTop = 0;
        scrollState.value.scrollDirection = "down";

        if (scrollState.value.hideTimer) {
            clearTimeout(scrollState.value.hideTimer);
            scrollState.value.hideTimer = null;
        }
    } catch (error) {
        console.error("数据加载失败：", error);
        ElMessage.error("数据加载失败");
    } finally {
        loading.value = false;
    }
};

// 事件处理方法
const handleTableChange = (value: any) => {
    if (isShowDetail.value || !value || !Array.isArray(value) || value.length < 2) {
        return;
    }

    const templateId = value[1];
    selectedTableDetail.value = templateList.value.find((item) => item.id === templateId);

    if (!selectedTableDetail.value) {
        return;
    }

    resetPagination();

    if (isCollegeDataType(selectedTableDetail.value.templateType)) {
        loadBatchesAndData();
    } else {
        loadTableData(templateId);
    }
};

const loadBatchesAndData = async () => {
    try {
        const batchRes = await cmsCategoryRowGetAllBatchNumber({
            params: { templateId: selectedTableDetail.value!.id },
        });

        availableBatches.value = batchRes.data;
        if (batchRes.data.length > 0) {
            selectedBatch.value = batchRes.data[0];
        } else {
            ElMessage.warning("当前数据表没有可用批次");
            tableData.value = [];
            selectedBatch.value = "";
        }
    } catch (error) {
        console.error("批次加载失败：", error);
        ElMessage.error("批次加载失败");
    }
};

const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
};

const handleDetail = (row: any) => {
    selectedItem.value = row;
    isShowDetail.value = true;
};

const handleDownload = (row: any, repeatModelDownload: boolean = false) => {
    handleFileDownload({
        row,
        fieldList: fieldList.value,
        repeatModelDownload,
        mode: "fieldList",
    });
};

const sortChange = ({ prop, order }: { prop: string; order: string }) => {
    handleTableSort(tableData.value, fieldList.value, prop, order);
};

// 导出相关方法
const switchExport = () => {
    isExport.value = !isExport.value;
    if (isExport.value) {
        toggleSelection(tableData.value);
    } else {
        clearSelection();
    }
};

const toggleSelection = (rows?: any[]) => {
    if (dataTableListRef.value && rows) {
        rows.forEach((row) => {
            dataTableListRef.value!.toggleRowSelection(row, undefined);
        });
    }
};

const clearSelection = () => {
    dataTableListRef.value?.clearSelection();
};

const exportSelected = async () => {
    if (multipleSelection.value.length === 0) {
        ElMessage.warning("请选择要导出的数据");
        return;
    }

    const loadingInstance = ElLoading.service({
        lock: true,
        text: "正在导出数据，请稍候...",
        background: "rgba(0, 0, 0, 0.7)",
    });

    try {
        const res = await cmsCategoryRowExportRowInfoSingle({
            body: multipleSelection.value.map((item) => item.id),
            options: { responseType: "blob" },
        });

        const blob = new Blob([res as BlobPart], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });

        const link = document.createElement("a");
        link.href = window.URL.createObjectURL(blob);
        link.download = selectedTableDetail.value!.templateName;
        link.click();
        window.URL.revokeObjectURL(link.href);

        ElMessageBox.alert("导出成功！请在浏览器下载文件查看！", "导出结果", {
            confirmButtonText: "确认",
            type: "success",
        });
    } catch (error) {
        console.error("导出失败:", error);
        ElMessage.error("导出失败");
    } finally {
        loadingInstance.close();
        multipleSelection.value = [];
        clearSelection();
        isExport.value = false;
    }
};

// 批次删除
const deleteBatch = () => {
    customDialog.confirm({
        title: "确认删除批次吗？",
        message: `删除批次后，当前数据表<span style="color: #1677ff;">${selectedTableDetail.value!.templateName}</span>的<span style="color: #1677ff;">${selectedBatch.value}</span>批次中的全部数据将被彻底删除，该操作不可撤销。如您已知晓后果，可点击"确定"删除批次。`,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonProps: { type: "danger" },
        dangerouslyUseHTMLString: true,
        type: "warning",
        alignCenter: true,
        onConfirm: async () => {
            try {
                await cmsCategoryRowDeleteByBatchNumber({
                    body: {
                        categoryTemplateId: selectedTableDetail.value!.id,
                        batchNumber: selectedBatch.value,
                    },
                });
                ElMessage.success("删除批次成功");
                if (selectedTable.value.length === 2) {
                    handleTableChange(selectedTable.value as [number, number]);
                }
            } catch (error) {
                console.error("删除批次失败:", error);
            }
        },
    });
};

// 重复数据处理
const toggleErrorItem = (index: number) => {
    errorItemStates.value = toggleErrorItemState(errorItemStates.value, index);
};

const handleClearFailInfo = () => {
    checkFailInfoStore.clearCheckFailInfo();
};

const handleContinueImport = async () => {
    try {
        await ElMessageBox.confirm("继续导入可能会造成数据重复，确定要继续吗？", "确认继续导入", {
            confirmButtonText: "确认导入",
            cancelButtonText: "取消",
            type: "warning",
        });

        duplicateImportLoading.value = true;
        
        if (checkFailInfoStore.checkFailInfo?.template.templateType == categoryTemplateType["院情数据"]) {
            // 院情数据暂不处理
        } else {
            const res = await cmsCategoryRowImportPerformanceFile({
                params: { id: checkFailInfoStore.checkFailInfo!.template.id },
                body: { file: checkFailInfoStore.checkFailFile.raw },
            });
            
            if (res.code === 200) {
                ElMessage.success("强制导入成功！");
                checkFailInfoStore.clearCheckFailInfo();
                router.push("/workspace/adminEntry/entryHome");
            } else {
                ElMessage.error("强制导入失败！请联系管理员！");
            }
        }

        if (selectedTable.value.length === 2) {
            handleTableChange(selectedTable.value as [number, number]);
        }
    } catch (error) {
        console.error("导入失败：", error);
        if (error !== 'cancel') {
            ElMessage.error("导入失败");
        }
    } finally {
        duplicateImportLoading.value = false;
    }
};

const backToAudit = () => {
    checkFailInfoStore.clearCheckFailInfo();
    router.back();
};

// 分页处理
const handleSizeChange = (val: number) => {
    pagination.value.pageSize = val;
    pagination.value.currentPage = 1;
    if (selectedTableDetail.value) {
        loadTableData(selectedTableDetail.value.id, selectedBatch.value, 1);
    }
};

const handleCurrentChange = (val: number) => {
    pagination.value.currentPage = val;
    if (selectedTableDetail.value) {
        loadTableData(selectedTableDetail.value.id, selectedBatch.value, val);
    }
};

const resetPagination = () => {
    resetPaginationUtil(pagination.value);
};

// 滚动处理
const throttledHandleTableScroll = ({ scrollLeft, scrollTop }: { scrollLeft: number; scrollTop: number }) => {
    if (scrollState.value.scrollTimeout) return;

    scrollState.value.scrollTimeout = setTimeout(() => {
        const getScrollInfo = () => {
            const tableElement = dataTableListRef.value?.$el;
            if (!tableElement) return null;

            const scrollWrapper = tableElement.querySelector(".el-table__body-wrapper");
            if (!scrollWrapper) return null;

            const scrollContainer = scrollWrapper.querySelector(".el-scrollbar__view") || scrollWrapper;
            const scrollHeight = scrollContainer.scrollHeight;
            const clientHeight = scrollWrapper.clientHeight;

            return {
                isAtBottom: scrollHeight - clientHeight - scrollTop <= 10,
            };
        };

        scrollState.value = handleTableScrollPagination(scrollState.value, scrollTop, getScrollInfo);
        scrollState.value.scrollTimeout = null;
    }, DATA_CONSTANTS.THROTTLE_DELAY);
};

// 详情相关方法
const getDetailAndScore = (rowId: number) => {
    cmsTagConfigGetDetailAndScoreDataId({ params: { id: rowId } }).then((res) => {
        detailAndScore.value = res.data;
    });
};

const getRecordList = async (rowId: number) => {
    try {
        const res = await cmsCheckGetRecord({ params: { rowId } });
        if (res.data) {
            recordList.value = res.data;
        }
    } catch (error) {
        console.error("获取审核历史失败：", error);
        ElMessage.error("获取审核历史失败");
    }
};

// 工具函数
const buildTableOptions = (newClassifyList: any[], newTemplateList: any[]) => {
    tableOptions.value = buildTableOptionsUtil(newClassifyList, newTemplateList);
};

const initErrorItemStates = (state: number) => {
    errorItemStates.value = createErrorItemStates(checkFailInfoStore.checkFailInfo?.errorList, state);
};

// 生命周期
onMounted(async () => {
    // 如果路由参数中有行id，则代表需要查该表的重复数据
    if (route.query.rowId) {
        try {
            const res = await cmsCategoryRowGetRepeat({ params: { rowId: Number(route.query.rowId) } });
            checkFailInfoStore.checkFailInfo = res.data;
            showMoreButtons.value = false;
        } catch (error) {
            console.error("获取重复数据失败：", error);
        }
    }

    // 设置监听器
    const stopDataWatchers = createDataWatchers(
        templateList, classifyList, selectedTable, selectedBatch, selectedTableDetail,
        loadTableData, 
        (batches: string[]) => { availableBatches.value = batches; },
        (batch: string) => { selectedBatch.value = batch; },
        (data: any[]) => { tableData.value = data; },
        resetPagination,
        clearSelection,
        (mode: boolean) => { isExport.value = mode; },
        buildTableOptions
    );

    const stopDuplicateWatchers = createDuplicateDataWatchers(
        computed(() => checkFailInfoStore.checkFailInfo),
        router,
        initErrorItemStates
    );

    const stopDetailWatchers = createDetailWatchers(
        isShowDetail,
        selectedItem,
        getDetailAndScore,
        getRecordList
    );

    onUnmounted(() => {
        stopDataWatchers();
        stopDuplicateWatchers();
        stopDetailWatchers();
        clearScrollTimers(scrollState.value);
    });
});
</script>

<style scoped lang="scss">
@use "../../../assets/styles/templateFieldsDetail.scss";

$padding: 0 30px;

// 过渡动画相关样式
.fade-enter-active,
.fade-leave-active {
    transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
    transform: translateX(10px);
}

.fade-enter-to,
.fade-leave-from {
    opacity: 1;
    transform: translateX(0);
}

// 分页进入动画
.pagination-enter-active {
    transition: all 0.3s ease-out;
}

.pagination-leave-active {
    transition: all 0.3s ease-in;
}

.pagination-enter-from {
    opacity: 0;
    transform: translateY(20px);
}

.pagination-leave-to {
    opacity: 0;
    transform: translateY(20px);
}

.pagination-enter-to,
.pagination-leave-from {
    opacity: 1;
    transform: translateY(0);
}

.pagination-container {
    z-index: 100;
    position: absolute;
    bottom: 20px;
    right: 95px;
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
}

.data-base-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
    background: #ffffff;
    border-radius: 6px 6px 0px 0px;
    padding: 24px 24px 0px 24px;
    height: 100%;
    
    .data-base-header {
        height: 80px;
        line-height: 80px;
        background-color: #f9fbff;
        color: #23346d;
        font-size: 20px;
        letter-spacing: 2.4px;
        text-align: center;
    }
    
    .data-base-content {
        flex: 1;
        height: 0;
        background-color: #fff;
    }
}

.con-box {
    height: 100%;
    display: flex;
    flex-direction: column;
}
</style>