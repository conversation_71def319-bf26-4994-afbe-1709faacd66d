<template>
    <el-dialog v-model="visible" :title="title" width="500px" :close-on-click-modal="false" @closed="handleClosed">
        <div class="upload-dialog-content">
            <el-upload
                ref="uploadRef"
                class="upload-area"
                drag
                multiple
                :auto-upload="false"
                :file-list="fileList"
                :on-change="handleFileChange"
                :on-remove="handleFileRemove"
                action="">
                <el-icon class="upload-icon"><Upload /></el-icon>
                <div class="upload-text">将文件拖到此处，或<em>点击上传</em></div>
                <div class="upload-tip">{{ acceptTips }}</div>
            </el-upload>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="handleConfirm">确认选择</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import { ElMessage } from "element-plus";
import { Upload } from "@element-plus/icons-vue";
import type { UploadUserFile, UploadProps, UploadInstance } from "element-plus";

const props = withDefaults(
    defineProps<{
        /** 弹窗标题 */
        title?: string;
        /** 是否显示弹窗 */
        modelValue: boolean;
        /** 文件上传类型限制 */
        accept?: string;
        /** 文件大小限制(MB) */
        maxSize?: number;
        /** 最大上传文件数量 */
        maxCount?: number;
        /** 初始文件列表（已上传的文件URL，以逗号分隔） */
        initialFileUrls?: string;
        /** 已选择的文件列表，包括未上传的文件 */
        selectedFiles?: UploadUserFile[];
    }>(),
    {
        title: "选择文件",
        accept: "*",
        maxSize: 100,
        maxCount: 10,
        initialFileUrls: "",
        selectedFiles: () => [],
    }
);

// 抛出的事件
const emit = defineEmits<{
    /** 更新弹窗显示状态 */
    (e: "update:modelValue", value: boolean): void;
    /** 文件选择完成后触发，返回选择的文件列表 */
    (e: "selected", files: UploadUserFile[]): void;
}>();

// 弹窗显示状态
const visible = ref(false);

// 上传组件引用
const uploadRef = ref<UploadInstance>();

// 监听props.modelValue变化以控制弹窗显示状态
watch(
    () => props.modelValue,
    (val) => {
        visible.value = val;
        // 当弹窗打开时，初始化文件列表
        if (val) {
            initFileList();
        }
    }
);

// 监听visible变化以更新父组件的modelValue
watch(visible, (val) => {
    emit("update:modelValue", val);
});

// 文件列表
const fileList = ref<UploadUserFile[]>([]);

// 计算接受文件类型的提示文字
const acceptTips = computed(() => {
    if (props.accept === "*") {
        return `支持上传任意类型文件，单个文件不超过${props.maxSize}MB`;
    }
    const types = props.accept
        .split(",")
        .map((type) => {
            const t = type.trim();
            return t.replace(".", "").toUpperCase();
        })
        .join("、");
    return `支持上传${types}格式文件，单个文件不超过${props.maxSize}MB`;
});

// 初始化文件列表，同时处理已上传文件和未上传文件
function initFileList() {
    // 先清空文件列表
    fileList.value = [];
    
    // 处理已上传的文件
    if (props.initialFileUrls) {
        const urls = props.initialFileUrls.split(",").filter(Boolean);
        const urlFiles = urls.map((url, index) => {
            // 从URL中提取文件名
            const fileName = url.substring(url.lastIndexOf("/") + 1);
            return {
                name: decodeURIComponent(fileName),
                url: url,
                uid: -1000 - index, // 使用负数uid避免与新文件冲突
                status: "success",
            };
        });
        fileList.value.push(...urlFiles);
    }
    
    // 处理已选择但未上传的文件
    if (props.selectedFiles && props.selectedFiles.length > 0) {
        // 过滤出没有URL的文件(未上传的)
        const pendingFiles = props.selectedFiles.filter(file => !file.url);
        if (pendingFiles.length > 0) {
            fileList.value.push(...pendingFiles);
        }
    }
}

// 文件改变时的处理函数
const handleFileChange: UploadProps["onChange"] = (uploadFile, uploadFiles) => {
    // 更新文件列表
    fileList.value = uploadFiles;

    // 检查文件类型
    if (props.accept !== "*") {
        const acceptTypes = props.accept.split(",").map((type) => type.trim().toLowerCase());
        const fileName = uploadFile.name.toLowerCase();
        const validType = acceptTypes.some((type) => {
            if (type.startsWith(".")) {
                return fileName.endsWith(type);
            }
            return true; // 如果不是以.开头的类型，则不做检查
        });

        if (!validType) {
            ElMessage.error(`文件类型不符合要求，请上传${acceptTypes.join("、")}格式的文件`);
            // 从文件列表中移除
            const index = fileList.value.indexOf(uploadFile);
            if (index !== -1) fileList.value.splice(index, 1);
            return;
        }
    }

    // 检查文件大小
    const maxSize = props.maxSize * 1024 * 1024; // 转为字节
    if (uploadFile.raw && uploadFile.raw.size > maxSize) {
        ElMessage.error(`文件大小不能超过${props.maxSize}MB`);
        // 从文件列表中移除
        const index = fileList.value.indexOf(uploadFile);
        if (index !== -1) fileList.value.splice(index, 1);
        return;
    }

    // 检查文件数量限制
    if (fileList.value.length > props.maxCount) {
        ElMessage.warning(`最多只能上传${props.maxCount}个文件`);
        // 保持在限制数量以内
        fileList.value = fileList.value.slice(0, props.maxCount);
    }
};

// 文件移除时的处理函数
const handleFileRemove: UploadProps["onRemove"] = (file) => {
    const index = fileList.value.indexOf(file);
    if (index !== -1) {
        fileList.value.splice(index, 1);
    }
};

// 确认选择文件
const handleConfirm = () => {
    if (fileList.value.length === 0) {
        ElMessage.warning("请先选择文件");
        return;
    }

    // 发送选择完成事件
    emit("selected", fileList.value);
    // 关闭弹窗
    visible.value = false;
};

// 弹窗关闭时重置文件列表
const handleClosed = () => {
    fileList.value = [];
};
</script>

<style scoped lang="scss">
.upload-dialog-content {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
}

.upload-area {
    width: 100%;

    :deep(.el-upload) {
        width: 100%;
    }

    :deep(.el-upload-dragger) {
        width: 100%;
        height: 180px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
}

.upload-icon {
    font-size: 40px;
    color: #586fbb;
    margin-bottom: 10px;
}

.upload-text {
    font-size: 16px;
    color: #606266;
    margin-bottom: 5px;

    em {
        color: #586fbb;
        font-style: normal;
    }
}

.upload-tip {
    font-size: 12px;
    color: #909399;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}
</style>
