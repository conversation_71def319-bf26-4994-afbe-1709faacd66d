<template>
    <div class="custom-submenu">
        <div class="submenu-title" @click="handleClick">
            <div class="title-content">
                <div class="icon-placeholder">
                    <component :is="RouterAndIconMap[title]" style="color: #fff" />
                </div>
                <span class="title-text">{{ title }}</span>
            </div>
        </div>
        <div class="submenu-content" v-show="isExpanded" ref="submenuContent">
            <slot></slot>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { CalendarOutlined, MailOutlined, SettingOutlined } from "@ant-design/icons-vue";

const props = defineProps<{
    title?: string;
    isExpanded?: boolean;
}>();

const RouterAndIconMap = {
    新闻简讯: CalendarOutlined,
    数字档案袋: MailOutlined,
    工作台: SettingOutlined,
    数据总览: SettingOutlined,
    后台管理: MailOutlined,
};

const emit = defineEmits<{
    (e: "toggle"): void;
}>();

const submenuContent = ref<HTMLElement>();

const handleClick = () => {
    emit("toggle");
};
</script>

<style scoped lang="scss">
.custom-submenu {
    font-size: 14px;
    display: flex;
    gap: 4px;
    flex-direction: column;

    .submenu-title {
        height: 40px;
        background: #001529;
        border-radius: 8px;
        padding: 0 28px;
        color: #ffffff;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 700;
        letter-spacing: 1.4px;
        display: flex;
        align-items: center;

        .title-content {
            display: flex;
            gap: 10px;
        }

        .icon-placeholder {
            width: 14px;
            height: 14px;
            background: transparent;
            flex-shrink: 0;
        }

        .title-text {
            min-height: 18px;
            line-height: 18px;
        }

        &:hover {
            background-color: rgba(255, 255, 255, 0.08);
        }

        .arrow {
            transition: transform 0.3s ease;
            color: rgba(255, 255, 255, 0.65);
            font-size: 12px;
            width: 12px;
            height: 12px;

            &.is-open {
                transform: rotate(180deg);
                color: #ffffff;
            }
        }
    }

    .submenu-content {
        display: flex;
        gap: 4px;
        padding:4px;
        flex-direction: column;
    }
}
</style>
