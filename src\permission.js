import router from './router'
import { ElMessage, ElMessageBox } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken, setToken } from '@/utils/auth'
import useUserStore from '@/store/modules/user'
import { decryptTokenIfNeeded } from '@/utils/tokenEncryption'

import usePermissionStore from '@/store/modules/permission'
import * as minimatch from "minimatch";

NProgress.configure({ showSpinner: false });

const whiteList = ['/login', '/auth-redirect', '/bind', '/register'];
const whiteListPatterns = whiteList.map(
    (pattern) => new minimatch.Minimatch(pattern)
);

router.beforeEach((to, from, next) => {
    NProgress.start()

    // 如果检测到地址中有token参数，解密并设置token，然后跳转到首页
    const urlToken = to.query.token
    if (urlToken) {
        console.log("🔐 检测到URL token，开始自动登录流程")
        try {
            // 解密token（如果需要的话）
            const decryptedToken = decryptTokenIfNeeded(urlToken)
            console.log("🔓 Token处理完成，准备设置到cookie")
            
            setToken(decryptedToken)
            console.log("✅ Token已成功设置到cookie")
            
            // 移除URL中的token参数，避免在地址栏显示敏感信息
            const newQuery = { ...to.query }
            delete newQuery.token
            
            next({ path: '/', query: newQuery, replace: true })
            ElMessage.success("自动登录成功")
            return
        } catch (error) {
            console.error("❌ 处理token时发生错误:", error)
            ElMessage.error(error.message || "自动登录失败，请手动登录")
            next({ path: '/login' })
            return
        }
    }

    if (getToken()) {
        /* has token*/
        if (to.path === '/login') {
            console.log("🔄 已有token，从登录页跳转到首页")
            next({ path: '/' })
            NProgress.done()
        }
        else if (whiteListPatterns.some((pattern) => pattern.match(to.path))) {
            // 在免登录白名单，直接进入
            next()
        }
        else {
            if (useUserStore().roles.length === 0) {
                // 判断当前用户是否已拉取完user_info信息
                console.log("👤 开始获取用户信息...")
                useUserStore().getInfo().then((res) => {
                    if (res.code !== 200) {
                        console.error("❌ 获取用户信息失败:", res.msg)
                        useUserStore().resetStore();
                        ElMessage.error(res.msg || "验证失败，请重新登录");
                        next({ path: "/login", query: { redirect: to.fullPath } });
                    } else {
                        console.log("✅ 用户信息获取成功，开始生成路由...")
                        usePermissionStore().generateRoutes().then(accessRoutes => {
                            // 根据roles权限生成可访问的路由表
                            console.log("🛣️ 动态路由生成成功，数量:", accessRoutes.length);
                            accessRoutes.forEach(route => {
                                router.addRoute(route) // 动态添加可访问路由表
                            })
                            console.log("🎉 自动登录流程完成，进入系统")
                            next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
                        }).catch(err => {
                            console.error("❌ 生成路由失败:", err)
                            useUserStore().resetStore();
                            if (useUserStore().roles.length === 0) {
                                ElMessageBox.confirm("该账号暂无任何权限，请联系管理员", "提示", {
                                    confirmButtonText: "确定",
                                    type: "warning",
                                })
                            }
                            next({ path: "/login", query: { redirect: to.fullPath } });
                        })
                    }
                }).catch(err => {
                    console.error("❌ 获取用户信息异常:", err)
                    useUserStore().resetStore();
                    ElMessage.error(err.message || "验证失败，请重新登录");
                    next({ path: "/login", query: { redirect: to.fullPath } });
                })
            } else {
                // 用户信息已存在，直接进入
                next()
            }
        }
    } else {
        // 没有token
        if (whiteListPatterns.some((pattern) => pattern.match(to.path))) {
            // 在免登录白名单，直接进入
            next()
        } else {
            next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
            NProgress.done()
        }
    }
})

router.afterEach(() => {
    NProgress.done()
})
