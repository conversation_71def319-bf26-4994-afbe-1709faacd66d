/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 校验数据 院情数据 POST /cms/cmsCategoryRow/checkData */
export async function cmsCategoryRowCheckData({
  params,
  body,
  file,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryRowCheckDataParams;
  body: {};
  file?: File;
  options?: { [key: string]: unknown };
}) {
  const formData = new FormData();

  if (file) {
    formData.append('file', file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as { [key: string]: any })[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (item instanceof Array) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request<API.CommonResultRowImportErrorMessage_>(
    '/cms/cmsCategoryRow/checkData',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      params: {
        ...params,
      },
      data: formData,
      ...(options || {}),
    }
  );
}

/** 校验数据 成果、工作数据 POST /cms/cmsCategoryRow/checkPerformance */
export async function cmsCategoryRowCheckPerformance({
  params,
  body,
  file,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryRowCheckPerformanceParams;
  body: {};
  file?: File;
  options?: { [key: string]: unknown };
}) {
  const formData = new FormData();

  if (file) {
    formData.append('file', file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as { [key: string]: any })[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (item instanceof Array) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request<API.CommonResultRowImportErrorMessage_>(
    '/cms/cmsCategoryRow/checkPerformance',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      params: {
        ...params,
      },
      data: formData,
      ...(options || {}),
    }
  );
}

/** 添加或修改行数据 强制录入，有重复也可以录入 POST /cms/cmsCategoryRow/constraintCreateOrUpdate */
export async function cmsCategoryRowConstraintCreateOrUpdate({
  body,
  options,
}: {
  body: API.InsertValueParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>(
    '/cms/cmsCategoryRow/constraintCreateOrUpdate',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 添加或修改行数据 POST /cms/cmsCategoryRow/createOrUpdate */
export async function cmsCategoryRowCreateOrUpdate({
  body,
  options,
}: {
  body: API.InsertValueParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListLong_>(
    '/cms/cmsCategoryRow/createOrUpdate',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 根据查询条件获取行数据 院情数据 POST /cms/cmsCategoryRow/data/getValuesByQuery */
export async function cmsCategoryRowDataGetValuesByQuery({
  body,
  options,
}: {
  body: API.CmsCategoryPublicDataParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageCmsCategoryRow_>(
    '/cms/cmsCategoryRow/data/getValuesByQuery',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 根据人员ID分页查询院情数据 GET /cms/cmsCategoryRow/data/listByPersonId */
export async function cmsCategoryRowDataListByPersonId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryRowDataListByPersonIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageCmsCategoryRow_>(
    '/cms/cmsCategoryRow/data/listByPersonId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据id删除行数据 POST /cms/cmsCategoryRow/delete */
export async function cmsCategoryRowDelete({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryRowDeleteParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/cms/cmsCategoryRow/delete', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 数据表负责人根据批次号删除 只要数据表id和批次号 POST /cms/cmsCategoryRow/deleteByBatchNumber */
export async function cmsCategoryRowDeleteByBatchNumber({
  body,
  options,
}: {
  body: API.CmsCategoryRow_;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>(
    '/cms/cmsCategoryRow/deleteByBatchNumber',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 根据查询条件导出大类文件 院情数据 POST /cms/cmsCategoryRow/export/data/file */
export async function cmsCategoryRowExportDataFile({
  body,
  options,
}: {
  body: API.CmsCategoryPublicDataParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.Resource>('/cms/cmsCategoryRow/export/data/file', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据查询条件导出大类文件 成果、工作数据 POST /cms/cmsCategoryRow/export/performance/file */
export async function cmsCategoryRowExportPerformanceFile({
  body,
  options,
}: {
  body: API.CmsCategoryPublicPerformanceParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.Resource>('/cms/cmsCategoryRow/export/performance/file', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据行数据id导出文件 仅支持单表 POST /cms/cmsCategoryRow/export/rowInfo/single */
export async function cmsCategoryRowExportRowInfoSingle({
  body,
  options,
}: {
  body: number[];
  options?: { [key: string]: unknown };
}) {
  return request<API.Resource>('/cms/cmsCategoryRow/export/rowInfo/single', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 全文检索查询 POST /cms/cmsCategoryRow/fullTextSearch */
export async function cmsCategoryRowFullTextSearch({
  body,
  options,
}: {
  body: API.FullTextSearch;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageCmsAchievement_>(
    '/cms/cmsCategoryRow/fullTextSearch',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 根据大类id查询所有批次 审核状态（0：草稿，1：待处理，2：不需要审核，3：审核通过，4：审核不通过）只有院情表才有用 GET /cms/cmsCategoryRow/getAllBatchNumber */
export async function cmsCategoryRowGetAllBatchNumber({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryRowGetAllBatchNumberParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListString_>(
    '/cms/cmsCategoryRow/getAllBatchNumber',
    {
      method: 'GET',
      params: {
        // status has a default value: 2,3
        status: '2,3',
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据行数据状态和数据录入类型查询归属当前用户行数据 审核状态（0：草稿，1：待处理，2：不需要审核，3：审核通过，4：审核不通过）录入类型（INSERT：手动录入，IMPORT：导入） GET /cms/cmsCategoryRow/getByApprovalAndRowType */
export async function cmsCategoryRowGetByApprovalAndRowType({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryRowGetByApprovalAndRowTypeParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageCmsRowValueResult_>(
    '/cms/cmsCategoryRow/getByApprovalAndRowType',
    {
      method: 'GET',
      params: {
        // approvalStatus has a default value: 2,3
        approvalStatus: '2,3',

        // rowTypes has a default value: INSERT
        rowTypes: 'INSERT',
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据id获取行数据 GET /cms/cmsCategoryRow/getById */
export async function cmsCategoryRowGetById({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryRowGetByIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCmsCategoryRow_>(
    '/cms/cmsCategoryRow/getById',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据团队ID分页查询成果 GET /cms/cmsCategoryRow/getByProjectId */
export async function cmsCategoryRowGetByProjectId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryRowGetByProjectIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageCmsAchievement_>(
    '/cms/cmsCategoryRow/getByProjectId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 查询当前用户数据表某个状态是否有数据 审核状态（0：草稿，1：待处理，2：不需要审核，3：审核通过，4：审核不通过）录入类型（INSERT：手动录入，IMPORT：导入） GET /cms/cmsCategoryRow/getOwnerByStatus */
export async function cmsCategoryRowGetOwnerByStatus({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryRowGetOwnerByStatusParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultBoolean_>(
    '/cms/cmsCategoryRow/getOwnerByStatus',
    {
      method: 'GET',
      params: {
        // approvalStatus has a default value: 2,3
        approvalStatus: '2,3',
        // rowTypes has a default value: INSERT
        rowTypes: 'INSERT',
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 查询当前行所有重复值 GET /cms/cmsCategoryRow/getRepeat */
export async function cmsCategoryRowGetRepeat({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryRowGetRepeatParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultRowImportErrorMessage_>(
    '/cms/cmsCategoryRow/getRepeat',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 首页待办事项 审核待办：0，录入待办：1，项目团队待办：3 GET /cms/cmsCategoryRow/getTodo */
export async function cmsCategoryRowGetTodo({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryRowGetTodoParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageTodoResult_>(
    '/cms/cmsCategoryRow/getTodo',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 导入行数据 院情数据导入 POST /cms/cmsCategoryRow/import/data/file */
export async function cmsCategoryRowImportDataFile({
  params,
  body,
  file,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryRowImportDataFileParams;
  body: {};
  file?: File;
  options?: { [key: string]: unknown };
}) {
  const formData = new FormData();

  if (file) {
    formData.append('file', file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as { [key: string]: any })[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (item instanceof Array) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request<API.CommonResultVoid_>(
    '/cms/cmsCategoryRow/import/data/file',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      params: {
        ...params,
      },
      data: formData,
      ...(options || {}),
    }
  );
}

/** 导入行数据 成果、工作数据导入 POST /cms/cmsCategoryRow/import/performance/file */
export async function cmsCategoryRowImportPerformanceFile({
  params,
  body,
  file,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryRowImportPerformanceFileParams;
  body: {};
  file?: File;
  options?: { [key: string]: unknown };
}) {
  const formData = new FormData();

  if (file) {
    formData.append('file', file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as { [key: string]: any })[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (item instanceof Array) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request<API.CommonResultVoid_>(
    '/cms/cmsCategoryRow/import/performance/file',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      params: {
        ...params,
      },
      data: formData,
      ...(options || {}),
    }
  );
}

/** 根据大类ID查询行数据 审核状态（0：草稿，1：待处理，2：不需要审核，3：审核通过，4：审核不通过）,院情表需要传批次号 GET /cms/cmsCategoryRow/listByCategoryId */
export async function cmsCategoryRowListByCategoryId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryRowListByCategoryIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsCategoryRow_>(
    '/cms/cmsCategoryRow/listByCategoryId',
    {
      method: 'GET',
      params: {
        // status has a default value: 2,3
        status: '2,3',
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据行数据状态查询当前用户的录入、导入的行数据 审核状态（0：草稿，1：待处理，2：不需要审核，3：审核通过，4：审核不通过）录入类型（INSERT：手动录入，IMPORT：导入） GET /cms/cmsCategoryRow/ownerCreate/getByApprovalAndType */
export async function cmsCategoryRowOwnerCreateGetByApprovalAndType({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryRowOwnerCreateGetByApprovalAndTypeParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageCmsRowValueResult_>(
    '/cms/cmsCategoryRow/ownerCreate/getByApprovalAndType',
    {
      method: 'GET',
      params: {
        // approvalStatus has a default value: 2,3
        approvalStatus: '2,3',

        // rowTypes has a default value: INSERT
        rowTypes: 'INSERT',
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据大类ID分页查询行数据 审核状态（0：草稿，1：待处理，2：不需要审核，3：审核通过，4：审核不通过）,院情表需要传批次号 GET /cms/cmsCategoryRow/pageByCategoryId */
export async function cmsCategoryRowPageByCategoryId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryRowPageByCategoryIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultPageCmsCategoryRow_>(
    '/cms/cmsCategoryRow/pageByCategoryId',
    {
      method: 'GET',
      params: {
        // status has a default value: 2,3
        status: '2,3',
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据查询条件获取行数据 成果数据 POST /cms/cmsCategoryRow/performance/getValuesByQuery */
export async function cmsCategoryRowPerformanceGetValuesByQuery({
  body,
  options,
}: {
  body: API.CmsCategoryPublicPerformanceParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageCmsCategoryRow_>(
    '/cms/cmsCategoryRow/performance/getValuesByQuery',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 根据人员ID分页查询成果、工作数据 GET /cms/cmsCategoryRow/performance/listByPersonId */
export async function cmsCategoryRowPerformanceListByPersonId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryRowPerformanceListByPersonIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageCmsCategoryRow_>(
    '/cms/cmsCategoryRow/performance/listByPersonId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 立项资料申请查询 POST /cms/cmsCategoryRow/searchProjectInitiationMaterials */
export async function cmsCategoryRowSearchProjectInitiationMaterials({
  body,
  options,
}: {
  body: API.SearchProjectInitiationMaterialsParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageCmsAchievement_>(
    '/cms/cmsCategoryRow/searchProjectInitiationMaterials',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
