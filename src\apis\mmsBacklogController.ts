/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 待办已完成 POST /mms/mmsBacklog/backlogComplete/${param0} */
export async function mmsBacklogBacklogCompleteId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.mmsBacklogBacklogCompleteidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultVoid_>(
    `/mms/mmsBacklog/backlogComplete/${param0}`,
    {
      method: 'POST',
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}

/** 获取消息待办列表 GET /mms/mmsBacklog/list */
export async function mmsBacklogList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.mmsBacklogListParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageMmsBacklog_>(
    '/mms/mmsBacklog/list',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}
