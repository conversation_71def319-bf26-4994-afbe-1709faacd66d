/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 获取员工类型分类人数饼图数据 GET /oms/overView/employeeTypeNumber/pieChart */
export async function overViewEmployeeTypeNumberPieChart({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewEmployeeTypeNumberPieChartParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsPicChartDataResult_>(
    '/oms/overView/employeeTypeNumber/pieChart',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取专任教师和兼职教师饼图数据 GET /oms/overView/fullOrPartTeacher/pieChart */
export async function overViewFullOrPartTeacherPieChart({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewFullOrPartTeacherPieChartParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsPicChartDataResult_>(
    '/oms/overView/fullOrPartTeacher/pieChart',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据大类ID、字段、字段值进行统计(今年、三年内、历史数据) GET /oms/overView/getCountByCondition */
export async function overViewGetCountByCondition({
  params,
  body,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewGetCountByConditionParams;
  body: API.OmsCountStaticsDto[];
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultOmsPicChartDataResult_>(
    '/oms/overView/getCountByCondition',
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      params: {
        ...params,
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 根据字段获取今年、历史数据总量(费用) 获取 [ 今年 ] timeId 必填 GET /oms/overView/getDataCost */
export async function overViewGetDataCost({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewGetDataCostParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultOmsPicChartDataCostResult_>(
    '/oms/overView/getDataCost',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据字段获取今年、历史数据总量(个数) 获取 [ 今年 ] timeId 必填 GET /oms/overView/getDataCount */
export async function overViewGetDataCount({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewGetDataCountParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultOmsPicChartDataResult_>(
    '/oms/overView/getDataCount',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据字段获取今年、历史数据总量(去重) 获取 [ 今年 ] timeId 必填 GET /oms/overView/getDataCountDistinct */
export async function overViewGetDataCountDistinct({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewGetDataCountDistinctParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultOmsPicChartDataResult_>(
    '/oms/overView/getDataCountDistinct',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取横向科研业绩3年内项目列表（学院） GET /oms/overView/getLandscapeOrientationResearchPerformanceThreeYearsProjectList */
export async function overViewGetLandscapeOrientationResearchPerformanceThreeYearsProjectList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewGetLandscapeOrientationResearchPerformanceThreeYearsProjectListParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsLandscapeOrientationResearchPerformanceDto_>(
    '/oms/overView/getLandscapeOrientationResearchPerformanceThreeYearsProjectList',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 近三年省级以上项目列表（学院） GET /oms/overView/getLast3YearProjectList/college */
export async function overViewGetLast3YearProjectListCollege({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewGetLast3YearProjectListCollegeParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsLast3YearProjectListResult_>(
    '/oms/overView/getLast3YearProjectList/college',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 近三年省级以上项目列表（专业） GET /oms/overView/getLast3YearProjectList/major */
export async function overViewGetLast3YearProjectListMajor({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewGetLast3YearProjectListMajorParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsLast3YearProjectListResult_>(
    '/oms/overView/getLast3YearProjectList/major',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 近三年省级以上项目列表（个人） GET /oms/overView/getLast3YearProjectList/person */
export async function overViewGetLast3YearProjectListPerson({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewGetLast3YearProjectListPersonParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsLast3YearProjectListResult_>(
    '/oms/overView/getLast3YearProjectList/person',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取横向科研业绩3年内项目列表（专业） GET /oms/overView/getMajorLandscapeOrientationResearchPerformanceThreeYearsProjectList */
export async function overViewGetMajorLandscapeOrientationResearchPerformanceThreeYearsProjectList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewGetMajorLandscapeOrientationResearchPerformanceThreeYearsProjectListParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsLandscapeOrientationResearchPerformanceDto_>(
    '/oms/overView/getMajorLandscapeOrientationResearchPerformanceThreeYearsProjectList',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取团队建设（全部历史数据） GET /oms/overView/getOrganizationLevelOccurrence */
export async function overViewGetOrganizationLevelOccurrence({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewGetOrganizationLevelOccurrenceParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsOrganizationOccurrenceDto_>(
    '/oms/overView/getOrganizationLevelOccurrence',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取横向科研业绩3年内项目列表（个人） GET /oms/overView/getPersonLandscapeOrientationResearchPerformanceThreeYearsProjectList */
export async function overViewGetPersonLandscapeOrientationResearchPerformanceThreeYearsProjectList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewGetPersonLandscapeOrientationResearchPerformanceThreeYearsProjectListParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsLandscapeOrientationResearchPerformanceDto_>(
    '/oms/overView/getPersonLandscapeOrientationResearchPerformanceThreeYearsProjectList',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取今年或历史数据行数据数量 获取 [ 今年 ] timeId 必填 GET /oms/overView/getRowCount */
export async function overViewGetRowCount({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewGetRowCountParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultOmsPicChartDataResult_>(
    '/oms/overView/getRowCount',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 学生科研饼图（近3年数据） GET /oms/overView/getStudentResearchPieChar */
export async function overViewGetStudentResearchPieChar({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewGetStudentResearchPieCharParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsPicChartDataResult_>(
    '/oms/overView/getStudentResearchPieChar',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据大类id查询需要统计的字段的统计的次数 GET /oms/overView/getTemplateValueOccurrence */
export async function overViewGetTemplateValueOccurrence({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewGetTemplateValueOccurrenceParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsPublicStaticsResultDto_>(
    '/oms/overView/getTemplateValueOccurrence',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 学校活动出席Top5 GET /oms/overView/getTop5Attendees */
export async function overViewGetTop5Attendees({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewGetTop5AttendeesParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsPicChartDataResult_>(
    '/oms/overView/getTop5Attendees',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取纵向科研业绩各级别数量（学院） GET /oms/overView/longitudinalResearchPerformanceLevel/college */
export async function overViewLongitudinalResearchPerformanceLevelCollege({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewLongitudinalResearchPerformanceLevelCollegeParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsPicChartDataResult_>(
    '/oms/overView/longitudinalResearchPerformanceLevel/college',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取专业饼图数据 GET /oms/overView/professional/pieChart */
export async function overViewProfessionalPieChart({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewProfessionalPieChartParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsPicChartDataResult_>(
    '/oms/overView/professional/pieChart',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据查询条件查询当年级别数量 可填：大类ID、所属字段ID、值（与所属字段ID对应）、级别字段ID、查询的级别值、开始时间字段ID（根据字段进行查询）；必填：大类ID，级别字段ID、查询的级别值、开始时间字段ID GET /oms/overView/public/statistics/thisYear */
export async function overViewPublicStatisticsThisYear({
  body,
  options,
}: {
  body: API.OmsPublicStaticsDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultMapStringInt_>(
    '/oms/overView/public/statistics/thisYear',
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 根据查询条件查询三年内级别数量 可填：大类ID、所属字段ID、值（与所属字段ID对应）、级别字段ID、查询的级别值、开始时间字段ID（根据字段进行查询）；必填：大类ID，级别字段ID、查询的级别值、开始时间字段ID GET /oms/overView/public/statistics/threeYears */
export async function overViewPublicStatisticsThreeYears({
  body,
  options,
}: {
  body: API.OmsPublicStaticsDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultMapStringInt_>(
    '/oms/overView/public/statistics/threeYears',
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 获取师生浮窗数据 GET /oms/overView/teacherStudentFloatData */
export async function overViewTeacherStudentFloatData({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.overViewTeacherStudentFloatDataParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultOmsTeacherStudentFloatResult_>(
    '/oms/overView/teacherStudentFloatData',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}
