<template>
    <div class="wrapper">
        <el-scrollbar wrap-class="scrollbar-wrapper">
            <div class="custom-menu">
                <custom-submenu
                    v-for="(route, index) in addRoutes"
                    :key="index"
                    :title="route.title"
                    :is-expanded="true">
                    <custom-menu-item
                        v-for="item in route.children"
                        :key="item.path"
                        :path="item.path"
                        :title="item.title"
                        @select="handleSelect" />
                </custom-submenu>
            </div>
        </el-scrollbar>
    </div>
</template>

<script setup lang="ts">
// import SidebarItem from './SidebarItem'
import { computed } from "vue";
import usePermissionStore from "@/store/modules/permission";
import { useDataEntryStore } from "@/store/modules/dataEntry";
import { useRouter } from "vue-router";
import { ElMessageBox } from "element-plus";
import CustomSubmenu from "@/components/CustomMenu/SubMenu.vue";
import CustomMenuItem from "@/components/CustomMenu/MenuItem.vue";
import { truncatePathToLevel } from "@/utils/pathUtils";
import { useCheckFailInfoStore } from "@/store/modules/checkFailInfo";

const permissionStore = usePermissionStore();
const addRoutes = computed(() => permissionStore.addRoutes);
const router = useRouter();
const dataEntryStore = useDataEntryStore();

// checkFailInfo store
const checkFailInfoStore = useCheckFailInfoStore();

// 获取当前app实例
const app = getCurrentInstance();
const currentRoute = computed(() => router.currentRoute.value);

const handleSelect = async (path: string) => {
    // 获取当前路由的前两级路径
    const currentBase = truncatePathToLevel(currentRoute.value.path, 2);
    // 获取目标路径的前两级路径
    const targetBase = truncatePathToLevel(path, 2);

    if (checkFailInfoStore.checkFailFile || checkFailInfoStore.checkFailInfo) {
        try {
            ElMessageBox.confirm("此时离开将取消导入，是否确认跳转到新页面？", "提示", {
                confirmButtonText: "确认跳转",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                checkFailInfoStore.clearCheckFailInfo();
                if (currentBase === targetBase) {
                    const timestamp = new Date().getTime();
                    // 使用带时间戳的replace方式强制刷新
                    router.replace({
                        path: path,
                        query: {
                            t: timestamp,
                            // 添加随机参数确保每次路由都不同
                            _: Math.random().toString(36).substr(2),
                        },
                    });
                } else {
                    router.push(path);
                }
            });
        } catch {
            // 用户取消切换，不做任何操作
        }
    } else if (dataEntryStore.isEnteringData) {
        try {
            ElMessageBox.confirm(
                "检测到您正在输入数据，此时切换页面将会丢失所有未保存的内容。是否确认跳转到新页面？",
                "提示",
                {
                    confirmButtonText: "确认跳转",
                    cancelButtonText: "取消",
                    type: "warning",
                }
            ).then(() => {
                dataEntryStore.isEnteringData = false;

                if (currentBase === targetBase) {
                    console.log("准备刷新");
                    const timestamp = new Date().getTime();
                    // 使用带时间戳的replace方式强制刷新
                    router
                        .replace({
                            path: path,
                            query: {
                                t: timestamp,
                                // 添加随机参数确保每次路由都不同
                                _: Math.random().toString(36).substr(2),
                            },
                        })
                        .then(() => {
                            // 强制刷新当前路由
                            router.replace({ path: path, query: {} });
                        });
                } else {
                    router.push(path);
                }
            });
        } catch {
            // 用户取消切换，不做任何操作
        }
    } else {
        router.push(path);
    }
};
</script>

<style scoped lang="scss">
.wrapper {
    width: 200px;
    background: #001529;
    height: 100vh;
}

.custom-menu {
    background-color: #001529;
    padding: 38px 4px 42px 4px;
    font-size: 14px;
    font-weight: 400;
    height: 100%;
    width: 200px;

    display: flex;
    gap: 4px;
    flex-direction: column;
    justify-content: center;
}

/* 隐藏水平滚动条 */
:deep(.el-scrollbar__bar.is-horizontal) {
    height: 0 !important;
    display: none !important;
}
</style>
