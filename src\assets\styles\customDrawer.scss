.custom-drawer-header-container {
    display: flex;
    // padding-bottom: 16px;
    padding: 16px 24px;
    gap: 12px;
    align-items: center;
    flex-shrink: 0;
    justify-content: space-between;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);

    .drawer-title {
        flex: 1;
        color: rgba(0, 0, 0, 0.88);
        font-size: 16px;
        font-weight: 600;
    }

    .drawer-title-icon {
        display: flex;
        padding: 0px 0px;
        gap: 12px;
        align-items: center;
        cursor: pointer;
    }
}

.custom-drawer-content {
    padding: 16px 24px;

    .custom-drawer-alert-warpper {
        padding: 16px 0px 24px 0px;

        .custom-drawer-alert {
            display: flex;
            padding: 20px 24px;
            background: #e6f4ff;
            border: 1px solid #91caff;
            border-radius: 8px;
            gap: 12px;

            &-content {
                display: flex;
                flex-direction: column;
                gap: 4px;

                &-title {
                    font-weight: 500;
                }

                &-description {
                    font-weight: 400;
                }
            }
        }
    }
}
