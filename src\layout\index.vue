<template>
    <div class="app-wrapper">
        <sidebar class="sidebar-container" />
        <div class="main-container">
            <app-main />
        </div>
    </div>
</template>

<script setup>
import { useWindowSize } from "@vueuse/core";
import Sidebar from "./components/Sidebar/index.vue";
import { AppMain, Navbar } from "./components";

const { width, height } = useWindowSize();
const WIDTH = 992; // refer to Bootstrap's responsive design

function handleClickOutside() {
    useAppStore().closeSideBar({ withoutAnimation: false });
}

const settingRef = ref(null);
function setLayout() {
    settingRef.value.openSetting();
}
</script>

<style lang="scss" scoped>
.app-wrapper {
    /* position: relative; */
    height: 100vh;
    width: 100%;
    /* overflow: auto; */
    display: flex;
    // border: 1px solid red;
    .main-container {
        height: 100vh;
        width: calc(100% - 200px);
        background: #f5f7ff;
    }
}
</style>
