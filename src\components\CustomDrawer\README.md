# CustomDrawer 自定义抽屉组件

一个基于 Element Plus 的抽屉组件，提供了统一的样式和灵活的插槽支持。

## 特性

- 🎨 统一的视觉设计，使用现有的 `customDrawer.scss` 样式
- 🔧 丰富的配置选项
- 📦 多个插槽支持自定义内容
- 🎯 TypeScript 支持
- 📱 响应式设计

## 基础用法

```vue
<template>
  <CustomDrawer v-model="visible" title="编辑信息">
    <el-form>
      <!-- 表单内容 -->
    </el-form>
  </CustomDrawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import CustomDrawer from '@/components/CustomDrawer'

const visible = ref(false)
</script>
```

## 自定义操作按钮

```vue
<template>
  <CustomDrawer v-model="visible" title="编辑信息">
    <template #actions="{ close }">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
      <el-button type="success" @click="handleSaveAndContinue">保存并继续</el-button>
    </template>
    
    <!-- 内容区域 -->
    <el-form>
      <!-- 表单内容 -->
    </el-form>
  </CustomDrawer>
</template>
```

## 带警告提示

```vue
<template>
  <CustomDrawer v-model="visible" title="删除确认">
    <template #alert>
      <el-icon><WarningFilled /></el-icon>
      <div class="custom-drawer-alert-content">
        <div class="custom-drawer-alert-content-title">注意</div>
        <div class="custom-drawer-alert-content-description">
          此操作将永久删除该项目，请谨慎操作。
        </div>
      </div>
    </template>
    
    <p>确定要删除这个项目吗？</p>
  </CustomDrawer>
</template>
```

## 自定义标题和关闭图标

```vue
<template>
  <CustomDrawer v-model="visible">
    <template #title>
      <el-icon><EditPen /></el-icon>
      <span style="margin-left: 8px;">自定义标题</span>
    </template>
    
    <template #close-icon>
      <el-icon><ArrowLeft /></el-icon>
    </template>
    
    <!-- 内容区域 -->
    <div>自定义内容</div>
  </CustomDrawer>
</template>
```

## Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| modelValue | 控制抽屉显示隐藏 | boolean | — | false |
| title | 抽屉标题 | string | — | '' |
| size | 抽屉尺寸 | string/number | — | '378px' |
| direction | 抽屉打开方向 | string | rtl/ltr/ttb/btt | 'rtl' |
| modal | 是否显示遮罩层 | boolean | — | true |
| lockScroll | 是否锁定 body 滚动 | boolean | — | true |
| appendToBody | 是否将 drawer 插入到 body 元素上 | boolean | — | true |
| closeOnClickModal | 是否可以通过点击 modal 关闭 | boolean | — | false |
| closeOnPressEscape | 是否可以通过按下 ESC 关闭 | boolean | — | true |
| cancelText | 取消按钮文本 | string | — | '取消' |
| confirmText | 确定按钮文本 | string | — | '确定' |
| drawerClass | 自定义 drawer 类名 | string | — | '' |

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:modelValue | 抽屉显示状态改变时触发 | (value: boolean) |
| close | 抽屉关闭时触发 | — |
| confirm | 点击确定按钮时触发 | — |
| before-close | 关闭前的回调，会暂停 drawer 的关闭 | (done: () => void) |

## Slots

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| default | 抽屉内容 | — |
| title | 标题区域 | — |
| actions | 操作按钮区域 | { close: () => void } |
| close-icon | 关闭图标 | — |
| alert | 警告提示区域 | — |

## 样式定制

组件使用了 `@/assets/styles/customDrawer.scss` 中定义的样式类：

- `.custom-drawer-header-container`: 头部容器样式
- `.custom-drawer-content`: 内容区域样式
- `.custom-drawer-alert-warpper`: 警告提示包装器样式
- `.custom-drawer-alert`: 警告提示样式

如需自定义样式，可以通过 `drawerClass` 属性添加自定义类名。

## 使用示例

### 替换原有抽屉代码

**原代码：**
```vue
<el-drawer v-model="basicInfoDialogVisible" :show-close="false" :close-on-click-modal="false">
  <template #header="{ close }">
    <div class="custom-drawer-header-container">
      <div class="drawer-title-icon" @click="close">
        <!-- 关闭图标 SVG -->
      </div>
      <div class="drawer-title">编辑基本信息</div>
      <div>
        <el-button @click="basicInfoDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitBasicInfoForm(basicInfoFormRef)">确定</el-button>
      </div>
    </div>
  </template>
  <div class="custom-drawer-content">
    <!-- 表单内容 -->
  </div>
</el-drawer>
```

**新代码：**
```vue
<CustomDrawer 
  v-model="basicInfoDialogVisible" 
  title="编辑基本信息"
  @confirm="submitBasicInfoForm(basicInfoFormRef)">
  <!-- 表单内容 -->
</CustomDrawer>
```

这样可以大大简化代码，提高可维护性和一致性。

## Composable 用法

### useCustomDrawer

对于需要更多控制逻辑的场景，可以使用 `useCustomDrawer` composable：

```vue
<template>
  <div>
    <el-button @click="drawer.open()">打开抽屉</el-button>
    
    <CustomDrawer 
      v-model="drawer.visible.value"
      v-bind="drawer.drawerProps"
      @confirm="drawer.confirm"
      @close="drawer.close"
      @before-close="drawer.beforeClose">
      
      <el-form ref="formRef" :model="form">
        <el-form-item label="姓名">
          <el-input v-model="form.name" />
        </el-form-item>
      </el-form>
    </CustomDrawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import CustomDrawer from '@/components/CustomDrawer'
import { useCustomDrawer } from '@/components/CustomDrawer/useCustomDrawer'

const form = reactive({
  name: ''
})

const drawer = useCustomDrawer({
  title: '编辑用户信息',
  onConfirm: async () => {
    // 表单验证和提交逻辑
    console.log('保存用户信息:', form)
    // 如果保存失败，抛出错误，抽屉不会关闭
    // throw new Error('保存失败')
  },
  onClose: () => {
    console.log('抽屉已关闭')
  }
})
</script>
```

### 快速创建函数

```vue
<script setup lang="ts">
import { createFormDrawer, createConfirmDrawer } from '@/components/CustomDrawer/useCustomDrawer'

// 创建表单抽屉
const editDrawer = createFormDrawer('编辑信息', async () => {
  // 保存逻辑
  await saveData()
})

// 创建确认抽屉
const deleteDrawer = createConfirmDrawer('删除确认', async () => {
  // 删除逻辑
  await deleteData()
})
</script>
``` 