<template>
    <CustomDialog
        v-model="visible"
        title="下载材料"
        type="confirm"
        :show-buttons="false"
        :width="504"
        dialog-class="file-download-dialog"
        :align-center="true"
        @close="handleClose">
        <div class="file-download-content">
            <!-- 表格头部 -->
            <div class="table-header">
                <div class="header-cell checkbox-cell">
                    <el-checkbox v-model="selectAll" :indeterminate="isIndeterminate" @change="handleSelectAllChange" />
                </div>
                <div class="header-cell file-name-cell">文件名</div>
                <div class="header-cell size-cell">大小</div>
                <div class="header-cell time-cell">更新时间</div>
                <div class="header-cell action-cell">操作</div>
            </div>

            <!-- 文件列表 -->
            <div class="file-list">
                <div
                    v-for="(file, index) in fileList"
                    :key="index"
                    class="file-row"
                    :class="{ selected: file.selected }">
                    <!-- 复选框 -->
                    <div class="file-cell checkbox-cell">
                        <el-checkbox v-model="file.selected" @change="updateSelection" />
                    </div>

                    <!-- 文件名 -->
                    <div class="file-cell file-name-cell">
                        <div class="file-info">
                            <img :src="getFileIcon(file.name)" class="file-icon" />
                            <span class="file-name">{{ file.name }}</span>
                        </div>
                    </div>

                    <!-- 文件大小 -->
                    <div class="file-cell size-cell">
                        <span class="file-size">{{ file.size || "-" }}</span>
                    </div>

                    <!-- 更新时间 -->
                    <div class="file-cell time-cell">
                        <span class="file-time">{{ file.updateTime || "-" }}</span>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="file-cell action-cell">
                        <el-button type="primary" link @click="downloadSingleFile(file)" :loading="file.downloading">
                            <el-icon><Download /></el-icon>
                            下载
                        </el-button>
                    </div>
                </div>
            </div>

            <!-- 底部统计和批量下载 -->
            <div class="footer-actions">
                <div class="selection-info">已选 {{ selectedCount }} 个文件，共 {{ totalSelectedSize }}</div>
                <el-button :disabled="selectedCount < 2" type="primary" plain @click="downloadBatchFile">
                    <el-icon><Download /></el-icon>
                    批量下载 ZIP
                </el-button>
            </div>
        </div>
    </CustomDialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import { Download } from "@element-plus/icons-vue";
import CustomDialog from "@/components/CustomDialog/index.vue";
import { downloadFile } from "@/utils/fileDownload";

// 引入文件图标
import ExcelIcon from "@/assets/Excel.png";
import WordIcon from "@/assets/word.png";
import PictureIcon from "@/assets/picture.png";
import ZipIcon from "@/assets/zip.png";
import { minioDownloadZip, minioGetFileInfos } from "@/apis/fmsMinioController";

interface FileItem {
    path: string;
    name: string;
    size?: string;
    updateTime?: string;
    selected: boolean;
    downloading: boolean;
}

interface Props {
    modelValue: boolean;
    fileListString: string;
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    fileListString: "",
});

const emit = defineEmits<{
    "update:modelValue": [value: boolean];
}>();

const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit("update:modelValue", value),
});

// 解析文件列表
const fileList = ref<FileItem[]>([]);

const parseFileList = (fileString: string) => {
    if (!fileString.trim()) {
        fileList.value = [];
        return;
    }

    const files = fileString.split(",").filter((item) => item.trim());


    fileList.value = files.map((fileItem) => {
        const trimmedItem = fileItem.trim();
        const parts = trimmedItem.split("::");
        if (parts.length >= 2) {
            return {
                // 拼接为完整路径
                path: parts[0] + "::" + parts[1],
                name: parts[1],
                size: "-", // 暂时显示为 "-"，后续可接入实际API
                updateTime: "-", // 暂时显示为 "-"，后续可接入实际API
                selected: false,
                downloading: false,
            };
        } else {
            // 如果没有 :: 分隔符，尝试从路径中提取文件名
            const fileName = trimmedItem.split("/").pop() || trimmedItem;
            return {
                path: trimmedItem,
                name: fileName,
                size: "-",
                updateTime: "-",
                selected: false,
                downloading: false,
            };
        }
    });

    // 重置全选状态
    selectAll.value = false;

    getFileListInfo();
};

// 获取文件列表的文件信息
const getFileListInfo = async () => {
    try {
        const res = await minioGetFileInfos({
            body: {
                fileUrls: fileList.value.map((item) => item.path),
            },
        });

        if (res.data && Array.isArray(res.data)) {
            // 更新文件列表的大小和更新时间信息
            fileList.value.forEach((file, index) => {
                const fileInfo = res.data[index];
                if (fileInfo) {
                    // 格式化文件大小
                    file.size = formatFileSize(fileInfo.size || 0);
                    // 格式化更新时间
                    file.updateTime = formatUpdateTime(fileInfo.lastModified || "");
                }
            });
        }
    } catch (error) {
        console.error("获取文件信息失败：", error);
    }
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 B";

    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 格式化更新时间
const formatUpdateTime = (dateString: string): string => {
    if (!dateString) return "-";

    try {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
    } catch (error) {
        console.error("日期格式化失败：", error);
        return "-";
    }
};

// 监听文件列表字符串变化
watch(
    () => props.fileListString,
    (newValue) => {
        parseFileList(newValue);
    },
    { immediate: true }
);

// 获取文件图标
const getFileIcon = (fileName: string): string => {
    const extension = fileName.split(".").pop()?.toLowerCase() || "";

    switch (extension) {
        case "xlsx":
        case "xls":
        case "csv":
            return ExcelIcon;
        case "doc":
        case "docx":
            return WordIcon;
        case "jpg":
        case "jpeg":
        case "png":
        case "gif":
        case "bmp":
        case "webp":
            return PictureIcon;
        default:
            return ZipIcon;
    }
};

// 计算选中文件数量
const selectedCount = computed(() => {
    return fileList.value.filter((file) => file.selected).length;
});

// 计算总选中文件大小
const totalSelectedSize = computed(() => {
    const selectedFiles = fileList.value.filter((file) => file.selected);
    if (selectedFiles.length === 0) return "0 B";

    // 如果还没有获取到文件大小信息，显示 "计算中..."
    if (selectedFiles.some((file) => file.size === "-")) {
        return "计算中...";
    }

    // 计算总字节数（从格式化的大小字符串中提取）
    let totalBytes = 0;
    selectedFiles.forEach((file) => {
        if (file.size && file.size !== "-") {
            totalBytes += parseSizeToBytes(file.size);
        }
    });

    return formatFileSize(totalBytes);
});

// 将格式化的文件大小转换为字节数
const parseSizeToBytes = (sizeStr: string): number => {
    if (!sizeStr || sizeStr === "-") return 0;

    const units = { B: 1, KB: 1024, MB: 1024 ** 2, GB: 1024 ** 3, TB: 1024 ** 4 };
    const match = sizeStr.match(/^([\d.]+)\s*([A-Z]+)$/);

    if (!match) return 0;

    const value = parseFloat(match[1]);
    const unit = match[2] as keyof typeof units;

    return Math.round(value * (units[unit] || 1));
};

// 更新选择状态
const updateSelection = () => {
    // 更新全选状态
    updateSelectAllStatus();
};

// 下载单个文件
const downloadSingleFile = async (file: FileItem) => {
    if (file.downloading) return;

    file.downloading = true;
    try {
        await downloadFile(file.path, file.name);
        ElMessage.success(`${file.name} 开始下载`);
    } catch (error) {
        console.error("下载失败：", error);
        ElMessage.error(`${file.name} 下载失败`);
    } finally {
        file.downloading = false;
    }
};

// 全选相关逻辑
const selectAll = ref(false);
const isIndeterminate = computed(() => {
    const selectedCount = fileList.value.filter((file) => file.selected).length;
    return selectedCount > 0 && selectedCount < fileList.value.length;
});

// 处理全选变化
const handleSelectAllChange = (checked: boolean) => {
    fileList.value.forEach((file) => {
        file.selected = checked;
    });
    updateSelection();
};

// 更新全选状态
const updateSelectAllStatus = () => {
    const selectedCount = fileList.value.filter((file) => file.selected).length;
    const totalCount = fileList.value.length;

    if (totalCount === 0) {
        selectAll.value = false;
        return;
    }

    selectAll.value = selectedCount === totalCount;
};

// 批量下载
const downloadBatchFile = async () => {
    const selectedFiles = fileList.value.filter((file) => file.selected);

    if (selectedFiles.length === 0) {
        ElMessage.warning("请先选择要下载的文件");
        return;
    }

    try {
        ElMessage.info("正在准备下载，请稍候...");

        // 提取文件路径
        const fileUrls = selectedFiles.map((file) => {
            return file.path;
        });

        const res = await minioDownloadZip({
            body: {
                fileUrls: fileUrls,
            },
            options: {
                responseType: "blob",
            },
        });
        // 下载成功后的处理
        ElMessage.success(`已开始下载 ${selectedFiles.length} 个文件的压缩包`);
        const blob = new Blob([res as BlobPart], { type: "application/zip" });

        const link = document.createElement("a");
        link.href = window.URL.createObjectURL(blob);
        link.download = selectedFiles[0].name + "等" + (selectedFiles.length - 1) + "个文件.zip";
        link.click();
        window.URL.revokeObjectURL(link.href);


    } catch (error) {
        console.error("批量下载失败：", error);
        ElMessage.error("批量下载失败，请重试");
    }
};

// 关闭弹窗
const handleClose = () => {
    visible.value = false;
};
</script>

<style scoped lang="scss">
.file-download-content {
    width: 100%;

    .table-header {
        display: flex;
        background: #fafafa;
        border-bottom: 1px solid #f0f0f0;
        border-radius: 8px 8px 0 0;

        .header-cell {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 16px;
            color: rgba(0, 0, 0, 0.88);
            font-weight: 600;
            font-size: 14px;
            line-height: 22px;
            border-right: 1px solid #f0f0f0;

            &:last-child {
                border-right: none;
            }

            &.checkbox-cell {
                width: 48px;
                flex-shrink: 0;
                justify-content: center;
            }

            &.file-name-cell {
                flex: 1;
            }

            &.size-cell {
                width: 61px;
                flex-shrink: 0;
            }

            &.time-cell {
                width: 116px;
                flex-shrink: 0;
            }

            &.action-cell {
                width: 114px;
                flex-shrink: 0;
            }
        }
    }

    .file-list {
        .file-row {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s;

            &:hover {
                background: #fafafa;
            }

            &.selected {
                background: #f6ffed;
            }

            .file-cell {
                display: flex;
                align-items: center;
                padding: 16px;
                border-right: 1px solid #f0f0f0;

                &:last-child {
                    border-right: none;
                }

                &.checkbox-cell {
                    width: 48px;
                    flex-shrink: 0;
                    justify-content: center;
                }

                &.file-name-cell {
                    flex: 1;
                    justify-content: flex-start;

                    .file-info {
                        display: flex;
                        align-items: center;
                        gap: 8px;

                        .file-icon {
                            width: 20px;
                            height: 20px;
                            flex-shrink: 0;
                        }

                        .file-name {
                            color: #333333;
                            font-size: 14px;
                            line-height: 22px;
                            word-break: break-all;
                        }
                    }
                }

                &.size-cell {
                    width: 60px;
                    flex-shrink: 0;
                    justify-content: center;

                    .file-size {
                        color: #999999;
                        font-size: 13px;
                        line-height: 20px;
                        text-align: center;
                    }
                }

                &.time-cell {
                    width: 116px;
                    flex-shrink: 0;
                    justify-content: center;

                    .file-time {
                        color: #999999;
                        font-size: 13px;
                        line-height: 20px;
                        text-align: center;
                    }
                }

                &.action-cell {
                    width: 114px;
                    flex-shrink: 0;
                    justify-content: center;
                }
            }
        }
    }

    .footer-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 17px 24px 0;
        border-top: 1px solid #f0f0f0;
        background: #ffffff;

        .selection-info {
            color: #666666;
            font-size: 13px;
            line-height: 20px;
        }

    }
}
</style>
