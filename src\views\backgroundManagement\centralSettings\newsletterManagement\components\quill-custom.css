/* 自定义Quill编辑器样式，解决行内元素问题 */

/* 确保P标签不产生多余的边距和强制换行 */
 /* white-space: normal !important; */

 /* 与下拉框高度保持一致 */
 /* .ql-editor p {
    margin: 0;
    padding: 0;
    display: block;
   
    line-height: 40px; 
  } */

  /* 确保下拉框元素正确显示 */
  /* 防止上下浮动 */
  /* 保持定位方式一致 */
  /* .ql-editor .el-cascader {
    display: inline-block !important;
    vertical-align: middle !important;
    margin: 0 3px;
    line-height: normal;
    position: relative; 
    top: 0; 
  } */
  
  /* 下拉框选中后的样式，确保垂直对齐 */
  /* .ql-editor .el-cascader span[contenteditable="false"] {
    display: inline-block;
    vertical-align: middle;
  } */
  
  /* 确保下拉框内部元素垂直居中 */
  .ql-editor .el-cascader .select-input-value {
    display: inline-flex;
    align-items: center;
    justify-content: flex-start;
    vertical-align: middle;
  }
  
  /* 防止编辑器自动添加的空格导致下拉框错位 */
  /* .ql-editor br {
    display: none;
  } */
  
  /* 确保光标可以正确定位 */
  .ql-container {
    position: relative;
  }
  
  /* 自定义每行的样式 */
  /* .ql-editor div {
    display: inline;
  } */
  
  /* 确保文本和下拉框在同一行对齐 *//* 与下拉框高度保持一致 */
  /* .ql-editor {
    line-height: 40px !important; 
    vertical-align: middle;
    display: flex;
    flex-wrap: wrap;
  } */
  
  /* 解决文本与下拉框基线对齐问题 */
  .ql-editor * {
    vertical-align: middle;
  }
  
  /* 确保文本基线与下拉框居中对齐 */
  /* .ql-editor span.ql-cursor,
  .ql-editor p > span {
    vertical-align: middle;
    line-height: 40px;
  } */



  /* :deep(.el-cascader) {
  display: -webkit-inline-box;
  position: relative;
  line-height: 40px;
  margin: 0 3px;



  li {
    &::before {
      content: "";
    }
  }

  
} */

.ql-el-cascader__dropdown {
    display: none;
    z-index: 111;
    margin: 5px 0;
    font-size: 14px;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .ql-el-cascader-panel {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    border-radius: 4px;
    font-size: 14px;
    border: 1px solid #e4e7ed;
  }

  .ql-el-scrollbar {
    overflow: hidden;
    position: relative;
  }

  .ql-el-scrollbar:active>.ql-el-scrollbar__bar,
  .ql-el-scrollbar:focus>.ql-el-scrollbar__bar,
  .ql-el-scrollbar:hover>.ql-el-scrollbar__bar {
    opacity: 1;
    -webkit-transition: opacity 0.34s ease-out;
    transition: opacity 0.34s ease-out;
  }

  .ql-el-scrollbar__wrap {
    overflow: scroll;
    height: 100%;
  }

  .ql-el-scrollbar__wrap--hidden-default {
    scrollbar-width: none;
  }

  .ql-el-scrollbar__wrap--hidden-default::-webkit-scrollbar {
    width: 0;
    height: 0;
  }

  .ql-el-scrollbar__thumb {
    position: relative;
    display: block;
    width: 0;
    height: 0;
    cursor: pointer;
    border-radius: inherit;
    background-color: rgba(144, 147, 153, 0.3);
    -webkit-transition: background-color 0.3s;
    transition: background-color 0.3s;
  }

  .ql-el-scrollbar__thumb:hover {
    background-color: rgba(144, 147, 153, 0.5);
  }

  .ql-el-scrollbar__bar {
    position: absolute;
    right: 2px;
    bottom: 2px;
    z-index: 1;
    border-radius: 4px;
    opacity: 0;
    -webkit-transition: opacity 0.12s ease-out;
    transition: opacity 0.12s ease-out;
  }

  .ql-el-scrollbar__bar.is-vertical {
    width: 6px;
    top: 2px;
  }

  .ql-el-scrollbar__bar.is-vertical>div {
    width: 100%;
  }

  .ql-el-scrollbar__bar.is-horizontal {
    height: 6px;
    left: 2px;
  }

  .ql-el-scrollbar__bar.is-horizontal>div {
    height: 100%;
  }

  .ql-el-cascader-panel.is-bordered {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
  }

  .ql-el-cascader-menu {
    min-width: 180px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #606266;
    border-right: 1px solid #e4e7ed;
  }

  .ql-el-cascader-menu:last-child {
    border-right: none;
  }

  .ql-el-cascader-menu:last-child .ql-el-cascader-node {
    padding-right: 20px;
  }

  .ql-el-cascader-menu__wrap {
    height: 204px;
  }

  .ql-el-cascader-menu__list {
    position: relative;
    max-height: 250px;
    overflow-y: auto;
    margin: 0;
    padding: 6px 0;
    list-style: none;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }

  .ql-el-avatar,
  .ql-el-drawer {
    -webkit-box-sizing: border-box;
    overflow: hidden;
  }

  .ql-el-cascader-menu__hover-zone {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  .ql-el-cascader-menu__empty-text {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    text-align: center;
    color: #c0c4cc;
  }

  .ql-el-cascader-node {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 0 30px 0 20px;
    height: 34px;
    line-height: 34px;
    outline: 0;
  }

  .ql-el-cascader-node.is-selectable.in-active-path {
    color: #606266;
  }

  .ql-el-cascader-node.in-active-path,
  .ql-el-cascader-node.is-active,
  .ql-el-cascader-node.is-selectable.in-checked-path {
    color: #4e6ef2;
    font-weight: 700;
  }

  .ql-el-cascader-node:not(.is-disabled) {
    cursor: pointer;
  }

  .ql-el-cascader-node:not(.is-disabled):focus,
  .ql-el-cascader-node:not(.is-disabled):hover {
    background: #f5f7fa;
  }

  .ql-el-cascader-node.is-disabled {
    color: #c0c4cc;
    cursor: not-allowed;
  }

  .ql-el-icon-check {
    width: 14px;
    height: 14px;
  }

  .ql-el-cascader-node__prefix {
    position: absolute;
    left: 10px;
  }

  .ql-el-cascader-node__postfix {
    position: absolute;
    right: 10px;
    background: url(./icon/arrow.png) no-repeat center;
    background-size: 13px;
  }

  .ql-el-cascader-node__label {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    padding: 0 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .ql-el-cascader-node>.ql-el-radio .ql-el-radio__label {
    padding-left: 0;
  }