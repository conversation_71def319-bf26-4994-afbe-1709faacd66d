import axios, {
    AxiosInstance,
    AxiosPromise,
    AxiosRequestConfig,
    AxiosRequestHeaders,
    AxiosResponse,
    Method,
} from "axios";
import qs from "qs";

import { ElNotification, ElMessageBox, ElMessage, ElLoading } from "element-plus";
import { getToken } from "@/utils/auth";
import errorCode from "@/utils/errorCode";
import { tansParams } from "@/utils/ruoyi";
import cache from "@/plugins/cache";
import useUserStore from "@/store/modules/user";
import { cs } from "element-plus/es/locale";

// let downloadLoadingInstance: any;

//@ts-ignore
axios.defaults.headers["Content-Type"] = "application/json;charset=utf-8";

// 创建axios实例
const service = axios.create({
    // axios中请求配置有baseURL选项，表示请求URL公共部分
    baseURL: import.meta.env.VITE_BASEURL,
    // 默认超时时间
    timeout: 10000,
});

interface ResponseData<T = any> {
    code: number;
    data: T;
    message: string;
}

// request拦截器
service.interceptors.request.use(
    (config) => {
        // 是否需要设置 token
        const isToken = (config.headers || {}).isToken === false;
        // 是否需要防止数据重复提交
        const isRepeatSubmit = (config.headers || {}).repeatSubmit === false;

        // 修改 get 请求参数处理逻辑
        if (config.method === "get" && config.params) {
            // 使用 qs 序列化参数
            const serializedParams = qs.stringify(config.params, {
                arrayFormat: "repeat", // 数组格式化方式
                skipNulls: true, // 忽略 null 值
                indices: false, // 不添加数组索引
            });

            // 构建完整 URL
            config.url = `${config.url}?${serializedParams}`;
            // 清空 params，因为已经附加到 URL 中
            config.params = {};
        }

        // 检查是否为文件上传请求，如果是则设置更长的超时时间
        const contentType = String(config.headers?.["Content-Type"] || config.headers?.["content-type"] || "");
        if (contentType.includes("multipart/form-data")) {
            config.timeout = 60000; // 文件上传请求设置为1分钟超时
        }

        if (getToken() && !isToken) {
            // 让每个请求携带自定义token 请根据实际情况自行修改
            (<AxiosRequestHeaders>config.headers)["Authorization"] = "Bearer " + getToken();
        }
        // get请求映射params参数
        if (config.method === "get" && config.params) {
            let url = config.url + "?" + tansParams(config.params);
            url = url.slice(0, -1);
            config.params = {};
            config.url = url;
        }
        if (!isRepeatSubmit && (config.method === "post" || config.method === "put")) {
            // 对于文件上传请求，不进行防重复提交检查
            const isFileUpload = contentType.includes("multipart/form-data");

            // 如果不是文件上传请求，才进行防重复提交检查
            if (!isFileUpload) {
                const requestObj = {
                    url: config.url,
                    data: typeof config.data === "object" ? JSON.stringify(config.data) : config.data,
                    time: new Date().getTime(),
                };
                const sessionObj = cache.session.getJSON("sessionObj");
                if (sessionObj === undefined || sessionObj === null || sessionObj === "") {
                    cache.session.setJSON("sessionObj", requestObj);
                } else {
                    const s_url = sessionObj.url; // 请求地址
                    const s_data = sessionObj.data; // 请求数据
                    const s_time = sessionObj.time; // 请求时间
                    const interval = 200; // 间隔时间(ms)，小于此时间视为重复提交
                    if (s_data === requestObj.data && requestObj.time - s_time < interval && s_url === requestObj.url) {
                        const message = "数据正在处理，请勿重复提交";
                        console.warn(`[${s_url}]: ` + message);
                        return Promise.reject(new Error(message));
                    } else {
                        cache.session.setJSON("sessionObj", requestObj);
                    }
                }
            } else {
                console.log("检测到文件上传请求，跳过防重复提交检查");
            }
        }
        return config;
    },
    (error) => {
        console.log(error);
        Promise.reject(error);
    }
);

// 响应拦截器
service.interceptors.response.use(
    (res) => {
        // 二进制数据则直接返回
        if (res.request.responseType === "blob" || res.request.responseType === "arraybuffer") {
            // 如果是下载文件且后端返回错误信息
            if (res.data instanceof Blob && res.data.type === "application/json") {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => {
                        const result = JSON.parse(reader.result as string);
                        ElMessage.error(result.message || "下载失败");
                        reject(result);
                    };
                    reader.readAsText(res.data);
                });
            }
            return res;
        }

        // 未设置状态码则默认成功状态
        const code = String(res.data.code || 200);
        //获取错误信息
        const msg = errorCode[code] || res.data.message || errorCode["default"];
        if (code === "401") {
            useUserStore().resetStore();
            ElMessageBox.alert("登录状态已过期,请重新登录", "系统提示", {
                confirmButtonText: "确定",
                callback: () => {
                    useUserStore()
                        .logOut()
                        .then(() => {
                            location.href = "/";
                        })
                        .catch(() => {
                            location.href = "/";
                        });
                },
            });
            return Promise.reject("无效的会话，或者会话已过期，请重新登录。");
        } else if (code === "500") {
            // 如果错误信息最后三个字为“已存在”，则提示“该数据已存在”
            if (msg.endsWith("已存在")) {
                ElMessageBox.alert(msg, "提示", {
                    confirmButtonText: "确定",
                    type: "error",
                });
                return Promise.reject(new Error(msg));
            } else {
                ElMessage({ message: msg, type: "error" });
                return Promise.reject(new Error(msg));
            }
        } else if (code === "601") {
            ElMessage({ message: msg, type: "warning" });
            return Promise.reject(new Error(msg));
        } else if (code !== "200") {
            ElNotification.error({ title: msg });
            return Promise.reject("error");
        } else {
            return Promise.resolve(res);
        }
    },
    (error) => {
        console.log("err" + error);
        let { message } = error;
        if (message == "Network Error") {
            message = "网络异常";
        } else if (message.includes("timeout")) {
            message = "系统接口请求超时";
        } else if (message.includes("Request failed with status code")) {
            message = "系统接口" + message.substr(message.length - 3) + "异常";
        }
        ElMessage({ message: message, type: "error", duration: 5 * 1000 });
        return Promise.reject(error);
    }
);

/** ---------------------------
 *用于下载文件的请求函数，返回完整的 AxiosResponse 对象，
 * 从而可以获取 header 信息（比如 content-disposition）来提取文件名
 ----------------------------*/
export async function downloadFileRequest<T = Blob>(
    url: string,
    data: any,
    options?: AxiosRequestConfig
): Promise<AxiosResponse<T>> {
    return service.post(url, data, {
        responseType: "blob",
        ...options,
    });
}
// request 函数
function request<T = any>(url: string, config: AxiosRequestConfig): Promise<T>;

function request<T = any>(config: AxiosRequestConfig): Promise<T>;

function request<T = any>(urlOrConfig: string | AxiosRequestConfig, maybeConfig?: AxiosRequestConfig) {
    if (typeof urlOrConfig === "string") {
        return service({
            url: urlOrConfig,
            ...maybeConfig,
        }).then((res) => res.data);
    }
    return service(urlOrConfig).then((res) => res.data);
}

export default request;
