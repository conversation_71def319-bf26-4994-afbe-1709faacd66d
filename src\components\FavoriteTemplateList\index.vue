<template>
    <div>
        <div class="title-wrapper">
            <div class="pin-icon"><img src="@/assets/thumbtack.png" alt="pin" class="pin-icon-image" /></div>
            <div class="title">已收藏</div>
        </div>
        <div v-if="favoriteTemplates.length > 0" class="templates-grid">
            <transition-group name="favorite-list" tag="div" class="templates-container">
                <div v-for="template in favoriteTemplates" :key="template.id" class="template-card">
                    <div v-if="template.templateName" class="template-item" @click="$emit('choose-entry', template)">
                        <div class="template-icon-left">
                            <img src="@/assets/templateIcon.png" alt="template" class="icon-image" />
                        </div>
                        <div class="template-content">
                            <span>{{ template.templateName }}</span>
                        </div>
                        <div @click.stop="handleCollectWithEvent(template, $event)" class="template-icon-star">
                            <img :src="getStarImage(template.id)" class="star-icon" />
                        </div>
                    </div>
                </div>
            </transition-group>
        </div>
        <div v-else>
            <p class="empty-text">暂无收藏数据表</p>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { CmsCategoryTemplate_ } from "@/apis/types";

defineProps<{
    favoriteTemplates: CmsCategoryTemplate_[];
    getStarImage: (id: number) => string;
}>();

const emit = defineEmits<{
    (e: "choose-entry", template: CmsCategoryTemplate_): void;
    (e: "collect", template: CmsCategoryTemplate_, event: Event): void;
}>();

const handleCollectWithEvent = (template: CmsCategoryTemplate_, event: Event) => {
    emit("collect", template, event);
};
</script>

<style scoped lang="scss">
@use "@/assets/styles/favorite.scss";

.title-wrapper {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 13px;
}

.pin-icon {
    img {
        width: 20px;
        height: 20px;
    }
}

.title {
    font-size: 14px;
    font-weight: 500;
    color: #1890FF;
    line-height: 28px;
}

.templates-grid {
    margin-left: 40px;
}

.templates-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.template-card {
    width: 240px;
    background: #FFFFFF;
    border: 1px solid #F0F0F0;
    border-radius: 6px;
    overflow: hidden;
}

.template-item {
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 17px;
    min-height: 44px;
}

.template-icon-left {
    flex-shrink: 0;
}

.icon-image {
    width: 20px;
    height: 20px;
}

.template-content {
    flex: 1;
    color: #333333;
    font-size: 14px;
    line-height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.template-icon-star {
    cursor: pointer;
    flex-shrink: 0;
}

.star-icon {
    width: 18px;
    height: 18px;
}

.empty-text {
    font-size: 15px;
    color: #6b7995;
    text-align: center;
    margin: 20px 0;
    margin-left: 40px;
}
</style>
