/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 新增角色 POST /ums/umsRole/add */
export async function umsRoleAdd({
  body,
  options,
}: {
  body: API.UmsRole1;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsRole/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分配卡资源 POST /ums/umsRole/allocCard */
export async function umsRoleAllocCard({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsRoleAllocCardParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsRole/allocCard', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 删除角色 POST /ums/umsRole/delete */
export async function umsRoleDelete({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsRoleDeleteParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsRole/delete', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 启用/禁用 POST /ums/umsRole/enableOrDisable */
export async function umsRoleEnableOrDisable({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsRoleEnableOrDisableParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsRole/enableOrDisable', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取所有角色和权限卡 用于管理所有角色使用 GET /ums/umsRole/getAll */
export async function umsRoleGetAll({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListUmsRole_>('/ums/umsRole/getAll', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取所有角色和权限卡 用于分配角色使用 GET /ums/umsRole/getAllByEnable */
export async function umsRoleGetAllByEnable({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListUmsRole_>('/ums/umsRole/getAllByEnable', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取角色详情 POST /ums/umsRole/getById */
export async function umsRoleGetById({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsRoleGetByIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultUmsRole_>('/ums/umsRole/getById', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据角色获取所有权限卡 GET /ums/umsRole/getByIds */
export async function umsRoleGetByIds({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsRoleGetByIdsParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListUmsAuthorityCard_>(
    '/ums/umsRole/getByIds',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 修改角色 POST /ums/umsRole/update */
export async function umsRoleUpdate({
  body,
  options,
}: {
  body: API.UmsRole1;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsRole/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
