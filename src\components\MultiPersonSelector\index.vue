<template>
    <div class="multi-person-selector">
        <div v-for="(row, index) in tableData" :key="index" class="person-row">
            <!-- 姓名选择区域 -->
            <div class="name-selector">
                <el-select
                    :filterable="isFilterable"
                    :disabled="isDisabled"
                    v-if="row.isInside"
                    v-model="row.selectedPerson"
                    :placeholder="outPlaceholder"
                    style="width: 100%"
                    @change="handleValueChange">
                    <el-option
                        v-for="(person, index2) in getFilteredPersonList(index)"
                        :key="index2"
                        :label="person.employeeName"
                        :value="person.id" />
                </el-select>
                <el-input
                    v-else
                    v-model:value="row.selectedPerson"
                    placeholder="输入院外人员姓名"
                    :disabled="isDisabled"
                    @change="handleValueChange"
                    class="person-input" />
            </div>

            <!-- 是否本院标签 -->
            <div class="is-Inside">是否本院</div>

            <!-- 本院/院外切换开关 -->
            <a-switch
                v-model:checked="row.isInside"
                :disabled="isDisabled"
                @change="() => handleInsideChange(row)"
                class="inside-switch" />

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <a-button
                    type="text"
                    :disabled="isDisabled || tableData.length <= 1"
                    @click="removeRow(index)"
                    class="remove-btn">
                    <template #icon>
                        <MinusOutlined />
                    </template>
                </a-button>
                <a-button type="text" :disabled="isDisabled" @click="addRow" class="add-btn">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                </a-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { UmsPerson_ } from "@/apis/types";
import { ref, watch, onMounted, computed } from "vue";
import { MinusOutlined, PlusOutlined } from "@ant-design/icons-vue";

// 定义每行数据的结构
interface TableRow {
    selectedPerson: number | string | null;
    isInside: boolean;
}

// 组件接收的 props
const props = defineProps<{
    personList: UmsPerson_[]; // 传入的人员列表
    isDisabled: boolean;
    outPlaceholder: string;
    isFilterable: boolean;
    modelValue?: (number | string)[] | string; // 添加 modelValue prop，接收数组类型
}>();

const emit = defineEmits<{
    (e: "update:modelValue", value: (number | string)[]): void;
}>();

// 存储表格数据，每一行都有姓名、是否校外的选项
const tableData = ref<TableRow[]>([{ selectedPerson: null, isInside: true }]);

// 初始化逻辑
onMounted(() => {
    // 确保初始状态下可以操作
    initializeTableData();
});

// 初始化表格数据
const initializeTableData = () => {
    if (props.modelValue && (Array.isArray(props.modelValue) ? props.modelValue.length > 0 : !!props.modelValue)) {
        const values = Array.isArray(props.modelValue) ? props.modelValue : props.modelValue.split(",").filter(Boolean);

        tableData.value = values.map((value) => ({
            selectedPerson: value,
            isInside: typeof value === "number" || /^\d+$/.test(String(value)),
        }));
    } else {
        // 确保有一个初始行
        tableData.value = [{ selectedPerson: null, isInside: true }];
    }
};

// 修复的 watch 逻辑
watch(
    () => props.modelValue,
    (newVal) => {
        if (newVal && (Array.isArray(newVal) ? newVal.length > 0 : !!newVal)) {
            const values = Array.isArray(newVal) ? newVal : newVal.split(",").filter(Boolean);

            // 避免重复设置相同的值，比较当前值和新值
            const currentValues = tableData.value
                .map((row) => row.selectedPerson)
                .filter((value) => value !== null && value !== "");

            // 只有当值不同时才更新
            if (JSON.stringify(currentValues) !== JSON.stringify(values)) {
                tableData.value = values.map((value) => ({
                    selectedPerson: value,
                    isInside: typeof value === "number" || /^\d+$/.test(String(value)),
                }));
            }
        } else if (tableData.value.length === 0) {
            // 当表格为空时，重置为初始状态
            tableData.value = [{ selectedPerson: null, isInside: true }];
        }
    },
    { deep: true }
);

// 获取过滤后的人员列表（排除已在其他行选中的人员）
const getFilteredPersonList = (currentIndex: number) => {
    if (!props.personList || props.personList.length === 0) return [];

    // 收集当前已选择的所有人员ID（排除当前行）
    const selectedPersonIds: (number | string)[] = [];
    tableData.value.forEach((row, index) => {
        // 跳过当前行和未选择的行
        if (index === currentIndex || !row.selectedPerson || !row.isInside) return;

        selectedPersonIds.push(row.selectedPerson);
    });

    // 过滤掉其他行已选的人员，但保留当前行已选的人员
    return props.personList.filter((person) => {
        return !selectedPersonIds.includes(person.id);
    });
};

const filterOption = (input: string, option: any) => {
    console.log(input, option);
    return option.label.indexOf(input) >= 0;
};

// 添加一行人员
const addRow = () => {
    // 使用解构方式添加新行，确保是响应式的
    tableData.value = [...tableData.value, { selectedPerson: null, isInside: true }];
    // 手动通知父组件值已更新
    handleValueChange();
};

// 删除当前行
const removeRow = (index: number) => {
    if (tableData.value.length > 1) {
        tableData.value.splice(index, 1);
        handleValueChange();
    }
};

// 处理"是否校外"状态切换
const handleInsideChange = (row: TableRow) => {
    // 只清除选中的人员，不影响其他操作
    row.selectedPerson = null;
    // 通知父组件值已更新
    handleValueChange();
};

// 值变化时通知父组件，不强制要求选择人员
const handleValueChange = () => {
    // 确保tableData不为空
    if (tableData.value.length === 0) {
        tableData.value.push({ selectedPerson: null, isInside: true });
    }

    const values = tableData.value
        .map((row) => row.selectedPerson)
        .filter((value) => value !== null && value !== "") as (number | string)[];

    emit("update:modelValue", values);
};
</script>

<style scoped lang="scss">
.multi-person-selector {
    .person-row {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;
        padding: 6px 0;
        max-width: 495px;

        &:last-child {
            margin-bottom: 0;
        }

        .name-selector {
            flex: 1;
            min-width: 300px;

            .person-select,
            .person-input {
                width: 276px;
                border-radius: 6px 0 0 6px;
            }
        }

        .is-Inside {
            color: rgba(0, 0, 0, 0.88);
            font-size: 14px;
            line-height: 20px;
            white-space: nowrap;
            margin: 0 8px;
        }

        .inside-switch {
            margin: 0 8px;
        }

        .action-buttons {
            display: flex;
            gap: 2px;

            .remove-btn,
            .add-btn {
                width: 32px;
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 6px;
                border: none;
                padding: 0;

                &:hover {
                    background-color: rgba(0, 0, 0, 0.04);
                }

                &:disabled {
                    color: rgba(0, 0, 0, 0.25);
                    background-color: transparent;
                }
            }
        }
    }
}
</style>
