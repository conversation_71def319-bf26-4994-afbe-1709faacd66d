/**
 * 将枚举转换为 Element Plus 表格过滤器选项
 * @param enumObj 枚举对象
 * @param enumMap 枚举映射对象
 * @returns 过滤器选项数组
 */
export const convertEnumToFilters = (enumObj: Record<string | number, any>, enumMap: Record<string | number, string>) => {
    return Object.values(enumObj)
        .filter(key => typeof key === 'number')
        .map(value => ({
            text: enumMap[value],
            value
        }));
};

/**
 * 创建过滤器配置
 * @param configs 过滤器配置对象，key为类型，value为生成函数
 * @returns 过滤器配置对象
 */
export const createFilterConfigs = <T extends Record<string | number, any>>(configs: T) => configs;

/**
 * 是否类型的过滤器选项
 */
export const booleanFilters = [
    { text: '是', value: true },
    { text: '否', value: false }
]; 