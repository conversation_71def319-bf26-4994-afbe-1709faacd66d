/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 删除 POST /nms/nmsNewsTemplate/delete */
export async function nmsNewsTemplateDelete({
  body,
  options,
}: {
  body: API.CommonDeleteDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/nms/nmsNewsTemplate/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据id获取新闻模版 GET /nms/nmsNewsTemplate/detail */
export async function nmsNewsTemplateDetail({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.nmsNewsTemplateDetailParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultNmsNewsTemplate_>(
    '/nms/nmsNewsTemplate/detail',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取数据表字段列表 GET /nms/nmsNewsTemplate/getCategoryFieldList */
export async function nmsNewsTemplateGetCategoryFieldList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.nmsNewsTemplateGetCategoryFieldListParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsCategoryTemplateValue_>(
    '/nms/nmsNewsTemplate/getCategoryFieldList',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取数据表 GET /nms/nmsNewsTemplate/getCategoryTemplate */
export async function nmsNewsTemplateGetCategoryTemplate({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsCategoryTemplate_>(
    '/nms/nmsNewsTemplate/getCategoryTemplate',
    {
      method: 'GET',
      ...(options || {}),
    }
  );
}

/** 根据大类id获取新闻模版列表 GET /nms/nmsNewsTemplate/getTemplatesByEntityId */
export async function nmsNewsTemplateGetTemplatesByEntityId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.nmsNewsTemplateGetTemplatesByEntityIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListNmsNewsTemplate_>(
    '/nms/nmsNewsTemplate/getTemplatesByEntityId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取新闻模板分页列表 POST /nms/nmsNewsTemplate/listPage */
export async function nmsNewsTemplateListPage({
  body,
  options,
}: {
  body: API.NmsNewsTemplatePageParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageNmsNewsTemplate_>(
    '/nms/nmsNewsTemplate/listPage',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 新增 POST /nms/nmsNewsTemplate/save */
export async function nmsNewsTemplateSave({
  body,
  options,
}: {
  body: API.NmsNewsTemplateSaveParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/nms/nmsNewsTemplate/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改 POST /nms/nmsNewsTemplate/update */
export async function nmsNewsTemplateUpdate({
  body,
  options,
}: {
  body: API.NmsNewsTemplateUpdateParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/nms/nmsNewsTemplate/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
