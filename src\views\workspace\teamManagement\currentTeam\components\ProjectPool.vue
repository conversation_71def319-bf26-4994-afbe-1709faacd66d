<template>
    <div class="project-pool">
        <div class="search">
            <el-input
                v-model="searchKeyword"
                style="width: 240px"
                placeholder="输入项目名称检索项目成果"
                :suffix-icon="Search" />
            <el-button color="#586FBB" @click="fetchProjectPoolData">搜索</el-button>
        </div>
        <div class="table">
            <el-table
                empty-text="暂无数据"
                show-overflow-tooltip
                :data="projectPoolData"
                border
                style="width: 100%">
                <el-table-column
                    prop="owner"
                    label="资料所属"
                    align="center"
                    :filters="personFilterList"
                    :filter-method="personFilterHandler">
                    <template #default="scope">
                        {{ getPersonName(allPersonListWithDisabled, String(scope.row.owner)) }}
                    </template>
                </el-table-column>
                <el-table-column prop="templateName" label="数据表" width="200" align="center" />
                <el-table-column prop="projectName" label="项目名称" width="130" align="center" />
                <el-table-column
                    prop="projectStatus"
                    :filters="projectStatusList"
                    label="项目状态"
                    :filter-method="projectStatusFilterHandler"
                    width="100"
                    align="center">
                    <template #default="scope">
                        <div>
                            {{ getStatusDescriptionById(scope.row.projectStatus) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="grade"
                    label="级别"
                    align="center"
                    :filters="gradeFilterList"
                    :filter-method="gradeFilterHandler">
                    <template #default="scope">
                        <div>
                            {{ getFieldName(dictList, scope.row.grade) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="projectTime"
                    label="成果完成时间"
                    align="center"
                    sortable
                    width="150"
                    :sort-method="
                        (a, b) => {
                            return a.projectTime - b.projectTime;
                        }
                    " />
                <el-table-column prop="proofMaterial" label="佐证材料" align="center" min-width="150">
                    <template #default="scope">
                        <FileIconDisplay v-if="scope.row.proofMaterial" :fileUrls="scope.row.proofMaterial || ''" />
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" align="center" min-width="220">
                    <template #default="scope">
                        <el-button
                            :disabled="scope.row.isExist === 1 || scope.row.isExist === 2"
                            type="primary"
                            style="padding: 0 15px"
                            class="SystemStytle-custom-round-button"
                            color="#E5F0FD"
                            @click="requestAchievement(scope.row)">
                            <span v-if="scope.row.isExist === 0">申请成果</span>
                            <span v-else-if="scope.row.isExist === 1">申请中</span>
                            <span v-else>已申请</span>
                        </el-button>
                        <el-button
                            type="primary"
                            style="padding: 0 15px"
                            class="SystemStytle-custom-round-button"
                            color="#E5F0FD"
                            >查看详情</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="pagination">
            <el-pagination
                size="small"
                background
                layout="prev, pager, next"
                :current-page="poolPagination.currentPage"
                :page-size="poolPagination.pageSize"
                :total="poolPagination.total"
                @current-change="handlePoolPageChange"
                prev-text="上一页"
                next-text="下一页" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import { cmsCategoryRowGetByProjectId } from "@/apis/cmsCategoryRowController";
import { tmsProjectRequestRequestShared } from "@/apis/tmsProjectRequestController";
import { useDict } from "@/hooks/useDict";
import { getFieldName, getPersonName } from "@/utils/getNames";
import { usePersons } from "@/hooks/usePersons";
import { useProjectStatusFilter, useGradeFilter, usePersonFilter } from "@/utils/filterUtils";
import FileIconDisplay from "@/components/FileIconDisplay/index.vue";

const { allPersonListWithDisabled } = usePersons(true);

// 属性定义
const props = defineProps<{
    projectId: number;
}>();

// 字典列表
const { dictList } = useDict();

// 使用提取出的筛选功能
const { projectStatusList, projectStatusFilterHandler, getStatusDescriptionById } = useProjectStatusFilter(dictList);
const { gradeFilterList, gradeFilterHandler } = useGradeFilter(dictList);

// 成果池数据、分页信息
const projectPoolData = ref([]);
const poolPagination = ref({
    currentPage: 1,
    pageSize: 5,
    total: 0,
});

// 搜索关键字
const searchKeyword = ref("");

// 使用人员筛选功能
const { personFilterList, personFilterHandler } = usePersonFilter(projectPoolData, allPersonListWithDisabled);

// 成果池发送申请
const requestAchievement = function (row: any) {
    tmsProjectRequestRequestShared({
        body: {
            groupId: row.rowId,
            info: "申请",
            participant: row.owner,
            projectId: props.projectId,
            dataType: 1,
        },
    }).then((res: any) => {
        if (res.code === 200) {
            ElMessageBox.alert("申请已发送！请等待对方通过。", "", {
                confirmButtonText: "确定",
                type: "success",
            });
        }
        fetchProjectPoolData();
    });
};

// 获取成果池表格数据
const fetchProjectPoolData = async () => {
    if (!props.projectId) return;
    const { currentPage, pageSize } = poolPagination.value;
    const res = await cmsCategoryRowGetByProjectId({
        params: {
            name: searchKeyword.value,
            pageNum: currentPage,
            pageSize: pageSize,
            projectId: props.projectId,
        },
    });
    projectPoolData.value = res.data.records;
    poolPagination.value.total = res.data.total;
};

// 成果池分页切换事件
const handlePoolPageChange = (newPage: number) => {
    poolPagination.value.currentPage = newPage;
    fetchProjectPoolData();
};

// 暴露给父组件的方法
defineExpose({
    fetchProjectPoolData,
});

// 监听projectId变化
watch(
    () => props.projectId,
    (newVal) => {
        if (newVal) {
            fetchProjectPoolData();
        }
    },
    { immediate: true }
);

// 同时监听表格数据和字典数据
watchEffect(() => {
    if (projectPoolData.value?.length > 0 && dictList.value?.length > 0) {
        // 强制更新表格数据以触发重新渲染
        projectPoolData.value = [...projectPoolData.value];
    }
});
</script>

<style scoped lang="scss">
.per-title {
    
    font-size: 15px;
    font-weight: 700;
    line-height: 19.8px;
    letter-spacing: 0.1em;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #6b7995;
    padding-left: 15px;
    position: relative;
    margin: 10px 0;

    &::before {
        content: "";
        position: absolute;
        left: 0;
        width: 4px;
        height: 20px;
        background-color: #23346d;
    }
}

.pagination {
    display: flex;
    justify-content: end;
    margin-top: 10px;
    margin-right: 20px;
}

.project-pool {
    .search {
        display: flex;
        justify-content: end;
        gap: 15px;
    }

    .table {
        margin-top: 15px;
    }
}
</style>
