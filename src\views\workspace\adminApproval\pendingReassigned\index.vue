<template>
    <div class="pending-reassigned">
        <div class="pending-reassigned-header">
            <div>
                <el-input
                    style="width: 420px; margin-right: 10px"
                    size="large"
                    placeholder="输入 数据表名称、项目名称、参与人 检索待审核内容"
                    v-model="keyword">
                    <template #append>
                        <el-button type="primary" :icon="Search" @click="searchList" />
                    </template>
                </el-input>
            </div>
            <div v-if="isShowDetail">
                <el-button
                    color="#586FBB"
                    type="primary"
                    @click="
                        isShowDetail = false;
                        detailData = null;
                    "
                    >返回</el-button
                >
            </div>
        </div>

        <!-- 待重新赋分列表 -->
        <div
            v-if="!isShowDetail"
            class="pending-reassigned-list"
            v-infinite-scroll="loadMore"
            :infinite-scroll-distance="10"
            :infinite-scroll-disabled="loading || !hasMore">
            <div v-for="(item, index) in pendingReassignedList" :key="item.id" class="list-item">
                <!-- 项目标题 -->
                <div class="item-title">
                    {{ getProjectTitle(item) }}
                </div>

                <!-- 项目信息和操作按钮 -->
                <div class="item-content">
                    <!-- 第一列：左侧项目信息 -->
                    <div class="project-info">
                        <div>数据表名称：{{ getTemplateName(item.row?.categoryTemplateId) || "-" }}</div>
                        <div>
                            项目负责人：{{ getPersonName(allPersonListWithDisabled, String(item.row?.owner)) || "-" }}
                        </div>
                        <div v-if="item.row?.projectTime">时间：{{ item.row?.projectTime }}</div>
                        <div v-if="item.row?.projectStatus">项目状态：{{ item.row?.projectStatus }}</div>
                        <template v-for="field in item.templateValues" :key="field.id">
                            <div v-if="field.value && includeFields.includes(field.categoryPublicType)">
                                <span>{{ field.value }}：</span>
                                <span>{{
                                    getFieldName(
                                        dictList,
                                        item.values.find((item) => item.categoryTemplateValueId === field.id)?.value
                                    )
                                }}</span>
                            </div>
                        </template>
                    </div>
                    <!-- 第二列：中间快捷重新赋分区域 -->
                    <div class="middle-section">
                        <!-- 快捷重新赋分按钮（未展开状态） -->
                        <el-button
                            v-if="!item.isShowQuickScore"
                            type="primary"
                            size="default"
                            @click="showQuickScore(item)">
                            快捷重新赋分
                        </el-button>

                        <!-- 快捷重新赋分面板（展开状态） -->
                        <div v-else class="quick-score-panel">
                            <div class="score-input-area">
                                <span class="score-text">绩效得分由</span>
                                <InputWithScore :model-value="item.oldScore" />
                                <span class="score-text">变更为</span>
                                <InputWithScore v-model="item.newScore" />
                            </div>
                            <div class="score-actions">
                                <el-button @click="cancelQuickScore(item)">取消</el-button>
                                <el-button type="primary" @click="confirmQuickScore(item)">确认</el-button>
                            </div>
                        </div>
                    </div>

                    <!-- 第三列：右侧操作按钮 -->
                    <div class="action-buttons">
                        <el-button type="default" size="small" class="detail-btn" @click="showDetail(index)">
                            查看详情
                        </el-button>
                        <el-button
                            type="default"
                            size="small"
                            class="detail-btn"
                            @click="getRuleChangeDetail(item.categoryId)">
                            查看绩效规则更改
                        </el-button>
                    </div>
                </div>
            </div>

            <!-- 加载状态 -->
            <div v-if="loading" class="loading-state">
                <el-icon class="is-loading">
                    <Loading />
                </el-icon>
                <span style="margin-left: 8px">正在加载...</span>
            </div>

            <!-- 没有更多数据 -->
            <div v-if="!hasMore && pendingReassignedList.length > 0" class="no-more-state">没有更多数据了</div>

            <!-- 空状态 -->
            <div v-if="pendingReassignedList.length === 0 && !loading" class="empty-state">
                <el-empty description="暂无待重新赋分数据" />
            </div>
        </div>

        <GeneralDataDisplay v-else>
            <template #content>
                <div class="content">
                    <DataTable
                        @loadComplete="handleLoadComplete"
                        ref="dataTableRef"
                        :is-load-first="isAutoLoadFirst"
                        :search-keyword="searchKeyword"
                        :is-local-search="false"
                        @itemClick="loadDetail"
                        :load-more-func="loadMoreFunc">
                        <template #title="{ item }: { item: CmsCheck0 }">
                            <!-- 主页点击跳转后，地址栏中的id会与此处的id对应： -->
                            <!-- {{ item.id }} -->
                            <div class="title">
                                {{ getTemplateNameById(item.row?.categoryTemplateId) || "-" }}
                            </div>
                        </template>
                        <template #desc="{ item }: { item: CmsCheck0 }">
                            <div>创建日期：{{ item.createdAt }}</div>
                            <div>项目名称：{{ item.projectName }}</div>
                        </template>
                        <template #detail="{ selectedItem }">
                            <Detail @refresh="refreshAll" :selected-item="detailData" :detailLoading="detailLoading" />
                        </template>
                    </DataTable>
                </div>
            </template>
        </GeneralDataDisplay>

        <el-dialog v-model="ruleChangeDialogVisible" width="640px" :show-close="true" class="rule-change-dialog">
            <template #header>
                <div class="dialog-header">
                    <span class="dialog-title">绩效规则更改</span>
                </div>
            </template>

            <div class="dialog-content">
                <div v-for="node in traverseTree(ruleTree, '')" :key="node.id">
                    <div v-if="node.isLeaf" class="rule-change-item">
                        <!-- 路径导航 -->
                        <div class="rule-path" v-if="node.parentPath">
                            {{ node.parentPath }}
                        </div>

                        <!-- 原绩效规则 -->
                        <div class="rule-block original-rule">
                            <div class="rule-content">
                                <strong>原绩效规则：</strong><br />
                                {{ node.oldTagName === null ? "-" : node.oldTagName }}
                            </div>
                        </div>

                        <!-- 现绩效规则 -->
                        <div class="rule-block current-rule">
                            <div class="rule-content">
                                <div><strong>现绩效规则：</strong></div>
                                <div>{{ node.content }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 空状态 -->
                <div v-if="!ruleTree || !traverseTree(ruleTree, '').some((node) => node.isLeaf)" class="empty-state">
                    <el-empty description="暂无绩效规则更改信息" />
                </div>
            </div>
            <!-- <template #footer>
                <div>
                    <el-button
                        type="primary"
                        @click="$router.push('/backgroundManagement/centralSettings/performanceRuleManagement')"
                        >更改绩效规则</el-button
                    >
                </div>
            </template> -->
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import InputWithScore from "@/components/InputWithScore/index.vue";
import DataTable from "@/components/DataTable/index.vue";
import GeneralDataDisplay from "@/components/GeneralDataDisplay/index.vue";
import Detail from "./detail.vue";
import { traverseTree } from "@/utils/tree";
import { CmsCategoryRow0, CmsCategoryTemplateValue10, CmsCheck0, CmsTagNode_ } from "@/apis/types";
import { Search, Loading } from "@element-plus/icons-vue";
import { useCategoryTemplateList } from "@/hooks/useCategoryTemplate";
import { cmsTagGetTagTreeById } from "@/apis/cmsTagController";
import { cmsCheckAgree, cmsCheckGetByStatus } from "@/apis/cmsCheckController";
import { cmsCategoryTemplateValueGetValueByTemplateId } from "@/apis/cmsCategoryTemplateValueController";
import { cmsCategoryRowGetById } from "@/apis/cmsCategoryRowController";
import { usePersons } from "@/hooks/usePersons";
import { getFieldName, getPersonName } from "@/utils/getNames";
import { ElMessage } from "element-plus";
import { CheckStatus } from "@/enums/approval/checkStatus";
import { CheckTypeEnum } from "@/enums/check/CheckType";
import { useDict } from "@/hooks/useDict";
import { publicPerformanceType } from "@/models/publicPerformanceType";

// 字典列表
const { dictList } = useDict();

// 需要显示的数据项
const includeFields = [publicPerformanceType.PROJECT_PARTICIPATE, publicPerformanceType.GRADE];

// 是否展示详情
const isShowDetail = ref(false);

// 绩效规则更改弹窗
const ruleChangeDialogVisible = ref(false);

// 所有人员列表
const { allPersonListWithDisabled } = usePersons(true);

// 所有大类列表
const { templateList } = useCategoryTemplateList();

// 根据 categoryTemplateId 获取对应的templateName
const getTemplateName = (categoryTemplateId: number | undefined) => {
    if (!categoryTemplateId) return "";
    return templateList.value.find((item) => item.id === categoryTemplateId)?.templateName || "";
};

// 搜索关键字
const keyword = ref("");

// 待赋分列表
const pendingReassignedList = ref<(CmsCheck0 & { isShowQuickScore?: boolean; newScore?: number; oldScore?: number })[]>(
    []
);

// 绩效规则树
const ruleTree = ref<CmsTagNode_>();

// 分页相关状态
const currentPage = ref(1);
const pageSize = ref(10);
const hasMore = ref(true);
const loading = ref(false);

// 获取项目标题
const getProjectTitle = (item: CmsCheck0) => {
    return item.projectName || item.row?.projectName || "-";
};

// 获取项目时间
const getProjectTime = (item: CmsCheck0) => {
    // 使用 row.projectTime 字段
    return item.row?.projectTime || "";
};

// 获取项目状态
const getProjectStatus = (item: CmsCheck0) => {
    // 使用 row.projectStatus 字段
    return item.row?.projectStatus || "-";
};

// 搜索列表
const searchList = () => {
    // 重置分页状态
    currentPage.value = 1;
    hasMore.value = true;
    pendingReassignedList.value = [];
    getPendingReassignedList(true);
};

// 获取待赋分列表
const getPendingReassignedList = (isReset = false) => {
    if (loading.value) return;

    loading.value = true;

    cmsCheckGetByStatus({
        params: {
            name: keyword.value,
            pageNum: currentPage.value,
            pageSize: pageSize.value,
            status: [CheckStatus.WAIT_CHECK],
            types: [CheckTypeEnum.REASSIGN],
        },
    })
        .then((res) => {
            const newItems = res.data.records.map((item) => ({
                ...item,
                isShowQuickScore: false,
                newScore: 0,
                oldScore: item.row?.rowScore || 0,
            }));

            if (isReset || currentPage.value === 1) {
                pendingReassignedList.value = newItems;
            } else {
                pendingReassignedList.value.push(...newItems);
            }

            // 检查是否还有更多数据
            hasMore.value = newItems.length === pageSize.value;

            loading.value = false;
        })
        .catch(() => {
            loading.value = false;
        });
};

// 加载更多数据
const loadMore = () => {
    if (!hasMore.value || loading.value) return;

    currentPage.value++;
    getPendingReassignedList();
};

// 显示快捷赋分面板
const showQuickScore = (item: any) => {
    item.isShowQuickScore = true;
    item.oldScore = item.row?.rowScore || 0;
    item.newScore = item.row?.rowScore || 0;
};

// 取消快捷赋分
const cancelQuickScore = (item: any) => {
    item.isShowQuickScore = false;
    item.newScore = 0;
    item.oldScore = 0;
};

// 确认快捷赋分
const confirmQuickScore = (item: any) => {
    if (item.newScore === undefined || item.newScore < 0) {
        ElMessage.warning("请输入有效的分数");
        return;
    }

    cmsCheckAgree({
        body: {
            id: item.rowId,
            score: item.newScore,
        },
    }).then((res) => {
        if (res.code === 200) {
            ElMessage.success("重新赋分成功");
            item.isShowQuickScore = false;
            // 重置列表
            currentPage.value = 1;
            hasMore.value = true;
            pendingReassignedList.value = [];
            getPendingReassignedList(true);
        } else {
            ElMessage.error(res.message);
        }
    });
};

// 获取绩效规则更改详情
const getRuleChangeDetail = (id: number) => {
    // console.log(id);
    if (!id) return;
    ruleChangeDialogVisible.value = true;
    cmsTagGetTagTreeById({
        params: {
            id,
        },
    }).then((res) => {
        ruleTree.value = res.data;
    });
};

// 展示详情
const showDetail = (index: number) => {
    isShowDetail.value = true;
    detailLoading.value = true;
    setTimeout(() => {
        dataTableRef.value.simulateClick(index);
        detailLoading.value = false;
    }, 500);
};

//#region ------------ 详情页 ------------

const route = useRoute();

// 存储当前选中项详情
const detailData = ref<
    | (CmsCategoryRow0 & {
          isPrincipal?: number;
          fieldsList?: CmsCategoryTemplateValue10[];
          isRepeat?: boolean;
          isRead?: number;
      })
    | null
>(null);
// 搜索关键字
const searchKeyword = ref("");
// 存储当前选中项的数据项列表
const fieldsList = ref([]);
// 存储dataTable组件实例
const dataTableRef = ref();

const { getTemplateNameById } = useCategoryTemplateList();
// 详情加载中
const detailLoading = ref(false);

// 获取详情
const loadDetail = (item) => {
    detailLoading.value = true;

    Promise.all([
        cmsCategoryTemplateValueGetValueByTemplateId({ params: { id: item.row.categoryTemplateId } }),
        cmsCategoryRowGetById({ params: { id: item.row.id } }),
    ]).then(([res1, res2]) => {
        fieldsList.value = res1.data;
        detailData.value = res2.data;
        detailData.value.isPrincipal = item.isPrincipal;
        detailData.value.fieldsList = fieldsList.value;
        detailData.value.isRepeat = item.isRepeat;
        detailData.value.isRead = item.isRead;
        detailLoading.value = false;
    });
};

// 处理搜索
const handleSearch = () => {
    if (dataTableRef.value) {
        dataTableRef.value.search();
    }
};

// 刷新待审核列表
const refreshAll = () => {
    if (dataTableRef.value) {
        dataTableRef.value.refreshAll();
        detailData.value = null;
    }
};

const loadMoreFunc = (pageNum: number, pageSize: number, searchKeyword?: string) => {
    return cmsCheckGetByStatus({
        params: {
            pageNum,
            pageSize,
            status: [CheckStatus.WAIT_CHECK],
            types: [CheckTypeEnum.REASSIGN],
            name: searchKeyword.trim(),
        },
    });
};

const isAutoLoadFirst = computed(() => {
    return !(route.query.queryListItemId && route.query.queryIndex);
});

// type: 审核待办：0，录入待办-待补充：1，录入待办-审核退回：2，项目团队待办：3
// 数据加载完成后的处理
const handleLoadComplete = (data) => {
    const queryIndex = Number(route.query.queryIndex);

    // 如果有索引参数，模拟点击对应索引的项目
    if (queryIndex) {
        // 延迟一点执行，确保列表已经完全渲染
        nextTick(() => {
            dataTableRef.value.simulateClick(Number(queryIndex));
        });
    }
};
//#endregion ------------ 详情页 ------------

// 刷新待审核列表
// const refreshAll = () => {
//     if (dataTableRef.value) {
//         dataTableRef.value.refreshAll();
//         detailData.value = null;
//     }
// };

onMounted(() => {
    getPendingReassignedList(true);
});
</script>

<style scoped lang="scss">
.content {
    .title {
        font-weight: bold;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
/* 绩效规则更改弹窗样式 */
:deep(.rule-change-dialog) {
    .el-dialog {
        border-radius: 8px;
        box-shadow:
            0px 9px 28px 8px rgba(0, 0, 0, 0.05),
            0px 3px 6px -4px rgba(0, 0, 0, 0.12),
            0px 6px 16px 0px rgba(0, 0, 0, 0.08);

        .el-dialog__header {
            padding: 20px 20px 0 20px;
            border-bottom: none;
        }

        .el-dialog__body {
            padding: 0 20px 20px 20px;
        }

        .el-dialog__close {
            font-size: 16px;
            color: #909399;

            &:hover {
                color: #606266;
            }
        }
    }
}

.dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .dialog-title {
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        color: rgba(0, 0, 0, 0.88);
    }
}

.dialog-content {
    .rule-change-item {
        margin-bottom: 24px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .rule-path {
        color: #697b94;
        font-size: 14px;
        line-height: 22px;
        margin-bottom: 16px;
    }

    .rule-block {
        background: #f5f7fa;
        border-radius: 6px;
        padding: 16px;
        margin-bottom: 12px;

        &:last-child {
            margin-bottom: 0;
        }

        .rule-content {
            color: rgba(0, 0, 0, 0.65);
            font-size: 14px;
            font-style: normal;
            line-height: 22px;
            min-height: 44px;

            strong {
                color: rgba(0, 0, 0, 0.85);
                font-weight: 500;
            }
        }
    }

    .original-rule {
        /* 可以添加特殊样式来区分原规则 */
    }

    .current-rule {
        /* 可以添加特殊样式来区分现规则 */
    }

    .empty-state {
        padding: 40px 0;
        text-align: center;
    }
}
.reassigned-score {
    border: 1px solid #afafaf;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .score-left {
        display: flex;
        align-items: center;
        gap: 5px;
        padding: 0 30px;
        height: 90px;
        border-right: 1px solid #afafaf;
    }
    .score-right {
        display: flex;
        align-items: center;
        padding: 0 30px;
    }
}

.pending-reassigned {
    .pending-reassigned-header {
        padding: 16px 32px;
        background: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #ffffff;
        border-radius: 6px;
    }

    .pending-reassigned-list {
        height: calc(100vh - 300px);
        overflow-y: auto;
        background: #fff;
        margin-top: 12px;

        .list-item {
            border-bottom: 1px solid #a2abbe;
            padding: 20px 24px;
            overflow-x: auto;

            .item-title {
                color: #23346d;
                font-size: 20px;
                font-weight: 700;
                line-height: 31.4px;
                letter-spacing: 2px;
                margin-bottom: 15px;
            }

            .item-content {
                display: flex;
                gap: 24px;
                align-items: stretch;
                justify-content: space-between;

                // 第一列：项目信息
                .project-info {
                    flex: 0 0 400px;
                    color: #6c7cb2;
                    font-size: 15px;
                    line-height: 24px;
                    letter-spacing: 1.5px;
                    display: flex;
                    flex-direction: column;
                    gap: 4px;
                }

                // 第二列：中间快捷赋分区域
                .middle-section {
                    flex: 1;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .quick-score-panel {
                        background: #f8f9fc;
                        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.05);
                        border: 1px solid #d0d6e2;
                        border-radius: 8px;
                        padding: 21px 24px;
                        display: flex;
                        gap: 8px;
                        align-items: center;

                        .score-input-area {
                            background: #ebf1ff;
                            border-radius: 6px;
                            padding: 12px 16px;
                            display: flex;
                            gap: 8px;
                            align-items: center;

                            .score-text {
                                color: #1677ff;
                                font-weight: 600;
                                line-height: 21px;
                                white-space: nowrap;
                            }
                        }

                        .score-actions {
                            display: flex;
                            .el-button {
                                min-width: 64px;
                            }
                        }
                    }
                }

                // 第三列：右侧操作按钮
                .action-buttons {
                    flex: 0 0 auto;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .el-button {
                        min-width: 125px;

                        &.detail-btn {
                            background: #e5f0fd;
                            border: 1px solid #6c90bf;
                            border-radius: 16px;
                            color: #38587d;
                            font-size: 14px;
                            line-height: 22px;
                            letter-spacing: 1.4px;
                            min-width: 93px;

                            &:last-child {
                                min-width: 153px;
                            }
                        }
                    }
                }
            }
        }

        .loading-state {
            padding: 20px;
            text-align: center;
            color: #606266;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;

            .is-loading {
                animation: rotating 2s linear infinite;
            }
        }

        @keyframes rotating {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        .no-more-state {
            padding: 20px;
            text-align: center;
            color: #909399;
            font-size: 14px;
            border-top: 1px solid #e4e7ed;
            background: #f8f9fa;
        }

        .empty-state {
            padding: 40px;
            text-align: center;
        }
    }
}
</style>
