import { DmsDict_, UmsPerson_ } from "@/apis";


// 获取字典名称的方法
export const getFieldName = (dictList:DmsDict_[], fieldId: string | undefined) => {

    // 如果输入是逗号分隔的字符串，先转换成数组
    if (typeof fieldId === 'string' && fieldId.includes(',')) {
        const fieldArray = fieldId.split(',');
        return fieldArray.map(item1 => {
            return dictList.find((item) => Number(item.id) === Number(item1))?.name || '-';
        }).join(",");
    }
    if (!fieldId || !dictList || dictList.length === 0) return '-';
    return dictList.find((item) => Number(item.id) === Number(fieldId))?.name || '-';
};

// 获取人员姓名
export const getPersonName = (allPersonList: UmsPerson_[], persons: string) => {
    if (!persons) return "-";

    // 如果输入是逗号分隔的字符串，先转换成数组
    if (typeof persons === 'string' && persons.includes(',')) {
        const personArray = persons.split(',');
        return personArray.map(item => {
            // 判断是否为数字（院内人员ID）
            const isId = /^\d+$/.test(item.trim());
            if (isId) {
                // 在allPersonList中查找对应的人员
                const person = allPersonList.find((p) => Number(p.id) === Number(item.trim()));
                return person ? person.employeeName : item.trim();
            }
            // 如果不是ID，则为院外人员，直接返回
            return item.trim();
        }).join(",");
    }

    if(typeof persons === 'string' && !persons.includes(',')){
        const person = allPersonList.find((p) => Number(p.id) === Number(persons));
        return person ? person.employeeName : persons;
    }

    // 判断是否为数组
    const isArray = Array.isArray(persons);
    if (isArray) {
        // 如果是数组，则返回数组中所有人员的姓名
        return persons.map((id) => {
            const person = allPersonList.find((p) => Number(p.id) === Number(id));
            return person ? person.employeeName : id;
        }).join(",");
    }

    // 判断是否为数字（院内人员ID）
    const isId = /^\d+$/.test(persons);

    if (isId) {
        // 在allPersonList中查找对应的人员
        const person = allPersonList.find((p) => Number(p.id) === Number(persons));
        return person ? person.employeeName : persons;
    }

    // 如果不是ID，则直接返回（院外人员）
    return persons;
};