<template>
    <div class="file-icon-container">
        <template v-if="isAllImages">
            <div class="icon-wrapper">
                <span v-for="url in fileList">
                    <el-image
                        hide-on-click-modal
                        style="width: 40px; height: 40px; margin-right: 20px"
                        :key="url"
                        :src="getFullUrl(url)"
                        :preview-src-list="previewSrcList"
                        fit="cover"
                        :zoom-rate="1.2"
                        :max-scale="7"
                        :min-scale="0.2"
                        preview-teleported>
                        <template #toolbar="{ actions, prev, next, reset, activeIndex }">
                            <el-icon @click="prev"><DArrowLeft /></el-icon>
                            <el-icon @click="next"><DArrowRight /></el-icon>
                            <el-icon @click="actions('zoomOut')"><ZoomOut /></el-icon>
                            <el-icon @click="actions('zoomIn', { enableTransition: false, zoomRate: 2 })">
                                <ZoomIn />
                            </el-icon>
                            <el-icon @click="actions('clockwise', { rotateDeg: 180, enableTransition: false })">
                                <RefreshRight />
                            </el-icon>
                            <el-icon @click="actions('anticlockwise')"><RefreshLeft /></el-icon>
                            <el-icon @click="reset"><Refresh /></el-icon>
                            <el-icon @click="handlePreviewDownload(previewSrcList[activeIndex])"><Download /></el-icon>
                        </template>
                    </el-image>
                </span>
            </div>
        </template>
        <template v-else>
            <div v-for="url in fileList" :key="url" class="icon-wrapper" @click="handleClick(url)">
                <img :src="getFileIcon(url)" alt="文件" class="file-icon" />
            </div>
        </template>
    </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import ExcelIcon from "@/assets/Excel.png";
import WordIcon from "@/assets/word.png";
import PictureIcon from "@/assets/picture.png";
import ZipIcon from "@/assets/zip.png";
import { downloadFile } from "@/utils/fileDownload";
import { filesBaseUrl } from "@/utils/filesBaseUrl";
import {
    DArrowLeft,
    DArrowRight,
    ZoomOut,
    ZoomIn,
    RefreshRight,
    RefreshLeft,
    Refresh,
    Download,
} from "@element-plus/icons-vue";

const props = defineProps<{
    fileUrls: string;
    downloadable?: boolean;
}>();

const fileList = computed(() => {
    return props.fileUrls.split(",").filter((url) => url.trim());
});

// 判断是否所有文件都是图片
const isAllImages = computed(() => {
    return fileList.value.every((url) => {
        const extension = url.split(".").pop()?.toLowerCase() || "";
        return ["jpg", "jpeg", "png", "gif", "bmp"].includes(extension);
    });
});

// 获取完整的图片URL
const getFullUrl = (url: string) => {
    return `${filesBaseUrl}${url}`;
};

// 获取预览图片列表
const previewSrcList = computed(() => {
    return fileList.value.map((url) => getFullUrl(url));
});

// 处理预览工具栏中的下载
const handlePreviewDownload = (url: string) => {
    // 将完整URL转换为相对路径
    const relativeUrl = url.replace(filesBaseUrl, "");
    const fileName = relativeUrl.split("/").pop()?.substring(32) || "file";
    downloadFile(relativeUrl, fileName);
};

function getFileIcon(url: string): string {
    // 从URL中提取文件扩展名
    const extension = url.split(".").pop()?.toLowerCase() || "";

    // 根据文件扩展名返回对应的图标
    switch (extension) {
        case "xlsx":
        case "xls":
        case "csv":
            return ExcelIcon;
        case "doc":
        case "docx":
            return WordIcon;
        case "jpg":
        case "jpeg":
        case "png":
        case "gif":
        case "bmp":
            return PictureIcon;
        default:
            return ZipIcon;
    }
}

const handleClick = async (url: string) => {
    if (!props.downloadable) return;

    const fileName = url.split("/").pop()?.substring(32) || "file";
    await downloadFile(url, fileName);
};
</script>

<style scoped lang="scss">
// 单元格样式
// 暂时注释掉，此处影响了绩效页面的显示
// :global(.el-table__cell) {
//     position: static !important; // 解决el-image 和 el-table冲突层级冲突问题
// }

.file-icon-container {
    display: inline-flex;
    gap: 8px;
}

.icon-wrapper {
    img:hover {
        cursor: v-bind("downloadable ? 'pointer' : 'default'");
        transition: opacity 0.3s;
        opacity: v-bind("downloadable ? 0.8 : 1");
    }

    span:hover {
        transition: opacity 0.3s;
        opacity: v-bind("downloadable ? 0.8 : 1");
    }
}

.file-icon {
    width: 30px;
    height: 30px;
}

.preview-image {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    object-fit: cover;
}
</style>
