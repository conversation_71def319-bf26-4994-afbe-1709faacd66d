/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 根据id删除绩效得分 POST /pms/pmsScore/delete/${param0} */
export async function pmsScoreDeleteId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.pmsScoreDeleteidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultVoid_>(`/pms/pmsScore/delete/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 导出所选用户的绩效得分列表 POST /pms/pmsScore/export/getAllScoreListByYear */
export async function pmsScoreExportGetAllScoreListByYear({
  body,
  options,
}: {
  body: API.ExportScoreListDTO;
  options?: { [key: string]: unknown };
}) {
  return request<API.Resource>('/pms/pmsScore/export/getAllScoreListByYear', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询所有用户的绩效得分列表 GET /pms/pmsScore/getAllScoreListByYear */
export async function pmsScoreGetAllScoreListByYear({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.pmsScoreGetAllScoreListByYearParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListPmsScoreDTO_>(
    '/pms/pmsScore/getAllScoreListByYear',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 查询所有用户的绩效得分分页列表 GET /pms/pmsScore/getAllScoreListByYearPage */
export async function pmsScoreGetAllScoreListByYearPage({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.pmsScoreGetAllScoreListByYearPageParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPagePmsScoreDTO_>(
    '/pms/pmsScore/getAllScoreListByYearPage',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 查询我的绩效各指标总分 GET /pms/pmsScore/getMyScoreTotal */
export async function pmsScoreGetMyScoreTotal({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.pmsScoreGetMyScoreTotalParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListPmsScoreTotalDto_>(
    '/pms/pmsScore/getMyScoreTotal',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据年份查询当前用户的绩效得分列表 GET /pms/pmsScore/getScoreListByYear */
export async function pmsScoreGetScoreListByYear({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.pmsScoreGetScoreListByYearParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListPmsScore_>(
    '/pms/pmsScore/getScoreListByYear',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据年份查询指定用户的绩效得分列表 GET /pms/pmsScore/getScoreListByYearAndPersonId */
export async function pmsScoreGetScoreListByYearAndPersonId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.pmsScoreGetScoreListByYearAndPersonIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListPmsScore_>(
    '/pms/pmsScore/getScoreListByYearAndPersonId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 查询指定用户绩效各指标总分 GET /pms/pmsScore/getScoreTotalByPersonId */
export async function pmsScoreGetScoreTotalByPersonId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.pmsScoreGetScoreTotalByPersonIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListPmsScoreTotalDto_>(
    '/pms/pmsScore/getScoreTotalByPersonId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据年份查询当前用户的绩效得分标签树 GET /pms/pmsScore/getScoreTreeByYear */
export async function pmsScoreGetScoreTreeByYear({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.pmsScoreGetScoreTreeByYearParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsTagNode_>(
    '/pms/pmsScore/getScoreTreeByYear',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据年份查询指定用户的绩效得分标签树 GET /pms/pmsScore/getScoreTreeByYearAndPersonId */
export async function pmsScoreGetScoreTreeByYearAndPersonId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.pmsScoreGetScoreTreeByYearAndPersonIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsTagNode_>(
    '/pms/pmsScore/getScoreTreeByYearAndPersonId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 查询当前专业用户的绩效得分列表 GET /pms/pmsScore/major/getAllScoreListByYear */
export async function pmsScoreMajorGetAllScoreListByYear({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.pmsScoreMajorGetAllScoreListByYearParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListPmsScoreDTO_>(
    '/pms/pmsScore/major/getAllScoreListByYear',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 查询本专业指定用户绩效各指标总分 GET /pms/pmsScore/major/getScoreTotalByPersonIdAndDept */
export async function pmsScoreMajorGetScoreTotalByPersonIdAndDept({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.pmsScoreMajorGetScoreTotalByPersonIdAndDeptParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListPmsScoreTotalDto_>(
    '/pms/pmsScore/major/getScoreTotalByPersonIdAndDept',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据年份查询本专业指定用户的绩效得分标签树 GET /pms/pmsScore/major/getScoreTreeByYearAndPersonId */
export async function pmsScoreMajorGetScoreTreeByYearAndPersonId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.pmsScoreMajorGetScoreTreeByYearAndPersonIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsTagNode_>(
    '/pms/pmsScore/major/getScoreTreeByYearAndPersonId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 新增或修改 POST /pms/pmsScore/saveOrUpdate */
export async function pmsScoreSaveOrUpdate({
  body,
  options,
}: {
  body: API.PmsScoreParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/pms/pmsScore/saveOrUpdate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
