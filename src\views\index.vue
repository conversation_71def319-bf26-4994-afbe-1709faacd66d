<template>
    <div id="wrapper">
        <div class="banner-background"></div>
        <div class="top-nav">
            <div class="nav-center">
                <template v-for="(item, index) in topbarRouters" :key="index">
                    <div class="menu-item" @click="toggleSubMenu(index)">
                        <span>{{ item.title }}</span>
                        <el-icon class="dropdown-icon">
                            <ArrowDown />
                        </el-icon>
                        <transition name="expand">
                            <div v-if="expandedIndex === index" class="submenu" v-click-outside="closeOrder">
                                <div v-for="(subItem, subIndex) in item.children" :key="subIndex" class="submenu-item">
                                    <RouterLink :to="subItem.path">
                                        {{ subItem.title }}
                                    </RouterLink>
                                </div>
                            </div>
                        </transition>
                    </div>
                </template>
            </div>
            <div class="nav-right">
                <div>
                    <el-button type="text" @click="logout" style="color: #fff">退出登录</el-button>
                </div>
                <router-link to="/index">
                    <div class="icon-button">
                        <el-icon size="24" color="#ffffff">
                            <HomeFilled />
                        </el-icon>
                    </div>
                </router-link>
                <Notification class="top-notification" />
                <div class="icon-button">
                    <el-icon size="24" color="#ffffff">
                        <User />
                    </el-icon>
                </div>
            </div>
        </div>
        <div class="banner">
            <div class="banner-top">
                <div class="top-left">
                    <div class="news-icon">
                        <img src="@/assets/fileIcon.png" alt="news" />
                    </div>
                    <span>新闻动态</span>
                </div>
                <div class="top-right">
                    <el-icon size="24" color="#5c6474" style="margin-right: 8px; flex-shrink: 0">
                        <Medal />
                    </el-icon>
                    <el-carousel
                        height="37px"
                        direction="vertical"
                        :autoplay="true"
                        :interval="3000"
                        indicator-position="none"
                        loop
                        class="brief-news-carousel"
                        style="flex-grow: 1; overflow: hidden">
                        <el-carousel-item v-for="(item, index) in newsLetterList" :key="item.id || index">
                            <div class="brief-news-item-text">
                                {{ item.title }}
                            </div>
                        </el-carousel-item>
                    </el-carousel>
                    <div class="more-text"  @click="goNews">更多简讯 ></div>
                </div>
            </div>
            <div class="banner-content">
                <el-carousel height="270px">
                    <!-- inNewsList -->
                    <el-carousel-item v-for="item in newsList" :key="item.id" arrow="always">
                        <div class="carousel-item">
                            <div class="title">{{ item.title }}</div>
                            <div class="con">
                                {{ extractTextFromHtml(item.content) }}
                            </div>
                            <div class="btns">
                                <el-button type="primary" @click="goNewsDetail(item.id)">查看详情</el-button>
                                <div class="more-text" @click="goNews">更多新闻 ></div>
                            </div>
                        </div>
                    </el-carousel-item>
                </el-carousel>
            </div>
        </div>
        <div class="todo-list">
            <TodoList v-if="hasCheckPermission" type="approval" :todos="allTodos" :count="allTodosCount" />
            <TodoList v-if="hasEntryPermission" type="entry" :todos="allTodos" :count="allTodosCount" />
            <TodoList type="team" :todos="allTodos" :count="allTodosCount" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ElMessage, ClickOutside as vClickOutside } from "element-plus";
import { ArrowRight, HomeFilled } from "@element-plus/icons-vue";
import TodoList from "@/components/toDoList/index.vue";
import Notification from "@/components/Notification/index.vue";
import { ref, computed } from "vue";
import usePermissionStore from "@/store/modules/permission";
import useUserStore from "@/store/modules/user";
import { useRouter } from "vue-router";
import { cmsCategoryRowGetTodo } from "@/apis/cmsCategoryRowController";
import { TodoResult } from "@/apis/types";
import { nmsNewsListBrief, nmsNewsListPublish } from "@/apis/nmsNewsController";
import { PermissionEnum } from "@/enums/roles/authorCards";
import auth from "@/plugins/auth";
import { extractTextFromHtml } from "@/utils/parseHtml";

const permissionStore = usePermissionStore();
const router = useRouter();

const topbarRouters = computed(() => permissionStore.topbarRouters);

// 当前展开的菜单索引
const expandedIndex = ref<number | null>(null);

// 切换子菜单的展开/收起状态
const toggleSubMenu = (index: number) => {
    expandedIndex.value = expandedIndex.value === index ? null : index;
};

// 点击其他空白区域收起折叠子菜单
const closeOrder = () => {
    expandedIndex.value = null;
};



const entryTodos = ref<TodoResult[]>([]);
const entryTodosCount = ref<number>(0);

const approvalTodos = ref<TodoResult[]>([]);
const approvalTodosCount = ref<number>(0);

const teamTodos = ref<TodoResult[]>([]);
const teamTodosCount = ref<number>(0);

// 所有待办事项数据
const allTodos = ref<TodoResult[]>([]);
const allTodosCount = ref<number>(0);

// 是否拥有审核权限
const hasCheckPermission = computed(() => {
    return (
        auth.hasPermi(PermissionEnum.CHECK_PERMISSION) ||
        auth.hasPermi(PermissionEnum.ADMIN_PERMISSION) ||
        auth.hasPermi(PermissionEnum.NEWS_ADMIN)
    );
});

// 是否拥有录入权限
const hasEntryPermission = computed(() => {
    return (
        auth.hasPermi(PermissionEnum.ADMIN_ENTRY) ||
        auth.hasPermi(PermissionEnum.TEACHER_ENTRY) ||
        auth.hasPermi(PermissionEnum.ADMIN_PERMISSION)
    );
});

// 退出登录
const logout = () => {
    useUserStore()
        .logOut()
        .then(() => {
            ElMessage.success("退出登录成功！");
            router.push("/login");
        })
        .catch((error) => {
            console.error(error);
        });
};

const goNews = () => {
    console.log(123);
    router.push("/newsAndBrief/collgeNews");
};

const goNewsDetail = (id: number) => {
    router.push({
        path: "/newsDetail",
        query: { id },
    });
};

const newsList = ref([]);

const newsLetterList = ref([]);

function fetchNews() {
    nmsNewsListPublish({
        params: {
            pageNum: 1,
            pageSize: 10,
        },
    }).then((res) => {
        newsList.value = res.data.records;
    });
}

function fetchNewsLetter() {
    nmsNewsListBrief({
        params: {
            pageNum: 1,
            pageSize: 10,
        },
    }).then((res) => {
        newsLetterList.value = res.data.records;
    });
}

fetchNews();
fetchNewsLetter();

onMounted(() => {
    // 获取所有类型的待办事项
    fetchAllTodos();
});

// 获取所有待办事项
const fetchAllTodos = async () => {
    try {
        // 获取所有类型的待办事项 (0, 1, 2, 3, 4)
        const todoTypes = [0, 1, 2, 3, 4];
        let allTodosList = [];
        let totalCount = 0;

        for (const type of todoTypes) {
            try {
                const res = await cmsCategoryRowGetTodo({
                    params: {
                        pageNum: 1,
                        pageSize: 10, // 增加每次获取的数量
                        type: type,
                    },
                });

                if (res.data && res.data.records) {
                    allTodosList.push(...res.data.records);
                    totalCount += res.data.total || 0;
                }
            } catch (error) {
                console.error(`获取type ${type}的待办事项失败:`, error);
            }
        }

        allTodos.value = allTodosList;
        allTodosCount.value = totalCount;

        // 为了兼容性，保留原有的分类数据
        approvalTodos.value = allTodosList.filter((todo) => [0, 4].includes(todo.type));
        approvalTodosCount.value = approvalTodos.value.length;

        entryTodos.value = allTodosList.filter((todo) => [1, 2].includes(todo.type));
        entryTodosCount.value = entryTodos.value.length;

        teamTodos.value = allTodosList.filter((todo) => todo.type === 3);
        teamTodosCount.value = teamTodos.value.length;
    } catch (error) {
        console.error("获取待办事项失败:", error);
    }
};
</script>

<style scoped lang="scss">
.more-text {
    display: flex;
    padding: 0px 12px;
    gap: 8px;
    justify-content: center;
    align-items: center;
    background: #ffffff;
    box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.02);
    border: 1px solid #4096ff;
    border-radius: 12px;
    color: #4096ff;
    font-size: 14px;
    line-height: 22px;
    cursor: pointer;
}

#wrapper {
    overflow: hidden;
    background: #f5f7ff;
    min-height: 100vh;

    .banner-background {
        position: absolute;
        z-index: 1;
        background: #b4c4dc;
        height: 320px;
        width: 100%;
    }

    .top-nav {
        position: relative;
        display: flex;
        z-index: 3;
        justify-content: space-between;
        align-items: center;
        background: #334155;
        width: 100%;
        height: 64px;
        padding: 0 32px;
        font-size: 16px;
        font-weight: 400;
        border-bottom: 1px solid rgba(255, 255, 255, 0.06);

        .nav-center {
            display: flex;
            align-items: center;
            flex: 1;
            justify-content: end;
            gap: 4px;
            margin-right: 80px;

            .menu-item {
                position: relative;
                display: flex;
                align-items: center;
                color: #ffffff;
                cursor: pointer;
                padding: 17px 16px;
                gap: 8px;
                letter-spacing: 2px;
                line-height: 30px;
                transition: background-color 0.3s ease;

                &:hover {
                    background-color: rgba(255, 255, 255, 0.1);
                }

                .dropdown-icon {
                    font-size: 12px;
                }

                .submenu {
                    position: absolute;
                    top: 100%;
                    left: 0;
                    min-width: 160px;
                    background-color: #1e293b;
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 6px;
                    margin-top: 4px;
                    overflow: hidden;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    z-index: 100;

                    .submenu-item {
                        color: rgba(255, 255, 255, 0.8);
                        font-size: 14px;
                        cursor: pointer;
                        transition: background-color 0.2s ease;

                        a {
                            display: block;
                            color: inherit;
                            text-decoration: none;
                            padding: 12px 16px;
                            line-height: 20px;
                        }

                        &:hover {
                            background-color: rgba(255, 255, 255, 0.1);
                            color: #ffffff;
                        }
                    }
                }
            }

            .nav-separator {
                display: flex;
                align-items: center;
                color: #ffffff;
                margin: 0 4px;
            }
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 32px;

            .top-notification {
                // 通知组件的样式
            }

            .icon-button {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 40px;
                height: 40px;
                background: rgba(255, 255, 255, 0.25);
                border-radius: 20px;
                cursor: pointer;
                transition: background-color 0.3s ease;

                &:hover {
                    background: rgba(255, 255, 255, 0.35);
                }
            }
        }
    }

    .banner {
        position: relative;
        z-index: 2;
        background: #fff;
        width: 90%;
        margin: 70px auto;
        border-radius: 12px;

        .banner-top {
            display: flex;
            height: 56px;

            .top-left {
                display: flex;
                align-items: center;
                justify-content: center;
                background: #6e88ac;
                width: 187px;
                color: #fff;
                font-weight: bolder;
                font-size: 18px;
                border-radius: 12px 0 0 0;

                .news-icon {
                    img {
                        width: 24px;
                        height: 24px;
                        margin-right: 18.5px;
                    }
                }
            }

            .top-right {
                display: flex;
                background: rgba(220, 236, 244, 0.85);

                align-items: center;
                justify-content: space-between;
                padding: 14px 92px 14px 24px;
                flex: 1;
                color: #24346c;
                border-radius: 0 12px 0 0;

                .top-title {
                    display: flex;
                    // height: 37px;
                    // width: 37px;
                }
            }
        }

        .banner-content {
            .carousel-item {
                // background: #ccc;
                padding: 50px;

                .title {
                    color: #23346d;
                    font-size: 24px;
                    font-weight: 700;
                }

                .con {
                    margin: 20px 0;
                    color: #6c7cb2;
                    font-size: 15px;
                    font-weight: 400;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 5;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .btns {
                    display: flex;
                    gap: 12px;
                    align-items: center;
                    justify-content: space-between;
                }
            }
        }
    }

    .todo-list {
        width: 90%;
        margin: 0 auto;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        gap: 16px;
    }
}

.brief-news-item-text {
    display: flex;
    align-items: center;
    height: 100%;
    color: #24346c;
    font-size: 1em;
    line-height: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 展开动画 */
.expand-enter-active,
.expand-leave-active {
    transition: opacity 0.5s ease;
}

.expand-enter-from,
.expand-leave-to {
    opacity: 0;
}
</style>
