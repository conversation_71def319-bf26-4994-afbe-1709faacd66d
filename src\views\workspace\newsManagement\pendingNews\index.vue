<template>
    <div class="news-draft">
        <GeneralDataDisplay>
            <template #top>
                <div class="top">
                    <div>
                        <el-input
                            style="width: 300px"
                            v-model="keyword"
                            placeholder="输入关键字检索新闻"
                            @keyup.enter="search"
                            clearable />
                        <el-button type="primary" @click="search">搜索</el-button>
                    </div>
                </div>
            </template>

            <template #content>
                <div class="content">
                    <DataTable
                        @loadComplete="handleLoadComplete"
                        :is-load-first="true"
                        ref="dataTableRef"
                        @itemClick="loadDetail"
                        :load-more-func="loadMoreFunc"
                        :search-keyword="keyword">
                        <template #title="{ item }">
                            <div class="title">
                                {{ item.title }}
                            </div>
                        </template>
                        <template #desc="{ item }">
                            <div>时间：{{ item.createdAt }}</div>
                        </template>
                        <template #detail="{ selectedItem }">
                            <Detail :selected-item="detailData" @refresh="refreshAll" :detailLoading="detailLoading" />
                        </template>
                    </DataTable>
                </div>
            </template>
        </GeneralDataDisplay>
    </div>
</template>

<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
import GeneralDataDisplay from "@/components/GeneralDataDisplay/index.vue";
import DataTable from "@/components/DataTable/index.vue";
import Detail from "./detail.vue";
import { nmsNewsGetSubmitById, nmsNewsListSubmit } from "@/apis/nmsNewsController";
import { NmsNews_ } from "@/apis/types";
import { useRoute } from "vue-router";

// 定义dataTableRef的类型
const dataTableRef = ref();

// 加载详情是否显示loading
const detailLoading = ref(false);

// 关键字
const keyword = ref("");

// 获取路由
const route = useRoute();

// 加载更多函数
const loadMoreFunc = (pageNum: number, pageSize: number) => {
    return nmsNewsListSubmit({
        params: {
            pageNum,
            pageSize,
        },
    });
};

// 定义detailData
const detailData = ref<NmsNews_>();

// 加载详情
const loadDetail = (item: NmsNews_) => {
    // 如果当前选中项和之前选中项相同，则不重新加载
    if (item.id === detailData.value?.id) return;

    detailLoading.value = true;
    detailData.value = null; // 清空之前的详情数据

    nmsNewsGetSubmitById({ params: { id: item.id } })
        .then((res) => {
            detailData.value = res.data;
        })
        .finally(() => {
            detailLoading.value = false;
        });
};

// 搜索
const search = () => {
    if (dataTableRef.value) {
        dataTableRef.value.refreshAll();
    }
};

// 刷新
const refreshAll = (id?: number) => {
    // 清空detailData
    detailData.value = null;
    // 如果id存在，则加载详情
    if (id) {
        detailLoading.value = true;
        nmsNewsGetSubmitById({ params: { id } })
            .then((res) => {
                detailData.value = res.data;
            })
            .finally(() => {
                detailLoading.value = false;
            });
    }

    if (dataTableRef.value) { 
        dataTableRef.value.refreshAll();
    }
};

// type: 审核待办：0，录入待办-待补充：1，录入待办-审核退回：2，项目团队待办：3
// 数据加载完成后的处理
const handleLoadComplete = (data) => {
    const queryIndex = Number(route.query.queryIndex);
    // 如果有索引参数，模拟点击对应索引的项目
    if (queryIndex !== undefined && queryIndex !== null) {
        // 延迟一点执行，确保列表已经完全渲染
        nextTick(() => {
            dataTableRef.value.simulateClick(Number(queryIndex));
        });
    }
};
</script>

<style scoped lang="scss">
.news-draft {
    .top {
        padding: 20px;
        background: #fff;
        display: flex;
        align-items: center;
        div {
            display: flex;
            align-items: center;
            gap: 10px;
        }
    }

    .content {
        .title {
            font-weight: bold;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
</style>
