import { categoryTemplateValueType_NEW } from "@/enums/categoryTemplate/categoryTemplateValueType";
import { CmsCategoryTemplateValue10 } from "@/apis/types";

/**
 * 表格排序处理函数
 */
export const handleTableSort = (
    tableData: any[],
    fieldList: CmsCategoryTemplateValue10[],
    prop: string,
    order: string
) => {
    if (!prop || !order) {
        return;
    }

    const field = fieldList.find((field) => field.value === prop);
    if (!field) return;

    tableData.sort((a, b) => {
        const aValue = a.values.find((val) => val.categoryTemplateValueId === field.id)?.value;
        const bValue = b.values.find((val) => val.categoryTemplateValueId === field.id)?.value;

        if (!aValue && !bValue) return 0;
        if (!aValue) return order === "ascending" ? -1 : 1;
        if (!bValue) return order === "ascending" ? 1 : -1;

        switch (field.type) {
            case categoryTemplateValueType_NEW.INTEGER:
            case categoryTemplateValueType_NEW.DOUBLE:
            case categoryTemplateValueType_NEW.MONEY:
                const aNum = parseFloat(aValue);
                const bNum = parseFloat(bValue);
                return order === "ascending" ? aNum - bNum : bNum - aNum;
            case categoryTemplateValueType_NEW.DATE:
            case categoryTemplateValueType_NEW.PROJECT_DATE:
                const aDate = new Date(aValue).getTime();
                const bDate = new Date(bValue).getTime();
                return order === "ascending" ? aDate - bDate : bDate - aDate;
            default:
                return order === "ascending" ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
        }
    });
};

/**
 * 错误项展开状态管理
 */
export const createErrorItemStates = (errorList: any[], initialState: number = 1) => {
    const states: { [key: number]: number } = {};
    if (errorList) {
        errorList.forEach((_, index) => {
            states[index] = initialState;
        });
    }
    return states;
};

/**
 * 切换错误项展开状态
 */
export const toggleErrorItemState = (states: { [key: number]: number }, index: number) => {
    const currentState = states[index] || 1;
    
    if (currentState === 1) {
        states[index] = 2;
    } else if (currentState === 2) {
        states[index] = 3;
    } else {
        states[index] = 2;
    }
    
    return { ...states };
};

/**
 * 分页状态管理
 */
export const createPaginationState = () => ({
    currentPage: 1,
    pageSize: 50,
    total: 0,
});

export const resetPagination = (pagination: any) => {
    pagination.currentPage = 1;
    pagination.total = 0;
};

/**
 * 滚动分页显示控制
 */
export const createScrollPaginationState = () => ({
    showPagination: false,
    lastScrollTop: 0,
    scrollDirection: "down" as "up" | "down",
    hideTimer: null as NodeJS.Timeout | null,
    scrollTimeout: null as NodeJS.Timeout | null,
});

/**
 * 处理表格滚动分页显示
 */
export const handleTableScrollPagination = (
    state: ReturnType<typeof createScrollPaginationState>,
    scrollTop: number,
    getScrollInfo: () => { isAtBottom: boolean } | null
) => {
    const scrollInfo = getScrollInfo();
    const scrollDelta = scrollTop - state.lastScrollTop;
    const scrollThreshold = 5;

    if (Math.abs(scrollDelta) < scrollThreshold) {
        return state;
    }

    const isAtBottom = scrollInfo?.isAtBottom || false;

    const clearHideTimer = () => {
        if (state.hideTimer) {
            clearTimeout(state.hideTimer);
            state.hideTimer = null;
        }
    };

    if (scrollDelta > 0) {
        // 向下滚动
        state.scrollDirection = "down";
        state.showPagination = true;

        clearHideTimer();

        if (!isAtBottom) {
            state.hideTimer = setTimeout(() => {
                const currentScrollInfo = getScrollInfo();
                if (state.scrollDirection === "down" && !currentScrollInfo?.isAtBottom) {
                    state.showPagination = false;
                }
                state.hideTimer = null;
            }, 3000);
        }
    } else if (scrollDelta < 0) {
        // 向上滚动
        state.scrollDirection = "up";

        if (!isAtBottom) {
            state.showPagination = false;
        }

        clearHideTimer();
    }

    // 如果滚动到顶部或底部，显示分页条
    if (scrollTop <= 10 || isAtBottom) {
        clearHideTimer();
        state.showPagination = true;
    }

    state.lastScrollTop = scrollTop;
    return state;
};

/**
 * 清理定时器
 */
export const clearScrollTimers = (state: ReturnType<typeof createScrollPaginationState>) => {
    if (state.hideTimer) {
        clearTimeout(state.hideTimer);
        state.hideTimer = null;
    }

    if (state.scrollTimeout) {
        clearTimeout(state.scrollTimeout);
        state.scrollTimeout = null;
    }
};