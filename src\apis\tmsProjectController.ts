/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 设置项目管理员 POST /tms/tmsProject/changePosition */
export async function tmsProjectChangePosition({
  body,
  options,
}: {
  body: API.TmsUpdateAdministratorDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/tms/tmsProject/changePosition', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建项目团队 POST /tms/tmsProject/createProject */
export async function tmsProjectCreateProject({
  body,
  options,
}: {
  body: API.TmsProjectDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultLong_>('/tms/tmsProject/createProject', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除项目人员 POST /tms/tmsProject/deleteMember */
export async function tmsProjectDeleteMember({
  body,
  options,
}: {
  body: API.TmsDeletePersonDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/tms/tmsProject/deleteMember', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据项目id删除项目团队 POST /tms/tmsProject/deleteProject */
export async function tmsProjectDeleteProject({
  body,
  options,
}: {
  body: API.CommonDeleteDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/tms/tmsProject/deleteProject', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询项目团队列表 GET /tms/tmsProject/getProjectList */
export async function tmsProjectGetProjectList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.tmsProjectGetProjectListParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageTmsProject_>(
    '/tms/tmsProject/getProjectList',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据id查询项目团队 GET /tms/tmsProject/selectProjectById */
export async function tmsProjectSelectProjectById({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.tmsProjectSelectProjectByIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultTmsProject_>(
    '/tms/tmsProject/selectProjectById',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 修改项目团队信息 POST /tms/tmsProject/updateProject */
export async function tmsProjectUpdateProject({
  body,
  options,
}: {
  body: API.TmsProjectUpdateDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/tms/tmsProject/updateProject', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
