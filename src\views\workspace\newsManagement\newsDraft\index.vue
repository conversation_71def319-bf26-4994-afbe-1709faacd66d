<template>
    <div class="news-draft">
        <GeneralDataDisplay>
            <template #top>
                <div class="top">
                    <div>
                        <el-button type="primary" @click="handleCreate">创建新闻</el-button>
                    </div>
                </div>
            </template>

            <template #content>
                <div class="content">
                    <DataTable
                        :is-load-first="true"
                        ref="dataTableRef"
                        @itemClick="loadDetail"
                        :load-more-func="loadMoreFunc">
                        <template #title="{ item }">
                            <div class="title">
                                {{ item.title }}
                            </div>
                        </template>
                        <template #desc="{ item }">
                            <div>时间：{{ item.createdAt }}</div>
                        </template>
                        <template #detail="{ selectedItem }">
                            <Detail :selected-item="detailData" @refresh="refreshAll" :detailLoading="detailLoading" />
                        </template>
                    </DataTable>
                </div>
            </template>
        </GeneralDataDisplay>
    </div>
</template>

<script setup lang="ts">
import GeneralDataDisplay from "@/components/GeneralDataDisplay/index.vue";
import DataTable from "@/components/DataTable/index.vue";
import Detail from "./detail.vue";
import { nmsNewsGetDraftById, nmsNewsListDraft, nmsNewsSaveDraft } from "@/apis/nmsNewsController";
import { NmsNews_ } from "@/apis/types";
import { ElMessage } from "element-plus";

// 定义dataTableRef的类型
const dataTableRef = ref();

// 加载详情是否显示loading
const detailLoading = ref(false);

// 创建新闻
const handleCreate = () => {
    nmsNewsSaveDraft({
        body: {
            title: "新闻标题",
            content: "新闻内容",
        },
    }).then((res) => {
        if (res.code === 200) {
            ElMessage.success("创建成功");
            refreshAll();
        } else {
            ElMessage.error("创建失败");
        }
    });
};

// 加载更多函数
const loadMoreFunc = (pageNum: number, pageSize: number) => {
    return nmsNewsListDraft({
        params: {
            pageNum,
            pageSize,
        },
    });
};

// 定义detailData
const detailData = ref<NmsNews_>();

// 加载详情
const loadDetail = (item: NmsNews_) => {
    // 如果当前选中项和之前选中项相同，则不重新加载
    if (item.id === detailData.value?.id) return;

    detailLoading.value = true;
    detailData.value = null; // 清空之前的详情数据

    nmsNewsGetDraftById({ params: { id: item.id } })
        .then((res) => {
            detailData.value = res.data;
        })
        .finally(() => {
            detailLoading.value = false;
        });
};

// 刷新
const refreshAll = (id?: number) => {
    // 清空detailData
    detailData.value = null;
    // 如果id存在，则加载详情
    if (id) {
        detailLoading.value = true;
        nmsNewsGetDraftById({ params: { id } })
            .then((res) => {
                detailData.value = res.data;
            })
            .finally(() => {
                detailLoading.value = false;
            });
    }

    if (dataTableRef.value) {
        dataTableRef.value.refreshAll();
    }
};
</script>

<style scoped lang="scss">
.news-draft {
    .top {
        padding: 20px;
        background: #fff;
        display: flex;
        justify-content: end;
        align-items: center;
    }

    .content {
        .title {
            font-weight: bold;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
</style>
