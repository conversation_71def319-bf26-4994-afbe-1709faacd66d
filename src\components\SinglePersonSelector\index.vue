<template>
    <div class="person-selector">
        <!-- 左侧：姓名和选择框 -->
        <div style="flex: 1; display: flex; align-items: center;">
            <el-select :filterable="isFilterable" :disabled="isDisabled" v-if="isInside" v-model="selectedPerson" :placeholder="outPlaceholder" style="width: 300px;">
                <el-option v-for="(person, index) in personList" :key="index" :label="person.employeeName" :value="person.id" />
            </el-select>

            <el-input v-else :disabled="isDisabled" v-model="selectedPerson" :placeholder="outPlaceholder" style="width: 300px;" />
        </div>

        <!-- 右侧：是否校外 -->
        <div style="margin-left: 20px;">
            <el-radio-group :disabled="isDisabled" v-model="isInside" size="small">
                <el-radio :label="true">本院</el-radio>
                <el-radio :label="false">院外</el-radio>
            </el-radio-group>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { UmsPerson_ } from '@/apis';

const props = defineProps<{
    personList: UmsPerson_[];// 传入的人员列表
    isDisabled: boolean;
    outPlaceholder: string;
    isFilterable: boolean;
    modelValue?: number | string; // 添加 modelValue prop
}>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: number | string): void;
}>();

// 是否为本院
const isInside = ref(true);

// 当前选择的人员
const selectedPerson = ref<number | string>(props.modelValue || '');

// 监听 props.modelValue 变化
watch(() => props.modelValue, (newVal) => {
    if (newVal !== selectedPerson.value) {
        selectedPerson.value = newVal || '';
    }
});

// 监听 selectedPerson 变化
watch(selectedPerson, (newVal) => {
    emit('update:modelValue', newVal);
});

// 监听 isInside 变化时更新 selectedPerson 的值类型
watch(isInside, () => {
    selectedPerson.value = '';
    emit('update:modelValue', '');
});
</script>

<style scoped lang="scss">
.person-selector {
    display: flex;
    align-items: center;
    border: 1px solid #dcdfe6;
    padding: 10px;
    
}
</style>