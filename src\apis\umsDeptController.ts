/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 添加部门 POST /ums/umsDept/create */
export async function umsDeptCreate({
  body,
  options,
}: {
  body: API.UmsDept_;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsDept/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除部门 POST /ums/umsDept/deleteById */
export async function umsDeptDeleteById({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsDeptDeleteByIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsDept/deleteById', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询依托专业 给所有录入数据回显使用 GET /ums/umsDept/getAllDependentMajor */
export async function umsDeptGetAllDependentMajor({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListUmsDept_>(
    '/ums/umsDept/getAllDependentMajor',
    {
      method: 'GET',
      ...(options || {}),
    }
  );
}

/** 查询依托专业 给人员管理页面，所有录入时候选择依托专业查询使用 GET /ums/umsDept/getDependentMajor */
export async function umsDeptGetDependentMajor({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListUmsDept_>(
    '/ums/umsDept/getDependentMajor',
    {
      method: 'GET',
      ...(options || {}),
    }
  );
}

/** 查询已启用部门树 GET /ums/umsDept/listOpenTree */
export async function umsDeptListOpenTree({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListUmsDept_>('/ums/umsDept/listOpenTree', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 查询所有部门树 GET /ums/umsDept/listTree */
export async function umsDeptListTree({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListUmsDept_>('/ums/umsDept/listTree', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 修改部门 POST /ums/umsDept/update */
export async function umsDeptUpdate({
  body,
  options,
}: {
  body: API.UmsDept_;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsDept/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
