import CustomDrawer from './index.vue'
import type { CustomDrawerProps, CustomDrawerEmits, CustomDrawerSlots } from './types'
import { useCustomDrawer, createDrawer, createFormDrawer, createConfirmDrawer } from './useCustomDrawer'
import type { UseCustomDrawerOptions } from './useCustomDrawer'

export default CustomDrawer
export { 
    CustomDrawer,
    useCustomDrawer,
    createDrawer,
    createFormDrawer,
    createConfirmDrawer
}
export type { 
    CustomDrawerProps, 
    CustomDrawerEmits, 
    CustomDrawerSlots,
    UseCustomDrawerOptions
} 