import { dmsDictList } from "@/apis/dmsDictController";

export const useDict = () => {

    const dictList = ref([]);

    const fetchDictList = async () => {
        try {
            const res = await dmsDictList({});
            dictList.value = res.data;
        } catch (error) {
            console.error("加载字典列表失败:", error);
        }
    };

    // 自动加载数据
    onMounted(fetchDictList);

    return {
        dictList,
        fetchDictList,
    };
};
