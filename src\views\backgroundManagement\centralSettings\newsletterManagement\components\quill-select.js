/**
 * @file 富文本select插件
 * <AUTHOR>
 * 2021-09-03
 */

// level 指定output下一级
import { addEventPopper, css, getName } from "./util";
import mitt from "mitt";
import { createPopper } from '@popperjs/core';
import warning from "@/assets/warning.png";

// 2. 从全局变量获取 Popper 构造函数

const eventHub = mitt();

class SelfSelect {
    constructor(ParentNode, data, placeHolder) {
        this.value = data.value || "";
        this.placeHolder = placeHolder;
        this.list = data.list || [];
        this.selectVavList = this.value.split("/");
        this.ParentNode = ParentNode;
        this.levelIndex = 0; // 层级管理 flex布局
        this.selectWraperALL = document.createElement("span"); // 下拉框外包裹
        this.elCascaderpanel = document.createElement("span"); // 下拉框
        this.selectDiv = document.createElement("span"); // 输入框包括
        this.input = document.createElement("span"); // input可视区
        this.elCascaderpanel.className = "ql-el-cascader-panel";
        this.selectWraperALL.className = "ql-el-cascader__dropdown";
        this.selectDiv.className = "select-input-value";
        this.input.className = "input-inner";
        this.popperJSmode = null; // poper实例
        this.initPopper();
        this.createSelectInput();
        this.createPanel();
        this.ParentNode.appendChild(this.selectDiv);

        // 关键改动：将下拉菜单添加到document.body而不是父节点
        document.body.appendChild(this.selectWraperALL);

        // 设置初始样式 - 隐藏下拉菜单
        this.selectWraperALL.style.display = "none";

        // 设置一个唯一ID，用于识别哪个下拉框属于哪个选择器
        const uniqueId = 'cascader-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
        this.selectDiv.dataset.dropdownId = uniqueId;
        this.selectWraperALL.dataset.dropdownId = uniqueId;

        // 在富文本编辑器元素上添加一个引用，以便清理
        if (this.ParentNode._selectInstances) {
            this.ParentNode._selectInstances.push(this);
        } else {
            this.ParentNode._selectInstances = [this];
        }

        // 设置MutationObserver监听元素是否从DOM中移除
        this.setupMutationObserver();
    }

    // 添加新方法：设置MutationObserver监听DOM变化
    setupMutationObserver() {
        // 创建一个观察器实例
        this.observer = new MutationObserver((mutations) => {
            // 检查父元素是否被移除
            mutations.forEach(mutation => {
                if (mutation.type === 'childList') {
                    Array.from(mutation.removedNodes).forEach(node => {
                        if (node === this.ParentNode || node.contains(this.ParentNode)) {
                            // 父元素被移除，清理资源
                            this.cleanup();
                        }
                    });
                }
            });
        });

        // 配置观察选项
        const config = {
            childList: true,
            subtree: true
        };

        // 开始观察document.body
        this.observer.observe(document.body, config);
    }

    // 添加清理方法
    cleanup() {
        // 移除事件监听器
        if (this.scrollHandler) {
            window.removeEventListener('scroll', this.scrollHandler, true);
            document.removeEventListener('scroll', this.scrollHandler, true);
        }

        if (this.closeDropdownHandler) {
            document.removeEventListener('click', this.closeDropdownHandler);
        }

        // 移除提示框
        const tooltip = document.querySelector(".ql-required-tooltip");
        if (tooltip && tooltip.parentNode) {
            tooltip.parentNode.removeChild(tooltip);
        }

        // 销毁popper实例
        if (this.popperJSmode) {
            this.popperJSmode.destroy();
        }

        // 移除下拉菜单元素
        if (this.selectWraperALL && this.selectWraperALL.parentNode) {
            this.selectWraperALL.parentNode.removeChild(this.selectWraperALL);
        }

        // 停止观察
        if (this.observer) {
            this.observer.disconnect();
        }

        // 从父元素的实例列表中移除
        if (this.ParentNode._selectInstances) {
            const index = this.ParentNode._selectInstances.indexOf(this);
            if (index !== -1) {
                this.ParentNode._selectInstances.splice(index, 1);
            }
        }
    }
    bindClick(dom, callback) {
        dom.onclick = (e) => {
            callback && callback.call(e);
            // 阻止事件冒泡，避免点击输入框时触发外部事件
            e.stopPropagation();
        };
    }
    createSelectInput() {
        let selectTxt = document.createElement("span");
        let selectArrow = document.createElement("i");
        selectArrow.className = "ql-el-input__icon";
        selectTxt.className = "input-wraper";
        selectTxt.appendChild(this.input);
        this.selectDiv.appendChild(selectTxt);
        this.selectDiv.appendChild(selectArrow);
        // 初始化数据input value
        this.resetInputValue(this.input);
        this.bindClick(this.selectDiv, () => {
            // 先关闭其他打开的下拉框
            if (SelfSelect.currentOpenSelect && SelfSelect.currentOpenSelect !== this) {
                SelfSelect.currentOpenSelect.closeSelect();
            }
            
            eventHub.emit("updatepopperJS");
            this.selectDiv.classList.add("is-focus");
            if (selectArrow.className.includes("is-reverse")) {
                selectArrow.classList.remove("is-reverse");
            } else {
                selectArrow.classList.add("is-reverse");
            }
            let status = css(this.selectWraperALL, "display");
            css(
                this.selectWraperALL,
                "display",
                status === "none" ? "block" : "none"
            );
            if (status === "none") {
                this.selectDiv.classList.add("is-focus");
                // 显示时更新位置
                this.popperJSmode.update();
                // 更新当前打开的下拉框引用
                SelfSelect.currentOpenSelect = this;
            } else {
                this.selectDiv.classList.remove("is-focus");
                // 关闭时清除当前引用
                if (SelfSelect.currentOpenSelect === this) {
                    SelfSelect.currentOpenSelect = null;
                }
            }
        });
    }
    // 动态level 创建指定level
    createPanel(list = this.list, level) {
        let ul = document.createElement("ul");
        let len = list;
        let selectWraper = document.createElement("span");
        let selectDropDown = document.createElement("span");
        ul.className = "ql-el-scrollbar__view ql-el-cascader-menu__list";
        selectWraper.className = "ql-el-scrollbar ql-el-cascader-menu";
        selectDropDown.className = "ql-el-cascader-menu__wrap ql-el-scrollbar__wrap";
        +this.levelIndex++;
        if (level) {
            this.levelIndex = level;
        }
        for (let i = 0; i < len.length; i++) {
            let keyData = len[i];
            let li = document.createElement("li");
            createLi.call(this, li, keyData);
            this.liBindEvent(keyData, li, ul);
            // 历史数据回填
            this.resetOptionValue(list, keyData);
            ul.append(li);
        }
        selectDropDown.appendChild(ul);
        selectWraper.appendChild(selectDropDown);
        clearHistoryPanel.call(this);
        this.elCascaderpanel.appendChild(selectWraper);
        this.selectWraperALL.appendChild(this.elCascaderpanel);

        function clearHistoryPanel() {
            //场景1 创建当前指定子节点 clear历史节点
            if (level) {
                this.elCascaderpanel.childNodes[level - 1] &&
                    this.elCascaderpanel.removeChild(
                        this.elCascaderpanel.childNodes[level - 1]
                    );
            }
            // 场景2 所有已经展开的节点
            for (let l = this.elCascaderpanel.children.length; l >= 0; l--) {
                if (l > this.levelIndex - 2) {
                    this.elCascaderpanel.children[l] &&
                        this.elCascaderpanel.removeChild(
                            this.elCascaderpanel.children[l]
                        );
                }
            }
        }

        function createLi(li, keyData) {
            let span = document.createElement("span");
            span.innerHTML = keyData.name;
            span.className = "ql-el-cascader-node__label";
            li.setAttribute("code", keyData.code);
            li.setAttribute("isRequired", keyData.isRequired);
            li.setAttribute("level", this.levelIndex);
            li.appendChild(span);
            li.className = "ql-el-cascader-node";
            if (keyData.isRequired === 0) {
                li.style.backgroundColor = "#E6EBF2";
                li.style.color = "#97A1C5"
            } else {
                li.style.fontWeight = "bold"
            }

            // 添加感叹号图标（当isRequired为0时）
            if (keyData.isRequired === "0" || keyData.isRequired === 0) {
                let exclamationIcon = document.createElement("img");
                exclamationIcon.src = warning;
                exclamationIcon.style.height = "15px";
                exclamationIcon.style.width = "15px";
                li.appendChild(exclamationIcon);

                // 添加鼠标悬浮事件
                li.addEventListener("mouseenter", function (e) {
                    // 创建提示框
                    let tooltip = document.querySelector(".ql-required-tooltip");
                    if (!tooltip) {
                        tooltip = document.createElement("div");
                        tooltip.className = "ql-required-tooltip";
                        tooltip.style.transition = "all 0.3s ease";
                        tooltip.style.position = "absolute";
                        tooltip.style.letterSpacing = "0.1rem";
                        tooltip.style.color = "#6B7995";
                        tooltip.style.zIndex = "2000";
                        tooltip.style.backgroundColor = "#fafafa";
                        tooltip.style.borderRadius = "4px";
                        tooltip.style.boxShadow = "0 2px 12px 0 rgba(0,0,0,.1)";
                        tooltip.style.padding = "20px";
                        tooltip.style.width = "400px";
                        tooltip.style.wordBreak = "break-word";
                        tooltip.innerHTML = `该数据项在该表内被设置为了<strong>非必填数据项</strong>，用户录入数据时可能不会填写此数据。推送的简讯中将会包含空数据项，导致句子不完整或不通顺。<br><br>
如果想将该数据项更改为<strong>必填</strong>数据项，请在<strong>数据表管理</strong>中找到该数据表并更改相关设置。如果仍要使用该数据项，您可以继续进行编辑。`;  // 这里可以自定义提示内容
                        document.body.appendChild(tooltip);
                    }

                    // 计算位置 - 显示在li的右侧
                    const rect = li.getBoundingClientRect();
                    tooltip.style.left = (rect.right + 10) + "px";
                    tooltip.style.top = rect.top + "px";
                    tooltip.style.display = "block";
                });

                // 鼠标移出时隐藏提示框
                li.addEventListener("mouseleave", function () {
                    const tooltip = document.querySelector(".ql-required-tooltip");
                    if (tooltip) {
                        tooltip.style.display = "none";
                    }
                });
            }

            if (Object.keys(keyData.children || []).length) {
                let after = document.createElement("i");
                after.className = "ql-el-icon-check ql-el-cascader-node__postfix";
                li.appendChild(after);
            }
            if (this.selectVavList.includes(keyData.code)) {
                li.classList.add("in-active-path");
            }
        }
    }
    resetOptionValue(list = this.list, keyData) {
        if (this.selectVavList.length) {
            let activedCode = this.selectVavList[this.levelIndex - 1];
            if (keyData.code === activedCode) {
                let subList = list.find((el) => el.code === activedCode);
                // 二级回填 自动展开
                if (
                    subList.children &&
                    subList.children.length &&
                    this.selectVavList.length > 1
                ) {
                    Promise.resolve(1).then(() => {
                        this.createPanel(subList.children);
                    });
                }
            }
        }
    }
    liBindEvent(keyData, li, ul) {
        let setSelectPach = () => {
            let selectVal = [];
            let selectCode = [];
            let thisLevel = +li.getAttribute("level");
            if (thisLevel < this.levelIndex) {
                for (
                    let l = this.elCascaderpanel.children.length;
                    l >= 0;
                    l--
                ) {
                    // debugger
                    if (l > this.levelIndex - thisLevel) {
                        this.elCascaderpanel.children[l] &&
                            this.elCascaderpanel.removeChild(
                                this.elCascaderpanel.children[l]
                            );
                    }
                }
            }
            Promise.resolve(1).then(() => {
                this.selectWraperALL
                    .querySelectorAll(".in-active-path")
                    .forEach((el) => {
                        selectVal.push(el.innerText);
                        selectCode.push(el.getAttribute("code"));
                    });
                this.input.innerHTML = selectVal.join("/");
                this.ParentNode.setAttribute("value", selectCode.join("/"));
                this.selectWraperALL.style.display = "none";
            });
        };
        this.bindClick(li, () => {
            Array.from(ul.children).forEach((el) => {
                el.classList.remove("in-active-path");
            });
            li.classList.add("in-active-path");
            if (Object.keys(keyData.children || []).length) {
                // 点击的时候需要判断 是右侧更新数据 还是在右侧创建panel？看levelIndex是
                // 是否比自己大
                this.createPanel(
                    keyData.children,
                    +li.getAttribute("level") + 1
                );
            } else {
                setSelectPach();
                this.selectWraperALL.style.display = "none";
            }
            // 更新弹窗poperjs
            setTimeout(() => {
                eventHub.emit("updatepopperJS");
            });
        });
    }
    resetInputValue(dom) {
        let str = [];
        this.selectVavList.forEach((el) => {
            str.push(getName(this.list, el));
        });
        dom.innerText = str.length > 1 ? str.join("/") : str.join("");
    }
    // 添加一个方法用于关闭当前下拉框
    closeSelect() {
        this.selectWraperALL.style.display = "none";
        this.selectDiv.classList.remove("is-focus");
        const arrowIcon = this.selectDiv.querySelector(".ql-el-input__icon");
        if (arrowIcon) {
            arrowIcon.classList.remove("is-reverse");
        }
        if (SelfSelect.currentOpenSelect === this) {
            SelfSelect.currentOpenSelect = null;
        }
    }
    initPopper() {
        this.popperJSmode = createPopper(this.selectDiv, this.selectWraperALL, {
            placement: "bottom-start",
            modifiers: [
                {
                    name: 'preventOverflow',
                    options: {
                        boundary: document.body,
                        padding: 8,
                    },
                },
                {
                    name: 'flip',
                    options: {
                        fallbackPlacements: ['top-start', 'bottom-end'],
                    },
                },
                {
                    name: 'computeStyles',
                    options: {
                        gpuAcceleration: false, // 避免在一些情况下的渲染问题
                    },
                },
                {
                    name: 'eventListeners',
                    options: {
                        scroll: true, // 确保滚动事件监听开启
                        resize: true,
                    },
                }
            ],
            onFirstUpdate: (state) => {
                this.popperJSmode.update();
            },
            strategy: 'fixed', // 使用fixed策略确保下拉框正确跟随
        });

        eventHub.on("updatepopperJS", (_) => {
            this.popperJSmode.update();
        });

        // 添加scroll事件监听，在滚动时更新popper位置
        const scrollHandler = () => {
            if (this.selectWraperALL.style.display === "block") {
                this.popperJSmode.update();
            }
        };

        // 为所有可能的滚动容器添加滚动监听
        window.addEventListener('scroll', scrollHandler, true); // true表示使用捕获阶段
        document.addEventListener('scroll', scrollHandler, true);

        // 保存引用以便在需要时移除
        this.scrollHandler = scrollHandler;

        // 改进全局点击处理，如果点击区域不在下拉菜单或输入框内，则关闭下拉菜单
        const closeDropdown = (e) => {
            // 如果点击的是当前选择器或其下拉菜单，不执行关闭
            if (
                this.selectDiv.contains(e.target) ||
                this.selectWraperALL.contains(e.target)
            ) {
                return;
            }

            this.closeSelect();
        };

        // 添加全局点击事件，取代之前的局部事件
        document.addEventListener('click', closeDropdown);

        // 保存引用以便在需要时移除
        this.closeDropdownHandler = closeDropdown;

        // 仍然保留原有方法以确保兼容性
        addEventPopper(this.ParentNode, () => {
            setTimeout(() => {
                this.closeSelect();
            });
        });
    }
}

// 添加静态属性用于跟踪当前打开的下拉框 - 移到类定义之后
SelfSelect.currentOpenSelect = null;

export default SelfSelect;
