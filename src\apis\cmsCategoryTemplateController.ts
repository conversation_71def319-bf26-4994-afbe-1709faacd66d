/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 校验表头 POST /cms/cmsCategoryTemplate/checkHead */
export async function cmsCategoryTemplateCheckHead({
  params,
  body,
  file,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryTemplateCheckHeadParams;
  body: {};
  file?: File;
  options?: { [key: string]: unknown };
}) {
  const formData = new FormData();

  if (file) {
    formData.append('file', file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as { [key: string]: any })[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (item instanceof Array) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request<API.CommonResultListString_>(
    '/cms/cmsCategoryTemplate/checkHead',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      params: {
        ...params,
      },
      data: formData,
      ...(options || {}),
    }
  );
}

/** 添加大类信息 POST /cms/cmsCategoryTemplate/create */
export async function cmsCategoryTemplateCreate({
  body,
  options,
}: {
  body: API.CmsCategoryTemplate1;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/cms/cmsCategoryTemplate/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除大类 POST /cms/cmsCategoryTemplate/delete */
export async function cmsCategoryTemplateDelete({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryTemplateDeleteParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/cms/cmsCategoryTemplate/delete', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 导出大类文件 GET /cms/cmsCategoryTemplate/export/file */
export async function cmsCategoryTemplateExportFile({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryTemplateExportFileParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.Resource>('/cms/cmsCategoryTemplate/export/file', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据ID查询大类 GET /cms/cmsCategoryTemplate/getById */
export async function cmsCategoryTemplateGetById({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryTemplateGetByIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCmsCategoryTemplate_>(
    '/cms/cmsCategoryTemplate/getById',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取大类列表 提供给教师录入 GET /cms/cmsCategoryTemplate/list */
export async function cmsCategoryTemplateList({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsCategoryTemplate_>(
    '/cms/cmsCategoryTemplate/list',
    {
      method: 'GET',
      ...(options || {}),
    }
  );
}

/** 获取大类列表 提供给管理员录入 GET /cms/cmsCategoryTemplate/listByAdmin */
export async function cmsCategoryTemplateListByAdmin({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsCategoryTemplate_>(
    '/cms/cmsCategoryTemplate/listByAdmin',
    {
      method: 'GET',
      ...(options || {}),
    }
  );
}

/** 根据分组ID获取大类列表 GET /cms/cmsCategoryTemplate/listByGroupId/${param0} */
export async function cmsCategoryTemplateListByGroupIdId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryTemplateListByGroupIdidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultListCmsCategoryTemplate_>(
    `/cms/cmsCategoryTemplate/listByGroupId/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}

/** 根据大类ID分页查询数据 GET /cms/cmsCategoryTemplate/listById */
export async function cmsCategoryTemplateListById({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryTemplateListByIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCmsCategoryTemplate_>(
    '/cms/cmsCategoryTemplate/listById',
    {
      method: 'GET',
      params: {
        // pageNum has a default value: 1
        pageNum: '1',
        // pageSize has a default value: 10
        pageSize: '10',
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 查询条件数据 人员 + 年， 或 人员 GET /cms/cmsCategoryTemplate/queryByOwnerAndYear */
export async function cmsCategoryTemplateQueryByOwnerAndYear({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryTemplateQueryByOwnerAndYearParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsCategoryRow_>(
    '/cms/cmsCategoryTemplate/queryByOwnerAndYear',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 导入大类文件 只导入表头（可多级表头，除了最低层表头只可左右合并，不可上下合并） POST /cms/cmsCategoryTemplate/save/file */
export async function cmsCategoryTemplateSaveFile({
  body,
  file,
  options,
}: {
  body: {};
  file?: File;
  options?: { [key: string]: unknown };
}) {
  const formData = new FormData();

  if (file) {
    formData.append('file', file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as { [key: string]: any })[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (item instanceof Array) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request<API.CommonResultVoid_>('/cms/cmsCategoryTemplate/save/file', {
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data: formData,
    ...(options || {}),
  });
}

/** 查询树形数据表 GET /cms/cmsCategoryTemplate/treeTemplate */
export async function cmsCategoryTemplateTreeTemplate({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsClassify_>(
    '/cms/cmsCategoryTemplate/treeTemplate',
    {
      method: 'GET',
      ...(options || {}),
    }
  );
}

/** 修改大类信息 POST /cms/cmsCategoryTemplate/update */
export async function cmsCategoryTemplateUpdate({
  body,
  options,
}: {
  body: API.CmsCategoryTemplate1;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/cms/cmsCategoryTemplate/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
