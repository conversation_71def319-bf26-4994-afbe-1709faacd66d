<template>
    <div class="imported-records">
        <GeneralDataDisplay>
            <template #top>
                <div class="top">
                    <div>
                        <el-input
                            clearable
                            v-model="search"
                            style="width: 420px; margin-right: 10px"
                            placeholder="请输入数据表名称、项目名称、参与人、导入人搜索"
                            suffix-icon="Search"
                            @keyup.enter="handleSearch" />
                    </div>
                    <div>
                        <el-button color="#586FBB" type="primary" @click="handleSearch">搜索</el-button>
                    </div>
                </div>
            </template>

            <template #content>
                <div class="content">
                    <DataTable
                        @load-complete="handleLoadComplete"
                        :search-keyword="search"
                        :is-local-search="false"
                        ref="dataTableRef"
                        :use-tab-style="true"
                        :tab-config="[
                            {
                                key: 'pending',
                                label: '待补充',
                                loadMoreFunc: loadMorePendingFunc,
                            },
                            {
                                key: 'approved',
                                label: '审核退回',
                                loadMoreFunc: loadMoreApprovedFunc,
                            },
                        ]"
                        :is-load-first="isAutoLoadFirst"
                        @itemClick="loadDetail"
                        @update:modelValue="handleTabChange">
                        <template #title="{ item }: { item: CmsRowValueResult }">
                            <div class="title">
                                {{ item.templateName }}
                            </div>
                        </template>
                        <template #desc="{ item }: { item: CmsRowValueResult }">
                            <div>项目名称：{{ item.projectName === null ? "-" : item.projectName }}</div>
                            <div v-if="currentActiveTab === 'pending'">
                                导入人：{{
                                    item.createdBy
                                        ? getPersonName(allPersonListWithDisabled, String(item.createdBy))
                                        : "-"
                                }}
                            </div>
                            <div v-else>
                                审核人：{{
                                    item.approvalBy
                                        ? getPersonName(allPersonListWithDisabled, String(item.approvalBy))
                                        : "-"
                                }}
                            </div>
                        </template>
                        <template #right>
                            <el-popover popper-class="edit-popover" placement="right" width="70" trigger="hover">
                                <template #reference>
                                    <div>
                                        <el-icon style="rotate: 90deg" size="12"><MoreFilled /></el-icon>
                                    </div>
                                </template>
                                <div style="text-align: center">
                                    <el-button text type="danger" @click="handleDeleteRow(detailData.id)"
                                        >删除</el-button
                                    >
                                </div>
                            </el-popover>
                        </template>
                        <template #detail="{ selectedItem }">
                            <Detail
                                :record-list="recordList"
                                :is-read-only="false"
                                :current-active-tab="currentActiveTab"
                                :all-person-list="allPersonListWithDisabled"
                                :selected-item="detailData"
                                @refresh="refreshAll"
                                :detailLoading="detailLoading" />
                        </template>
                    </DataTable>
                </div>
            </template>
        </GeneralDataDisplay>
    </div>
</template>

<script setup lang="ts">
import GeneralDataDisplay from "@/components/GeneralDataDisplay/index.vue";
import DataTable from "@/components/DataTable/index.vue";
import Detail from "./detail.vue";

import { ElMessage, ElMessageBox } from "element-plus";
import { cmsCategoryRowDelete, cmsCategoryRowGetByApprovalAndRowType } from "@/apis/cmsCategoryRowController";
import { CmsCheck_, CmsRowValueResult } from "@/apis/types";
import { cmsCheckGetRecord } from "@/apis/cmsCheckController";
import { getPersonName } from "@/utils/getNames";
import { usePersons } from "@/hooks/usePersons";
import { useRoute } from "vue-router";

// 所有人员列表(包含已被禁用的人员)
const { allPersonListWithDisabled } = usePersons(true);

const route = useRoute();

// 定义dataTableRef的类型\
const dataTableRef = ref();

// 跟踪当前活动的标签页
const currentActiveTab = ref("pending");

// 详情加载状态
const detailLoading = ref<boolean>(false);

// 详情数据
const detailData = ref<CmsRowValueResult>();

// 搜索
const search = ref<string>("");

// 审核历史
const recordList = ref<CmsCheck_[]>([]);

// 处理标签页变化
function handleTabChange(tabKey: string) {
    currentActiveTab.value = tabKey;
    // 每次切换标签页时清空详情数据
    detailData.value = null;
    // 刷新
    refreshAll();
}

// 处理搜索
const handleSearch = () => {
    if (dataTableRef.value) {
        dataTableRef.value.search();
    }
};
// 刷新
const refreshAll = () => {
    // 清空detailData
    detailData.value = null;

    if (dataTableRef.value) {
        // 获取当前活动的标签页并传递
        dataTableRef.value.refreshAll(currentActiveTab.value);
    }
};

// 加载更多函数【待补充】
const loadMorePendingFunc = (pageNum: number, pageSize: number, searchKeyword: string) => {
    return cmsCategoryRowGetByApprovalAndRowType({
        params: {
            approvalStatus: [0],
            rowTypes: ["IMPORT", "INSERT"],
            pageNum,
            pageSize,
            name: searchKeyword,
        },
    });
};

// 加载更多函数【审核退回】
const loadMoreApprovedFunc = (pageNum: number, pageSize: number, searchKeyword: string) => {
    return cmsCategoryRowGetByApprovalAndRowType({
        params: {
            approvalStatus: [4],
            rowTypes: ["IMPORT", "INSERT"],
            pageNum,
            pageSize,
            name: searchKeyword,
        },
    });
};

// 处理加载完成事件，用于从待办事项跳转时自动选择对应项目

// 监听路由参数，处理从待办事项跳转过来的情况
onMounted(() => {
    const queryType = route.query.type;
    if (queryType) {
        // 根据type值选择对应标签页
        if (queryType === "1") {
            // 待补充
            currentActiveTab.value = "pending";
        } else if (queryType === "2") {
            // 审核退回
            currentActiveTab.value = "approved";
        }
    }
});

const isAutoLoadFirst = computed(() => {
    return !(route.query.queryListItemId && route.query.queryIndex);
});

// type: 审核待办：0，录入待办-待补充：1，录入待办-审核退回：2，项目团队待办：3
// 数据加载完成后的处理
const handleLoadComplete = () => {
    const queryListItemId = Number(route.query.queryListItemId);
    const queryIndex = Number(route.query.queryIndex);
    const queryType = Number(route.query.queryType);

    // 如果有路由参数，处理自动选择
    if (queryListItemId || queryIndex) {
        // 延迟执行确保数据已渲染
        nextTick(() => {
            if (queryIndex !== undefined) {
                // 有索引参数时直接点击对应索引项
                const goTabKey = queryType === 1 ? "pending" : "approved";
                dataTableRef.value.simulateClick(Number(queryIndex), goTabKey, queryListItemId);
            }
        });
    }
};

// 加载详情
const loadDetail = (item: CmsRowValueResult) => {
    // 如果当前选中项和之前选中项相同，则不重新加载
    if (item.id === detailData.value?.id) return;

    detailLoading.value = true;
    detailData.value = null; // 清空之前的详情数据

    cmsCheckGetRecord({ params: { rowId: item.id } }).then((res) => {
        recordList.value = res.data;
    });
    detailData.value = item;
    detailLoading.value = false;
};

// 删除行
const handleDeleteRow = (id: number) => {
    ElMessageBox.confirm("确定删除该行吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    }).then(() => {
        cmsCategoryRowDelete({ params: { id } }).then((res) => {
            if (res.code === 200) {
                ElMessage.success("删除成功");
                // 刷新
                refreshAll();
            }
        });
    });
};
// 定义接口
interface ImportedRecord extends CmsRowValueResult {
    values: Array<{
        value: string | null;
    }>;
    createdBy: number;
    approvalBy: number;
    id: number;
}

defineSlots<{
    title: { item: ImportedRecord };
    desc: { item: ImportedRecord };
    detail: { selectedItem: ImportedRecord | null };
}>();
</script>

<style scoped lang="scss">
// 覆盖el-popover样式,取消min-width限制
:global(.el-popover.edit-popover) {
    min-width: unset !important;
    padding: 5px 0px !important;
}

.imported-records {
    height: 100%;
    .top {
        padding: 20px;
        background: #fff;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .content {
        height: 100%;
        .title {
            font-weight: bold;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
</style>
