/**
 * 最高学历：大学本科毕业、研究生班毕业、硕士研究生毕业、博士研究生毕业、其他
 */
export enum HighestEducationType {
    /** 大学本科毕业 */
    UNDERGRADUATE = 0,
    /** 研究生班毕业 */
    POSTGRADUATE_COURSE = 1,
    /** 硕士研究生毕业 */
    MASTER = 2,
    /** 博士研究生毕业 */
    DOCTOR = 3,
    /** 其他 */
    OTHER = 4,
}

// 生成对应Map
export const HighestEducationTypeMap: Record<HighestEducationType, string> = {
    [HighestEducationType.UNDERGRADUATE]: "大学本科毕业",
    [HighestEducationType.POSTGRADUATE_COURSE]: "研究生班毕业",
    [HighestEducationType.MASTER]: "硕士研究生毕业",
    [HighestEducationType.DOCTOR]: "博士研究生毕业",
    [HighestEducationType.OTHER]: "其他",
} 