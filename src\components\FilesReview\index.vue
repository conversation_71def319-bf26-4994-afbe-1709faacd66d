<template>
    <div class="files-review-container">
        <div class="header">
            <div class="file-name-header">文件名</div>
            <div class="actions-header">
                <div>预览</div>
                <div>下载</div>
            </div>
        </div>
        <div class="files-list">
            <div v-for="(file, index) in parsedFiles" :key="index" class="file-item">
                <div class="file-info">
                    <div class="file-icon">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                            width="14"
                            height="14"
                            viewBox="0 0 14 14">
                            <defs>
                                <clipPath id="clipPath3892462415">
                                    <path
                                        d="M0 0L14 0L14 14L0 14L0 0Z"
                                        fill-rule="nonzero"
                                        transform="matrix(1 0 0 1 0 0)" />
                                </clipPath>
                            </defs>
                            <g clip-path="url(#clipPath3892462415)">
                                <path
                                    d="M9.45783 1.10391C7.98595 -0.367969 5.58908 -0.367969 4.11876 1.10391L0.0406396 5.17891C0.0140772 5.20547 1.45435e-05 5.24141 1.45435e-05 5.27891C1.45435e-05 5.31641 0.0140769 5.35234 0.0406396 5.37891L0.617202 5.95547C0.644636 5.98278 0.677709 5.99644 0.716421 5.99644C0.755133 5.99644 0.788206 5.98278 0.815639 5.95547L4.89376 1.88047C5.40001 1.37422 6.07345 1.09609 6.78908 1.09609C7.5047 1.09609 8.17814 1.37422 8.68283 1.88047C9.18908 2.38672 9.4672 3.06016 9.4672 3.77422C9.4672 4.48984 9.18908 5.16172 8.68283 5.66797L4.52658 9.82266L3.85314 10.4961C3.22345 11.1258 2.20001 11.1258 1.57033 10.4961C1.26564 10.1914 1.09845 9.78672 1.09845 9.35547C1.09845 8.92422 1.26564 8.51953 1.57033 8.21484L5.69376 4.09297C5.79845 3.98984 5.93595 3.93203 6.08283 3.93203L6.08439 3.93203C6.23126 3.93203 6.3672 3.98984 6.47033 4.09297C6.57501 4.19766 6.63126 4.33516 6.63126 4.48203C6.63126 4.62734 6.57345 4.76484 6.47033 4.86797L3.10001 8.23516C3.07345 8.26172 3.05939 8.29766 3.05939 8.33516C3.05939 8.37266 3.07345 8.40859 3.10001 8.43516L3.67658 9.01172C3.70401 9.03903 3.73708 9.05269 3.7758 9.05269C3.81451 9.05269 3.84758 9.03903 3.87502 9.01172L7.24376 5.64297C7.5547 5.33203 7.72501 4.91953 7.72501 4.48047C7.72501 4.04141 7.55314 3.62734 7.24376 3.31797C6.60158 2.67578 5.55783 2.67734 4.91564 3.31797L4.51564 3.71953L0.793765 7.43984C0.538642 7.69347 0.342275 7.98647 0.204664 8.31885C0.0670524 8.65123 -0.00116388 8.99729 1.50204e-05 9.35703C1.50204e-05 10.0805 0.282828 10.7602 0.793765 11.2711C1.32345 11.7992 2.0172 12.0633 2.71095 12.0633C3.4047 12.0633 4.09845 11.7992 4.62658 11.2711L9.45783 6.44297C10.1688 5.73047 10.5625 4.78203 10.5625 3.77422C10.5641 2.76484 10.1703 1.81641 9.45783 1.10391Z"
                                    fill-rule="nonzero"
                                    transform="matrix(1 0 0 1 1.71873 0.967969)"
                                    fill="rgb(0, 0, 0)"
                                    fill-opacity="0.8784" />
                            </g>
                        </svg>
                    </div>
                    <div class="file-name">{{ file.name }}</div>
                </div>
                <div class="file-actions">
                    <div v-if="file.isPdf" class="action-btn preview-btn" @click="previewFile(file)">
                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                            <path
                                d="M1 7C1 7 3.4 3.6 7 3.6S13 7 13 7S10.6 10.4 7 10.4S1 7 1 7Z"
                                stroke="#1677FF"
                                stroke-width="1"
                                stroke-linecap="round"
                                stroke-linejoin="round" />
                            <circle cx="7" cy="7" r="2.1" stroke="#1677FF" stroke-width="1" />
                        </svg>
                    </div>
                    <div class="action-btn download-btn" @click="downloadSingleFile(file)">
                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                            <path
                                d="M12.25 8.75V11.25C12.25 11.8023 11.8023 12.25 11.25 12.25H2.75C2.19772 12.25 1.75 11.8023 1.75 11.25V8.75"
                                stroke="#1677FF"
                                stroke-width="1"
                                stroke-linecap="round"
                                stroke-linejoin="round" />
                            <path
                                d="M4.375 5.25L7 7.875L9.625 5.25"
                                stroke="#1677FF"
                                stroke-width="1"
                                stroke-linecap="round"
                                stroke-linejoin="round" />
                            <path
                                d="M7 7.875V1.75"
                                stroke="#1677FF"
                                stroke-width="1"
                                stroke-linecap="round"
                                stroke-linejoin="round" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { downloadFile } from "@/utils/fileDownload";
import { ElMessage } from "element-plus";
import { filesBaseUrl } from "@/utils/filesBaseUrl";
interface Props {
    files?: string;
}

interface ParsedFile {
    originPath: string;
    name: string;
    isPdf: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    files: "",
});

const parsedFiles = computed(() => {
    if (!props.files) return [];
    return props.files
        .split(",")
        .map((fileStr) => {
            console.log(fileStr, "fileStr");
            const [_, name] = fileStr.split("::");
            const isPdf = name?.toLowerCase().endsWith(".pdf") || false;

            return {
                originPath: fileStr?.trim(),
                name: name?.trim() || "",
                isPdf,
            };
        })
        .filter((file) => file.originPath && file.name);
});

const previewFile = (file: ParsedFile) => {
    if (file.isPdf) {
        const previewUrl = `${filesBaseUrl}${file.originPath}`;
        window.open(previewUrl, "_blank");
    }
};

const downloadSingleFile = async (file: ParsedFile) => {
    try {
        await downloadFile(file.originPath, file.name);
        ElMessage.success(`${file.name} 开始下载`);
    } catch (error) {
        console.error("下载失败：", error);
        ElMessage.error(`${file.name} 下载失败`);
    }
};
</script>

<style scoped lang="scss">
.files-review-container {
    width: 100%;
    max-width: 534.5px;
    font-size: 14px;
    font-weight: 400;

    .header {
        background: #ebf1ff;
        border: 1px solid #e7e7e7;
        border-radius: 6px 6px 0 0;
        display: flex;
        justify-content: space-between;
        padding: 8px 32px 8px 24px;

        .file-name-header {
            color: rgba(0, 0, 0, 0.88);
            line-height: 21px;
            min-height: 21px;
        }

        .actions-header {
            display: flex;
            gap: 16px;
            color: rgba(0, 0, 0, 0.88);
            line-height: 21px;
            min-height: 21px;
        }
    }

    .files-list {
        background: #ffffff;
        border: 1px solid #e7e7e7;
        border-top: none;
        border-radius: 0 0 6px 6px;
        padding: 8px 16px;

        .file-item:last-child {
            border-bottom: none;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 14.5px 23px 14.5px 0;
            border-bottom: 1px solid rgba(5, 5, 5, 0.06);

            .file-info {
                display: flex;
                align-items: center;
                gap: 16px;

                .file-icon {
                    color: rgba(0, 0, 0, 0.65);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .file-name {
                    color: rgba(0, 0, 0, 0.88);
                    line-height: 22px;
                    min-height: 22px;
                    max-width: 200px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }

            .file-actions {
                display: flex;
                gap: 30px;
                align-items: center;

                .action-btn {
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 14px;
                    height: 14px;
                    transition: opacity 0.2s;
                }

                .action-btn:hover {
                    opacity: 0.7;
                }

                .preview-btn {
                    color: #1677ff;
                }

                .download-btn {
                    color: #1677ff;
                }
            }
        }
    }
}
</style>
