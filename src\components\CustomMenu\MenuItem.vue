<template>
    <div class="custom-menu-item" :class="{ 'is-active': isActive }" @click="handleClick">
        {{ title }}
    </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useRoute } from "vue-router";

const props = defineProps<{
    path: string;
    title: string;
}>();

const emit = defineEmits<{
    (e: "select", path: string): void;
}>();

const route = useRoute();

const isActive = computed(() => {
    return route.path.split("/").slice(0, 3).join("/") === props.path;
});

const handleClick = () => {
    emit("select", props.path);
};
</script>

<style scoped lang="scss">
.custom-menu-item {
    padding: 0px 16px 0px 56px;
    height: 40px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    line-height: 18px;
    letter-spacing: 1.4px;
    min-height: 18px;
    display: flex;
    align-items: center;


    &:hover {
        background-color: rgba(255, 255, 255, 0.08);
        border-radius: 8px;
    }

    &.is-active {
        background-color: #1668dc;
        border-radius: 8px;
        color: #ffffff;
        font-weight: 700;
    }
}
</style>
