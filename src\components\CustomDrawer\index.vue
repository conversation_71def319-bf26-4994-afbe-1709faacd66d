<template>
    <el-drawer
        v-model="visible"
        :show-close="false"
        :close-on-click-modal="closeOnClickModal"
        :close-on-press-escape="closeOnPressEscape"
        :size="size"
        :direction="direction"
        :modal="modal"
        :lock-scroll="lockScroll"
        :append-to-body="appendToBody"
        :before-close="handleBeforeClose"
        :class="drawerClass">
        <template #header="{ close }">
            <div class="custom-drawer-header-container">
                <!-- 关闭图标 -->
                <div class="drawer-title-icon" @click="handleClose(close)">
                    <slot name="close-icon">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                            width="16"
                            height="16"
                            viewBox="0 0 16 16">
                            <defs>
                                <clipPath id="clipPath2722600539">
                                    <path
                                        d="M0 0L16 0L16 16L0 16L0 0Z"
                                        fill-rule="nonzero"
                                        transform="matrix(1 0 0 1 0 0)" />
                                </clipPath>
                            </defs>
                            <g clip-path="url(#clipPath2722600539)">
                                <path
                                    d="M11.3135 9.31714e-05C11.3138 9.31714e-05 11.3142 0.000450271 11.3149 0.00116456L12.3451 1.03152C12.3458 1.03206 12.346 1.03241 12.3462 1.03295C12.3463 1.0333 12.3463 1.03367 12.3462 1.03402C12.3462 1.03456 12.3458 1.03491 12.3451 1.03563L7.20759 6.17313L12.3451 11.3106C12.3458 11.3113 12.346 11.3117 12.3462 11.3122C12.3463 11.3126 12.3463 11.3131 12.3462 11.3135C12.3462 11.3138 12.3458 11.3142 12.3451 11.3149L11.3147 12.3451C11.3142 12.3458 11.3138 12.346 11.3135 12.3462C11.3131 12.3463 11.3126 12.3463 11.3122 12.3462C11.3117 12.3462 11.3113 12.3458 11.3106 12.3451L6.17313 7.20759L1.03563 12.3451C1.03491 12.3458 1.03456 12.346 1.03402 12.3462C1.03361 12.3463 1.03318 12.3463 1.03277 12.3462C1.03241 12.3462 1.03206 12.3458 1.03134 12.3451L0.00116456 11.3147C0.000450271 11.3142 0.000271743 11.3138 9.31714e-05 11.3135C-3.10571e-05 11.3131 -3.10571e-05 11.3126 9.31714e-05 11.3122C9.31714e-05 11.3117 0.000450271 11.3113 0.00116456 11.3106L5.13866 6.17313L0.00116456 1.03563C0.000450271 1.03491 0.000271743 1.03456 9.31714e-05 1.03402C-3.10571e-05 1.03361 -3.10571e-05 1.03318 9.31714e-05 1.03277C9.31714e-05 1.03241 0.000450271 1.03206 0.00116456 1.03134L1.03152 0.00116456C1.03206 0.000450271 1.03241 0.000271743 1.03277 9.31714e-05C1.03318 -3.10571e-05 1.03361 -3.10571e-05 1.03402 9.31714e-05C1.03456 9.31714e-05 1.03491 0.000450271 1.03563 0.00116456L6.17313 5.13866L11.3106 0.00116456C11.3113 0.000450271 11.3117 0.000271743 11.3122 9.31714e-05C11.3126 -3.10571e-05 11.3131 -3.10571e-05 11.3135 9.31714e-05Z"
                                    fill-rule="evenodd"
                                    transform="matrix(1 0 0 1 1.82687 1.82687)"
                                    fill="rgb(0, 0, 0)"
                                    fill-opacity="0.45" />
                            </g>
                        </svg>
                    </slot>
                </div>

                <!-- 标题 -->
                <div class="drawer-title">
                    <slot name="title">{{ title }}</slot>
                </div>

                <!-- 操作按钮区域 -->
                <div>
                    <slot name="actions" :close="handleClose.bind(null, close)">
                        <el-button @click="handleClose(close)">{{ cancelText }}</el-button>
                        <el-button type="primary" @click="handleConfirm">{{ confirmText }}</el-button>
                    </slot>
                </div>
            </div>
        </template>

        <!-- 内容区域 -->
        <div class="custom-drawer-content">
            <!-- 警告提示区域（可选） -->
            <div v-if="$slots.alert" class="custom-drawer-alert-warpper">
                <div class="custom-drawer-alert">
                    <slot name="alert"></slot>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <slot></slot>
        </div>
    </el-drawer>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, watch } from "vue";

type DrawerDirection = "rtl" | "ltr" | "ttb" | "btt";

// Props定义
const props = withDefaults(
    defineProps<{
        modelValue: boolean;
        title?: string;
        size?: string | number;
        direction?: DrawerDirection;
        modal?: boolean;
        lockScroll?: boolean;
        appendToBody?: boolean;
        closeOnClickModal?: boolean;
        closeOnPressEscape?: boolean;
        cancelText?: string;
        confirmText?: string;
        drawerClass?: string;
    }>(),
    {
        modelValue: false,
        title: "",
        size: 378,
        direction: "rtl",
        modal: true,
        lockScroll: true,
        appendToBody: true,
        closeOnClickModal: false,
        closeOnPressEscape: true,
        cancelText: "取消",
        confirmText: "确定",
        drawerClass: "",
    }
);

// Emits定义
const emit = defineEmits<{
    "update:modelValue": [value: boolean];
    close: [];
    confirm: [];
    "before-close": [done: () => void];
}>();

// 计算属性
const visible = computed({
    get: () => props.modelValue,
    set: (value: boolean) => emit("update:modelValue", value),
});

// 方法
const handleClose = (close?: () => void) => {
    if (close) {
        close();
    } else {
        visible.value = false;
    }
    emit("close");
};

const handleConfirm = () => {
    emit("confirm");
};

const handleBeforeClose = (done: () => void) => {
    emit("before-close", done);
};

// 手动ESC键监听器（作为备选方案，确保ESC键能够正常工作）
const handleEscapeKey = (event: KeyboardEvent) => {
    if (event.key === "Escape" && props.closeOnPressEscape && visible.value) {
        event.preventDefault();
        event.stopPropagation();
        handleClose();
    }
};

// 监听抽屉的显示状态，动态添加/移除ESC键监听器
watch(
    visible,
    (newValue) => {
        if (newValue && props.closeOnPressEscape) {
            // 抽屉打开时添加ESC键监听
            document.addEventListener("keydown", handleEscapeKey, true);
        } else {
            // 抽屉关闭时移除ESC键监听
            document.removeEventListener("keydown", handleEscapeKey, true);
        }
    },
    { immediate: true }
);

// 组件卸载时清理事件监听器
onUnmounted(() => {
    document.removeEventListener("keydown", handleEscapeKey, true);
});
</script>

<style lang="scss" scoped>
// 导入现有的抽屉样式
@use "@/assets/styles/customDrawer.scss";
</style>
