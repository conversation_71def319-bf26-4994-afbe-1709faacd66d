/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 添加后台资源分类 POST /ums/umsResourceCategory/create */
export async function umsResourceCategoryCreate({
  body,
  options,
}: {
  body: API.UmsResourceCategory_;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsResourceCategory/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据ID删除后台资源 POST /ums/umsResourceCategory/delete/${param0} */
export async function umsResourceCategoryDeleteId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsResourceCategoryDeleteidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultVoid_>(
    `/ums/umsResourceCategory/delete/${param0}`,
    {
      method: 'POST',
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}

/** 查询所有后台资源分类 GET /ums/umsResourceCategory/listAll */
export async function umsResourceCategoryListAll({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListUmsResourceCategory_>(
    '/ums/umsResourceCategory/listAll',
    {
      method: 'GET',
      ...(options || {}),
    }
  );
}

/** 修改后台资源分类 POST /ums/umsResourceCategory/update/${param0} */
export async function umsResourceCategoryUpdateId({
  params,
  body,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsResourceCategoryUpdateidParams;
  body: API.UmsResourceCategory_;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultVoid_>(
    `/ums/umsResourceCategory/update/${param0}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      params: { ...queryParams },
      data: body,
      ...(options || {}),
    }
  );
}
