import { createWebHistory, createRouter, createWebHashHistory } from 'vue-router'
import Index from "@/views/index"
import component from 'element-plus/es/components/tree-select/src/tree-select-option.mjs';
import { getDynamicRedirectPath } from '@/utils/routeRedirect';
/* Layout */
// import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
    {
        path: "/:pathMatch(.*)*",
        component: () => import('@/views/error/404'),
        hidden: true
    },
    {
        path: '/401',
        component: () => import('@/views/error/401'),
        hidden: true
    },
    {
        path: '',
        redirect: '/index',
        // children: [
        //   {
        //     path: '/index',
        //     component: () => import('@/views/index'),
        //     name: 'Index',
        //   }
        // ]
    },
    {
        path: '/index',
        component: () => import('@/views/index'),
    },
    {
        path: '/login',
        component: () => import('@/views/login'),
    },
    {
        path: '/newsDetail',
        component: () => import('@/views/newsDetail'),
    },
    // 动态重定向：根据用户权限重定向至有权限访问的第一个页面
    {
        path: "/backgroundManagement/centralSettings",
        redirect: () => {
            return getDynamicRedirectPath('/backgroundManagement/centralSettings') || '/404';
        },
    },
    {
        path: "/backgroundManagement/permissionManagement",
        redirect: () => {
            return getDynamicRedirectPath('/backgroundManagement/permissionManagement') || '/404';
        },
    },
    {
        path: "/workspace/teamManagement",
        redirect: () => {
            return getDynamicRedirectPath('/workspace/teamManagement') || '/404';
        },
    },
    {
        path: "/workspace/adminApproval",
        redirect: () => {
            return getDynamicRedirectPath('/workspace/adminApproval') || '/404';
        },
    },
    {
        path: "/workspace/adminEntry",
        redirect: () => {
            return getDynamicRedirectPath('/workspace/adminEntry') || '/404';
        },
    },
    {
        path: "/workspace/teacherEntry",
        redirect: () => {
            return getDynamicRedirectPath('/workspace/teacherEntry') || '/404';
        },
    },
    {
        path: "/workspace/newsManagement",
        redirect: () => {
            return getDynamicRedirectPath('/workspace/newsManagement') || '/404';
        },
    },
    {
        path: "/backgroundManagement/infoManagement",
        redirect: () => {
            return getDynamicRedirectPath('/backgroundManagement/infoManagement') || '/404';
        },
    },
    // 学院绩效统计页 - 根据用户权限动态重定向
    {
        path: "/workspace/CollegePerformanceStatistics",
        redirect: () => {
            return getDynamicRedirectPath('/workspace/CollegePerformanceStatistics') || '/404';
        },
    },
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
    {
        path: '/system/user-auth',
        component: "",
        hidden: true,
        permissions: ['system:user:edit'],
        children: [
            {
                path: 'role/:userId(\\d+)',
                component: "",
                name: 'AuthRole',
                meta: { title: '分配角色', activeMenu: '/system/user' }
            }
        ]
    },
    {
        path: '/system/role-auth',
        component: "",
        hidden: true,
        permissions: ['system:role:edit'],
        children: [
            {
                path: 'user/:roleId(\\d+)',
                component: "",
                name: 'AuthUser',
                meta: { title: '分配用户', activeMenu: '/system/role' }
            }
        ]
    }
]

const router = createRouter({
    history: createWebHashHistory(),
    routes: constantRoutes,
    scrollBehavior(to, from, savedPosition) {
        if (savedPosition) {
            return savedPosition
        } else {
            return { top: 0 }
        }
    },
});

export default router;
