<template>
    <div>
        <!-- 人员类型字段（单选或多选） -->
        <div v-if="isPersonField">
            {{ getPersonName(personList, fieldValue) }}
        </div>

        <!-- 日期类型字段 -->
        <div v-else-if="fieldType === categoryTemplateValueType_NEW.DATE || 
                       fieldType === categoryTemplateValueType_NEW.PROJECT_DATE">
            {{ fieldValue ? dayjs(fieldValue).format("YYYY-MM-DD") : "-" }}
        </div>

        <!-- 文件类型字段 -->
        <div v-else-if="fieldType === categoryTemplateValueType_NEW.FILE">
            <FileIconDisplay
                v-if="fieldValue"
                :fileUrls="fieldValue || ''" />
            <div v-else>-</div>
        </div>

        <!-- 字典类型字段（级别、单选、多选） -->
        <div v-else-if="isDictionaryField">
            {{ getFieldName(dictList, fieldValue) }}
        </div>

        <!-- 依托专业类型字段 -->
        <div v-else-if="fieldType === categoryTemplateValueType_NEW.DEPENDENT_MAJOR">
            {{ getDependentMajorNames(orgList, fieldValue) }}
        </div>

        <!-- 是否类型字段 -->
        <div v-else-if="fieldType === categoryTemplateValueType_NEW.ISN">
            {{ getIsField(fieldValue) }}
        </div>

        <!-- 默认显示（字符串、整数、浮点数、金额等） -->
        <div v-else>
            {{ fieldValue || "-" }}
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { categoryTemplateValueType_NEW } from "@/enums/categoryTemplate/categoryTemplateValueType";
import { getFieldName, getPersonName } from "@/utils/getNames";
import { dayjs } from "element-plus";
import FileIconDisplay from "@/components/FileIconDisplay/index.vue";
import type { DmsDict_, OmsOrganization_, UmsPerson_, CmsCategoryTemplateValue10, UmsDept_ } from "@/apis/types";

// 定义组件属性
const props = defineProps<{
    /** 字段定义信息 */
    field: CmsCategoryTemplateValue10;
    /** 行数据 */
    rowData: {
        values: Array<{
            categoryTemplateValueId: number;
            value: any;
        }>;
    };
    /** 人员列表 */
    personList: UmsPerson_[];
    /** 字典列表 */
    dictList: DmsDict_[];
    /** 组织列表 */
    orgList: UmsDept_[];
}>();

// 计算字段ID，确保存在
const fieldId = computed(() => props.field?.id || 0);

// 计算字段类型，确保存在
const fieldType = computed(() => props.field?.type || categoryTemplateValueType_NEW.STRING);

// 计算字段值
const fieldValue = computed(() => {
    if (!fieldId.value) return '';
    
    return props.rowData.values.find(
        (val) => val.categoryTemplateValueId === fieldId.value
    )?.value;
});

// 判断是否为人员类型字段
const isPersonField = computed(() => {
    return (
        fieldType.value === categoryTemplateValueType_NEW.PERSON_SINGLE ||
        fieldType.value === categoryTemplateValueType_NEW.PERSON_MULTI ||
        fieldType.value === categoryTemplateValueType_NEW.PROJECT_MAIN ||
        fieldType.value === categoryTemplateValueType_NEW.PROJECT_PARTICIPATE
    );
});

// 判断是否为字典类型字段
const isDictionaryField = computed(() => {
    return (
        fieldType.value === categoryTemplateValueType_NEW.LEVEL ||
        fieldType.value === categoryTemplateValueType_NEW.ENUM_SINGLE ||
        fieldType.value === categoryTemplateValueType_NEW.ENUM_MULTI ||
        fieldType.value === categoryTemplateValueType_NEW.PROJECT_STATUS
    );
});

// 获取依托专业名称
function getDependentMajorNames(orgList: UmsDept_[], value: string | undefined): string {
    if (!value) return "-";
    // 拆分多个ID
    const ids = value.split(",");
    // 获取每个ID对应的组织名称
    const names = ids.map((id) => {
        const org = orgList.find((org) => org.id.toString() === id.trim());
        return org ? org.name : id; // 如果找不到对应组织，则显示原始ID
    });

    // 用逗号连接所有名称
    return names.join(", ");
}

// 获取是否字段值
function getIsField(value: string): string {
    if (!value && value !== "0") return "-";
    return value === "1" ? "是" : "否";
}
</script> 