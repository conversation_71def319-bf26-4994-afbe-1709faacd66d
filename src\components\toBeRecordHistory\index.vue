<template>
    <div class="wrapper">
        <GeneralDataDisplay>
            <template #top>
                <div class="top">
                    <div>
                        <el-input
                            style="width: 420px; margin-right: 10px"
                            placeholder="请输入数据表名称、项目名称、审核人、参与人搜索"
                            v-model="searchKeyword"
                            :suffix-icon="Search"
                            @keyup.enter="handleSearch" />
                        <el-button color="#586FBB" type="primary" @click="handleSearch">搜索</el-button>
                    </div>
                </div>
            </template>

            <template #content>
                <div class="content">
                    <DataTable
                        :is-load-first="true"
                        :search-keyword="searchKeyword"
                        :is-local-search="false"
                        ref="dataTableRef"
                        @itemClick="loadDetail"
                        :load-more-func="loadMoreFunc">
                        <template #title="{ item }: { item: CmsRowValueResult }">
                            <div class="title">
                                {{ getTemplateNameById(item.categoryTemplateId) }}
                            </div>
                        </template>
                        <template #desc="{ item }: { item: CmsRowValueResult }">
                            <div>项目名称：{{ item.projectName ? item.projectName : "-" }}</div>
                            <div>
                                项目归属人：{{
                                    item.owner > 0 ? getPersonName(allPersonListWithDisabled, String(item.owner)) : "-"
                                }}
                            </div>
                            <div>
                                审批人：{{
                                    item.approvalBy
                                        ? getPersonName(allPersonListWithDisabled, String(item.approvalBy))
                                        : "-"
                                }}
                            </div>
                            <div>
                                审批状态：{{ item.approvalStatus ? approvalStatusMap[item.approvalStatus] : "-" }}
                            </div>
                        </template>
                        <template #detail="{ selectedItem }">
                            <Detail
                                :selected-item="detailData"
                                @refresh="refreshAll"
                                :detailLoading="detailLoading"
                                :all-person-list="allPersonListWithDisabled" />
                        </template>
                    </DataTable>
                </div>
            </template>
        </GeneralDataDisplay>
    </div>
</template>

<script setup lang="ts">
import GeneralDataDisplay from "@/components/GeneralDataDisplay/index.vue";
import DataTable from "@/components/DataTable/index.vue";
import Detail from "./detail.vue";
import { Search } from "@element-plus/icons-vue";
import { CmsRowValueResult } from "@/apis/types";
import { cmsCategoryRowOwnerCreateGetByApprovalAndType } from "@/apis/cmsCategoryRowController";
import { approvalStatusMap } from "@/enums/approval/approvalStatus";
import { useCategoryTemplateList } from "@/hooks/useCategoryTemplate";
import { usePersons } from "@/hooks/usePersons";
import { getPersonName } from "@/utils/getNames";

const { getTemplateNameById } = useCategoryTemplateList();

const searchKeyword = ref("");
const detailData = ref<CmsRowValueResult>();
const dataTableRef = ref();
const detailLoading = ref(false);
// 所有人员列表(包含已被禁用的人员)
const { allPersonListWithDisabled } = usePersons(true);

// 加载详情
const loadDetail = (item) => {
    detailLoading.value = true;
    detailData.value = null; // 清空之前的详情数据

    detailData.value = item;
    detailLoading.value = false;
};

// 刷新列表
const refreshAll = () => {
    if (dataTableRef.value) {
        dataTableRef.value.refreshAll();
    }
};

// 处理搜索
const handleSearch = () => {
    if (dataTableRef.value) {
        dataTableRef.value.search();
    }
};

// 加载更多函数
const loadMoreFunc = (pageNum: number, pageSize: number, searchKeyword?: string) => {
    return cmsCategoryRowOwnerCreateGetByApprovalAndType({
        params: {
            pageNum,
            pageSize,
            approvalStatus: [0, 1, 2, 3, 4],
            rowTypes: ["IMPORT", "INSERT"],
            name: searchKeyword.trim(),
        },
    });
};
</script>

<style scoped lang="scss">
.wrapper {
    height: 100%;
    .top {
        padding: 20px;
        background: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .content {
        height: 100%;
        .title {
            font-weight: bold;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
</style>
