<template>
    <div class="wrapper">
        <div class="table">
            <el-table
                :header-cell-style="{ backgroundColor: '#D9E3F6', color: '#6B7995', fontWeight: '600' }"
                :data="tagList"
                max-height="calc(100vh - 300px)"
                row-key="id"
                :expand-row-keys="expandedRowKeys"
                @expand-change="handleExpandChange"
                class="custom-tree-table"
                :row-class-name="tableRowClassName"
                :cell-style="cellStyle">
                <el-table-column prop="tagName" label="绩效规则" min-width="150" />
                <el-table-column prop="maxScore" label="分值" width="60" align="center" />
                <el-table-column label="操作" width="420">
                    <template #default="scope">
                        <el-button
                            class="custom-table-button"
                            color="#D2E7FF"
                            size="small"
                            @click="handleEdit(scope.row)">
                            编辑
                        </el-button>
                        <el-button
                            class="custom-table-button"
                            color="#D2E7FF"
                            size="small"
                            @click="handleDelete(scope.row)">
                            删除
                        </el-button>
                        <el-button
                            class="custom-table-button"
                            v-if="isShowAddIndicators(scope.row)"
                            color="#D2E7FF"
                            size="small"
                            @click="addIndicator(scope.row, TagType.INDICATORS)">
                            添加下级指标
                        </el-button>
                        <el-button
                            class="custom-table-button"
                            v-if="isShowAddRules(scope.row)"
                            color="#D2E7FF"
                            size="small"
                            @click="addIndicator(scope.row, TagType.RULES)">
                            添加绩效规则
                        </el-button>
                        <el-button
                            class="custom-table-button"
                            @click="handleConfigAllocation(scope.row)"
                            v-if="scope.row.tagType === TagType.RULES"
                            color="#D2E7FF"
                            size="small">
                            配置分配规则
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="addBtn">
            <el-button @click="addIndicator(null, TagType.INDICATORS)" color="#6B79954D" circle>+</el-button
            >新增一级指标
        </div>

        <!-- 新增/编辑标签弹窗 -->
        <el-dialog v-model="addOrUpdateTagFormVisible" :title="isAddOREdit + isIndicatorsOrRules" width="500">
            <el-form
                label-position="top"
                ref="addOrUpdateTagFormRef"
                :model="addOrUpdateTagForm"
                :rules="addOrUpdateTagFormRules">
                <el-form-item
                    :label="isIndicatorsOrRules == '下级指标' ? isIndicatorsOrRules + '名称' : '评价标准'"
                    prop="tagName">
                    <el-input
                        placeholder="请输入文本"
                        :autosize="{ minRows: 6, maxRows: 24 }"
                        type="textarea"
                        style="width: 100%"
                        v-model="addOrUpdateTagForm.tagName"
                        class="performance-rule-textarea" />
                </el-form-item>
                <el-form-item label="分数上限" prop="maxScore">
                    <el-input placeholder="请输入数字" v-model.number="addOrUpdateTagForm.maxScore" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="addOrUpdateTagFormVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitAddTagForm(addOrUpdateTagFormRef)">保存</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 新增/编辑标签弹窗 -->

        <!-- 配置分配规则弹窗 -->
        <el-dialog v-model="configAllocationFormVisible" title="配置分配规则" width="600">
            <div>
                <div style="color: #000000e0; font-size: 16px; margin-top: 15px">评价标准：</div>
                <div class="evaluation-criteria">{{ configAllocationData.evaluationCriteria }}</div>
                <div style="color: #000000e0; font-size: 16px; margin-top: 24px">已关联数据表：</div>
                <div style="color: rgba(0, 0, 0, 0.45); margin-top: 12px">
                    点击"添加数据表"可将本条评价标准关联到已有数据表。点击数据表可进行具体得分规则的配置。
                </div>
                <div class="template-list">
                    <div
                        @click="showConfigAllocationDetail(templateId)"
                        class="template-item"
                        v-for="templateId in chooseTemplateList"
                        :key="templateId">
                        <div class="template-name">{{ getTemplateName(Number(templateId)) }}</div>
                        <span>></span>
                    </div>
                </div>
                <div style="margin-top: 20px">
                    <el-button type="primary" @click="openAddRelatedTemplateDialog()"
                        >{{ chooseTemplateList.length > 0 ? "更改已关联" : "+ 添加关联" }}数据表</el-button
                    >
                </div>
            </div>
            <template #footer>
                <div>
                    <el-button @click="configAllocationFormVisible = false">取消</el-button>
                    <el-button type="primary" @click="configAllocationFormVisible = false">确认</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 添加数据表弹窗 -->
        <el-dialog
            v-model="addRelatedTemplateFormVisible"
            title="添加数据表"
            width="600"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :before-close="cancelAddRelatedTemplateForm">
            <div>
                <el-select
                    filterable
                    multiple
                    v-model="tempList"
                    placeholder="请选择数据表（可多选），输入数据表名称可快速搜索">
                    <el-option
                        v-for="v in filteredList"
                        :key="v.id"
                        :label="v.templateName"
                        :value="v.id"
                        :disabled="isTemplateRelated(v.id)">
                        <span v-if="isTemplateRelated(v.id)" style="color: #c0c4cc"
                            >{{ v.templateName }} （已被其他绩效规则关联）</span
                        >
                        <span v-else>{{ v.templateName }}</span>
                    </el-option>
                </el-select>
            </div>
            <template #footer>
                <div>
                    <el-button @click="cancelAddRelatedTemplateForm">取消</el-button>
                    <el-button type="primary" @click="saveRelatedTemplate">保存</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 添加数据表弹窗 -->

        <!-- 详细配置分配规则弹窗 -->
        <el-dialog
            class="config-allocation-detail-dialog"
            v-model="configAllocationDetailFormVisible"
            @close="handleCloseConfigAllocationDetailForm"
            title="详细配置分配规则"
            width="600">
            <div style="padding: 10px">
                <div
                    style="
                        border: 1px solid #99a0b8;
                        width: fit-content;
                        padding: 5px 10px;
                        margin-bottom: 10px;
                        background-color: #e2edff;
                        color: #23346d;
                        letter-spacing: 0.1em;
                    ">
                    {{ getTemplateName(nowSelectedTemplateId) }}
                </div>
                <div style="color: #23346d; font-size: 16px">评价标准：</div>
                <div class="evaluation-criteria">{{ configAllocationData.evaluationCriteria }}</div>
                <el-divider />
                <div>
                    <el-form ref="configAllocationDetailFormRef" :model="configAllocationDetailForm">
                        <el-form-item label="分配模式：">
                            <el-radio-group v-model="configAllocationDetailForm.allocateType">
                                <el-radio :value="tagAllocateType.PROJECT_SCORE"
                                    >项目制记分<img style="width: 20px" src="../../../../assets/question.png"
                                /></el-radio>
                                <el-radio :value="tagAllocateType.HEAD_SCORE"
                                    >人头制记分<img style="width: 20px" src="../../../../assets/question.png"
                                /></el-radio>
                                <el-radio :value="tagAllocateType.AMOUNT_SCORE"
                                    >金额累计制记分<img style="width: 20px" src="../../../../assets/question.png"
                                /></el-radio>
                            </el-radio-group>
                        </el-form-item>

                        <!-- BEGIN 项目制计分 -->
                        <div v-if="configAllocationDetailForm.allocateType === tagAllocateType.PROJECT_SCORE">
                            <el-form-item label="赋分方式：">
                                <el-radio-group v-model="configAllocationDetailForm.scoringMethod">
                                    <el-radio :value="tagScoringMethod.MANUAL">手动输入</el-radio>
                                    <el-radio :value="tagScoringMethod.AUTO">平台计算</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <div v-if="configAllocationDetailForm.scoringMethod === tagScoringMethod.AUTO">
                                <el-form-item label="计算方式：">
                                    <el-radio-group v-model="configAllocationDetailForm.calculationMethod">
                                        <el-radio :value="tagCalculationMethod.DATA_ITEM_COMBINATION"
                                            >数据项组合分值<img
                                                style="width: 20px"
                                                src="../../../../assets/question.png"
                                        /></el-radio>
                                        <el-radio :value="tagCalculationMethod.FIXED_SCORE"
                                            >固定分值<img style="width: 20px" src="../../../../assets/question.png"
                                        /></el-radio>
                                        <el-radio :value="tagCalculationMethod.DATA_ITEM_EQUAL_SCORE"
                                            >数据项等值分值<img
                                                style="width: 20px"
                                                src="../../../../assets/question.png"
                                        /></el-radio>
                                    </el-radio-group>
                                </el-form-item>
                                <div
                                    v-if="
                                        configAllocationDetailForm.calculationMethod ==
                                        tagCalculationMethod.DATA_ITEM_COMBINATION
                                    ">
                                    <el-form-item label="数据项选择：">
                                        <div class="checkBox">
                                            <div style="background-color: #f5f7fa; padding: 0 12px">
                                                <el-checkbox
                                                    v-model="checkAll"
                                                    :indeterminate="isIndeterminate"
                                                    @change="handleCheckAllChange">
                                                    <span style="color: #909399">可选数据项</span>
                                                </el-checkbox>
                                            </div>
                                            <div style="padding: 0 15px">
                                                <el-checkbox-group
                                                    style="display: flex; flex-direction: column"
                                                    v-model="configAllocationDetailForm.dataItemCombination"
                                                    @change="handleCheckedOptionsChange">
                                                    <el-checkbox
                                                        v-for="item in filteredFieldsOptions_combination"
                                                        :key="item.value"
                                                        :label="item.label"
                                                        :value="item.value">
                                                        {{ item.label }}
                                                    </el-checkbox>
                                                </el-checkbox-group>
                                            </div>
                                        </div>
                                    </el-form-item>
                                    <el-form-item
                                        v-if="configAllocationDetailForm.dataItemCombination?.length > 0"
                                        label="数据项组合：">
                                        <div class="combination-grid">
                                            <div
                                                class="combination-item"
                                                v-for="(item, i) in dataItemCombination"
                                                :key="i">
                                                <div class="combination-label">
                                                    {{ item.combinationName }}
                                                </div>
                                                <el-input
                                                    placeholder="请输入"
                                                    class="borderless-input combination-input"
                                                    v-model="item.score" />
                                                <div class="combination-unit">分</div>
                                            </div>
                                        </div>
                                    </el-form-item>
                                </div>
                                <el-form-item
                                    v-if="
                                        configAllocationDetailForm.calculationMethod ===
                                        tagCalculationMethod.FIXED_SCORE
                                    "
                                    label="填写固定分值：">
                                    <div style="display: flex; gap: 10px">
                                        <el-input-number
                                            v-model="configAllocationDetailForm.fixedScore"
                                            placeholder="请输入"
                                            :min="1"
                                            controls-position="right" />
                                        <div>分</div>
                                    </div>
                                </el-form-item>
                                <el-form-item
                                    v-if="
                                        configAllocationDetailForm.calculationMethod ==
                                        tagCalculationMethod.DATA_ITEM_EQUAL_SCORE
                                    "
                                    label="选择数据项：">
                                    <el-select
                                        placeholder="请选择"
                                        style="width: 200px"
                                        v-model="configAllocationDetailForm.dataItemEqualScore">
                                        <el-option
                                            v-for="v in dataItemEqualScoreOptions"
                                            :key="v.value"
                                            :label="v.label"
                                            :value="v.value" />
                                    </el-select>
                                </el-form-item>
                            </div>
                        </div>
                        <!-- END 项目制计分 -->

                        <!-- BEGIN 人头制计分 -->
                        <div v-if="configAllocationDetailForm.allocateType === tagAllocateType.HEAD_SCORE">
                            <div style="display: flex">
                                <div>可计分数据项：</div>
                                <img style="width: 20px; height: 20px" src="../../../../assets/question.png" />
                                <div>
                                    <div v-for="item in configAllocationDetailForm.headScoreDataItem">
                                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px">
                                            <span style="min-width: 110px; text-align: right">{{ item.name }}</span>
                                            <el-input-number
                                                controls-position="right"
                                                style="width: 150px"
                                                v-model.number="item.score" />
                                            <span>分</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- END 人头制计分 -->

                        <!-- BEGIN 金额累计制计分 -->
                        <div v-if="configAllocationDetailForm.allocateType === tagAllocateType.AMOUNT_SCORE">
                            <el-form-item label="计分数据项：">
                                <el-select
                                    multiple
                                    placeholder="请选择"
                                    style="width: 200px"
                                    v-model="configAllocationDetailForm.amountComputeFieldIds">
                                    <el-option
                                        v-for="v in amountScoreDataItemOptions"
                                        :key="v.value"
                                        :label="v.label"
                                        :value="v.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="金额计分条件：">
                                <div
                                    style="margin-bottom: 8px"
                                    v-for="(item, index) in configAllocationDetailForm.amountComputeConditions"
                                    :key="index">
                                    <span>总金额达到</span>
                                    <el-input-number
                                        style="width: 108px; margin: 0 5px"
                                        controls-position="right"
                                        v-model="item.amountThreshold" />
                                    <span>（万元）获得</span>
                                    <el-input-number
                                        style="width: 108px; margin: 0 5px"
                                        controls-position="right"
                                        v-model="item.performanceScore" />
                                    <span>绩效分</span>
                                </div>
                                <el-button
                                    style="margin-top: 12px"
                                    :icon="Plus"
                                    type="primary"
                                    @click="addAmountComputeCondition"
                                    >添加计分条件</el-button
                                >
                            </el-form-item>
                        </div>
                        <!-- END 金额累计制计分 -->
                    </el-form>
                </div>
            </div>
            <template #footer>
                <div>
                    <el-button @click="configAllocationDetailFormVisible = false">取消</el-button>
                    <el-button @click="saveConfigAllocationDetail" type="primary">确认</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { TagType } from "@/enums/tag/tagType";
import { cmsTagSave, cmsTagGetTagList, cmsTagUpdate } from "@/apis/cmsTagController";
import { useCategoryTemplateList } from "@/hooks/useCategoryTemplate";
import { ElMessage, ElMessageBox, FormInstance, FormRules } from "element-plus";
import { useCustomDialog } from "@/components/CustomDialog";
import {
    cmsTagConfigBatchRelateCategory,
    CmsTagNode_,
    CmsTagConfigCombinationResp,
    cmsTagDeleteId,
    cmsTagGetById,
    cmsTagConfigGetCombinationListByType,
    cmsTagConfigGetTagConfig,
    cmsTagConfigGetRelateCategoryList,
    CmsCategoryTemplate0,
    cmsCategoryTemplateValueGetValueByTemplateId,
    cmsTagConfigSaveOrUpdate,
    CmsTag_,
} from "@/apis";
import { tagAllocateType, tagScoringMethod, tagCalculationMethod } from "@/enums/tag/tagConfig";
import { categoryTemplateValueType_NEW } from "@/enums/categoryTemplate/categoryTemplateValueType";
import { publicPerformanceType } from "@/models/publicPerformanceType";
import { Plus } from "@element-plus/icons-vue";

const customDialog = useCustomDialog();

onMounted(() => {
    fetchData();
});

const MAX_LEVEL = 5; // 最大层级
const isEdit = ref(false);
const tagList = ref<CmsTagNode_[]>([]);
const addOrUpdateTagFormRef = ref<FormInstance>();

const addOrUpdateTagFormVisible = ref(false);
const configAllocationFormVisible = ref(false);
const addRelatedTemplateFormVisible = ref(false);
const configAllocationDetailFormVisible = ref(false);

// 添加金额计分条件
const addAmountComputeCondition = () => {
    configAllocationDetailForm.amountComputeConditions.push({
        amountThreshold: 0,
        performanceScore: 0,
    });
};

// 已被关联的数据表ID列表（除当前规则已关联的）
const relatedCategoryIds = ref<number[]>([]);

const configAllocationDetailForm = reactive({
    /** 分配模式 */
    allocateType: tagAllocateType.PROJECT_SCORE,
    /** 赋分方式 */
    scoringMethod: null,
    /** 计算方式 */
    calculationMethod: null,
    /** 数据项组合 */
    dataItemCombination: [],
    /** 固定分值 */
    fixedScore: null,
    /** 数据项等值分值 */
    dataItemEqualScore: null,
    /** 可计分数据项 */
    headScoreDataItem: [],
    /** （金额累计制计分）金额计分条件列表 */
    amountComputeConditions: [
        {
            /** 金额达到指定阈值，阈值配置项（万元） */
            amountThreshold: 0,
            /** 达到指定金额后获得的绩效分 */
            performanceScore: 0,
        },
    ],
    /** （金额累计制计分）金额计分数据项id列表 */
    amountComputeFieldIds: [],
});

const configAllocationDetailFormRef = ref<FormInstance>();

const addOrUpdateTagForm = reactive({
    id: null,
    categoryTemplateIds: [],
    tagName: "",
    tagType: -1,
    parentId: 0,
    maxScore: null,
});

const tableRowClassName = ({ row }: { row: CmsTag_ }) => {
    if (row.tagType == TagType.RULES) {
        return `level-${row.tagLevel} level-rules`;
    } else {
        return `level-${row.tagLevel}`;
    }
};

// 设置单元格样式
const cellStyle = ({ row, column }) => {
    if (column.property === "tagName") {
        return {
            backgroundColor: "transparent",
        };
    }
};

const addOrUpdateTagFormRules = reactive<FormRules>({
    tagName: [{ required: true, message: "请输入数据项名称", trigger: "blur" }],
    // categoryTemplateIds: [{ required: true, message: "请选择数据项类型", trigger: "blur" }],
    maxScore: [
        // { required: true, message: "请输入分数上限", trigger: "blur" },
        { min: 0, message: "分数上限必须大于0且为数字", trigger: "blur", type: "number" },
    ],
});

// 当前展开的行
const expandedRowKeys = ref<string[]>([]);

// 处理行展开状态变化
const handleExpandChange = (row, expanded) => {
    if (expanded) {
        expandedRowKeys.value.push(row.id.toString());
    } else {
        const index = expandedRowKeys.value.indexOf(row.id.toString());
        if (index !== -1) {
            expandedRowKeys.value.splice(index, 1);
        }
    }
};

// 获取tagList
function fetchData() {
    // 保存当前展开的行
    const currentExpandedRows = [...expandedRowKeys.value];

    return new Promise<void>((resolve) => {
        cmsTagGetTagList({}).then((res) => {
            tagList.value = res.data;

            // 恢复之前展开的行
            expandedRowKeys.value = currentExpandedRows;

            resolve();
        });
        // 获取已关联的数据表，返回一个数组：[1,2,3]，里面为已关联数据表的id
        cmsTagConfigGetRelateCategoryList({}).then((res) => {
            if (res.code === 200 && res.data) {
                relatedCategoryIds.value = res.data;
            }
        });
    });
}

const { getFilteredList } = useCategoryTemplateList();
// 筛选出templateType不为1，即不包含院情数据
const filteredList = getFilteredList((template) => template.templateType != 1);

// 是否展示 添加下级指标按钮
const isShowAddIndicators = (row: CmsTagNode_) => {
    // 如果有子节点，且字节点中有tagType为RULES的，则不能再添加下级指标
    if (row.children && row.children.length > 0 && row.children[0].tagType === TagType.RULES) {
        return false;
    }
    // 如果当前层级已到第四级，则不能再添加下级指标
    if (row.tagLevel === 4) {
        return false;
    }

    if (row.tagType === TagType.INDICATORS) {
        return true;
    }
    if (row.tagType === TagType.RULES) {
        return false;
    }
};

// 是否展示 添加绩效规则按钮
const isShowAddRules = (row) => {
    if (row.children && row.children.length > 0 && row.children[0].tagType === TagType.RULES) {
        return true;
    }
    if (row.children && row.children.length > 0) {
        return false;
    } else if (row.tagType === TagType.RULES) {
        return false;
    } else if (row.children === "") {
        return true;
    }
    return true;
};

// 新增或编辑表单提交
const submitAddTagForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    await formEl.validate((valid, fields) => {
        if (valid) {
            if (isEdit.value) {
                cmsTagUpdate({
                    body: {
                        categoryTemplateIds: addOrUpdateTagForm.categoryTemplateIds,
                        id: addOrUpdateTagForm.id,
                        maxScore: addOrUpdateTagForm.maxScore,
                        tagName: addOrUpdateTagForm.tagName,
                    },
                }).then((res: any) => {
                    if (res.code === 200) {
                        ElMessage.success({ message: "编辑成功" });
                        addOrUpdateTagFormVisible.value = false;
                        fetchData();
                    } else {
                        ElMessage.error({ message: res.msg });
                    }
                });
            } else {
                const parentId = addOrUpdateTagForm.parentId;
                cmsTagSave({
                    body: {
                        categoryTemplateIds: addOrUpdateTagForm.categoryTemplateIds,
                        maxScore: addOrUpdateTagForm.maxScore,
                        tagName: addOrUpdateTagForm.tagName,
                        tagType: addOrUpdateTagForm.tagType,
                        parentTagId: parentId,
                    },
                }).then((res: any) => {
                    if (res.code === 200) {
                        ElMessage.success({ message: "添加成功" });
                        addOrUpdateTagFormVisible.value = false;
                        fetchData().then(() => {
                            // 如果父ID存在且尚未展开，则将其添加到展开列表
                            if (parentId && !expandedRowKeys.value.includes(parentId.toString())) {
                                expandedRowKeys.value.push(parentId.toString());
                            }
                        });
                    } else {
                        ElMessage.error({ message: res.msg });
                    }
                });
            }
        }
    });
};

// 新增下级指标 / 绩效规则
function addIndicator(row: any, tagType: TagType) {
    addOrUpdateTagForm.parentId = row?.id || 0;
    if (tagType === TagType.INDICATORS) {
        addOrUpdateTagForm.tagType = TagType.INDICATORS;
    } else if (tagType === TagType.RULES) {
        addOrUpdateTagForm.tagType = TagType.RULES;
    }
    addOrUpdateTagFormVisible.value = true;
}

// 计算属性判断addOrUpdateTagForm.tagType为TagType.INDICATORS时，新增弹窗标题为"新增下级指标"还是"新增绩效规则"
const isIndicatorsOrRules = computed(() => {
    if (addOrUpdateTagForm.tagType === TagType.INDICATORS) {
        return "下级指标";
    } else if (addOrUpdateTagForm.tagType === TagType.RULES) {
        return "绩效规则";
    }
});

// 计算当前是编辑还是新增
const isAddOREdit = computed(() => {
    if (isEdit.value) {
        return "编辑";
    } else {
        return "添加";
    }
});

// 编辑tag
function handleEdit(row: CmsTagNode_) {
    isEdit.value = true;

    nextTick(() => {
        Object.assign(addOrUpdateTagForm, row);
    });
    addOrUpdateTagFormVisible.value = true;
}

function handleDelete(row: any) {
    if (row.children && row.children.length > 0) {
        ElMessage.error({ message: "该指标下有子指标，无法删除" });
        return;
    }
    // 弹窗二次确认是否删除
    ElMessageBox.confirm("确定删除该条数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    }).then(() => {
        cmsTagDeleteId({ params: { id: row.id } }).then((res: any) => {
            if (res.code === 200) {
                ElMessage.success({ message: "删除成功" });
                addOrUpdateTagFormVisible.value = false;
                fetchData();
            } else {
                ElMessage.error({ message: res.msg });
            }
        });
    });
}

// 当前tag
const nowTag = ref<CmsTagNode_>(null);

// 当前选择的数据表id
const nowSelectedTemplateId = ref<number>(null);

// 配置分配规则
function handleConfigAllocation(row: CmsTagNode_) {
    nowTag.value = row;
    configAllocationFormVisible.value = true;

    chooseTemplateList.value = row.categoryTemplateIds;
    configAllocationData.evaluationCriteria = row.tagName;
}

// 配置分配规则弹窗数据
const configAllocationData = reactive<{
    evaluationCriteria: string;
    relatedTemplateList: CmsCategoryTemplate0[];
}>({
    evaluationCriteria: "",
    relatedTemplateList: [],
});

// 选择的数据表id
const chooseTemplateList = ref<number[]>([]);

// 取消添加数据表弹窗
function cancelAddRelatedTemplateForm() {
    addRelatedTemplateFormVisible.value = false;
    tempList.value = [];
}

// 根据templateId获取templateName
const getTemplateName = (templateId: number) => {
    return filteredList.value.find((template) => template.id === templateId)?.templateName;
};

// 临时数据表id
const tempList = ref<number[]>([]);

// 保存添加数据表
function saveRelatedTemplate() {
    cmsTagConfigBatchRelateCategory({
        body: {
            tagId: nowTag.value.id,
            categoryIdList: tempList.value,
        },
    }).then((res: any) => {
        if (res.code === 200) {
            ElMessage.success({ message: "保存成功" });
            addRelatedTemplateFormVisible.value = false;
            cmsTagGetById({ params: { id: nowTag.value.id } }).then((res: any) => {
                chooseTemplateList.value = res.data.categoryTemplateIds;
            });
            fetchData();
        } else {
            ElMessage.error({ message: res.msg });
        }
    });
}

// 显示详细配置分配规则弹窗
async function showConfigAllocationDetail(templateId: number) {
    nowSelectedTemplateId.value = templateId;
    configAllocationDetailFormVisible.value = true;
    // 等待getFilteredFieldsOptions执行完毕
    await getFilteredFieldsOptions();
    cmsTagConfigGetTagConfig({ params: { tagId: nowTag.value.id, categoryId: nowSelectedTemplateId.value } }).then(
        (res) => {
            const data = res.data;

            // 回显项目制计分
            configAllocationDetailForm.dataItemCombination =
                data.configDetail.combinationType === null ? [] : data.configDetail.combinationType;
            configAllocationDetailForm.allocateType = data.allocateType;
            configAllocationDetailForm.calculationMethod = data.scoreSource;
            configAllocationDetailForm.scoringMethod = data.scoringMethod;
            if (data.allocateType === tagAllocateType.PROJECT_SCORE) {
                // 项目制计分，保存数据项组合
                dataItemCombination.value = data.configDetail.combinationList;
            } else if (data.allocateType === tagAllocateType.HEAD_SCORE) {
                // 人头制计分，保存可计分数据项
                configAllocationDetailForm.headScoreDataItem.forEach((item) => {
                    // 将dataItemCombination中的score赋值给headScoreDataItem
                    item.score = data.configDetail.combinationList.find(
                        (item2) => item2.dataFieldId === item.dataFieldId,
                    )?.score;
                });
            } else if (data.allocateType === tagAllocateType.AMOUNT_SCORE) {
                // 回显金额累计制计分
                configAllocationDetailForm.amountComputeConditions = data.configDetail.amountComputeConditions.map(
                    (item) => ({
                        amountThreshold: item.amountThreshold,
                        performanceScore: item.performanceScore,
                    }),
                );
                configAllocationDetailForm.amountComputeFieldIds = data.configDetail.amountComputeFieldIds;
            }

            // 回显固定分值
            configAllocationDetailForm.dataItemEqualScore = data.configDetail.dataFieldId;
            configAllocationDetailForm.fixedScore = data.configDetail.fixedScore;

            // 设置复选框状态
            const selectedCount = configAllocationDetailForm.dataItemCombination.length;
            const totalCount = filteredFieldsOptions_combination.value.length;

            if (selectedCount === totalCount && totalCount > 0) {
                checkAll.value = true;
                isIndeterminate.value = false;
            } else if (selectedCount > 0 && selectedCount < totalCount) {
                checkAll.value = false;
                isIndeterminate.value = true;
            }
        },
    );
}

// 关闭配置分配规则弹窗
function handleCloseConfigAllocationDetailForm() {
    configAllocationDetailFormVisible.value = false;
    configAllocationDetailForm.dataItemCombination = [];
    dataItemCombination.value = [];
}

/** 可供组合计分的数据项 */
const filteredFieldsOptions_combination = ref<{ label: string; value: string; tag: string }[]>([]);

/** 数据项等值分支下拉 */
const dataItemEqualScoreOptions = ref<{ label: string; value: number }[]>([]);

/** 金额累计制计分 数据项列表 */
const amountScoreDataItemOptions = ref<{ value: number; label: string }[]>([]);

// 获取可供计分的数据项
const getFilteredFieldsOptions = async () => {
    // 根据selectedTemplateId发送网络请求，获取大类数据项列表
    const res = await cmsCategoryTemplateValueGetValueByTemplateId({ params: { id: nowSelectedTemplateId.value } });
    const list = res.data;
    // 获取list中，类型为下拉（即字典类型）的数据项
    const options = list
        .filter(
            (item) =>
                item.type === categoryTemplateValueType_NEW.ENUM_SINGLE ||
                item.type === categoryTemplateValueType_NEW.LEVEL ||
                item.type === categoryTemplateValueType_NEW.PROJECT_STATUS,
        )
        .map((item) => ({
            label: item.value,
            value: item.tag,
            tag: item.tag,
        }));

    // 数据项等值分值
    const equalScoreOptions = list
        .filter((item) => item.type === categoryTemplateValueType_NEW.INTEGER)
        .map((item) => ({
            label: item.value,
            value: item.id,
            tag: item.tag,
        }));

    // 获取可计分数据项
    const headScoreDataItemOptions = list
        .filter(
            (item) =>
                item.categoryPublicType === publicPerformanceType.PROJECT_PARTICIPATE ||
                item.categoryPublicType === publicPerformanceType.PROJECT_MAIN,
        )
        .map((item) => ({
            dataFieldId: item.id,
            score: 0,
            name: item.value,
        }));

    // 金额累计制计分 数据项列表
    amountScoreDataItemOptions.value = list
        .filter((item) => item.type === categoryTemplateValueType_NEW.MONEY)
        .map((item) => ({
            label: item.value,
            value: item.id,
        }));

    // 可计分数据项
    configAllocationDetailForm.headScoreDataItem = headScoreDataItemOptions;

    // 数据项组合
    filteredFieldsOptions_combination.value = options;

    // 数据项等值分值
    dataItemEqualScoreOptions.value = equalScoreOptions;
};

/** 全选 */
const checkAll = ref(false);
/** 半选 */
const isIndeterminate = ref(false);
/** 数据项组合 */
const dataItemCombination = ref<CmsTagConfigCombinationResp[]>([]);

// 全选
const handleCheckAllChange = (val: boolean) => {
    configAllocationDetailForm.dataItemCombination = val
        ? filteredFieldsOptions_combination.value.map((item) => item.value)
        : [];
    // 如果全选，则获取所有数据项组合
    if (val) {
        cmsTagConfigGetCombinationListByType({
            params: { combinationType: configAllocationDetailForm.dataItemCombination.join(",") },
        }).then((res) => {
            dataItemCombination.value = res.data;
        });
    }
    isIndeterminate.value = false;
};

// 单选
const handleCheckedOptionsChange = (value: string[]) => {
    const checkedCount = value.length;
    checkAll.value = checkedCount === filteredFieldsOptions_combination.value.length;
    isIndeterminate.value = checkedCount > 0 && checkedCount < filteredFieldsOptions_combination.value.length;
    cmsTagConfigGetCombinationListByType({ params: { combinationType: value.join(",") } }).then((res) => {
        dataItemCombination.value = res.data;
    });
};

// 保存配置分配规则
function saveConfigAllocationDetail() {
    cmsTagConfigSaveOrUpdate({
        body: {
            allocateType: configAllocationDetailForm.allocateType,
            categoryId: nowSelectedTemplateId.value,
            scoreSource: configAllocationDetailForm.calculationMethod,
            scoringMethod: configAllocationDetailForm.scoringMethod,
            tagId: nowTag.value.id,
            configDetail: {
                // 如果分配类型为项目制计分，则保存数据项组合，否则保存可计分数据项
                combinationList:
                    configAllocationDetailForm.allocateType === tagAllocateType.PROJECT_SCORE
                        ? dataItemCombination.value
                        : configAllocationDetailForm.headScoreDataItem,
                combinationType: configAllocationDetailForm.dataItemCombination,
                dataFieldId: configAllocationDetailForm.dataItemEqualScore,
                fixedScore: configAllocationDetailForm.fixedScore,
                amountComputeConditions: configAllocationDetailForm.amountComputeConditions,
                amountComputeFieldIds: configAllocationDetailForm.amountComputeFieldIds,
            },
        },
    }).then((res) => {
        if (res.code === 200) {
            customDialog.success({
                message: "绩效规则保存成功！",
                alignCenter: true,
                onConfirm: () => {
                    configAllocationDetailFormVisible.value = false;
                    configAllocationFormVisible.value = false;
                    fetchData();
                },
            });
        } else {
            ElMessage.error({ message: res.message });
        }
    });
}

// 清空表单
watch(addOrUpdateTagFormVisible, (newVal) => {
    if (newVal === false) {
        addOrUpdateTagFormRef.value?.resetFields();
        nowTag.value = null;
        isEdit.value = false;
        chooseTemplateList.value = [];
        tempList.value = [];
    }
});

// 计算已关联数据表中排除当前规则已关联的
const alreadyRelatedCategoryIds = computed(() => {
    if (!nowTag.value) return relatedCategoryIds.value;
    // 从全部已关联数据表中排除当前规则已关联的
    return relatedCategoryIds.value.filter((id) => !chooseTemplateList.value.includes(id));
});

// 检查数据表是否已被关联
const isTemplateRelated = (templateId: number) => {
    return alreadyRelatedCategoryIds.value.includes(templateId);
};

// 打开添加关联数据表对话框
function openAddRelatedTemplateDialog() {
    // 刷新已关联数据表列表
    cmsTagConfigGetRelateCategoryList({}).then((res) => {
        if (res.code === 200 && res.data) {
            relatedCategoryIds.value = res.data;
            addRelatedTemplateFormVisible.value = true;
            tempList.value = chooseTemplateList.value;
        }
    });
}
</script>

<style scoped lang="scss">
@use "sass:map";

// 隐藏表格展开图标
:deep(.el-table__expand-icon .el-icon) {
    display: none !important;
}

// 为问号图标添加间隔
:deep(.el-radio__label) {
    display: flex;
    align-items: center;
    gap: 5px;
}

// 隐藏el-radio的间距，并设置最小宽度
:deep(.el-radio) {
    margin-right: 0;
    min-width: 150px;
}

// 配置分配规则弹窗限制高度，溢出自动滚动
:deep(.config-allocation-detail-dialog) {
    .el-dialog__body {
        max-height: 60vh;
        overflow: auto;
    }
}

.custom-table-button {
    min-width: 52px;
    padding: 0px 12px;
    background: #ffffff;
    box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.02);
    border: 1px solid #4096ff;
    border-radius: 12px;
    color: #4096ff;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
}

.evaluation-criteria {
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 1.571 */

    font-size: 14px;
    background: #fafafa;
    border-radius: 6px;
    padding: 18px 16px;
    margin-top: 8px;
    letter-spacing: 0.1em;
}

.template-list {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 10px;
    .template-item {
        cursor: pointer;
        display: flex;
        min-width: 200px;
        height: 40px;
        padding: 6px 12px;
        gap: 8px;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        background: #f4f4f5;
        border: 1px solid #e9e9eb;
        border-radius: 4px;

        .template-name {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #909399;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 1.429 */
        }
        span {
            color: #909399;
        }
    }
}

.checkBox {
    border: 1px solid #b6c0d2;
    border-radius: 5px;
    // padding: 10px;
}

.combination-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.combination-item {
    border: 1px solid #b6c0d2;
    border-radius: 5px;
    display: flex;
    align-items: center;
    // padding: 0 10px;
    margin: 0;
    min-width: 208px;
}

.combination-label {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #f5f7fa;
    padding: 0 8px;
    color: #909399;
    flex: 3;
}

.combination-input {
    width: 70px;
}

.combination-unit {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #f5f7fa;
    padding: 0 8px;
    color: #909399;
    flex: 1;
}

.borderless-input {
    :deep(.el-input__wrapper) {
        border: none !important;
        box-shadow: none !important;
        background-color: transparent !important;
    }

    :deep(.el-input__inner) {
        text-align: center;
        background-color: transparent !important;
    }
}

$base-padding: 20px;

.custom-tree-table {
    // 层级背景色映射
    $level-colors: (
        1: #4a709b,
        2: #8eb7de,
        3: #cde4fa,
        4: #dcf3ff,
        5: #defcfd,
    );

    // 层级行样式混合
    @mixin level-style($level) {
        position: relative;

        &::after {
            content: "";
            position: absolute;
            left: $base-padding * $level + 40px;
            right: 20px;
            top: 0;
            bottom: 0;
            border-radius: 5px;
            background-color: map.get($level-colors, $level);
        }

        td {
            background-color: transparent;
            position: relative;
            z-index: 1;
            @if $level <= 2 {
                color: #fff;
            }
            @if $level == 1 {
                font-weight: bold;
            }
        }

        td:first-child .cell:has(> .el-table__expand-icon) {
            // 包含图标时的样式
            padding-left: $base-padding * ($level + 1);
            span {
                padding: 0 !important;
                width: 0 !important;
            }
            .el-table__expand-icon {
                margin-right: 22px;
            }
        }

        td:first-child .cell:not(:has(> .el-table__expand-icon)) {
            // 不包含图标时的样式
            padding-left: $base-padding * ($level + 1) + 35px;
            span {
                padding: 0 !important;
                width: 0 !important;
            }
        }
    }

    // 规则行特殊样式
    :deep(.level-rules::after) {
        background-color: #fffcf9 !important;
        border-radius: 0 !important;
    }

    // 规则行文字颜色样式
    :deep(.level-rules td) {
        color: #606266 !important;
    }

    // 生成各层级样式
    @for $i from 1 through 5 {
        :deep(.level-#{$i}) {
            @include level-style($i);
        }
    }

    // 行间距
    :deep(.el-table__body) {
        border-collapse: separate;
        border-spacing: 0 8px;
    }

    // 自定义展开图标
    :deep(.el-table__expand-icon) {
        position: relative;
        z-index: 2;
        margin-right: 8px;

        transform-origin: 75% 50%;

        &.el-table__expand-icon--expanded {
            transform: rotate(90deg);
        }

        &::before {
            content: "";
            position: absolute;
            z-index: 99;
            left: 100%;
            top: 50%;
            transform: translate(-50%, -50%);
            border: 5px solid transparent;
            border-left-color: #000;
        }
    }

    // 去除默认样式
    :deep(.el-table__row) {
        td {
            border: none;
        }

        &:hover td {
            background-color: transparent !important;
        }
    }
}

.wrapper {
    width: 83vw;
    margin: 20px auto;

    .table {
        min-width: 700px;
    }

    .addBtn {
        color: #787878;
        font-weight: bold;
        display: flex;
        align-items: center;
        gap: 10px;
        margin-top: 10px;
    }
}
</style>
