// 提取HTML中的纯文本内容，去除图片和HTML标签
export const extractTextFromHtml = (html: string): string => {
    if (!html) return '';
    
    // 创建一个临时的DOM元素来解析HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    
    // 移除所有img标签
    const images = tempDiv.querySelectorAll('img');
    images.forEach(img => img.remove());
    
    // 返回纯文本内容，并去除多余的空白字符
    return tempDiv.textContent || tempDiv.innerText || '';
};