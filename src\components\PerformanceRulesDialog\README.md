# PerformanceRulesDialog 绩效规则弹窗组件

绩效规则展示弹窗组件，支持普通组件使用和指令式调用两种方式。

## 功能特点

- 🎯 显示树形结构的绩效规则列表
- 📊 自动计算并显示规则数量
- 🔧 支持自定义标题和空状态文案
- 🎛️ 可配置按钮显示和文案
- 🚀 支持指令式调用，使用方便
- 📱 响应式设计，支持不同设备

## 使用方式

### 1. 普通组件使用

```vue
<template>
    <div>
        <el-button @click="showDialog = true">查看绩效规则</el-button>
        
        <PerformanceRulesDialog
            v-model="showDialog"
            :performance-rules-list="performanceRulesList"
            title="自定义标题"
            @change-rules="handleChangeRules"
            @close="handleClose"
        />
    </div>
</template>

<script setup>
import PerformanceRulesDialog from '@/components/PerformanceRulesDialog';

const showDialog = ref(false);
const performanceRulesList = ref([]);

const handleChangeRules = () => {
    console.log('点击了更改绩效规则');
};

const handleClose = () => {
    console.log('弹窗关闭');
};
</script>
```

### 2. 指令式调用

```typescript
import { usePerformanceRulesDialog } from '@/components/PerformanceRulesDialog';

const { show } = usePerformanceRulesDialog();

// 基本使用
const showPerformanceRules = async () => {
    const action = await show({
        performanceRulesList: performanceRulesList.value,
    });
    
    if (action === 'change-rules') {
        console.log('用户点击了更改绩效规则');
    }
};

// 高级配置
const showCustomPerformanceRules = async () => {
    const action = await show({
        performanceRulesList: performanceRulesList.value,
        title: '项目相关绩效规则',
        emptyText: '该项目暂未关联任何绩效规则',
        changeRulesButtonText: '配置规则',
        changeRulesPath: '/custom/performance/rules',
        onChangeRules: async () => {
            // 自定义处理逻辑
            console.log('执行自定义更改规则逻辑');
        },
        onClose: async () => {
            console.log('弹窗关闭回调');
        },
    });
};
```

## API

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | `boolean` | `false` | 弹窗显示状态 |
| performanceRulesList | `CmsTagNode_[]` | `[]` | 绩效规则列表数据 |
| title | `string` | - | 自定义标题，不传则自动生成 |
| emptyText | `string` | `'暂无关联的绩效规则'` | 空状态提示文案 |
| showChangeRulesButton | `boolean` | `true` | 是否显示更改规则按钮 |
| changeRulesButtonText | `string` | `'更改绩效规则'` | 更改规则按钮文案 |
| changeRulesPath | `string` | `'/backgroundManagement/centralSettings/performanceRuleManagement'` | 更改规则跳转路径 |
| showCloseButton | `boolean` | `true` | 是否显示关闭按钮 |
| closeButtonText | `string` | `'关闭'` | 关闭按钮文案 |
| closeOnClickModal | `boolean` | `false` | 点击遮罩层是否关闭弹窗 |
| closeOnPressEscape | `boolean` | `true` | 按ESC是否关闭弹窗 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | `(value: boolean)` | 弹窗显示状态变化 |
| change-rules | - | 点击更改规则按钮 |
| close | - | 弹窗关闭 |

### 指令式API选项 (PerformanceRulesDialogOptions)

包含所有Props，额外增加：

| 参数 | 类型 | 说明 |
|------|------|------|
| onChangeRules | `() => void \| Promise<void>` | 更改规则按钮点击回调 |
| onClose | `() => void \| Promise<void>` | 弹窗关闭回调 |

### 方法

#### usePerformanceRulesDialog()

返回指令式API方法：

- `show(options: PerformanceRulesDialogOptions): Promise<PerformanceRulesDialogAction>` - 显示弹窗
- `closeAll(): void` - 关闭所有弹窗实例

## 类型定义

```typescript
import type { CmsTagNode_ } from '@/apis/types';

// 弹窗配置选项
interface PerformanceRulesDialogOptions {
    performanceRulesList: CmsTagNode_[];
    title?: string;
    emptyText?: string;
    showChangeRulesButton?: boolean;
    changeRulesButtonText?: string;
    changeRulesPath?: string;
    showCloseButton?: boolean;
    closeButtonText?: string;
    closeOnClickModal?: boolean;
    closeOnPressEscape?: boolean;
    onChangeRules?: () => void | Promise<void>;
    onClose?: () => void | Promise<void>;
}

// 用户操作类型
type PerformanceRulesDialogAction = "change-rules" | "close";
```

## 应用场景

1. **数据表管理** - 查看数据表关联的绩效规则
2. **项目管理** - 显示项目相关的绩效规则
3. **审批流程** - 在审批过程中展示相关规则
4. **数据录入** - 录入时查看关联的绩效规则

## 注意事项

1. 组件依赖 `@/utils/tree` 中的 `traverseTree` 和 `getLeafNodesCount` 工具函数
2. 需要确保传入的 `performanceRulesList` 数据结构符合 `CmsTagNode_` 类型
3. 指令式调用时，组件会自动挂载到 `document.body`
4. 弹窗关闭时会有300ms的动画延迟后才销毁实例 