// 模板字段详情样式
// 用于表格管理和其他需要展示字段详情的页面

// 工具类样式
.star {
    &::before {
        content: "*";
        color: #ff4d4f;
        font-size: 14px;
        margin-right: 2px;
    }
}

.handle {
    cursor: grab;
}

.desc {
    font-size: 14px;
    color: #818181;
}

// 拖拽时的样式
.ghost {
    opacity: 0.7;
    background: #fffb8f;
}

// 动画效果
.slide-fade-enter-active,
.slide-fade-leave-active {
    transition:
        transform 0.5s ease,
        opacity 0.5s ease;
}

.slide-fade-enter {
    transform: translateX(20px);
}

.slide-fade-leave-to {
    opacity: 0;
    transform: translateX(-10px);
}

// 录入信息标题
.section-title {
    // margin-bottom: 16px;
    span {
        border-left: 3px solid #1677ff;
        padding-left: 11px;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        color: #000000;
    }
}

// 字段列表容器
.right-scroll {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;

    // 顶部标题栏
    .header-bar {
        position: sticky;
        top: 0;
        z-index: 1;
        height: 64px;
        background: #ebf1ff;
        border-radius: 0 6px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px 16px 24px;
        box-shadow: rgba(149, 157, 165, 0.2) 0 4px 12px;

        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;

            .table-title {
                color: #000000;
                font-size: 20px;
                line-height: 24px;
                font-weight: 400;
            }

            .view-performance-btn {
                background: #1677ff;
                border: none;
                border-radius: 6px;
                box-shadow: 0px 2px 0px 0px rgba(5, 145, 255, 0.1);
                color: #ffffff;
                padding: 5px 16px;
                line-height: 22px;

                &:hover {
                    background: #1677ff;
                    opacity: 0.9;
                }
            }
        }
    }

    .right-content {
        padding: 24px 16px;
        overflow-y: auto;
        height: 100%;
        // height: calc(100vh - 284px);
        display: flex;
        gap: 10px;
        flex-direction: column;
        // 字段列表容器
        .fields-container {
            background: #f9fbff;
            border-radius: 8px;
            padding: 24px 16px;
            display: flex;
            flex-direction: column;
            gap: 16px;

            // 为VueDraggable生成的包裹层设置相同的布局
            > [scrollel] {
                display: flex;
                flex-direction: column;
                gap: 16px;
            }

            // 字段卡片
            .field-card {
                border: 1px solid #d9d9d9;
                border-radius: 6px;
                background: #ffffff;
                overflow: hidden;
                position: relative;

                // 动画过渡效果
                &.moving {
                    z-index: 999;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                }

                // 字段头部
                .field-header {
                    background: #ebf1ff;
                    border-radius: 6px 6px 0 0;
                    padding: 8px 16px;
                    min-height: 37px;
                    display: flex;
                    align-items: center;

                    .field-header-content {
                        display: flex;
                        align-items: center;
                        gap: 4px;
                        width: 100%;

                        .field-name {
                            color: rgba(0, 0, 0, 0.88);
                            line-height: 20px;
                            font-size: 14px;
                            font-weight: 400;
                        }

                        .is-category-public-field {
                            color: #1677ff;
                            font-weight: 600;
                        }

                        .flag-icon {
                            width: 20px;
                            height: 20px;
                            background-image: url("data:image/svg+xml;base64,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");
                            background-size: contain;
                            background-repeat: no-repeat;
                            margin-left: 4px;
                        }

                        .info-icon {
                            margin-left: 12px;

                            .info-circle {
                                width: 16px;
                                height: 16px;
                                background-image: url("data:image/svg+xml;base64,CjxzdmcgbWV4cG9ydD0iMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiB2aWV3Qm94PSIwIDAgMTYgMTYiPgoJPGRlZnM+CgkJPGNsaXBQYXRoIGlkPSJjbGlwUGF0aDA6MDowIj4KCQkJPHBhdGggZD0iTTAgMEwxNiAwTDE2IDE2TDAgMTZMMCAwWiIgZmlsbC1ydWxlPSJub256ZXJvIiB0cmFuc2Zvcm09Im1hdHJpeCgxIDAgMCAxIDAgMCkiLz4KCQk8L2NsaXBQYXRoPgoJPC9kZWZzPgoJPGcgY2xpcC1wYXRoPSJ1cmwoI2NsaXBQYXRoMDowOjApIj4KCQk8cGF0aCBkPSJNOCAwQzMuNTgyMTQgMCAwIDMuNTgyMTQgMCA4QzAgMTIuNDE3OSAzLjU4MjE0IDE2IDggMTZDMTIuNDE3OSAxNiAxNiAxMi40MTc5IDE2IDhDMTYgMy41ODIxNCAxMi40MTc5IDAgOCAwWk04LjU3MTQzIDExLjg1NzFDOC41NzE0MyAxMS45MzU3IDguNTA3MTQgMTIgOC40Mjg1NyAxMkw3LjU3MTQzIDEyQzcuNDkyODYgMTIgNy40Mjg1NyAxMS45MzU3IDcuNDI4NTcgMTEuODU3MUw3LjQyODU3IDdDNy40Mjg1NyA2LjkyMTQzIDcuNDkyODYgNi44NTcxNCA3LjU3MTQzIDYuODU3MTRMOC40Mjg1NyA2Ljg1NzE0QzguNTA3MTQgNi44NTcxNCA4LjU3MTQzIDYuOTIxNDMgOC41NzE0MyA3TDguNTcxNDMgMTEuODU3MVpNOCA1LjcxNDI5QzcuNzc1NyA1LjcwOTcxIDcuNTYyMTMgNS42MTczOSA3LjQwNTEzIDUuNDU3MTRDNy4yNDgxMiA1LjI5NjkgNy4xNjAxOCA1LjA4MTQ5IDcuMTYwMTggNC44NTcxNEM3LjE2MDE4IDQuNjMyOCA3LjI0ODEyIDQuNDE3MzkgNy40MDUxMyA0LjI1NzE0QzcuNTYyMTMgNC4wOTY5IDcuNzc1NyA0LjAwNDU4IDggNEM4LjIyNDMgNC4wMDQ1OCA4LjQzNzg3IDQuMDk2OSA4LjU5NDg4IDQuMjU3MTRDOC43NTE4OSA0LjQxNzM5IDguODM5ODIgNC42MzI4IDguODM5ODIgNC44NTcxNEM4LjgzOTgyIDUuMDgxNDkgOC43NTE4OSA1LjI5NjkgOC41OTQ4OCA1LjQ1NzE0QzguNDM3ODcgNS42MTczOSA4LjIyNDMgNS43MDk3MSA4IDUuNzE0MjlaIiBmaWxsLXJ1bGU9Im5vbnplcm8iIHRyYW5zZm9ybT0ibWF0cml4KDEgMCAwIDEgMCAwKSIgZmlsbD0icmdiKDAsIDAsIDApIiBmaWxsLW9wYWNpdHk9IjAuMjUiLz4KCTwvZz4KPC9zdmc+Cg==");
                                background-size: contain;
                                background-repeat: no-repeat;
                                cursor: pointer;
                            }
                        }
                    }
                }

                // 字段内容
                .field-content {
                    padding: 16px 16px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    gap: 8px;
                    background-color: #f9fbff;

                    // 操作按钮
                    .action-buttons {
                        display: flex;
                        gap: 8px;
                        align-items: center;
                        div {
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            height: 32px;
                            width: 32px;
                            cursor: pointer;
                            &:hover {
                                background-color: #eaf0fc;
                                transition: all 0.3s ease;
                            }
                        }
                    }
                }
            }
        }
    }
}
