<template>
    <el-dialog
        v-model="visible"
        :width="width"
        :show-close="false"
        :close-on-click-modal="closeOnClickModal"
        :close-on-press-escape="closeOnPressEscape"
        :align-center="alignCenter"
        :before-close="handleBeforeClose"
        class="custom-dialog"
        :class="dialogClass"
        append-to-body>
        <div class="custom-dialog-content">
            <!-- 标题行：图标+标题 -->
            <div class="dialog-header">
                <!-- 图标区域 -->
                <div v-if="shouldShowIcon" class="dialog-icon">
                    <img 
                        v-if="iconUrl || getTypeIcon()" 
                        :src="iconUrl || getTypeIcon()" 
                        :style="`width: ${iconSize}px; height: ${iconSize}px;`" 
                    />
                </div>
                
                <!-- 标题 -->
                <div v-if="title" class="dialog-title">
                    {{ title }}
                </div>
                
                <!-- 关闭按钮（仅confirm类型显示） -->
                <div v-if="type === 'confirm'" class="dialog-close-btn" @click="handleClose">
                    <el-icon :size="16">
                        <Close />
                    </el-icon>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="dialog-body">
                <div v-if="message" class="dialog-message" :style="{ marginLeft: shouldShowIcon ? '36px' : '0' }">
                    <span v-if="dangerouslyUseHTMLString" v-html="message"></span>
                    <span v-else>{{ message }}</span>
                </div>
                <slot v-else></slot>
            </div>

            <!-- 按钮区域 -->
            <div v-if="showButtons" class="dialog-buttons">
                <el-button 
                    v-if="showCancelButton" 
                    v-bind="getCancelButtonProps"
                    @click="handleCancel" 
                    class="cancel-btn"
                >
                    {{ cancelButtonText }}
                </el-button>
                <el-button
                    v-if="showConfirmButton"
                    v-bind="getConfirmButtonProps"
                    @click="handleConfirm"
                    class="confirm-btn"
                >
                    {{ confirmButtonText }}
                </el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { Close } from "@element-plus/icons-vue";
import type { Component } from "vue";
import type { ButtonProps } from "./types";

// 引入本地图标
import successIcon from '@/assets/dialog_icons/success.png';
import errorIcon from '@/assets/dialog_icons/error.png';
import warningIcon from '@/assets/dialog_icons/warning.png';
import infoIcon from '@/assets/dialog_icons/info.png';

interface Props {
    modelValue?: boolean;
    title?: string;
    message?: string;
    dangerouslyUseHTMLString?: boolean;
    icon?: Component | string;
    iconUrl?: string;
    iconSize?: number;
    iconColor?: string;
    width?: string | number;
    showClose?: boolean;
    showButtons?: boolean;
    showCancelButton?: boolean;
    showConfirmButton?: boolean;
    cancelButtonText?: string;
    confirmButtonText?: string;
    cancelButtonProps?: ButtonProps;
    confirmButtonProps?: ButtonProps;
    buttonSize?: "large" | "default" | "small";
    closeOnClickModal?: boolean;
    closeOnPressEscape?: boolean;
    alignCenter?: boolean;
    dialogClass?: string;
    beforeClose?: (done: () => void) => void;
    type?: 'success' | 'error' | 'warning' | 'info' | 'confirm';
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    dangerouslyUseHTMLString: false,
    iconSize: 24,
    iconColor: "#52C41A",
    width: 384,
    showClose: false,
    showButtons: true,
    showCancelButton: false,
    showConfirmButton: true,
    cancelButtonText: "取 消",
    confirmButtonText: "确 定",
    buttonSize: "default",
    closeOnClickModal: false,
    closeOnPressEscape: true,
    alignCenter: false,
});

const emit = defineEmits<{
    "update:modelValue": [value: boolean];
    confirm: [];
    cancel: [];
    close: [];
}>();

const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit("update:modelValue", value),
});

const handleConfirm = () => {
    emit("confirm");
    visible.value = false;
};

const handleCancel = () => {
    emit("cancel");
    visible.value = false;
};

const handleClose = () => {
    emit("close");
    visible.value = false;
};

const handleBeforeClose = (done: () => void) => {
    if (props.beforeClose) {
        props.beforeClose(done);
    } else {
        done();
        handleClose();
    }
};

// 根据类型获取对应图标
const getTypeIcon = () => {
    if (props.iconUrl) return props.iconUrl;
    
    const iconMap = {
        success: successIcon,
        error: errorIcon,
        warning: warningIcon,
        info: infoIcon,
        confirm: '' // confirm类型不显示图标
    };
    
    return props.type ? iconMap[props.type] : '';
};

// 是否显示图标
const shouldShowIcon = computed(() => {
    return props.type !== 'confirm' && (props.iconUrl || getTypeIcon() || props.icon);
});

// 获取取消按钮属性
const getCancelButtonProps = computed(() => {
    const defaultProps = {
        size: props.buttonSize || 'default'
    };
    
    return {
        ...defaultProps,
        ...props.cancelButtonProps
    };
});

// 获取确认按钮属性
const getConfirmButtonProps = computed(() => {
    const defaultProps = {
        size: props.buttonSize || 'default',
        type: 'primary'
    };
    
    return {
        ...defaultProps,
        ...props.confirmButtonProps
    };
});
</script>

<style scoped lang="scss">
:global(.custom-dialog.el-dialog) {
    background: #ffffff;
    box-shadow:
        0px 9px 28px 8px rgba(0, 0, 0, 0.05),
        0px 3px 6px -4px rgba(0, 0, 0, 0.00),
        0px 6px 16px 0px rgba(0, 0, 0, 0.08);
    border-radius: 8px;
}

:global(.custom-dialog .el-dialog__header) {
    display: none;
}

.custom-dialog-content {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .dialog-header {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        position: relative;

        .dialog-icon {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            
            img {
                display: block;
            }
        }

        .dialog-title {
            color: rgba(0, 0, 0, 0.88);
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            flex-grow: 1;
        }

        .dialog-close-btn {
            position: absolute;
            top: 0;
            right: 0;
            cursor: pointer;
            color: rgba(0, 0, 0, 0.45);
            
            &:hover {
                color: rgba(0, 0, 0, 0.88);
            }
        }
    }

    .dialog-body {
        .dialog-message {
            color: rgba(0, 0, 0, 0.88);
            font-size: 14px;
            line-height: 22px;
            text-align: left;
            // margin-left: 36px; // 与图标对齐
        }
    }

    // 清除el-button之间默认的间距
    .el-button+.el-button{
        margin-left: 0;
    }

    .dialog-buttons {
        display: flex;
        gap: 8px;
        margin-top: 4px;
        justify-content: flex-end;
    }
}

</style>
