/**
 * 级别：正高级、副高级、中级、初级
 */
export enum ProfessionalRankType {
    /** 正高级 */
    SENIOR = 0,
    /** 副高级 */
    ASSOCIATE_SENIOR = 1,
    /** 中级 */
    INTERMEDIATE = 2,
    /** 初级 */
    JUNIOR = 3,
}

// 生成对应Map
export const ProfessionalRankTypeMap: Record<ProfessionalRankType, string> = {
    [ProfessionalRankType.SENIOR]: "正高级",
    [ProfessionalRankType.ASSOCIATE_SENIOR]: "副高级",
    [ProfessionalRankType.INTERMEDIATE]: "中级",
    [ProfessionalRankType.JUNIOR]: "初级",
} 