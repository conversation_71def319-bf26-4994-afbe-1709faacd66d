<template>
    <div class="notification-container">
        <div class="notification-icon" :class="{ 'has-unread': unreadCount > 0 }" @click="toggleNotice">
            <el-icon size="24" color="#ffffff">
                <Bell />
            </el-icon>
            <!-- <span v-if="unreadCount > 0" class="unread-badge">{{ unreadCount }}</span> -->
        </div>
        <transition name="expand">
            <div v-if="showNotice" class="notification-panel" v-click-outside="closePanel">
                <div class="panel-header">
                    <el-button
                        style="color: #fff"
                        type="primary"
                        color="#8BA0C9"
                        size="small"
                        @click="clearReadMessages"
                        >清空已读</el-button
                    >
                </div>
                <div class="panel-content" v-if="notificationsList.length > 0">
                    <div
                        v-for="(notice, index) in notificationsList"
                        :key="index"
                        class="notice-item"
                        :class="{ unread: notice.status === 0 }"
                        @mouseenter="markAsRead(notice)">
                        <el-badge v-if="notice.status === 0" is-dot class="notice-badge" type="danger" />
                        <div class="notice-content">
                            <div class="notice-title">{{ notice.title }}</div>
                            <div class="notice-message">{{ notice.content }}</div>
                            <div class="notice-time">{{ dayjs(notice.createdAt).format("YYYY-MM-DD") }}</div>
                        </div>
                    </div>
                </div>
                <div v-else class="empty-notice">
                    <el-empty description="暂无通知消息" />
                </div>
            </div>
        </transition>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage, ClickOutside as vClickOutside } from "element-plus";
import { mmsMessageDeleteAll, mmsMessageList, mmsMessageMessageReadId } from "@/apis/mmsMessageController";
import { MmsMessage_ } from "@/apis/types";
import dayjs from "dayjs";

// 通知消息列表
const notificationsList = ref<MmsMessage_[]>([]);

const showNotice = ref(false);

// 计算未读消息数量
const unreadCount = computed(() => {
    return notificationsList.value.filter((notice) => notice.status === 0).length;
});

// 是否有已读消息
const hasRead = computed(() => {
    return notificationsList.value.some((notice) => notice.status === 1);
});

// 切换通知面板显示状态
const toggleNotice = () => {
    showNotice.value = !showNotice.value;
};

// 关闭通知面板
const closePanel = () => {
    showNotice.value = false;
};

// 将消息标记为已读
const markAsRead = (notice: MmsMessage_) => {
    if (notice.status === 0) {
        // 只处理未读消息
        mmsMessageMessageReadId({
            params: {
                id: notice.id,
            },
        }).then((res) => {
            if (res.code === 200) {
                // 更新本地消息状态
                notice.status = 1;
            }
        });
    }
};

// 清空已读消息
const clearReadMessages = () => {
    if (!hasRead.value) {
        ElMessage({
            type: "warning",
            message: "暂无已读消息",
            duration: 500,
        });
        return;
    }
    mmsMessageDeleteAll({}).then((res) => {
        if (res.code === 200) {
            ElMessage({
                type: "success",
                message: "清空成功！",
                duration: 500,
            });
            fetchNotifications();
        }
    });
};

const fetchNotifications = () => {
    mmsMessageList({}).then((res) => {
        notificationsList.value = res.data;
    });
};

onMounted(() => {
    fetchNotifications();
});
</script>

<style scoped lang="scss">
.notification-container {
    position: relative;

    .notification-icon {
        position: relative;
        background: #667180;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: background-color 0.3s ease;

        &:hover {
            background: rgba(255, 255, 255, 0.35);
        }
    }

    .unread-badge {
        position: absolute;
        top: 3px;
        right: 3px;
        min-width: 16px;
        height: 16px;
        border-radius: 8px;
        background-color: #f56c6c;
        color: #fff;
        font-size: 12px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 4px;
    }

    .notification-icon.has-unread::after {
        content: "";
        position: absolute;
        top: 5px;
        right: 8px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #f53232;
    }

    .notification-panel {
        position: absolute;
        top: 50px;
        right: 0;
        width: 330px;
        height: 300px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        z-index: 1500;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .panel-header {
            display: flex;
            justify-content: end;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid #ebeef5;
        }

        .panel-content {
            overflow-y: auto;
            padding: 0;
            max-height: 300px;

            .notice-item {
                position: relative;
                padding: 12px 16px;
                border-bottom: 1px solid #f2f6fc;
                transition: background-color 0.2s ease;

                &:hover {
                    background-color: #f5f7fa;
                }
                .notice-title {
                    font-weight: 600;
                    color: #23346d;
                }

                .notice-badge {
                    position: absolute;
                    left: 5px;
                    top: 10px;
                }

                .notice-content {
                    padding-left: 6px;

                    .notice-title {
                        color: #23346d;
                        font-size: 15px;
                        margin-bottom: 5px;
                    }

                    .notice-message {
                        color: #6c7cb2;
                        font-size: 13px;
                        margin-bottom: 5px;
                        word-break: break-word;
                    }

                    .notice-time {
                        color: #909399;
                        font-size: 12px;
                    }
                }
            }
        }

        .empty-notice {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .panel-footer {
            padding: 10px 16px;
            display: flex;
            justify-content: center;
            border-top: 1px solid #ebeef5;
        }
    }
}

/* 展开动画 */
.expand-enter-active,
.expand-leave-active {
    transition:
        opacity 0.3s,
        transform 0.3s;
}

.expand-enter-from,
.expand-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}
</style>
