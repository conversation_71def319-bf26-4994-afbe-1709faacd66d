import { gradeOptionsMap } from "@/enums/options/gradeOptions";
import { ref, computed } from "vue";
/**
 * 将枚举类型转换为可迭代的数组，用于下拉框等需要 label 和 value 的场景
 * @param enumType - 枚举类型
 * @returns {Array<{ label: string; value: number | string }>} 枚举的键值对数组
 * @example
 * enum ExampleEnum {
 *   "OptionA" = 0,
 *   "OptionB" = 1,
 * }
 * const options = enumToOptions(ExampleEnum);
 * console.log(options);
 * Output: [{ label: "OptionA", value: 0 }, { label: "OptionB", value: 1 }]
 */
export function enumToOptions(enumType: object): Array<{ label: string; value: number | string }> {
    return Object.entries(enumType)
        .filter(([key]) => isNaN(Number(key))) // 过滤掉反向映射的数值键
        .map(([key, value]) => ({
            label: key,
            value,
        }));
}

/**
 * 将枚举类型转换为可迭代的数组，用于下拉框。该函数接受两个参数
 * @param enumType - 枚举类型
 * @param enumMap 枚举表
 * @returns {Array<{ label: string; value: number | string }>} 枚举的键值对数组
 */
export function enumToOptionsGeneric<T extends Record<string, number | string>>(
    enumType: T,
    enumMap: Record<T[keyof T], string>
): Array<{ label: string; value: T[keyof T] }> {
    return Object.entries(enumType)
        .filter(([key]) => isNaN(Number(key))) // 过滤掉反向映射的数值键
        .map(([key, value]) => ({
            label: enumMap[value as T[keyof T]],
            value: value as T[keyof T],
        }));
}

/**
 * 动态下拉选项逻辑，使用 ref 存储选项
 */
export function useDynamicDropdownOptions<T extends Record<string, number | string>>(
    initialEnumType: T,
    initialEnumMap: Record<T[keyof T], string>,
    isSwitch: boolean = false
) {
    // 动态选项响应式对象，允许更新不同类型，如果isSwitch为true，则存在enabled属性，否则不添加该属性
    const options: Ref<Array<{ label: string; value: number | string; enabled?: 0 | 1; useCount?: number | null }>> = ref(
        Object.entries(initialEnumType)
            .filter(([key]) => isNaN(Number(key)))
            .map(([key, value]) => {
                const baseOption = {
                    label: initialEnumMap[value as T[keyof T]],
                    value: value as T[keyof T],
                    useCount:null,
                };

                // 如果isSwitch为true，则添加enabled属性
                return isSwitch ? { ...baseOption, enabled: 1 } : baseOption;
            })
    );

    // 更新选项的方法，支持传入不同枚举类型和映射
    function updateOptions<U extends Record<string, number | string>>(
        newEnumType: U,
        newEnumMap: Record<U[keyof U], string>,
        isSwitch: boolean = false
    ) {
        options.value = Object.entries(newEnumType)
            .filter(([key]) => isNaN(Number(key)))
            .map(([key, value]) => {
                const baseOption = {
                    label: newEnumMap[value as U[keyof U]],
                    value: value as U[keyof U],
                    useCount:null,
                };

                // 如果isSwitch为true，则添加enabled属性
                return isSwitch ? { ...baseOption, enabled: 1 } : baseOption;
            }); 
    }

    // 为组织列表(orgList)类型的数据更新选项
    function updateOptionsFromOrgList(
        orgList: Array<{id?: number, name?: string, [key: string]: any}>,
        isSwitch: boolean = false
    ) {
        options.value = orgList
            .filter(org => org.id !== undefined && org.name !== undefined)
            .map(org => {
                const baseOption = {
                    label: org.name as string,
                    value: org.id as number,
                };

                // 如果isSwitch为true，则添加enabled属性
                return isSwitch ? { ...baseOption, enabled: 1 } : baseOption;
            });
    }

    return { options, updateOptions, updateOptionsFromOrgList };
}


/**
 * 获取排序后的级别选项
 * @param dictList 字典列表
 * @returns 排序后的级别选项
 */
export function getSortedLevelOptions(dictList: any[]) {
    if (!dictList || dictList.length === 0) return [];
    
    // 创建一个映射，用于快速查找每个级别名称对应的索引
    const gradeNameToIndex = new Map();
    Object.entries(gradeOptionsMap).forEach(([key, value], index) => {
        gradeNameToIndex.set(value, Number(key));
    });
    
    return [...dictList].sort((a, b) => {
        // 通过名称查找对应的枚举值，再根据枚举值排序
        const aValue = gradeNameToIndex.get(a.name) ?? 999;
        const bValue = gradeNameToIndex.get(b.name) ?? 999;
        return aValue - bValue;
    });
}