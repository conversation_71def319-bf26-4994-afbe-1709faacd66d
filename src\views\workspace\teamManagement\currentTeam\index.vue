<template>
    <div class="wrapper">
        <GeneralDataDisplay>
            <template #top>
                <div class="top">
                    <div>
                        <CustomSearch
                            v-model="searchKeyword"
                            placeholder="输入关键字检索项目团队"
                            @search="handleSearch"
                            @clear="refreshAll" />
                    </div>
                    <div>
                        <el-button @click="addProjectDialogVisible = true" type="primary">创建团队</el-button>
                    </div>
                </div>
            </template>

            <template #content>
                <div class="content">
                    <DataTable
                        :search-keyword="searchKeyword"
                        :is-local-search="false"
                        :is-load-first="true"
                        ref="dataTableRef"
                        @itemClick="loadDetail"
                        :load-more-func="loadMoreFunc">
                        <template #title="{ item }">
                            <div class="title">
                                {{ item.projectName }}
                            </div>
                        </template>
                        <template #desc="{ item }">
                            <div>发布时间：{{ item.projectBrief }}</div>
                        </template>
                        <template #detail="{ selectedItem }">
                            <Detail :selected-item="detailData" @refresh="refreshAll" />
                        </template>
                    </DataTable>
                </div>
            </template>
        </GeneralDataDisplay>
        <!-- 创建团队弹窗 -->
        <el-dialog v-model="addProjectDialogVisible" title="创建项目" width="500" align-center>
            <el-form :model="addProjectForm" :rules="addProjectRules" ref="addProjectFormRef" label-position="top">
                <el-form-item label="项目名称" :label-width="formLabelWidth" prop="projectName">
                    <el-input v-model="addProjectForm.projectName" />
                </el-form-item>
                <el-form-item label="项目简介" :label-width="formLabelWidth" prop="projectBrief">
                    <el-input type="textarea" v-model="addProjectForm.projectBrief" />
                </el-form-item>
                <el-form-item label="执行负责人" :label-width="formLabelWidth" prop="projectLeader">
                    <el-select filterable v-model="addProjectForm.projectLeader" placeholder="请选择">
                        <el-option
                            v-for="item in allPersonList"
                            :key="item.id"
                            :label="item.employeeName"
                            :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="立项时间" :label-width="formLabelWidth" prop="projectInitiationDate">
                    <el-date-picker v-model="addProjectForm.projectInitiationDate" type="date" />
                </el-form-item>
                <el-form-item label="预计完成时间" :label-width="formLabelWidth" prop="projectExpectedEndDate">
                    <el-date-picker v-model="addProjectForm.projectExpectedEndDate" type="date" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="addProjectDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitCreateForm(addProjectFormRef)"> 确认 </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import GeneralDataDisplay from "@/components/GeneralDataDisplay/index.vue";
import DataTable from "@/components/DataTable/index.vue";
import Detail from "./detail.vue";
import CustomSearch from "@/components/CustomSearch/index.vue";
import { ElMessage, FormInstance } from "element-plus";
import { nextTick } from "vue";
import {
    tmsProjectCreateProject,
    tmsProjectGetProjectList,
    tmsProjectSelectProjectById,
} from "@/apis/tmsProjectController";
import { TmsProject0 } from "@/apis/types";
import { usePersons } from "@/hooks/usePersons";

const { allPersonList } = usePersons();

const searchKeyword = ref("");
const detailData = ref<TmsProject0>(null);

const dataTableRef = ref();
const addProjectFormRef = ref();

const formLabelWidth = "100px";

const addProjectRules = ref({
    projectName: [{ required: true, message: "请输入项目名称", trigger: "blur" }],
    projectStatus: [{ required: true, message: "请选择项目状态", trigger: "blur" }],
});

// 创建项目弹窗
const addProjectDialogVisible = ref(false);

// 创建项目表单
const addProjectForm = ref({
    projectName: "",
    projectBrief: "",
    projectLeader: null,
    projectInitiationDate: null,
    projectExpectedEndDate: null,
});

// 处理搜索
const handleSearch = () => {
    detailData.value = null;
    if (dataTableRef.value) {
        dataTableRef.value.search();
    }
};

// 加载详情
const loadDetail = (item) => {
    // 如果当前选中项和之前选中项相同，则不重新加载
    if (item.id === detailData.value?.id) return;
    detailData.value = null; // 清空之前的详情数据
    tmsProjectSelectProjectById({ params: { id: item.id } }).then((res) => {
        detailData.value = res.data;
    });
};

// 刷新列表
const refreshAll = (id?: number) => {
    detailData.value = null;
    if (id) {
        tmsProjectSelectProjectById({ params: { id: id } }).then((res) => {
            detailData.value = res.data;
        });
    }

    if (dataTableRef.value) {
        dataTableRef.value.refreshAll();
    }
};

// 加载更多函数
const loadMoreFunc = (pageNum: number, pageSize: number, searchKeyword?: string) => {
    return tmsProjectGetProjectList({
        params: {
            pageNum,
            pageSize,
            keyword: searchKeyword.trim(),
        },
    });
};

// 创建团队
const submitCreateForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    await formEl.validate((valid, fields) => {
        if (valid) {
            tmsProjectCreateProject({
                body: {
                    projectBrief: addProjectForm.value.projectBrief,
                    projectName: addProjectForm.value.projectName,
                    leaderId: addProjectForm.value.projectLeader,
                    projectInitiationDate: addProjectForm.value.projectInitiationDate,
                    projectExpectedEndDate: addProjectForm.value.projectExpectedEndDate,
                },
            }).then((res: any) => {
                if (res.code === 200) {
                    ElMessage.success("创建成功");
                    addProjectDialogVisible.value = false;
                    refreshAll();
                }
            });
        } else {
            ElMessage.error("请填写完整信息");
        }
    });
};
</script>

<style scoped lang="scss">
.wrapper {
    height: 100%;

    .top {
        padding: 20px;
        background: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .content {
        height: 100%;
    }
}
</style>
