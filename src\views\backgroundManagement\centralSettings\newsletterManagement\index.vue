<template>
    <div class="newsletter-management">
        <div class="left">
            <div class="search-box">
                <el-input placeholder="输入数据表名称查询" clearable v-model="searchValue" :suffix-icon="Search">
                </el-input>
            </div>
            <div class="left-title">数据表</div>
            <div class="left-list">
                <el-scrollbar ref="leftScrollbar" height="100%">
                    <div
                        style="display: flex; justify-content: space-between"
                        class="template"
                        :class="{ active: currentIndex === i }"
                        v-for="(v, i) in filteredTemplateList"
                        @click="loadDeatil(v.id, i, true, v)"
                        :key="v.id">
                        <div class="template-name" :title="v.templateName">
                            {{ v.templateName }}
                        </div>
                        <!-- 有三种类型，院情、工作、成果，根据三种不同类型添加不同样式 -->
                        <el-tag
                            :class="{
                                'type-1': v.templateType === categoryTemplateType['院情数据'],
                                'type-2': v.templateType === categoryTemplateType['工作数据'],
                                'type-3': v.templateType === categoryTemplateType['成果数据'],
                            }"
                            >{{ categoryTemplateType[v.templateType].substring(0, 2) }}</el-tag
                        >
                    </div>
                </el-scrollbar>
            </div>
        </div>
        <div class="right">
            <div class="right-scroll">
                <!-- 顶部标题栏 -->
                <div class="header-bar">
                    <div class="header-left">
                        <div class="table-title">
                            {{ selectedTemplate?.templateName || "" }}
                        </div>
                    </div>
                </div>
                <div class="right-main" v-loading="loading">
                    <div class="right-main-container-top">
                        <div class="right-main-container">
                            <div class="right-main-title">
                                <div>简讯模板</div>
                                <div class="header-right">
                                    <el-button v-if="!isEdit" type="primary" @click="switchEditMode"
                                        >编辑简讯模板</el-button
                                    >
                                    <el-button v-else type="primary" @click="switchSaveMode">保存简讯模板</el-button>
                                </div>
                            </div>
                            <div class="each-rule" v-for="(item, index) in editorContent" :key="index">
                                <div v-if="item.isEdit" class="rule-box">
                                    <div class="edit-container">
                                        <div class="edit-content">
                                            <div
                                                v-for="(contentItem, contentIndex) in item.contentItems"
                                                :key="contentIndex"
                                                class="content-item">
                                                <!-- 文本输入框 -->
                                                <template v-if="contentItem.type === 'text'">
                                                    <div class="text-input-container">
                                                        <el-input
                                                            style="height: 32px; min-width: 100px; width: 160px"
                                                            v-model="contentItem.value"
                                                            placeholder="请输入文本" />
                                                        <div
                                                            class="delete-btn"
                                                            @click="deleteContentItem(index, contentIndex)">
                                                            <el-icon class="delete-icon"><Close /></el-icon>
                                                        </div>
                                                    </div>
                                                </template>

                                                <!-- 下拉选择框 -->
                                                <template v-else-if="contentItem.type === 'dropdown'">
                                                    <div class="dropdown-container">
                                                        <el-select
                                                            popper-class="newsletter-select-popper"
                                                            class="newsletter-select"
                                                            style="width: 160px"
                                                            v-model="contentItem.value"
                                                            placeholder="选择数据项">
                                                            <el-option
                                                                v-for="option in availableOptions"
                                                                :key="option.code"
                                                                :label="option.name"
                                                                :value="option.code">
                                                                <div>
                                                                    <el-tooltip
                                                                        :show-arrow="false"
                                                                        :offset="16"
                                                                        :hide-after="0"
                                                                        popper-class="dropdown-tooltip"
                                                                        effect="customized"
                                                                        v-if="option.isRequired === 0"
                                                                        content="该数据项在该表内被设置为了<strong>非必填数据项</strong>，用户录入数据时可能不会填写此数据。推送的简讯中将会包含空数据项，导致句子不完整或不通顺。<br><br>
                                                                            如果想将该数据项更改为<strong>必填</strong>数据项，请在<strong>数据表管理</strong>中找到该数据表并更改相关设置。如果仍要使用该数据项，您可以继续进行编辑。"
                                                                        placement="right-start"
                                                                        raw-content>
                                                                        <div class="dropdown-options">
                                                                            <span
                                                                                :class="{
                                                                                    required: option.isRequired === 0,
                                                                                }"
                                                                                >{{ option.name }}</span
                                                                            >
                                                                            <span v-if="option.isRequired === 0">
                                                                                <img
                                                                                    src="../../../../assets/warning.png"
                                                                            /></span>
                                                                        </div>
                                                                    </el-tooltip>
                                                                    <div v-else class="dropdown-options">
                                                                        <span>{{ option.name }}</span>
                                                                    </div>
                                                                </div>
                                                            </el-option>
                                                        </el-select>
                                                        <div
                                                            class="delete-btn"
                                                            @click="deleteContentItem(index, contentIndex)">
                                                            <el-icon class="delete-icon"><Close /></el-icon>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                            <!-- 插入内容按钮 -->
                                            <div class="insert-content">
                                                <el-dropdown @command="(command) => handleInsert(command, index)">
                                                    <div class="insert-btn">
                                                        <span>插入内容</span>
                                                        <el-icon><ArrowDown /></el-icon>
                                                    </div>
                                                    <template #dropdown>
                                                        <el-dropdown-menu>
                                                            <el-dropdown-item command="text">插入文本</el-dropdown-item>
                                                            <el-dropdown-item command="dropdown"
                                                                >插入数据项</el-dropdown-item
                                                            >
                                                        </el-dropdown-menu>
                                                    </template>
                                                </el-dropdown>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 级别选择区域 -->
                                    <div class="grade-selection">
                                        <div class="grade-selection-top">
                                            <div class="grade-title">请选择符合推送条件的项目级别：</div>
                                            <div class="grade-options">
                                                <el-checkbox-group
                                                    style="display: flex; flex-direction: column"
                                                    v-model="editorContent[index].rule">
                                                    <el-checkbox
                                                        v-for="(item, index) in gradeList"
                                                        :key="index"
                                                        :value="Number(item.id)">
                                                        {{ item.name }}
                                                    </el-checkbox>
                                                </el-checkbox-group>
                                            </div>
                                        </div>
                                        <div class="action-buttons">
                                            <el-button @click="cancelEdit(index)" style="padding: 0 25px"
                                                >取消</el-button
                                            >
                                            <el-button
                                                @click="saveRule(index)"
                                                style="padding: 0 25px; margin-left: 0"
                                                type="primary"
                                                >保存</el-button
                                            >
                                        </div>
                                    </div>
                                </div>

                                <div v-else class="show-content-wrapper">
                                    <div
                                        class="show-content"
                                        v-special-block="{
                                            className: 'dropDownId',
                                            replaceTag: 'span',
                                            optionsMap: getOptionsMap(),
                                        }">
                                        <div
                                            class="show-text"
                                            v-special-block="{
                                                className: 'dropDownId',
                                                replaceTag: 'span',
                                                optionsMap: getOptionsMap(),
                                            }"
                                            v-html="item.content"></div>
                                    </div>
                                    <div class="btns" v-if="!item.isEdit && isEdit">
                                        <el-button @click="editRule(index)">编辑</el-button>
                                        <el-button @click="deleteRule(index)" type="danger" plain>删除</el-button>
                                    </div>
                                </div>
                            </div>
                            <div class="add-btn" v-if="isEdit">
                                <el-button type="primary" @click="addEditorContent"> + 新增简讯推送管理 </el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { CmsCategoryTemplate0, CmsCategoryTemplateValue0, DmsDict_, NmsNewsTemplate_ } from "@/apis/types";
import { categoryTemplateType } from "@/enums/categoryTemplate/categoryTemplateType";
import {
    nmsNewsTemplateDelete,
    nmsNewsTemplateGetCategoryFieldList,
    nmsNewsTemplateGetCategoryTemplate,
    nmsNewsTemplateGetTemplatesByEntityId,
    nmsNewsTemplateSave,
    nmsNewsTemplateUpdate,
} from "@/apis/nmsNewsTemplateController";
import { ElMessage, ElMessageBox } from "element-plus";
import { ArrowDown, Close, Search } from "@element-plus/icons-vue";
import { ref } from "vue";
import { cmsCategoryTemplateValueGetDropdownValues } from "@/apis/cmsCategoryTemplateValueController";

const editorContent = ref<
    (NmsNewsTemplate_ & {
        isEdit?: boolean;
        contentItems?: { type: "text" | "dropdown"; value: string }[];
    })[]
>([]);

const availableOptions = ref<{ name: string; code: string; isRequired: number; isEdit: boolean }[]>([]);

// 搜索大类关键词
const searchValue = ref("");

const loading = ref(false);
// 当前左侧选中的index
const currentIndex = ref(-1);

const selectedTemplate = ref<CmsCategoryTemplate0>();

const templateTitle = ref("");

// 添加过滤后的列表引用
const filteredTemplateList = ref<CmsCategoryTemplate0[]>([]); // 新增过滤后的列表

const fieldList = ref<CmsCategoryTemplateValue0[]>([]);

const gradeList = ref<DmsDict_[]>([]);

const templateList = ref<CmsCategoryTemplate0[]>([]);

const isEdit = ref(false);

// 添加搜索值的watch监听
watch(searchValue, (newVal) => {
    if (!newVal) {
        filteredTemplateList.value = [...templateList.value];
    } else {
        filteredTemplateList.value = templateList.value.filter((item) => item.templateName.includes(newVal));
    }
});

// 新增简讯推送管理
const addEditorContent = () => {
    // 检查是否有规则正在编辑
    const editingIndex = editorContent.value.findIndex((item) => item.isEdit);

    if (editingIndex !== -1) {
        // 询问用户是否保存当前编辑的规则
        ElMessageBox.confirm("有规则正在编辑中，添加新规则将丢失未保存的更改。是否继续？", "添加新规则", {
            confirmButtonText: "继续",
            cancelButtonText: "取消",
            type: "warning",
        })
            .then(() => {
                // 用户确认，关闭当前编辑的规则并添加新规则
                editorContent.value[editingIndex].isEdit = false;
                saveNewRule();
            })
            .catch(() => {
                // 用户取消，不做任何操作
            });
    } else {
        // 没有规则在编辑，直接添加新规则
        saveNewRule();
    }
};

// 切换编辑模式
const switchEditMode = () => {
    isEdit.value = true;
};

const switchSaveMode = () => {
    isEdit.value = false;
    editorContent.value.forEach((item, index) => {
        if (item.isEdit) {
            saveRule(index);
            item.isEdit = false;
        }
    });
};

// 抽取保存新规则的逻辑为单独函数
const saveNewRule = () => {
    nmsNewsTemplateSave({
        body: {
            entityId: selectedTemplate.value.id,
            content: "",
            rule: [],
            type: 1,
        },
    }).then((res) => {
        if (res.code === 200) {
            ElMessage.success("新增成功！");
            loadDeatil(selectedTemplate.value.id, currentIndex.value, false, selectedTemplate.value);
        }
    });
};

// 加载大类详情
function loadDeatil(
    templateId: number,
    index: number = currentIndex.value,
    isRefresh: boolean = false,
    template: CmsCategoryTemplate0,
) {
    loading.value = true;
    templateTitle.value = template.templateName;
    selectedTemplate.value = template;
    availableOptions.value = [];
    editorContent.value = [];
    // 修改判断条件，即使当前索引相同也允许加载，因为可能是搜索结果中的唯一项
    if (currentIndex.value === index && isRefresh && searchValue.value === "") {
        loading.value = false;
        return;
    }
    currentIndex.value = index;

    Promise.all([
        nmsNewsTemplateGetTemplatesByEntityId({
            params: {
                entityId: templateId,
            },
        }),
        nmsNewsTemplateGetCategoryFieldList({
            params: {
                categoryId: templateId,
            },
        }),
        cmsCategoryTemplateValueGetDropdownValues({
            params: {
                templateId: selectedTemplate?.value?.id,
                type: 5,
            },
        }),
    ])
        .then(([templatesRes, fieldsRes, gradeRes]) => {
            // 处理模板数据
            editorContent.value = templatesRes.data.map((item) => ({
                id: item.id,
                entityId: selectedTemplate.value.id,
                content: item.content,
                isEdit: false,
                rule: item.rule || [],
                contentItems: [], // 初始化contentItems
            }));

            // 处理字段列表数据
            fieldList.value = fieldsRes.data;
            availableOptions.value = [];
            fieldList.value.forEach((item) => {
                availableOptions.value.push({
                    name: item.value,
                    code: item.id.toString(),
                    isRequired: item.isRequired,
                    isEdit: false,
                });
            });
            gradeList.value = gradeRes.data;
        })
        .catch((error) => {
            console.error("加载数据失败:", error);
        })
        .finally(() => {
            loading.value = false;
        });
}

// 编辑规则
const editRule = (index) => {
    // 检查是否已有其他规则处于编辑状态
    const editingIndex = editorContent.value.findIndex((item) => item.isEdit);

    // 如果有其他规则正在编辑，先关闭它
    if (editingIndex !== -1 && editingIndex !== index) {
        // 询问用户是否保存当前编辑的规则
        ElMessageBox.confirm("有其他规则正在编辑中，切换将丢失未保存的更改。是否继续？", "切换编辑", {
            confirmButtonText: "继续",
            cancelButtonText: "取消",
            type: "warning",
        })
            .then(() => {
                // 清理可能存在的下拉框
                cleanupPossibleDropdowns();

                // 用户确认切换，关闭当前编辑的规则
                editorContent.value[editingIndex].isEdit = false;

                // 打开新选择的规则
                if (!editorContent.value[index].rule) {
                    editorContent.value[index].rule = [];
                }
                editorContent.value[index].isEdit = true;

                // 解析现有内容为可编辑项
                parseContentToItems(index);
            })
            .catch(() => {
                // 用户取消切换，不做任何操作
            });
    } else {
        // 没有其他规则在编辑，直接打开
        if (!editorContent.value[index].rule) {
            editorContent.value[index].rule = [];
        }
        editorContent.value[index].isEdit = true;

        // 解析现有内容为可编辑项
        parseContentToItems(index);
    }
};

// 解析现有内容为可编辑项
const parseContentToItems = (index) => {
    const content = editorContent.value[index].content || "";
    const contentItems = [];

    // 使用正则表达式匹配specialBlock标签
    const regex = /<specialBlock>(.*?)<\/specialBlock>/g;
    let lastIndex = 0;
    let match;

    // 查找所有specialBlock标签
    while ((match = regex.exec(content)) !== null) {
        // 添加specialBlock之前的文本（如果有）
        if (match.index > lastIndex) {
            const textBefore = content.substring(lastIndex, match.index);
            if (textBefore.trim()) {
                contentItems.push({ type: "text", value: textBefore });
            }
        }

        // 添加下拉框项
        contentItems.push({ type: "dropdown", value: match[1] });

        lastIndex = match.index + match[0].length;
    }

    // 添加最后一个specialBlock之后的文本（如果有）
    if (lastIndex < content.length) {
        const textAfter = content.substring(lastIndex);
        if (textAfter.trim()) {
            contentItems.push({ type: "text", value: textAfter });
        }
    }

    // 如果没有内容，添加一个空白文本项
    if (contentItems.length === 0) {
        contentItems.push({ type: "text", value: "" });
    }

    editorContent.value[index].contentItems = contentItems;
};

// 添加清理下拉框的函数
const cleanupPossibleDropdowns = () => {
    // 这个函数用于清理编辑状态中可能存在的下拉框
    // 由于这是自定义指令相关的清理，目前只是一个空函数以解决linter错误
    // 实际实现可能需要根据具体情况进行调整
};

// 删除规则
const deleteRule = (index) => {
    // 检查是否有规则正在编辑
    const editingIndex = editorContent.value.findIndex((item) => item.isEdit);

    if (editingIndex !== -1 && editingIndex !== index) {
        // 询问用户是否保存当前编辑的规则
        ElMessageBox.confirm("有规则正在编辑中，删除其他规则将丢失未保存的更改。是否继续？", "删除规则", {
            confirmButtonText: "继续",
            cancelButtonText: "取消",
            type: "warning",
        })
            .then(() => {
                // 用户确认，关闭当前编辑的规则并执行删除
                editorContent.value[editingIndex].isEdit = false;
                performDelete(index);
            })
            .catch(() => {
                // 用户取消，不做任何操作
                ElMessage.info("已取消删除");
            });
    } else {
        // 确认删除
        ElMessageBox.confirm("确定要删除此规则吗？", "删除规则", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        })
            .then(() => {
                performDelete(index);
            })
            .catch(() => {});
    }
};

// 抽取执行删除的逻辑为单独函数
const performDelete = (index) => {
    nmsNewsTemplateDelete({
        body: {
            id: editorContent.value[index].id,
        },
    }).then((res) => {
        if (res.code === 200) {
            ElMessage.success("删除成功！");
            loadDeatil(selectedTemplate.value.id, currentIndex.value, false, selectedTemplate.value);
        }
    });
};

// 处理插入内容
const handleInsert = (type, index) => {
    if (!editorContent.value[index].contentItems) {
        editorContent.value[index].contentItems = [];
    }

    if (type === "text") {
        editorContent.value[index].contentItems.push({ type: "text", value: "" });
    } else if (type === "dropdown") {
        editorContent.value[index].contentItems.push({
            type: "dropdown",
            value: "",
        });
    }
};

// 删除内容项
const deleteContentItem = (ruleIndex, contentIndex) => {
    editorContent.value[ruleIndex].contentItems.splice(contentIndex, 1);

    // 如果删除后没有内容项，添加一个空文本项
    if (editorContent.value[ruleIndex].contentItems.length === 0) {
        editorContent.value[ruleIndex].contentItems.push({
            type: "text",
            value: "",
        });
    }
};

// 保存规则
const saveRule = (index) => {
    // 将编辑的内容项转换为保存格式
    let content = "";
    if (editorContent.value[index].contentItems && editorContent.value[index].contentItems.length > 0) {
        content = editorContent.value[index].contentItems
            .map((item) => {
                if (item.type === "text") {
                    return item.value;
                } else if (item.type === "dropdown") {
                    return `<specialBlock>${item.value}</specialBlock>`;
                }
                return "";
            })
            .join("");
    }

    editorContent.value[index].content = content;
    editorContent.value[index].isEdit = false;

    // 保存到后端
    nmsNewsTemplateUpdate({
        body: {
            id: editorContent.value[index].id,
            entityId: selectedTemplate.value.id,
            content: editorContent.value[index].content,
            rule: editorContent.value[index].rule,
        },
    }).then((res) => {
        if (res.code === 200) {
            ElMessage.success("保存成功！");
            loadDeatil(selectedTemplate.value.id, currentIndex.value, false, selectedTemplate.value);
        } else {
            ElMessage.error("保存失败！");
        }
    });
};

// 取消编辑
const cancelEdit = (index) => {
    // 重新加载数据，恢复原始状态
    if (selectedTemplate.value) {
        loadDeatil(selectedTemplate.value.id, currentIndex.value, false, selectedTemplate.value);
    } else {
        // 如果没有选中模板，直接修改编辑状态
        editorContent.value[index].isEdit = false;
    }
};

// 添加一个函数用于生成optionsMap
function getOptionsMap() {
    const optionsMap: Record<string, string> = {};
    availableOptions.value.forEach((option) => {
        optionsMap[option.code] = option.name;
    });
    return optionsMap;
}

onMounted(() => {
    nmsNewsTemplateGetCategoryTemplate({}).then((res) => {
        templateList.value = [...res.data]; // 保存原始数据
        filteredTemplateList.value = [...res.data]; // 初始化为完整列表
        if (filteredTemplateList.value.length > 0) {
            const newTemplate = filteredTemplateList.value[0];
            loadDeatil(newTemplate.id, 0, true, newTemplate);
        }
    });
});
</script>

<style scoped lang="scss">
@use "../../../../assets/styles/templateFieldsDetail.scss";

$padding: 0 30px;

// 每行最低高度
$min-height: 70px;

// 覆盖el-tooltip样式
:global(.el-popper.is-customized.dropdown-tooltip) {
    letter-spacing: 0.1rem;
    font-size: 16px;
    padding: 20px;
    background: #fafafa;
    color: #6b7995;
    font-weight: 400;
    max-width: 600px; // 限制悬浮窗最大宽度
    box-shadow: rgba(0, 0, 0, 0.24) 0 3px 8px;
}

:global(.el-popper.is-customized.dropdown-tooltip .el-popper__arrow::before) {
    background: #fafafa;
    right: 0;
}

.required {
    color: #97a1c5;
}

// 院情
.type-1 {
    background: #fff7e6;
    border: 1px solid #ffd591;
    color: #d46b08;
}
// 工作
.type-2 {
    border: 1px solid #87e8de;
    background-color: #e6fffb;
    color: #08979c;
}
// 成果
.type-3 {
    background: #e6f4ff;
    border: 1px solid #91caff;
    color: #0958d9;
}

.active {
    background-color: #f4e4cc;
}

.newsletter-management {
    height: 100%;
    display: flex;
    align-items: flex-start;
    flex-shrink: 0;
    overflow: auto;

    .left {
        display: flex;
        width: 188px;
        height: 100%;
        padding: 16px 0px;
        flex-direction: column;
        align-items: flex-start;
        /* flex-shrink: 0; */
        background: #cedaf1;
        border-radius: 6px 0px 0px 0;
        box-sizing: border-box;

        color: #23346d;

        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        line-height: 1.3; /* 17px */
        letter-spacing: 1.3px;
        .search-box {
            padding: 0 8px 8px 8px;
            border-radius: 6px 0px 0px 0px;
        }
        .left-title {
            display: flex;
            width: 188px;
            padding: 8px 16px;
            align-items: center;
            flex-shrink: 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
            color: #a0add9;
            font-size: 13px;
            font-weight: 400;
        }
        .left-list {
            flex: 1;
            height: 0;
        }
        .template {
            display: flex;
            width: 188px;
            padding: 8px 8px 8px 16px;
            align-items: center;
            flex-shrink: 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
            cursor: pointer;
            transition: all 0.2s;

            .template-name {
                width: 108px;
                height: 100%;
                text-wrap: nowrap;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1;
                line-clamp: 1;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            &:hover {
                background-color: #f4e4cc;
            }
            .type {
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
                border-radius: 4px;
                width: 38px;
                height: 22px;
                padding: 0 7px;
                font-size: 12px;
                font-weight: 400;
                line-height: 20px;
            }
        }
    }

    .right {
        position: relative;
        background: #fff;
        width: 100%;
        height: 100%;

        .right-scroll {
            .right-top {
                height: 60px;
                display: flex;
                align-items: center;
                padding-left: 20px;
                border-bottom: 1px solid #d3d3d3;
                color: #23346d;
                font-size: 24px;
                font-weight: bold;
            }
            .right-main {
                height: calc(100vh - 320px);
                overflow-y: auto;
                padding: 24px 16px;
                display: flex;
                gap: 16px;
                flex-direction: column;
                flex-shrink: 0;

                .right-main-container-top {
                    padding: 24px 16px;
                    .right-main-container {
                        background: #f9fbff;
                        border-radius: 8px;
                        display: flex;
                        padding: 24px 16px;
                        gap: 16px;
                        flex-direction: column;
                        align-items: flex-start;

                        .right-main-title {
                            width: 100%;
                            display: flex;
                            padding-bottom: 12px;
                            justify-content: space-between;
                            color: #23346d;
                            font-size: 20px;
                            font-style: normal;
                            font-weight: 700;
                            line-height: 31.4px; /* 1.572 */
                            letter-spacing: 1px;
                        }

                        .each-rule {
                            flex-wrap: wrap;

                            width: 100%;
                            display: flex;
                            /* padding: 16px 16px; */
                            gap: 16px;
                            align-items: center;
                            flex-shrink: 0;

                            background: #ebf1ff;
                            border: 1px solid #d9d9d9;
                            border-radius: 6px;

                            .show-content-wrapper {
                                display: grid;
                                grid-template-columns: 1fr auto; /* 横向划分：左滚动，右固定 */
                                align-items: center;
                                width: 100%;
                                gap: 8px;
                                .show-content {
                                    overflow-x: hidden;
                                    width: 100%;
                                    flex: 5;
                                    border-radius: 3px;
                                    font-weight: bold;
                                    letter-spacing: 0.1em;
                                    padding: 16px;

                                    .show-text {
                                        overflow-x: auto; /* ✅ 让这个元素能滚动 */
                                        white-space: nowrap; /* ✅ 不换行才会横向滚动 */
                                        max-width: 100%;

                                        color: #000000;
                                        font-family: "Alibaba PuHuiTi 3.0";
                                        font-size: 14px;
                                        font-style: normal;
                                        font-weight: 400;
                                        line-height: 21px; /* 1.5 */
                                        padding: 14px 16px;

                                        display: flex;
                                        align-items: center;
                                        gap: 8px;

                                        /* 自定义滚动条样式 */
                                        &::-webkit-scrollbar {
                                            height: 4px;
                                        }

                                        &::-webkit-scrollbar-track {
                                            background: rgba(0, 0, 0, 0.05);
                                            border-radius: 4px;
                                        }

                                        &::-webkit-scrollbar-thumb {
                                            cursor: pointer;
                                            background: rgba(88, 111, 187, 0.5);
                                            border-radius: 4px;
                                        }

                                        &::-webkit-scrollbar-thumb:hover {
                                            background: rgba(88, 111, 187, 0.7);
                                        }
                                    }
                                }
                                .btns {
                                    padding-right: 16px;
                                    display: flex;
                                    align-items: center;
                                    height: 100%;
                                    flex: 1;
                                }
                                .show-content {
                                    flex: 5;
                                    border-radius: 3px;
                                    font-weight: bold;
                                    letter-spacing: 0.1em;
                                    padding: 16px;

                                    .show-text {
                                        color: #000000;
                                        font-family: "Alibaba PuHuiTi 3.0";
                                        font-size: 14px;
                                        font-style: normal;
                                        font-weight: 400;
                                        line-height: 21px; /* 1.5 */
                                        padding: 14px 16px;

                                        display: flex;
                                        align-items: center;
                                        gap: 8px;
                                    }
                                }
                                .btns {
                                    padding-right: 16px;
                                    display: flex;
                                    justify-content: flex-end;
                                    align-items: center;
                                    flex: 1;
                                }
                            }

                            .grade-selection {
                                display: flex;
                                padding: 16px 16px;
                                gap: 16px;
                                flex-direction: column;
                                align-items: flex-start;
                                flex-shrink: 0;

                                .grade-selection-top {
                                    display: flex;
                                    padding: 16px 32px;
                                    width: 100%;
                                    gap: 16px;
                                    flex-direction: column;
                                    align-items: flex-start;
                                    flex-shrink: 0;

                                    background: #f9fbff;
                                    border-radius: 6px;

                                    .grade-title {
                                        color: #23346d;
                                        font-weight: bold;
                                    }
                                }

                                .action-buttons {
                                    display: flex;
                                    width: 100%;
                                    gap: 16px;
                                    justify-content: flex-end;
                                    align-items: flex-start;
                                    flex-shrink: 0;
                                }
                            }
                        }

                        .add-btn {
                            padding: 12px 0;
                        }
                    }
                }
            }
        }
    }
}

// 添加编辑相关样式
.rule-box {
    width: 100%;
    display: flex;
    flex-direction: column;
}

// 下拉框样式
:deep(.newsletter-select .el-select__wrapper) {
    /* background-color: #586fbb; */
    height: 32px;
    border: 1px solid #1677ff;
    .el-select__placeholder {
        /* color: #fff; */
    }
    .el-select__caret {
        /* color: #fff; */
    }
}
// 下拉框里每个li的padding
:global(.newsletter-select-popper .el-select-dropdown__wrap ul li) {
    padding: 0 15px !important;
}

// 修改下拉框样式
:deep(.dropDownId) {
    padding: 0 15px;

    display: inline-block;
    height: 32px; /* 按需设置高度 */
    line-height: 32px; /* 和高度一样，使文字垂直居中（适合单行文本） */
    text-align: center;

    color: var(--Global-Colors-Neutral-Text-colorText, rgba(0, 0, 0, 0.88));
    font-weight: 400;

    background: var(--Global-Colors-Neutral-Background-colorBgContainer, #ffffff);
    box-shadow: 0px 2px 0px 0px var(--Global-Colors-Neutral-Fill-colorFillQuaternary, rgba(0, 0, 0, 0.02));
    border: 1px solid #1677ff;
    border-radius: 6px;
}

.dropdown-options {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
    img {
        height: 18px;
    }
    span:nth-child(2) {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.edit-container {
    display: flex;
    padding: 16px 16px;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
    background: #ebf1ff;
    border-bottom: 1px solid #d9d9d9;

    .edit-content {
        height: 60px;
        flex: 1;
        width: 0;
        align-items: center;
        display: flex;
        flex-wrap: nowrap;
        gap: 12px;
        flex: 1;
        overflow-x: auto;
        padding: 0 5px;

        /* 自定义滚动条样式 */
        &::-webkit-scrollbar {
            height: 4px;
        }

        &::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
            cursor: pointer;
            background: rgba(88, 111, 187, 0.5);
            border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb:hover {
            background: rgba(88, 111, 187, 0.7);
        }

        .content-item {
            position: relative;
            flex-shrink: 0; /* 防止元素被压缩 */

            .text-input-container,
            .dropdown-container {
                position: relative;
                display: inline-block;

                .delete-btn {
                    position: absolute;
                    top: -3px;
                    right: -3px;
                    width: 14px;
                    height: 14px;
                    background-color: #da5939;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    z-index: 10;

                    .delete-icon {
                        color: #fff;
                        font-size: 12px;
                    }
                }
            }
        }
    }

    .insert-content {
        display: flex;
        justify-content: flex-end;

        .insert-btn {
            height: 40px;
            width: 110px;
            color: #fff;
            padding: 0 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;

            background: #1677ff;
            box-shadow: 0px 2px 0px 0px rgba(5, 145, 255, 0.1);
            border-radius: 6px;

            span {
                // margin-right: 5px;
            }
        }
    }
}
</style>
