# FieldValueDisplay 组件

这个组件用于根据字段类型显示不同格式的字段值，支持以下字段类型：

- 人员（单选/多选）
- 日期
- 文件
- 枚举（单选/多选）
- 级别
- 依托专业
- 是/否
- 其他基础类型（字符串、整数、浮点数、金额等）

## 属性

| 属性名      | 类型      | 必须 | 描述                                 |
| ---------- | --------- | ---- | ------------------------------------ |
| field      | Object    | 是   | 字段定义信息（CmsCategoryTemplateValue10） |
| rowData    | Object    | 是   | 行数据，包含 values 数组              |
| personList | Array     | 是   | 人员列表                             |
| dictList   | Array     | 是   | 字典列表                             |
| orgList    | Array     | 是   | 组织/依托专业列表                    |

## 示例

```vue
<template>
  <FieldValueDisplay
    :field="field"
    :row-data="rowData"
    :person-list="allPersonList"
    :dict-list="dictList"
    :org-list="orgList"
  />
</template>

<script setup>
import FieldValueDisplay from '@/components/FieldValueDisplay/index.vue';
</script>
```

## 设计说明

该组件将原本散落在各个页面中的字段值显示逻辑封装起来，减少代码重复，提高可维护性。根据字段类型，组件会自动调用相应的格式化函数（如 getPersonName、getDependentMajorNames 等）展示适当的值。 