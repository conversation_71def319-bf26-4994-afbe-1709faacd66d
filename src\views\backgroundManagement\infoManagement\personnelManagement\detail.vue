<template>
    <div class="detail-content" v-if="selectedItem" v-loading="detailLoading">
        <div class="person-name">
            {{ selectedItem?.employeeName }}
        </div>
        <div class="main">
            <!-- 基本信息 -->
            <div class="each-box">
                <div class="per-title">
                    <div>基本信息</div>
                    <svg
                        style="cursor: pointer"
                        @click="openEditBasicInfoDialog(false)"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24">
                        <defs>
                            <clipPath id="clipPath9849960044">
                                <path
                                    d="M0 0L24 0L24 24L0 24L0 0Z"
                                    fill-rule="nonzero"
                                    transform="matrix(1 0 0 1 0 0)" />
                            </clipPath>
                        </defs>
                        <g clip-path="url(#clipPath9849960044)">
                            <path
                                d="M12.0964 1.96339L10.133 0L0.417857 9.7125L0 12.0964L2.38125 11.6759L12.0964 1.96339Z"
                                fill-rule="nonzero"
                                transform="matrix(1 0 0 1 6.57568 4.04517)"
                                fill="rgb(230, 244, 255)" />
                            <path
                                d="M20.5714 19.3929L0.857143 19.3929C0.383036 19.3929 0 19.7759 0 20.25L0 21.2143C0 21.3321 0.0964286 21.4286 0.214286 21.4286L21.2143 21.4286C21.3321 21.4286 21.4286 21.3321 21.4286 21.2143L21.4286 20.25C21.4286 19.7759 21.0455 19.3929 20.5714 19.3929ZM3.90268 17.1429C3.95625 17.1429 4.00982 17.1375 4.06339 17.1295L8.56875 16.3393C8.62232 16.3286 8.67321 16.3045 8.71071 16.2643L20.0652 4.90982C20.09 4.88504 20.1097 4.85561 20.1232 4.8232C20.1366 4.7908 20.1435 4.75606 20.1435 4.72098C20.1435 4.6859 20.1366 4.65116 20.1232 4.61876C20.1097 4.58636 20.09 4.55692 20.0652 4.53214L15.6134 0.0776786C15.5625 0.0267858 15.4955 0 15.4232 0C15.3509 0 15.2839 0.0267858 15.233 0.0776786L3.87857 11.4321C3.83839 11.4723 3.81429 11.5205 3.80357 11.5741L3.01339 16.0795C2.98734 16.223 2.99665 16.3706 3.04052 16.5097C3.08439 16.6488 3.1615 16.7751 3.26518 16.8777C3.44196 17.0491 3.66429 17.1429 3.90268 17.1429ZM5.70804 12.4714L15.4232 2.75893L17.3866 4.72232L7.67143 14.4348L5.29018 14.8554L5.70804 12.4714Z"
                                fill-rule="nonzero"
                                transform="matrix(1 0 0 1 1.28467 1.28613)"
                                fill="rgb(22, 119, 255)" />
                        </g>
                    </svg>
                </div>
                <el-descriptions :column="4">
                    <el-descriptions-item label="教职工号：">{{ selectedItem.employeeId ?? "-" }}</el-descriptions-item>
                    <el-descriptions-item label="员工类型：">{{
                        getEmployeeTypeName(selectedItem.employeeType)
                    }}</el-descriptions-item>
                    <el-descriptions-item label="部门：">{{
                        findDepartmentNameById(selectedItem.deptId)
                    }}</el-descriptions-item>
                    <el-descriptions-item label="岗位：">{{
                        getPositionName(selectedItem.position).toString()
                    }}</el-descriptions-item>
                    <el-descriptions-item label="性别：">{{ getGenderName(selectedItem.gender) }}</el-descriptions-item>
                    <el-descriptions-item label="民族：">{{
                        getEthnicityName(selectedItem.ethnicity)
                    }}</el-descriptions-item>
                    <el-descriptions-item label="政治面貌：">{{
                        getPoliticalStatusName(selectedItem.political)
                    }}</el-descriptions-item>
                    <el-descriptions-item label="出生地：">{{ selectedItem.birthPlace ?? "-" }}</el-descriptions-item>
                    <el-descriptions-item label="出生日期：">{{ selectedItem.birthDate ?? "-" }}</el-descriptions-item>
                    <el-descriptions-item label="身份证号：">{{
                        personSensitiveInfo?.idCard ?? "-"
                    }}</el-descriptions-item>
                    <el-descriptions-item label="来校时间：">{{ selectedItem.arriveDate ?? "-" }}</el-descriptions-item>
                </el-descriptions>
            </div>

            <!-- 学历信息 -->
            <div class="per-title">
                <div>学历信息</div>
                <svg
                    style="cursor: pointer"
                    @click="openEditEducationDialog"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24">
                    <defs>
                        <clipPath id="clipPath9849960044">
                            <path d="M0 0L24 0L24 24L0 24L0 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 0 0)" />
                        </clipPath>
                    </defs>
                    <g clip-path="url(#clipPath9849960044)">
                        <path
                            d="M12.0964 1.96339L10.133 0L0.417857 9.7125L0 12.0964L2.38125 11.6759L12.0964 1.96339Z"
                            fill-rule="nonzero"
                            transform="matrix(1 0 0 1 6.57568 4.04517)"
                            fill="rgb(230, 244, 255)" />
                        <path
                            d="M20.5714 19.3929L0.857143 19.3929C0.383036 19.3929 0 19.7759 0 20.25L0 21.2143C0 21.3321 0.0964286 21.4286 0.214286 21.4286L21.2143 21.4286C21.3321 21.4286 21.4286 21.3321 21.4286 21.2143L21.4286 20.25C21.4286 19.7759 21.0455 19.3929 20.5714 19.3929ZM3.90268 17.1429C3.95625 17.1429 4.00982 17.1375 4.06339 17.1295L8.56875 16.3393C8.62232 16.3286 8.67321 16.3045 8.71071 16.2643L20.0652 4.90982C20.09 4.88504 20.1097 4.85561 20.1232 4.8232C20.1366 4.7908 20.1435 4.75606 20.1435 4.72098C20.1435 4.6859 20.1366 4.65116 20.1232 4.61876C20.1097 4.58636 20.09 4.55692 20.0652 4.53214L15.6134 0.0776786C15.5625 0.0267858 15.4955 0 15.4232 0C15.3509 0 15.2839 0.0267858 15.233 0.0776786L3.87857 11.4321C3.83839 11.4723 3.81429 11.5205 3.80357 11.5741L3.01339 16.0795C2.98734 16.223 2.99665 16.3706 3.04052 16.5097C3.08439 16.6488 3.1615 16.7751 3.26518 16.8777C3.44196 17.0491 3.66429 17.1429 3.90268 17.1429ZM5.70804 12.4714L15.4232 2.75893L17.3866 4.72232L7.67143 14.4348L5.29018 14.8554L5.70804 12.4714Z"
                            fill-rule="nonzero"
                            transform="matrix(1 0 0 1 1.28467 1.28613)"
                            fill="rgb(22, 119, 255)" />
                    </g>
                </svg>
            </div>
            <el-descriptions :column="3">
                <el-descriptions-item label="最高学历：">{{
                    getHighestEducationName(selectedItem.highestEducation)
                }}</el-descriptions-item>
                <el-descriptions-item label="获得学历的机构或院校：">{{
                    selectedItem.graduationSchool ?? "-"
                }}</el-descriptions-item>
                <el-descriptions-item label="学历所学专业：">{{
                    selectedItem.graduationSchoolMajor ?? "-"
                }}</el-descriptions-item>
                <el-descriptions-item label="最高学位：">{{
                    getHighestDegreeName(selectedItem.highestDegree)
                }}</el-descriptions-item>
                <el-descriptions-item label="获得学位的机构或院校：">{{
                    selectedItem.degreeSchool ?? "-"
                }}</el-descriptions-item>
                <el-descriptions-item label="学位所学专业：">{{
                    selectedItem.highestMajor ?? "-"
                }}</el-descriptions-item>
            </el-descriptions>

            <!-- 职称 -->
            <div class="per-title space-between">
                <div>职称</div>
                <div><el-button @click="openAddTitleDialog">+ 新增职称</el-button></div>
            </div>
            <div class="info-box">
                <el-table :data="selectedItem.portList" style="width: 100%" header-row-class-name="table-header">
                    <el-table-column prop="level" label="级别">
                        <template #default="{ row }">
                            {{ getProfessionalRankName(row.level) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="post" label="职务">
                        <template #default="{ row }">
                            {{ getProfessionalTitleName(row.post) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="major" label="专业">
                        <template #default="{ row }">
                            {{ row.major ?? "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="issueDate" label="发证时间">
                        <template #default="{ row }">
                            {{ row.issueDate ?? "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="certificateNumber" label="证书编号">
                        <template #default="{ row }">
                            {{ row.certificateNumber ?? "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="certificateAuthority" label="发证机构">
                        <template #default="{ row }">
                            {{ row.certificateAuthority ?? "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="supportingMaterialsIds" label="佐证材料">
                        <template #default="{ row }">
                            <FileIconDisplay
                                v-if="row.supportingMaterialsIds"
                                :fileUrls="row.supportingMaterialsIds"
                                :downloadable="true" />
                            <div v-else>-</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" min-width="100">
                        <template #default="{ row }">
                            <el-button link type="primary" size="small" @click="openEditTitleDialog(row)"
                                >编辑</el-button
                            >
                            <el-button link type="primary" size="small" @click="deleteTitle(row.id)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!-- 职业资格证书 -->
            <div class="per-title space-between">
                <div>职业资格证书</div>
                <div>
                    <el-button @click="openAddCertificateDialog">+ 新增职业资格证书</el-button>
                </div>
            </div>
            <div class="info-box">
                <el-table :data="selectedItem.certificateList" style="width: 100%" header-row-class-name="table-header">
                    <el-table-column prop="name" label="职业资格证名称">
                        <template #default="{ row }">
                            {{ row.name ?? "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="level" label="级别">
                        <template #default="{ row }">
                            {{ getVocationalQualificationLevelName(row.level) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="issueDate" label="发证时间">
                        <template #default="{ row }">
                            {{ row.issueDate ?? "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="certificateNumber" label="证书编号">
                        <template #default="{ row }">
                            {{ row.certificateNumber ?? "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="certificateAuthority" label="发证机构">
                        <template #default="{ row }">
                            {{ row.certificateAuthority ?? "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="supportingMaterialsIds" label="佐证材料">
                        <template #default="{ row }">
                            <FileIconDisplay
                                v-if="row.supportingMaterialsIds"
                                :fileUrls="row.supportingMaterialsIds"
                                :downloadable="true" />
                            <div v-else>-</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" min-width="100">
                        <template #default="{ row }">
                            <el-button link type="primary" size="small" @click="openEditCertificateDialog(row)"
                                >编辑</el-button
                            >
                            <el-button link type="primary" size="small" @click="deleteCertificate(row.id)"
                                >删除</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!-- 教师资格证书 -->
            <div class="per-title space-between">
                <div>教师资格证书</div>
                <div>
                    <el-button :disabled="selectedItem.teacherList.length >= 1" @click="openAddTeacherCertDialog"
                        >+ 新增教师资格证书</el-button
                    >
                </div>
            </div>
            <div class="info-box">
                <el-table
                    show
                    :data="selectedItem.teacherList"
                    style="width: 100%"
                    header-row-class-name="table-header">
                    <el-table-column prop="kind" label="资格种类">
                        <template #default="{ row }">
                            {{ row.kind ?? "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="issueDate" label="发证时间">
                        <template #default="{ row }">
                            {{ row.issueDate ?? "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="issueDate" label="发证时间">
                        <template #default="{ row }">
                            {{ row.major ?? "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="certificateNumber" label="证书编号">
                        <template #default="{ row }">
                            {{ row.certificateNumber ?? "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="certificateAuthority" label="发证机构">
                        <template #default="{ row }">
                            {{ row.certificateAuthority ?? "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="supportingMaterialsIds" label="佐证材料">
                        <template #default="{ row }">
                            <FileIconDisplay
                                v-if="row.supportingMaterialsIds"
                                :fileUrls="row.supportingMaterialsIds"
                                :downloadable="true" />
                            <div v-else>-</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" min-width="100">
                        <template #default="{ row }">
                            <el-button link type="primary" size="small" @click="openEditTeacherCertDialog(row)"
                                >编辑</el-button
                            >
                            <el-button link type="primary" size="small" @click="deleteTeacherCert(row.id)"
                                >删除</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!-- 学术/社会组织兼职 -->
            <div class="per-title space-between">
                <div>学术/社会组织兼职</div>
                <div>
                    <el-button @click="openAddAcademicRoleDialog">+ 新增学术/社会组织兼职</el-button>
                </div>
            </div>
            <div class="info-box">
                <el-table :data="selectedItem.academicRolesList" style="width: 100%" header-row-class-name="table-header">
                    <el-table-column prop="orgName" label="学术/社会组织名称">
                        <template #default="{ row }">
                            {{ row.orgName ?? "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="orgLevel" label="组织级别">
                        <template #default="{ row }">
                            {{ row.orgLevel ?? "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="position" label="担任职务">
                        <template #default="{ row }">
                            {{ row.position ?? "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="startDate" label="聘任开始时间">
                        <template #default="{ row }">
                            {{ row.startDate ?? "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="endDate" label="聘任结束时间">
                        <template #default="{ row }">
                            {{ row.endDate ?? "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="supportingMaterialsIds" label="佐证材料">
                        <template #default="{ row }">
                            <FileIconDisplay
                                v-if="row.supportingMaterialsIds"
                                :fileUrls="row.supportingMaterialsIds"
                                :downloadable="true" />
                            <div v-else>-</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" min-width="100">
                        <template #default="{ row }">
                            <el-button link type="primary" size="small" @click="openEditAcademicRoleDialog(row)"
                                >编辑</el-button
                            >
                            <el-button link type="primary" size="small" @click="deleteAcademicRole(row.id)"
                                >删除</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!-- 是否双师 -->
            <div class="per-title">
                <div>其他信息</div>
            </div>
            <div class="info-box">
                <el-form :model="selectedItem" style="width: 100%">
                    <el-form-item label="是否双师：">
                        <el-radio-group
                            v-model="selectedItem.isDualTeacher"
                            @change="updateDualTeacherStatus">
                            <el-radio :value="1">是</el-radio>
                            <el-radio :value="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
            </div>
        </div>

        <!-- 基本信息抽屉 -->
        <CustomDrawer
            v-model="basicInfoDialogVisible"
            title="编辑基本信息"
            @confirm="submitBasicInfoForm(basicInfoFormRef)"
            @close="basicInfoDialogVisible = false">
            <el-form
                :model="basicInfoForm"
                :rules="basicInfoRules"
                ref="basicInfoFormRef"
                label-width="120px"
                label-position="top">
                <el-form-item label="姓名" prop="employeeName">
                    <el-input v-model="basicInfoForm.employeeName" placeholder="请输入姓名" />
                </el-form-item>
                <el-form-item label="头像" prop="avatar">
                    <el-upload
                        class="avatar-uploader"
                        action=""
                        :show-file-list="false"
                        :auto-upload="false"
                        :on-change="handleAvatarChange">
                        <img v-if="avatarPreviewUrl" :src="avatarPreviewUrl" class="avatar" />
                        <img
                            v-else-if="basicInfoForm.avatar"
                            :src="filesBaseUrl + basicInfoForm.avatar"
                            class="avatar" />
                        <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                    </el-upload>
                </el-form-item>
                <el-form-item label="教职工号" prop="employeeId">
                    <el-input
                        :disabled="!!basicInfoForm.id"
                        v-model="basicInfoForm.employeeId"
                        placeholder="请输入教职工号" />
                </el-form-item>
                <el-form-item label="员工类型" prop="employeeType">
                    <el-select v-model="basicInfoForm.employeeType" placeholder="请选择员工类型" clearable>
                        <el-option
                            v-for="item in employeeTypeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="性别" prop="gender">
                    <el-select v-model="basicInfoForm.gender" placeholder="请选择性别" clearable>
                        <el-option
                            v-for="item in genderOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="民族" prop="ethnicity">
                    <el-select v-model="basicInfoForm.ethnicity" placeholder="请选择民族" clearable>
                        <el-option
                            v-for="item in ethnicityOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="政治面貌" prop="political">
                    <el-select v-model="basicInfoForm.political" placeholder="请选择政治面貌" clearable>
                        <el-option
                            v-for="item in politicalStatusOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="出生地" prop="birthPlaceValue">
                    <el-cascader
                        style="width: 100%"
                        v-model="basicInfoForm.birthPlaceValue"
                        :options="birthplaceOptions"
                        :props="{
                            expandTrigger: 'hover',
                            checkStrictly: false,
                            label: 'name',
                            value: 'name',
                            children: 'children',
                            emitPath: true,
                        }"
                        clearable
                        placeholder="请选择出生地" />
                </el-form-item>
                <el-form-item label="出生日期" prop="birthDate">
                    <el-date-picker
                        style="width: 100%"
                        v-model="basicInfoForm.birthDate"
                        type="date"
                        placeholder="请选择出生日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item label="身份证号" prop="idCard">
                    <el-input v-model="basicInfoForm.idCard" placeholder="请输入身份证号" />
                </el-form-item>
                <el-form-item label="来校时间" prop="arriveDate">
                    <el-date-picker
                        style="width: 100%"
                        v-model="basicInfoForm.arriveDate"
                        type="date"
                        placeholder="请选择来校时间"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item label="所属部门" prop="deptId">
                    <el-cascader
                        v-model="basicInfoForm.deptId"
                        :options="orgOptions"
                        :props="cascaderProps"
                        placeholder="请选择所属部门"
                        clearable />
                </el-form-item>
                <el-form-item label="岗位" prop="position">
                    <el-select v-model="basicInfoForm.position" multiple placeholder="请选择岗位" style="width: 100%">
                        <el-option v-for="item in positionList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
            </el-form>
        </CustomDrawer>

        <!-- 职称抽屉 -->
        <CustomDrawer
            v-model="titleDialogVisible"
            title="职称信息"
            @confirm="submitTitleForm(titleFormRef)"
            @close="titleDialogVisible = false">
            <el-form :model="titleForm" :rules="titleRules" ref="titleFormRef" label-width="120px" label-position="top">
                <el-form-item label="级别" prop="level">
                    <el-select v-model="titleForm.level" placeholder="请选择级别" clearable>
                        <el-option
                            v-for="item in professionalRankOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="职务" prop="post">
                    <el-select v-model="titleForm.post" placeholder="请选择职务" clearable>
                        <el-option
                            v-for="item in professionalTitleOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="专业" prop="major">
                    <el-input v-model="titleForm.major" placeholder="请输入专业" />
                </el-form-item>
                <el-form-item label="发证时间" prop="issueDate">
                    <el-date-picker
                        style="width: 100%"
                        v-model="titleForm.issueDate"
                        type="date"
                        placeholder="请选择发证时间"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item label="证书编号" prop="certificateNumber">
                    <el-input v-model="titleForm.certificateNumber" placeholder="请输入证书编号" />
                </el-form-item>
                <el-form-item label="发证机构" prop="certificateAuthority">
                    <el-input v-model="titleForm.certificateAuthority" placeholder="请输入发证机构" />
                </el-form-item>
                <el-form-item label="佐证材料" prop="supportingMaterialsIds">
                    <el-upload
                        class="material-upload"
                        :auto-upload="false"
                        multiple
                        :limit="fileLimit"
                        v-model:file-list="fileList"
                        :on-change="handleFileChange"
                        action="">
                        <el-button type="primary">
                            选择文件<el-icon class="el-icon--right"><Upload /></el-icon>
                        </el-button>
                        <template #tip>
                            <div class="el-upload__tip">最多上传10个文件，单个文件不超过100MB</div>
                        </template>
                    </el-upload>
                </el-form-item>
            </el-form>
        </CustomDrawer>

        <!-- 职业资格证书抽屉 -->
        <CustomDrawer
            v-model="certificateDialogVisible"
            title="职业资格证书信息"
            @confirm="submitCertificateForm(certificateFormRef)"
            @close="certificateDialogVisible = false">
            <el-form
                :model="certificateForm"
                :rules="certificateRules"
                ref="certificateFormRef"
                label-width="120px"
                label-position="top">
                <el-form-item label="证书名称" prop="name">
                    <el-input v-model="certificateForm.name" placeholder="请输入职业资格证名称" />
                </el-form-item>
                <el-form-item label="级别" prop="level">
                    <el-select v-model="certificateForm.level" placeholder="请输入级别" clearable>
                        <el-option
                            v-for="item in vocationalQualificationLevelOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="发证时间" prop="issueDate">
                    <el-date-picker
                        style="width: 100%"
                        v-model="certificateForm.issueDate"
                        type="date"
                        placeholder="请选择发证时间"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item label="证书编号" prop="certificateNumber">
                    <el-input v-model="certificateForm.certificateNumber" placeholder="请输入证书编号" />
                </el-form-item>
                <el-form-item label="发证机构" prop="certificateAuthority">
                    <el-input v-model="certificateForm.certificateAuthority" placeholder="请输入发证机构" />
                </el-form-item>
                <el-form-item label="佐证材料" prop="supportingMaterialsIds">
                    <el-upload
                        class="material-upload"
                        :auto-upload="false"
                        multiple
                        :limit="fileLimit"
                        v-model:file-list="fileList"
                        :on-change="handleFileChange"
                        action="">
                        <el-button type="primary">
                            选择文件<el-icon class="el-icon--right"><Upload /></el-icon>
                        </el-button>
                        <template #tip>
                            <div class="el-upload__tip">最多上传10个文件，单个文件不超过100MB</div>
                        </template>
                    </el-upload>
                </el-form-item>
            </el-form>
        </CustomDrawer>

        <!-- 教师资格证书抽屉 -->
        <CustomDrawer
            v-model="teacherCertDialogVisible"
            title="教师资格证书信息"
            @confirm="submitTeacherCertForm(teacherCertFormRef)"
            @close="teacherCertDialogVisible = false">
            <el-form
                :model="teacherCertForm"
                :rules="teacherCertRules"
                ref="teacherCertFormRef"
                label-width="120px"
                label-position="top">
                <el-form-item label="资格种类" prop="kind">
                    <el-input v-model="teacherCertForm.kind" placeholder="请输入资格种类" />
                </el-form-item>
                <el-form-item label="发证时间" prop="issueDate">
                    <el-date-picker
                        style="width: 100%"
                        v-model="teacherCertForm.issueDate"
                        type="date"
                        placeholder="请选择发证时间"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item label="专业" prop="major">
                    <el-input v-model="teacherCertForm.major" placeholder="请输入专业" />
                </el-form-item>
                <el-form-item label="证书编号" prop="certificateNumber">
                    <el-input v-model="teacherCertForm.certificateNumber" placeholder="请输入证书编号" />
                </el-form-item>
                <el-form-item label="发证机构" prop="certificateAuthority">
                    <el-input v-model="teacherCertForm.certificateAuthority" placeholder="请输入发证机构" />
                </el-form-item>
                <el-form-item label="佐证材料" prop="supportingMaterialsIds">
                    <el-upload
                        class="material-upload"
                        :auto-upload="false"
                        multiple
                        :limit="fileLimit"
                        v-model:file-list="fileList"
                        :on-change="handleFileChange"
                        action="">
                        <el-button type="primary">
                            选择文件<el-icon class="el-icon--right"><Upload /></el-icon>
                        </el-button>
                        <template #tip>
                            <div class="el-upload__tip">最多上传10个文件，单个文件不超过100MB</div>
                        </template>
                    </el-upload>
                </el-form-item>
            </el-form>
        </CustomDrawer>

        <!-- 学术/社会组织兼职抽屉 -->
        <CustomDrawer
            v-model="academicRoleDialogVisible"
            title="学术/社会组织兼职信息"
            @confirm="submitAcademicRoleForm(academicRoleFormRef)"
            @close="academicRoleDialogVisible = false">
            <el-form
                :model="academicRoleForm"
                :rules="academicRoleRules"
                ref="academicRoleFormRef"
                label-width="120px"
                label-position="top">
                <el-form-item label="组织名称" prop="orgName">
                    <el-input v-model="academicRoleForm.orgName" placeholder="请输入学术/社会组织名称" />
                </el-form-item>
                <el-form-item label="组织级别" prop="orgLevel">
                    <el-input v-model="academicRoleForm.orgLevel" placeholder="请输入组织级别" />
                </el-form-item>
                <el-form-item label="担任职务" prop="position">
                    <el-input v-model="academicRoleForm.position" placeholder="请输入担任职务" />
                </el-form-item>
                <el-form-item label="聘任开始时间" prop="startDate">
                    <el-date-picker
                        style="width: 100%"
                        v-model="academicRoleForm.startDate"
                        type="date"
                        placeholder="请选择聘任开始时间"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item label="聘任结束时间" prop="endDate">
                    <el-date-picker
                        style="width: 100%"
                        v-model="academicRoleForm.endDate"
                        type="date"
                        placeholder="请选择聘任结束时间"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item label="佐证材料" prop="supportingMaterialsIds">
                    <el-upload
                        class="material-upload"
                        :auto-upload="false"
                        multiple
                        :limit="fileLimit"
                        v-model:file-list="fileList"
                        :on-change="handleFileChange"
                        action="">
                        <el-button type="primary">
                            选择文件<el-icon class="el-icon--right"><Upload /></el-icon>
                        </el-button>
                        <template #tip>
                            <div class="el-upload__tip">最多上传10个文件，单个文件不超过100MB</div>
                        </template>
                    </el-upload>
                </el-form-item>
            </el-form>
        </CustomDrawer>

        <!-- 学历信息抽屉 -->
        <CustomDrawer
            v-model="educationDialogVisible"
            title="学历信息"
            @confirm="submitEducationForm(educationFormRef)"
            @close="educationDialogVisible = false">
            <el-form
                :model="educationForm"
                :rules="educationRules"
                ref="educationFormRef"
                label-width="200px"
                label-position="top">
                <el-form-item label="最高学历" prop="highestEducation">
                    <el-select v-model="educationForm.highestEducation" placeholder="请选择最高学历" clearable>
                        <el-option
                            v-for="item in highestEducationOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="获得学历的机构或院校" prop="graduationSchool">
                    <el-input v-model="educationForm.graduationSchool" placeholder="请输入获得学历的机构或院校" />
                </el-form-item>
                <el-form-item label="学历所学专业" prop="graduationSchoolMajor">
                    <el-input v-model="educationForm.graduationSchoolMajor" placeholder="请输入学历所学专业" />
                </el-form-item>

                <el-form-item label="最高学位" prop="highestDegree">
                    <el-select v-model="educationForm.highestDegree" placeholder="请选择最高学位" clearable>
                        <el-option
                            v-for="item in highestDegreeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="获得学位的机构或院校" prop="degreeSchool">
                    <el-input v-model="educationForm.degreeSchool" placeholder="请输入获得学位的机构或院校" />
                </el-form-item>
                <el-form-item label="学位所学专业" prop="highestMajor">
                    <el-input v-model="educationForm.highestMajor" placeholder="请输入学位所学专业" />
                </el-form-item>
            </el-form>
        </CustomDrawer>
    </div>
    <div v-else class="detail-content">
        <el-empty description="请从左侧选择一个人员查看详情。" />
    </div>
</template>

<script setup lang="ts">
import { UmsPerson1Res, UmsPersonSensitive_ } from "@/apis/types";
import {
    umsPersonSaveAndUpdateCertificates,
    umsPersonSaveAndUpdateAcademicRoles,
    umsPersonSensitiveDetails,
    umsPersonDeleteCertificates,
    umsPersonDeleteAcademicRoles,
    umsPersonUpdate,
    umsPersonCreate,
} from "@/apis/umsPersonController";
import { minioUploads } from "@/apis/fmsMinioController";
import { Upload } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import type { FormInstance, FormRules, UploadProps, UploadUserFile } from "element-plus";
import FileIconDisplay from "@/components/FileIconDisplay/index.vue";
import { enumToOptionsGeneric } from "@/utils/enums";
import { EmployeeType, EmployeeTypeMap } from "@/enums/options/EmployeeType";
import { GenderType, GenderTypeMap } from "@/enums/options/GenderType";
import { EthnicityType, EthnicityTypeMap } from "@/enums/options/EthnicityType";
import { PoliticalStatusType, PoliticalStatusTypeMap } from "@/enums/options/PoliticalStatusType";
import { HighestEducationType, HighestEducationTypeMap } from "@/enums/options/HighestEducationType";
import { HighestDegreeType, HighestDegreeTypeMap } from "@/enums/options/HighestDegreeType";
import { ProfessionalRankType, ProfessionalRankTypeMap } from "@/enums/options/ProfessionalRankType";
import { ProfessionalTitleType, ProfessionalTitleTypeMap } from "@/enums/options/ProfessionalTitleType";
import {
    VocationalQualificationLevelType,
    VocationalQualificationLevelTypeMap,
} from "@/enums/options/VocationalQualificationLevelType";
import { Birthplace } from "@/enums/options/BirthplaceJSON";
import { umsDeptListOpenTree } from "@/apis/umsDeptController";
import type { UmsDept_, UmsPost_ } from "@/apis/types";
import { filesBaseUrl } from "@/utils/filesBaseUrl";
import { uploadFiles } from "@/utils/fileUpload";
import CustomDrawer from "@/components/CustomDrawer";

const props = defineProps<{
    selectedItem: UmsPerson1Res;
    detailLoading: boolean;
    positionList: UmsPost_[];
    getPositionName: (position: string) => string[];
}>();

const orgOptions = ref<UmsDept_[]>([]);

umsDeptListOpenTree({}).then((res) => {
    orgOptions.value = res.data;
});

const cascaderProps = {
    expandTrigger: "hover" as const,
    value: "id",
    label: "name",
    children: "children",
    emitPath: false,
};

const emit = defineEmits<{
    (e: "refresh"): void;
    (e: "refreshAll"): void;
}>();

// 人员敏感信息
const personSensitiveInfo = ref<UmsPersonSensitive_>();

// 根据人员id获取人员敏感信息
const getPersonSensitiveInfo = async (id: UmsPerson1Res["id"]) => {
    try {
        const res = await umsPersonSensitiveDetails({
            params: { id },
        });
        personSensitiveInfo.value = res.data;
    } catch (error) {
        console.error("获取人员敏感信息失败:", error);
        ElMessage.error("获取人员敏感信息失败");
    }
};

// 选中人员后获取人员敏感信息
watch(
    () => props.selectedItem,
    (newVal) => {
        if (newVal && newVal.id) {
            getPersonSensitiveInfo(newVal.id);
        }
    }
);

// 处理佐证材料
const handleSupportingMaterials = (supportingMaterialsIds: string) => {
    if (supportingMaterialsIds) {
        const urls = supportingMaterialsIds.split(",");
        fileList.value = urls.map((url: string) => ({
            name: url.split("::").pop() || url,
            url: url,
            status: "success",
        }));
    } else {
        fileList.value = [];
    }
};

// ========================= 下拉选项 =========================

// 员工类型下拉选项 - 使用带Map的版本显示中文
const employeeTypeOptions = enumToOptionsGeneric(EmployeeType, EmployeeTypeMap);

// 性别下拉选项 - 使用带Map的版本显示中文
const genderOptions = enumToOptionsGeneric(GenderType, GenderTypeMap);

// 民族下拉选项 - 使用带Map的版本显示中文
const ethnicityOptions = enumToOptionsGeneric(EthnicityType, EthnicityTypeMap);

// 政治面貌下拉选项 - 使用带Map的版本显示中文
const politicalStatusOptions = enumToOptionsGeneric(PoliticalStatusType, PoliticalStatusTypeMap);

// 最高学历下拉选项 - 使用带Map的版本显示中文
const highestEducationOptions = enumToOptionsGeneric(HighestEducationType, HighestEducationTypeMap);

// 最高学位下拉选项 - 使用带Map的版本显示中文
const highestDegreeOptions = enumToOptionsGeneric(HighestDegreeType, HighestDegreeTypeMap);

// 职称级别下拉选项 - 使用带Map的版本显示中文
const professionalRankOptions = enumToOptionsGeneric(ProfessionalRankType, ProfessionalRankTypeMap);

// 职称职务下拉选项 - 使用带Map的版本显示中文
const professionalTitleOptions = enumToOptionsGeneric(ProfessionalTitleType, ProfessionalTitleTypeMap);

// 职业资格证书级别下拉选项 - 使用带Map的版本显示中文
const vocationalQualificationLevelOptions = enumToOptionsGeneric(
    VocationalQualificationLevelType,
    VocationalQualificationLevelTypeMap
);

// 格式化出生地数据，将city和area都转换为children属性
const formatBirthplaceData = (birthplaceData: any[]): any[] => {
    return birthplaceData.map((province) => {
        const formattedProvince = { ...province };
        if (province.city && province.city.length > 0) {
            formattedProvince.children = province.city.map((city: any) => {
                const formattedCity = { ...city };
                if (city.area && city.area.length > 0) {
                    formattedCity.children = city.area;
                }
                delete formattedCity.area;
                return formattedCity;
            });
            delete formattedProvince.city;
        }
        return formattedProvince;
    });
};

// 出生地级联选项
const birthplaceOptions = formatBirthplaceData(Birthplace);

// ========================= 枚举值转显示名称的函数 =========================

// 获取员工类型名称
function getEmployeeTypeName(type: number | null | undefined): string {
    if (type === null || type === undefined) return "-";
    return EmployeeTypeMap[type as EmployeeType] || "-";
}

// 获取性别名称
function getGenderName(gender: number | null | undefined): string {
    if (gender === null || gender === undefined) return "-";
    return GenderTypeMap[gender as GenderType] || "-";
}

// 获取民族名称
function getEthnicityName(ethnicity: number | null | undefined): string {
    if (ethnicity === null || ethnicity === undefined) return "-";
    return EthnicityTypeMap[ethnicity as EthnicityType] || "-";
}

// 获取政治面貌名称
function getPoliticalStatusName(political: number | null | undefined): string {
    if (political === null || political === undefined) return "-";
    return PoliticalStatusTypeMap[political as PoliticalStatusType] || "-";
}

// 获取最高学历名称
function getHighestEducationName(education: number | null | undefined): string {
    if (education === null || education === undefined) return "-";
    return HighestEducationTypeMap[education as HighestEducationType] || "-";
}

// 获取最高学位名称
function getHighestDegreeName(degree: number | null | undefined): string {
    if (degree === null || degree === undefined) return "-";
    return HighestDegreeTypeMap[degree as HighestDegreeType] || "-";
}

// 获取职称级别名称
function getProfessionalRankName(value: ProfessionalRankType | null | undefined): string {
    if (value === null || value === undefined) return "-";
    return ProfessionalRankTypeMap[value] || String(value);
}

// 获取职称职务名称
function getProfessionalTitleName(value: ProfessionalTitleType | null | undefined): string {
    if (value === null || value === undefined) return "-";
    return ProfessionalTitleTypeMap[value] || String(value);
}

// 职业资格证书级别转换函数
function getVocationalQualificationLevelName(value: VocationalQualificationLevelType | null | undefined): string {
    if (value === null || value === undefined) return "-";
    return VocationalQualificationLevelTypeMap[value] || String(value);
}

// ========================= 基本信息抽屉相关 =========================

const basicInfoDialogVisible = ref(false);
const basicInfoFormRef = ref<FormInstance>();
const basicInfoForm = reactive<UmsPerson1Res & { birthPlaceValue: any[]; idCard: string; position: any }>({
    id: null,
    employeeId: "", // 教职工号
    employeeType: null, // 员工类型
    gender: null, // 性别
    ethnicity: null, // 民族
    political: null, // 政治面貌
    birthPlaceValue: [] as any[], // 出生地级联值
    birthPlace: "", // 出生地(存储用)
    birthDate: "", // 出生日期
    idCard: "", // 身份证号
    arriveDate: "", // 来校时间
    deptId: null, // 所属部门
    position: "", // 岗位
});

// 重置表单
const resetBasicInfoForm = () => {
    basicInfoForm.id = null;
    basicInfoForm.employeeId = "";
    basicInfoForm.employeeType = null;
    basicInfoForm.gender = null;
    basicInfoForm.ethnicity = null;
    basicInfoForm.political = null;
    basicInfoForm.birthPlaceValue = [];
    basicInfoForm.birthPlace = "";
    basicInfoForm.birthDate = "";
    basicInfoForm.idCard = "";
    basicInfoForm.arriveDate = "";
    basicInfoForm.deptId = null;
    basicInfoForm.position = "";
    basicInfoForm.avatar = "";
    basicInfoForm.employeeName = "";
};

// 监听级联选择器变化
watch(
    () => basicInfoForm.birthPlaceValue,
    (val) => {
        if (val && val.length > 0) {
            // 将级联选择器的值转换为字符串，例如：北京市/市辖区/朝阳区
            basicInfoForm.birthPlace = val.join("/");
        } else {
            basicInfoForm.birthPlace = "";
        }
    }
);

const basicInfoRules = reactive<FormRules>({
    employeeName: [{ required: true, message: "请输入姓名", trigger: "blur" }],
    employeeId: [{ required: true, message: "请输入教职工号", trigger: "blur" }],
    employeeType: [{ required: true, message: "请选择员工类型", trigger: "blur" }],
    gender: [{ required: true, message: "请选择性别", trigger: "blur" }],
    ethnicity: [{ required: true, message: "请选择民族", trigger: "blur" }],
    // idCard: [{ required: true, message: "请输入身份证号", trigger: "blur" }],
    deptId: [{ required: true, message: "请选择所属部门", trigger: "blur" }],
    position: [{ required: true, message: "请选择岗位", trigger: "blur" }],
});

// 打开编辑基本信息弹窗
const openEditBasicInfoDialog = (isAdding: boolean = false) => {
    // 清除之前的临时预览URL
    if (avatarPreviewUrl.value) {
        URL.revokeObjectURL(avatarPreviewUrl.value);
        avatarPreviewUrl.value = "";
    }
    // 如果是新增人员，则清空表单
    if (isAdding) {
        resetBasicInfoForm();
    } else {
        // 如果是编辑人员，则填充表单
        const item = props.selectedItem;
        basicInfoForm.id = item?.id;
        basicInfoForm.avatar = item?.avatar || "";
        basicInfoForm.employeeName = item?.employeeName || "";
        basicInfoForm.employeeId = item?.employeeId || "";
        basicInfoForm.employeeType = item?.employeeType;
        basicInfoForm.gender = item?.gender;
        basicInfoForm.ethnicity = item?.ethnicity;
        basicInfoForm.political = item?.political;
        // 处理出生地级联选择器的初始值
        if (item?.birthPlace) {
            basicInfoForm.birthPlaceValue = item.birthPlace.split("/");
            basicInfoForm.birthPlace = item.birthPlace;
        } else {
            basicInfoForm.birthPlaceValue = [];
            basicInfoForm.birthPlace = "";
        }
        basicInfoForm.birthDate = item?.birthDate || "";
        basicInfoForm.idCard = personSensitiveInfo.value?.idCard || "";
        basicInfoForm.arriveDate = item?.arriveDate || "";
        basicInfoForm.deptId = item?.deptId;
        const positionIds = item?.position?.split(",").map(Number);
        basicInfoForm.position = positionIds;
    }
    basicInfoDialogVisible.value = true;
};

// 暴露打开编辑基本信息弹窗的方法
defineExpose({ openEditBasicInfoDialog });

// ========================= 上传头像相关 =========================
const avatarFile = ref<File | null>(null);
const avatarPreviewUrl = ref<string>("");

// 处理头像变更
const handleAvatarChange: UploadProps["onChange"] = (uploadFile) => {
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (uploadFile.raw.size > maxSize) {
        ElMessage.error("头像文件大小不能超过5MB");
        return;
    }

    if (!["image/jpeg", "image/png", "image/gif", "image/webp"].includes(uploadFile.raw.type)) {
        ElMessage.error("请上传图片格式文件");
        return;
    }

    avatarFile.value = uploadFile.raw;

    // 创建临时预览URL
    if (avatarPreviewUrl.value) {
        URL.revokeObjectURL(avatarPreviewUrl.value);
    }
    avatarPreviewUrl.value = URL.createObjectURL(uploadFile.raw);
};

// 上传头像并获取URL
const uploadAvatar = (): Promise<string> => {
    if (!avatarFile.value) {
        return Promise.resolve(basicInfoForm.avatar || "");
    }

    const formData = new FormData();
    formData.append("files", avatarFile.value);

    return minioUploads({
        body: formData as any,
        options: {
            headers: {
                "Content-Type": "multipart/form-data",
            },
        },
    })
        .then((res) => {
            if (res.data && res.data.length > 0) {
                return res.data[0].url;
            }
            return basicInfoForm.avatar || "";
        })
        .catch((error) => {
            console.error("头像上传失败:", error);
            ElMessage.error("头像上传失败");
            return basicInfoForm.avatar || "";
        });
};

// 提交基本信息表单
const submitBasicInfoForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    try {
        await formEl.validate();
        const loading = ElLoading.service({
            lock: true,
            text: "正在保存数据，请稍后...",
            background: "rgba(237, 243, 255, 0.7)",
        });

        // 处理头像上传
        const processForm = async () => {
            // 如果有头像文件需要上传
            if (avatarFile.value) {
                try {
                    const avatarUrl = await uploadAvatar();
                    basicInfoForm.avatar = avatarUrl;
                    // 清除临时预览URL
                    if (avatarPreviewUrl.value) {
                        URL.revokeObjectURL(avatarPreviewUrl.value);
                        avatarPreviewUrl.value = "";
                    }
                    return await submitFormData();
                } catch (error) {
                    console.error("头像上传失败:", error);
                    throw error;
                }
            } else {
                return await submitFormData();
            }
        };

        // 提交表单数据
        const submitFormData = async () => {
            // 构造请求数据
            const formData = {
                id: basicInfoForm.id,
                employeeId: basicInfoForm.employeeId,
                employeeType: basicInfoForm.employeeType,
                gender: basicInfoForm.gender,
                ethnicity: basicInfoForm.ethnicity,
                political: basicInfoForm.political,
                birthPlace: basicInfoForm.birthPlace,
                birthDate: basicInfoForm.birthDate,
                arriveDate: basicInfoForm.arriveDate,
                deptId: basicInfoForm.deptId,
                position: basicInfoForm.position.join(","),
                avatar: basicInfoForm.avatar,
                idCard: basicInfoForm.idCard,
                employeeName: basicInfoForm.employeeName,
            };

            if (basicInfoForm.id) {
                // 更新人员信息
                return await umsPersonUpdate({
                    body: formData,
                });
            } else {
                // 添加人员并注册
                return await umsPersonCreate({
                    body: formData as any,
                });
            }
        };

        try {
            // 执行流程
            const res = await processForm();
            if (res.code === 200) {
                ElMessage.success("保存成功");
                // 如果是新增人员，需要刷新整个列表；如果是编辑现有人员，只需要刷新详情
                if (basicInfoForm.id) {
                    emit("refresh");
                } else {
                    emit("refreshAll");
                }
            }
        } catch (error) {
            console.error("保存失败:", error);
            ElMessage.error("保存失败");
        } finally {
            loading.close();
        }
    } catch (error) {
        console.error("表单验证失败:", error);
    }
};

// ========================= 文件上传相关 =========================
// 文件上传限制
const fileLimit = 10;
// 共享的文件列表变量 - 所有弹窗共用
const fileList = ref<UploadUserFile[]>([]);

// 处理文件变更
const handleFileChange: UploadProps["onChange"] = (uploadFile) => {
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (uploadFile.raw.size > maxSize) {
        ElMessage.error("文件大小不能超过100MB，文件名为：" + uploadFile.raw.name);
        // 从文件列表中移除该文件
        const index = fileList.value.indexOf(uploadFile);
        if (index > -1) {
            fileList.value.splice(index, 1);
        }
    }
};

// ========================= 职称弹窗相关 =========================
const titleDialogVisible = ref(false);
const titleFormRef = ref<FormInstance>();
const titleForm = reactive({
    id: null,
    personId: null,
    level: null, // 级别
    post: null, // 职务
    major: "", // 专业
    issueDate: "", // 发证时间
    certificateNumber: "", // 证书编号
    certificateAuthority: "", // 发证机构
    supportingMaterialsIds: "", // 佐证材料s
    certificateType: 1, // 证书类型：1-职称
});

const titleRules = reactive<FormRules>({
    level: [{ required: true, message: "请输入级别", trigger: "blur" }],
    // post: [{ required: true, message: "请输入职务", trigger: "blur" }],
    // major: [{ required: true, message: "请输入专业", trigger: "blur" }],
    // issueDate: [{ required: true, message: "请选择发证时间", trigger: "change" }],
    certificateNumber: [{ required: true, message: "请输入证书编号", trigger: "blur" }],
    // certificateAuthority: [{ required: true, message: "请输入发证机构", trigger: "blur" }],
});

// ========================= 职业资格证书弹窗相关 =========================
const certificateDialogVisible = ref(false);
const certificateFormRef = ref<FormInstance>();
const certificateForm = reactive({
    id: null,
    personId: null,
    name: "", // 职业资格证名称
    level: null, // 级别
    issueDate: "", // 发证时间
    certificateNumber: "", // 证书编号
    certificateAuthority: "", // 发证机构
    supportingMaterialsIds: "", // 佐证材料s
    certificateType: 0, // 证书类型：0-资格证书
});

const certificateRules = reactive<FormRules>({
    name: [{ required: true, message: "请输入职业资格证名称", trigger: "blur" }],
    // level: [{ required: true, message: "请输入级别", trigger: "blur" }],
    // issueDate: [{ required: true, message: "请选择发证时间", trigger: "change" }],
    certificateNumber: [{ required: true, message: "请输入证书编号", trigger: "blur" }],
    // certificateAuthority: [{ required: true, message: "请输入发证机构", trigger: "blur" }],
});

// ========================= 教师资格证书弹窗相关 =========================
const teacherCertDialogVisible = ref(false);
const teacherCertFormRef = ref<FormInstance>();
const teacherCertForm = reactive({
    id: null,
    personId: null,
    kind: "", // 资格种类
    issueDate: "", // 发证时间
    major: "", // 专业
    certificateNumber: "", // 证书编号
    certificateAuthority: "", // 发证机构
    supportingMaterialsIds: "", // 佐证材料s
    certificateType: 2, // 证书类型：2-教师资格证
});

const teacherCertRules = reactive<FormRules>({
    kind: [{ required: true, message: "请输入资格种类", trigger: "blur" }],
    // issueDate: [{ required: true, message: "请选择发证时间", trigger: "change" }],
    // major: [{ required: true, message: "请输入专业", trigger: "blur" }],
    certificateNumber: [{ required: true, message: "请输入证书编号", trigger: "blur" }],
    // certificateAuthority: [{ required: true, message: "请输入发证机构", trigger: "blur" }],
});

// ========================= 学术/社会组织兼职弹窗相关 =========================
const academicRoleDialogVisible = ref(false);
const academicRoleFormRef = ref<FormInstance>();
const academicRoleForm = reactive({
    id: null,
    personId: null,
    orgName: "", // 学术/社会组织名称
    orgLevel: null, // 组织级别
    position: "", // 担任职务
    startDate: "", // 聘任开始时间
    endDate: "", // 聘任结束时间
    supportingMaterialsIds: "", // 佐证材料s
});

const academicRoleRules = reactive<FormRules>({
    orgName: [{ required: true, message: "请输入组织名称", trigger: "blur" }],
    orgLevel: [{ required: true, message: "请输入组织级别", trigger: "blur" }],
    position: [{ required: true, message: "请输入担任职务", trigger: "blur" }],
    // startDate: [{ required: true, message: "请选择聘任开始时间", trigger: "change" }],
    // endDate: [{ required: false, message: "请选择聘任结束时间", trigger: "change" }],
});

// ========================= 学历信息弹窗相关 =========================
const educationDialogVisible = ref(false);
const educationFormRef = ref<FormInstance>();
const educationForm = reactive({
    id: null,
    highestEducation: null,
    graduationSchool: "",
    graduationSchoolMajor: "",
    highestDegree: null,
    degreeSchool: "",
    highestMajor: "",
});

const educationRules = reactive<FormRules>({
    highestEducation: [{ required: true, message: "请选择最高学历", trigger: "change" }],
    graduationSchool: [{ required: true, message: "请输入获得学历的机构或院校", trigger: "blur" }],
    highestDegree: [{ required: true, message: "请选择最高学位", trigger: "change" }],
    degreeSchool: [{ required: true, message: "请输入获得学位的机构或院校", trigger: "blur" }],
});

// ========================= 弹窗方法 =========================

// 打开新增职称弹窗
const openAddTitleDialog = () => {
    titleForm.id = null;
    titleForm.personId = props.selectedItem?.id;
    titleForm.level = "";
    titleForm.post = "";
    titleForm.major = "";
    titleForm.issueDate = "";
    titleForm.certificateNumber = "";
    titleForm.certificateAuthority = "";
    titleForm.supportingMaterialsIds = "";
    fileList.value = []; // 清空共享文件列表
    titleDialogVisible.value = true;
};

// 打开编辑职称弹窗
const openEditTitleDialog = (row: any) => {
    Object.assign(titleForm, row);
    titleForm.personId = props.selectedItem?.id;
    // 清空文件列表
    fileList.value = [];
    // 如果有佐证材料，进行文件回显
    if (row.supportingMaterialsIds) {
        handleSupportingMaterials(row.supportingMaterialsIds);
    }
    titleDialogVisible.value = true;
};

// 提交职称表单
const submitTitleForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    await formEl.validate(async (valid) => {
        if (valid) {
            try {
                // 创建loading实例
                const loading = ElLoading.service({
                    lock: true,
                    text: "正在上传文件并保存数据，请稍后...",
                    background: "rgba(237, 243, 255, 0.7)",
                });

                // 只上传当前文件列表中的文件
                titleForm.supportingMaterialsIds = await uploadFiles(fileList.value);

                const res = await umsPersonSaveAndUpdateCertificates({
                    body: titleForm,
                });

                loading.close();

                if (res.code === 200) {
                    ElMessage.success("保存成功");
                    titleDialogVisible.value = false;
                    formEl.resetFields(); // 重置表单
                    fileList.value = []; // 清空文件列表
                    // 重新获取人员信息
                    emit("refresh");
                }
            } catch (error) {
                console.error("保存失败:", error);
                ElMessage.error("保存失败");
            }
        }
    });
};

// 打开新增职业资格证书弹窗
const openAddCertificateDialog = () => {
    certificateForm.id = null;
    certificateForm.personId = props.selectedItem?.id;
    certificateForm.name = "";
    certificateForm.level = null;
    certificateForm.issueDate = "";
    certificateForm.certificateNumber = "";
    certificateForm.certificateAuthority = "";
    certificateForm.supportingMaterialsIds = "";
    fileList.value = []; // 清空共享文件列表
    certificateDialogVisible.value = true;
};

// 打开编辑职业资格证书弹窗
const openEditCertificateDialog = (row: any) => {
    Object.assign(certificateForm, row);
    certificateForm.personId = props.selectedItem?.id;
    // 清空文件列表
    fileList.value = [];
    // 如果有佐证材料，进行文件回显
    if (row.supportingMaterialsIds) {
        handleSupportingMaterials(row.supportingMaterialsIds);
    }
    certificateDialogVisible.value = true;
};

// 提交职业资格证书表单
const submitCertificateForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    await formEl.validate(async (valid) => {
        if (valid) {
            try {
                // 创建loading实例
                const loading = ElLoading.service({
                    lock: true,
                    text: "正在上传文件并保存数据，请稍后...",
                    background: "rgba(237, 243, 255, 0.7)",
                });

                // 只上传当前文件列表中的文件
                certificateForm.supportingMaterialsIds = await uploadFiles(fileList.value);

                // @ts-ignore 忽略类型错误
                const res = await umsPersonSaveAndUpdateCertificates({
                    body: certificateForm,
                });

                loading.close();

                if (res.code === 200) {
                    ElMessage.success("保存成功");
                    certificateDialogVisible.value = false;
                    formEl.resetFields(); // 重置表单
                    fileList.value = []; // 清空文件列表
                    // 重新获取人员信息
                    emit("refresh");
                }
            } catch (error) {
                console.error("保存失败:", error);
                ElMessage.error("保存失败");
            }
        }
    });
};

// 打开新增教师资格证书弹窗
const openAddTeacherCertDialog = () => {
    teacherCertForm.id = null;
    teacherCertForm.personId = props.selectedItem?.id;
    teacherCertForm.kind = "";
    teacherCertForm.issueDate = "";
    teacherCertForm.major = "";
    teacherCertForm.certificateNumber = "";
    teacherCertForm.certificateAuthority = "";
    teacherCertForm.supportingMaterialsIds = "";
    fileList.value = []; // 清空共享文件列表
    teacherCertDialogVisible.value = true;
};

// 打开编辑教师资格证书弹窗
const openEditTeacherCertDialog = (row: any) => {
    Object.assign(teacherCertForm, row);
    teacherCertForm.personId = props.selectedItem?.id;
    // 清空文件列表
    fileList.value = [];
    // 如果有佐证材料，进行文件回显
    if (row.supportingMaterialsIds) {
        handleSupportingMaterials(row.supportingMaterialsIds);
    }
    teacherCertDialogVisible.value = true;
};

// 提交教师资格证书表单
const submitTeacherCertForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    await formEl.validate(async (valid) => {
        if (valid) {
            try {
                // 创建loading实例
                const loading = ElLoading.service({
                    lock: true,
                    text: "正在上传文件并保存数据，请稍后...",
                    background: "rgba(237, 243, 255, 0.7)",
                });

                // 只上传当前文件列表中的文件
                teacherCertForm.supportingMaterialsIds = await uploadFiles(fileList.value);

                const res = await umsPersonSaveAndUpdateCertificates({
                    body: teacherCertForm,
                });

                loading.close();

                if (res.code === 200) {
                    ElMessage.success("保存成功");
                    teacherCertDialogVisible.value = false;
                    formEl.resetFields(); // 重置表单
                    fileList.value = []; // 清空文件列表
                    // 重新获取人员信息
                    emit("refresh");
                }
            } catch (error) {
                console.error("保存失败:", error);
                ElMessage.error("保存失败");
            }
        }
    });
};

// 打开新增学术/社会组织兼职弹窗
const openAddAcademicRoleDialog = () => {
    academicRoleForm.id = null;
    academicRoleForm.personId = props.selectedItem?.id;
    academicRoleForm.orgName = "";
    academicRoleForm.orgLevel = "";
    academicRoleForm.position = "";
    academicRoleForm.startDate = "";
    academicRoleForm.endDate = "";
    academicRoleForm.supportingMaterialsIds = "";
    fileList.value = []; // 清空共享文件列表
    academicRoleDialogVisible.value = true;
};

// 打开编辑学术/社会组织兼职弹窗
const openEditAcademicRoleDialog = (row: any) => {
    Object.assign(academicRoleForm, row);
    academicRoleForm.personId = props.selectedItem?.id;
    // 清空文件列表
    fileList.value = [];
    // 如果有佐证材料，进行文件回显
    if (row.supportingMaterialsIds) {
        handleSupportingMaterials(row.supportingMaterialsIds);
    }
    academicRoleDialogVisible.value = true;
};

// 提交学术/社会组织兼职表单
const submitAcademicRoleForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    await formEl.validate(async (valid) => {
        if (valid) {
            try {
                // 创建loading实例
                const loading = ElLoading.service({
                    lock: true,
                    text: "正在上传文件并保存数据，请稍后...",
                    background: "rgba(237, 243, 255, 0.7)",
                });

                // 只上传当前文件列表中的文件
                academicRoleForm.supportingMaterialsIds = await uploadFiles(fileList.value);

                const res = await umsPersonSaveAndUpdateAcademicRoles({
                    body: academicRoleForm,
                });

                loading.close();

                if (res.code === 200) {
                    ElMessage.success("保存成功");
                    academicRoleDialogVisible.value = false;
                    formEl.resetFields(); // 重置表单
                    fileList.value = []; // 清空文件列表
                    // 重新获取人员信息
                    emit("refresh");
                }
            } catch (error) {
                console.error("保存失败:", error);
                ElMessage.error("保存失败");
            }
        }
    });
};

// 删除职称
const deleteTitle = (id: number) => {
    ElMessageBox.confirm("确认删除此职称记录？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    }).then(async () => {
        try {
            const res = await umsPersonDeleteCertificates({
                params: {
                    id,
                },
            });
            if (res.code === 200) {
                ElMessage.success("删除成功");
                // 刷新数据
                emit("refresh");
            }
        } catch (error) {
            ElMessage.error("删除失败");
        }
    });
};

// 删除职业资格证书
const deleteCertificate = (id: number) => {
    ElMessageBox.confirm("确认删除此职业资格证书记录？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    }).then(async () => {
        try {
            const res = await umsPersonDeleteCertificates({
                params: {
                    id,
                },
            });
            if (res.code === 200) {
                ElMessage.success("删除成功");
                // 刷新数据
                emit("refresh");
            }
        } catch (error) {
            ElMessage.error("删除失败");
        }
    });
};

// 删除教师资格证书
const deleteTeacherCert = (id: number) => {
    ElMessageBox.confirm("确认删除此教师资格证书记录？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    }).then(async () => {
        try {
            const res = await umsPersonDeleteCertificates({
                params: {
                    id,
                },
            });
            if (res.code === 200) {
                ElMessage.success("删除成功");
                // 刷新数据
                emit("refresh");
            }
        } catch (error) {
            ElMessage.error("删除失败");
        }
    });
};

// 删除学术/社会组织兼职
const deleteAcademicRole = (id: number) => {
    ElMessageBox.confirm("确认删除此学术/社会组织兼职记录？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    }).then(async () => {
        try {
            const res = await umsPersonDeleteAcademicRoles({
                params: {
                    id,
                },
            });
            if (res.code === 200) {
                ElMessage.success("删除成功");
                // 刷新数据
                emit("refresh");
            }
        } catch (error) {
            ElMessage.error("删除失败");
        }
    });
};

// 打开编辑学历信息弹窗
const openEditEducationDialog = () => {
    const item = props.selectedItem;
    educationForm.id = item?.id;
    educationForm.highestEducation = item?.highestEducation;
    educationForm.graduationSchool = item?.graduationSchool || "";
    educationForm.graduationSchoolMajor = item?.graduationSchoolMajor || "";
    educationForm.highestDegree = item?.highestDegree;
    educationForm.degreeSchool = item?.degreeSchool || "";
    educationForm.highestMajor = item?.highestMajor || "";
    educationDialogVisible.value = true;
};

// 提交学历信息表单
const submitEducationForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.validate((valid) => {
        if (valid) {
            const loading = ElLoading.service({
                lock: true,
                text: "正在保存数据，请稍后...",
                background: "rgba(237, 243, 255, 0.7)",
            });

            // 构造请求数据
            const formData = {
                id: educationForm.id,
                highestEducation: educationForm.highestEducation,
                graduationSchool: educationForm.graduationSchool,
                graduationSchoolMajor: educationForm.graduationSchoolMajor,
                highestDegree: educationForm.highestDegree,
                degreeSchool: educationForm.degreeSchool,
                highestMajor: educationForm.highestMajor,
            };

            // 更新人员信息
            umsPersonUpdate({
                body: formData,
            })
                .then((res) => {
                    if (res.code === 200) {
                        ElMessage.success("保存成功");
                        educationDialogVisible.value = false;
                        emit("refresh");
                    }
                })
                .catch((error) => {
                    console.error("保存失败:", error);
                    ElMessage.error("保存失败");
                })
                .finally(() => {
                    loading.close();
                });
        }
    });
};

// ========================= 是否双师直接切换相关 =========================
// 更新双师状态
const updateDualTeacherStatus = async (value: string | number | boolean) => {
    const item = props.selectedItem;
    if (!item?.id) {
        ElMessage.error("人员信息不完整");
        return;
    }

    const loading = ElLoading.service({
        lock: true,
        text: "正在保存数据，请稍后...",
        background: "rgba(237, 243, 255, 0.7)",
    });

    // 确保 value 为数字类型
    const isDualTeacher = typeof value === "number" ? value : Number(value);

    // 构造请求数据
    const formData = {
        id: item.id,
        isDualTeacher,
    };

    try {
        const res = await umsPersonUpdate({
            body: formData,
        });

        if (res.code === 200) {
            ElMessage.success("保存成功");
            emit("refreshAll");
        }
    } catch (error) {
        console.error("保存失败:", error);
        ElMessage.error("保存失败");
        // 恢复原来的值
        if (item) {
            item.isDualTeacher = isDualTeacher === 1 ? 0 : 1;
        }
    } finally {
        loading.close();
    }
};

// 递归查找部门名称
function findDepartmentNameById(id: number): string {
    // 递归查找函数
    function findDeptRecursively(departments: any[], targetId: number): string | null {
        // 遍历当前层级的部门
        for (const dept of departments) {
            // 如果找到匹配的ID，直接返回部门名称
            if (dept.id === targetId) {
                return dept.name;
            }

            // 如果有子部门，递归查找
            if (dept.children && dept.children.length > 0) {
                const found = findDeptRecursively(dept.children, targetId);
                // 如果在子部门中找到，返回结果
                if (found) {
                    return found;
                }
            }
        }
        // 未找到匹配的部门
        return null;
    }

    // 调用递归查找函数
    const result = findDeptRecursively(orgOptions.value, id);
    return result || "-";
}
</script>

<style scoped lang="scss">
:deep(.table-header) {
    th {
        background-color: #f6f6f6 !important;
        color: rgba(0, 0, 0, 0.88);
        font-weight: 500;
    }
}
// 头像上传样式
.avatar-uploader {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);

    &:hover {
        border-color: var(--el-color-primary);
    }
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar {
    width: 100px;
    height: 100px;
    display: block;
    object-fit: cover;
}

// 修复上传文件列表中文件名对齐问题
:deep(.el-upload-list__item-name) {
    text-align: left;
}

.bold {
    font-weight: 700;
}

.three-column {
    flex: 0 0 33.33%;
}

.four-column {
    flex: 0 0 25%;
}

.space-between {
    justify-content: space-between;
}

.per-title {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    padding-left: 8px;
    position: relative;
    display: flex;
    align-items: center;
    gap: 4px;

    &::before {
        content: "";
        position: absolute;
        left: 0;
        width: 3px; // 左侧线条宽度
        height: 20px;
        background-color: #1677ff;
    }
}

.detail-content {
    overflow-y: auto;
    padding: 24px;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 24px;
    .person-name {
        color: #23346d;
        font-size: 24px;
        font-weight: 700;
        line-height: 34px;
        letter-spacing: 2.4px;
    }

    .main {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .each-box {
            display: flex;
            flex-direction: column;
            gap: 16px;

            .info-box {
                display: flex;
                flex-wrap: wrap;
                .info-item {
                    box-sizing: border-box; /* 包含padding和border */
                    margin: 5px 0;
                }
            }
        }
    }
}

// 文件上传相关样式
.material-upload {
    width: 100%;

    :deep(.el-upload-list) {
        max-height: 200px;
        overflow-y: auto;
        margin-top: 10px;
    }

    :deep(.el-upload-list__item) {
        transition: all 0.3s;

        &:hover {
            background-color: #f5f7fa;
        }
    }

    :deep(.el-upload-list__item-name) {
        color: #606266;
        display: inline-block;
        max-width: 350px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
    }

    :deep(.el-upload__tip) {
        color: #909399;
        margin-top: 7px;
        font-size: 12px;
        line-height: 1.4;
    }
}
</style>
