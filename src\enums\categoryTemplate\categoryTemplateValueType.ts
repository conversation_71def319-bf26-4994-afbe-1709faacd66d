import ISN from "@/assets/cascaderPics/ISN.png";
import LEVEL from "@/assets/cascaderPics/LEVEL.png";
import PROJECT_STATUS from "@/assets/cascaderPics/PROJECT_STATUS.png";
import ENUM_SINGLE from "@/assets/cascaderPics/ENUM_SINGLE.png";
import ENUM_MULTI from "@/assets/cascaderPics/ENUM_MULTI.png";
import FILE from "@/assets/cascaderPics/FILE.png";

/**
 * 大类字段type对应类型
 * - 定义字段类型枚举和对应的中文描述映射
 */
export enum categoryTemplateValueType {
    /** 字符串 */
    STRING = 0,
    /** 整数 */
    INTEGER = 1,
    /** 浮点数 */
    DOUBLE = 2,
    /** 日期 */
    DATE = 3,
    /** 文件 */
    FILE = 4,
    /** 人员(单选) */
    PERSON_SINGLE = 6,
    /** 人员(多选) */
    PERSON_MULTI = 7,
    /** 字典(单选) */
    ENUM_SINGLE = 8,
    /** 字典(多选) */
    ENUM_MULTI = 9,
    /** 金额 */
    MONEY = 10,
    /** 是否 */
    ISN = 11,
    /** 依托专业 */
    DEPENDENT_MAJOR = 13,
    /** 级别 */
    LEVEL = 101,
}

/**
 * categoryTemplateValueType 的中文描述映射
 */
export const categoryTemplateValueTypeMap: Record<categoryTemplateValueType, string> = {
    [categoryTemplateValueType.STRING]: "文本",
    [categoryTemplateValueType.INTEGER]: "数字",
    [categoryTemplateValueType.DOUBLE]: "数字（带小数点）",
    [categoryTemplateValueType.DATE]: "日期",
    [categoryTemplateValueType.FILE]: "文件",
    [categoryTemplateValueType.PERSON_SINGLE]: "人员(单个)",
    [categoryTemplateValueType.PERSON_MULTI]: "人员(多选)",
    [categoryTemplateValueType.ENUM_SINGLE]: "自定义 - 仅单选",
    [categoryTemplateValueType.ENUM_MULTI]: "自定义 - 可多选",
    [categoryTemplateValueType.MONEY]: "金额",
    [categoryTemplateValueType.ISN]: "是否",
    [categoryTemplateValueType.DEPENDENT_MAJOR]: "依托专业 - 可多选",
    [categoryTemplateValueType.LEVEL]: "级别",
};

/**
 * 大类字段类型下拉框选项，提供给element-plus的cascader级联选择器使用
 */
export const categoryTemplateValueTypeOptions_Cascader = [
    {
        value: "op1",
        label: "基础数据项类型",
        children: [
            {
                value: categoryTemplateValueType.STRING,
                label: categoryTemplateValueTypeMap[categoryTemplateValueType.STRING],
            },
            {
                value: categoryTemplateValueType.DATE,
                label: categoryTemplateValueTypeMap[categoryTemplateValueType.DATE],
            },
            {
                value: categoryTemplateValueType.FILE,
                label: categoryTemplateValueTypeMap[categoryTemplateValueType.FILE],
            },
            {
                value: categoryTemplateValueType.ISN,
                label: categoryTemplateValueTypeMap[categoryTemplateValueType.ISN],
            },
        ],
    },
    {
        value: "op2",
        label: "数字数据项类型",
        children: [
            {
                value: categoryTemplateValueType.INTEGER,
                label: categoryTemplateValueTypeMap[categoryTemplateValueType.INTEGER],
            },
            {
                value: categoryTemplateValueType.DOUBLE,
                label: categoryTemplateValueTypeMap[categoryTemplateValueType.DOUBLE],
            },
            {
                value: categoryTemplateValueType.MONEY,
                label: categoryTemplateValueTypeMap[categoryTemplateValueType.MONEY],
            },
        ],
    },
    {
        value: "op3",
        label: "教职人员数据项类型",
        children: [
            {
                value: categoryTemplateValueType.PERSON_SINGLE,
                label: categoryTemplateValueTypeMap[categoryTemplateValueType.PERSON_SINGLE],
            },
            {
                value: categoryTemplateValueType.PERSON_MULTI,
                label: categoryTemplateValueTypeMap[categoryTemplateValueType.PERSON_MULTI],
            },
        ],
    },
    {
        value: "op4",
        label: "依托专业",
        children: [
            {
                value: categoryTemplateValueType.DEPENDENT_MAJOR,
                label: categoryTemplateValueTypeMap[categoryTemplateValueType.DEPENDENT_MAJOR],
            },
        ],
    },
    {
        value: "op5",
        label: "级别",
        children: [
            {
                value: categoryTemplateValueType.LEVEL,
                label: categoryTemplateValueTypeMap[categoryTemplateValueType.LEVEL],
            },
        ],
    },
    {
        value: "op6",
        label: "自定义下拉菜单",
        children: [
            {
                value: categoryTemplateValueType.ENUM_SINGLE,
                label: categoryTemplateValueTypeMap[categoryTemplateValueType.ENUM_SINGLE],
            },
            {
                value: categoryTemplateValueType.ENUM_MULTI,
                label: categoryTemplateValueTypeMap[categoryTemplateValueType.ENUM_MULTI],
            },
        ],
    },
];

// ------------------------------------------------------------------------

// 输入类
//PROJECT_NAME(110, "String", "String"), // 项目名称
//STRING(0, "String", "String"), // 普通文本
//INTEGER(1, "Double", "Long"), // 整数
//DOUBLE(2, "Double", "Double"), // 数字（带小数点）
//MONEY(10, "Double", "Double"), // 金额（万元）

// 选择类
//ISN(11, "String", "Integer"), // 是/否
//DEPENDENT_MAJOR(13, "String", "Long"), // 依托专业
//LEVEL(101, "String", "Long"), // 级别
//PROJECT_STATUS(111, "String", "Long"), // 项目状态
//ENUM_SINGLE(8, "String", "Long"), // 自定义单选
//ENUM_MULTI(9, "String", "Long"), // 自定义多选

// 人员类 - 当人是院外的为字符串，当是院内的为Long
//PROJECT_MAIN(112, "String", "String"), // 项目负责人
//PROJECT_PARTICIPATE(113, "String", "String"), // 参与人
//PERSON_SINGLE(6, "String", "String"), // 单选人员
//PERSON_MULTI(7, "String", "String"), // 多选人员/其他人员
//STUDENT(114, "String", "String"), // 学生

// 日期类
//PROJECT_DATE(115, "Date", "Date"), // 重要日期
//DATE(3, "Date", "Date"), // 一般日期
// 文件类
//FILE(4, "File", "String"), // 文件（文件）

/**
 * 新数据表字段类型枚举
 */
export enum categoryTemplateValueType_NEW {
    /** 项目名称 */
    PROJECT_NAME = 110,
    /** 普通文本 */
    STRING = 0,
    /** 整数 */
    INTEGER = 1,
    /** 数字（带小数点） */
    DOUBLE = 2,
    /** 金额（万元） */
    MONEY = 10,
    /** 是/否 */
    ISN = 11,
    /** 依托专业 */
    DEPENDENT_MAJOR = 13,
    /** 级别 */
    LEVEL = 101,
    /** 项目状态 */
    PROJECT_STATUS = 111,
    /** 自定义单选 */
    ENUM_SINGLE = 8,
    /** 自定义多选 */
    ENUM_MULTI = 9,
    /** 项目负责人 */
    PROJECT_MAIN = 112,
    /** 参与人 */
    PROJECT_PARTICIPATE = 113,
    /** 单选人员 */
    PERSON_SINGLE = 6,
    /** 多选人员/其他人员 */
    PERSON_MULTI = 7,
    /** 学生 */
    STUDENT = 114,
    /** 重要日期 */
    PROJECT_DATE = 115,
    /** 一般日期 */
    DATE = 3,
    /** 文件 */
    FILE = 4,
}

/**
 * categoryTemplateValueType_NEW 的中文描述映射
 */
export const categoryTemplateValueTypeMap_NEW: Record<categoryTemplateValueType_NEW, string> = {
    [categoryTemplateValueType_NEW.PROJECT_NAME]: "项目名称",
    [categoryTemplateValueType_NEW.STRING]: "普通文本",
    [categoryTemplateValueType_NEW.INTEGER]: "整数",
    [categoryTemplateValueType_NEW.DOUBLE]: "数字（带小数点）",
    [categoryTemplateValueType_NEW.MONEY]: "金额（万元）",
    [categoryTemplateValueType_NEW.ISN]: "是/否",
    [categoryTemplateValueType_NEW.DEPENDENT_MAJOR]: "依托专业",
    [categoryTemplateValueType_NEW.LEVEL]: "级别",
    [categoryTemplateValueType_NEW.PROJECT_STATUS]: "项目状态",
    [categoryTemplateValueType_NEW.ENUM_SINGLE]: "自定义单选",
    [categoryTemplateValueType_NEW.ENUM_MULTI]: "自定义多选",
    [categoryTemplateValueType_NEW.PROJECT_MAIN]: "项目负责人",
    [categoryTemplateValueType_NEW.PROJECT_PARTICIPATE]: "参与人",
    [categoryTemplateValueType_NEW.PERSON_SINGLE]: "单选人员",
    [categoryTemplateValueType_NEW.PERSON_MULTI]: "多选人员/其他人员",
    [categoryTemplateValueType_NEW.STUDENT]: "学生",
    [categoryTemplateValueType_NEW.PROJECT_DATE]: "重要日期",
    [categoryTemplateValueType_NEW.DATE]: "一般日期",
    [categoryTemplateValueType_NEW.FILE]: "文件",
};

/**
 * 新数据表字段类型下拉框选项（重构）
 */
export const categoryTemplateValueTypeOptions_Cascader_NEW = [
    {
        value: "op1",
        label: "输入类",
        children: [
            {
                value: "op1-1",
                label: "文本",
                children: [
                    {
                        value: categoryTemplateValueType_NEW.PROJECT_NAME,
                        label: categoryTemplateValueTypeMap_NEW[categoryTemplateValueType_NEW.PROJECT_NAME],
                        desc: `<div class='cascader-title'>用户自行输入的项目或奖项名称</div>
                        <div class='cascader-desc'>
                        <strong>常见示例</strong>
                        <div>       
                            常规项目/奖项名称<br>
                            教材建设，论文，专利名称<br>
                            实践基地建设
                        </div>
                        </div>
                        `,
                    },
                    {
                        value: categoryTemplateValueType_NEW.STRING,
                        label: categoryTemplateValueTypeMap_NEW[categoryTemplateValueType_NEW.STRING],
                        desc: `<div class='cascader-title'>用户自行输入的文本</div>
                        <div class='cascader-desc'>
                        用于输入文字信息，支持中英文、数字、符号等组合。<br><br>
                        <strong>常见示例</strong>
                        <div>        
                            项目名称<br>
                            编号（字母/数字/符号等组合
                        </div>
                        </div>
                        `,
                    },
                ],
            },
            {
                value: "op1-2",
                label: "数字",
                children: [
                    {
                        value: categoryTemplateValueType_NEW.INTEGER,
                        label: categoryTemplateValueTypeMap_NEW[categoryTemplateValueType_NEW.INTEGER],
                        desc: `<div class='cascader-title'>仅接受整数输入</div>
                        <div class='cascader-desc'>
                        仅接受整数输入，适用于不包含小数点的整数类型信息。<br><br>
                        <strong>常见示例</strong>
                        <div>      
                            参与人数<br>
                            排名<br>
                            字数
                        </div>
                        </div>
                        `,
                    },
                    {
                        value: categoryTemplateValueType_NEW.DOUBLE,
                        label: categoryTemplateValueTypeMap_NEW[categoryTemplateValueType_NEW.DOUBLE],
                        desc: `<div class='cascader-title'>接受带有小数点的数字</div>
                        <div class='cascader-desc'>
                        接受带有小数点的数字输入，适用于需要精确到小数点的信息。<br><br>
                        <strong>常见示例</strong>
                        <div>        
                            占比，百分比，比例等
                        </div>
                        </div>
                        `,
                    },
                ],
            },
            {
                value: categoryTemplateValueType_NEW.MONEY,
                label: categoryTemplateValueTypeMap_NEW[categoryTemplateValueType_NEW.MONEY],
                desc: `<div class='cascader-title'>金额的专用类型，单位为万元</div>
                <div class='cascader-desc'>
                区别于其他数字类型，该类型为所有金额数据的专属类型，单位为万元。<br><br>
                <strong>常见示例</strong>
                <div>        
                    各类金额<br>
                    各类经费
                </div>
                </div>
                `,
            },
        ],
    },
    {
        value: "op2",
        label: "选择类",
        children: [
            {
                value: categoryTemplateValueType_NEW.ISN,
                label: categoryTemplateValueTypeMap_NEW[categoryTemplateValueType_NEW.ISN],
                desc: `<div class='cascader-title'>是非选择选项</div>
                <div class='cascader-desc'>
                用于对某个问题或条件进行简单明确的确认或否定。<br>
                <div class='cascader-img'><img src='${ISN}'/></div>
                `,
            },
            {
                value: "op2-1",
                label: "下拉菜单",
                children: [
                    {
                        value: categoryTemplateValueType_NEW.DEPENDENT_MAJOR,
                        label: categoryTemplateValueTypeMap_NEW[categoryTemplateValueType_NEW.DEPENDENT_MAJOR],
                        desc: `<div class='cascader-title'>包含专业的下拉菜单</div>
                        <div class='cascader-desc'>
                        用于提供给录入者一个下拉菜单，录入者可以选择该成果或工作归属于哪个专业。<br><br>
                        <strong>常见示例</strong>
                        <div>        
                            各类成果<br>
                            各类奖项<br>
                            论文
                        </div>
                        </div>
                        `,
                    },
                    {
                        value: categoryTemplateValueType_NEW.LEVEL,
                        label: categoryTemplateValueTypeMap_NEW[categoryTemplateValueType_NEW.LEVEL],
                        desc: `<div class='cascader-title'>包含行政级别的下拉菜单</div>
                        <div class='cascader-desc'>
                        用于提供给录入者一个下拉菜单，录入者可以选择级别。您可以自由选择是否启用下拉菜单的内容，以满足不同场景的需求。<br>
                        <div class='cascader-img'><img src='${LEVEL}'/></div>
                        `,
                    },
                    {
                        value: categoryTemplateValueType_NEW.PROJECT_STATUS,
                        label: categoryTemplateValueTypeMap_NEW[categoryTemplateValueType_NEW.PROJECT_STATUS],
                        desc: `<div class='cascader-title'>可以自行编辑项目状态的单选下拉菜单</div>
                        <div class='cascader-desc'>
                        用于提供给录入者一个下拉菜单，录入者可以选择项目的状态。“项目状态”下拉菜单包含三种预设的常见的项目状态，您可以根据实际情况增加、删除、编辑该下拉菜单的内容和数量。<br>
                        <div class='cascader-img'><img src='${PROJECT_STATUS}'/></div>
                        `,
                    },
                    {
                        value: "op2-1-1",
                        label: "自定义下拉菜单",
                        children: [
                            {
                                value: categoryTemplateValueType_NEW.ENUM_SINGLE,
                                label: categoryTemplateValueTypeMap_NEW[categoryTemplateValueType_NEW.ENUM_SINGLE],
                                desc: `<div class='cascader-title'>可以自行编辑内容的单选下拉菜单</div>
                                <div class='cascader-desc'>
                                用于提供给录入者一个下拉菜单，录入者可以在您编辑的选项中进行单选。<br>
                                *注：如果您想要自定义项目状态相关的下拉菜单，请使用“下拉菜单”内预设的“项目状态”。<br>
                                <div class='cascader-img'><img src='${ENUM_SINGLE}'/></div>
                                `,
                            },
                            {
                                value: categoryTemplateValueType_NEW.ENUM_MULTI,
                                label: categoryTemplateValueTypeMap_NEW[categoryTemplateValueType_NEW.ENUM_MULTI],
                                desc: `<div class='cascader-title'>可以自行编辑内容的多选下拉菜单</div>
                                <div class='cascader-desc'>
                                用于提供给录入者一个下拉菜单，录入者可以在您编辑的选项中进行多选。<br>
                                <div class='cascader-img'><img src='${ENUM_MULTI}'/></div>
                                `,
                            },
                        ],
                    },
                ],
            },
        ],
    },
    {
        value: "op3",
        label: "人员类",
        children: [
            {
                value: categoryTemplateValueType_NEW.PROJECT_MAIN,
                label: categoryTemplateValueTypeMap_NEW[categoryTemplateValueType_NEW.PROJECT_MAIN],
                desc: `<div class='cascader-title'>该项目获奖项的负责人的姓名</div>
                <div class='cascader-desc'>
                该成果/工作的负责人。若该项成果/工作产生了绩效，项目负责人将会获得一定的绩效分数。<br><br>
                <strong>常见示例</strong>
                <div>        
                    获奖人<br>
                    主持人<br>
                    第一主编（教材建设）<br>
                    第一作者（论文）
                </div>
                </div>
                `,
            },
            {
                value: categoryTemplateValueType_NEW.PROJECT_PARTICIPATE,
                label: categoryTemplateValueTypeMap_NEW[categoryTemplateValueType_NEW.PROJECT_PARTICIPATE],
                desc: `<div class='cascader-title'>该项目获奖项的参与人的姓名</div>
                <div class='cascader-desc'>
                参与了该成果/工作的成员。若该项成果/工作产生了绩效，参与人将会获得一定的绩效分数。   <br>
                `,
            },
            {
                value: categoryTemplateValueType_NEW.STUDENT,
                label: categoryTemplateValueTypeMap_NEW[categoryTemplateValueType_NEW.STUDENT],
                desc: `<div class='cascader-title'>用户自行输入的学生姓名</div>
                <div class='cascader-desc'>
                支持用户填写任意学生姓名。<br>
                `,
            },
            {
                value: categoryTemplateValueType_NEW.PERSON_MULTI,
                label: "其他人员",
                desc: `<div class='cascader-title'>非项目负责人和参与人人员的姓名</div>
                <div class='cascader-desc'>
                区别于项目负责人与参与人，该类型仅用于一些不常见的人名信息填写。若数据表内存在不参与绩效计算，但是需要记录的人名，则可以选择该类型。录入者可以自行在填写时选择院内与院外。<br><br>
                <strong>常见示例</strong>
                <div>        
                    通讯作者<br>
                    来访企业家姓名<br>
                    兼职教师
                </div>
                </div>
                `,
            },
        ],
    },
    {
        value: "op4",
        label: "日期类",
        children: [
            {
                value: categoryTemplateValueType_NEW.PROJECT_DATE,
                label: categoryTemplateValueTypeMap_NEW[categoryTemplateValueType_NEW.PROJECT_DATE],
                desc: `<div class='cascader-title'>跟绩效和检索相关的唯一日期</div>
                <div class='cascader-desc'>
                在成果数据中，老师在“重要日期”数据项中填写的日期将用于绩效所属的周期，并且该时间可以被用户搜索。例如，日期为「2024-04-23」，则相关数据自动纳入2024年度绩效统计，并在2024年成果搜索结果中展示。<br>
                <br>
                在工作数据中，“重要日期”仅用作于确定绩效所属的周期，例如，日期为「2024-04-23」，则相关数据自动纳入2024年度绩效统计。<br>
                <br>
                每张数据表仅可设置一个重要日期。<br>
                <br>
                如果您不确定您的数据表中的时间应该选择“重要日期”还是“一般日期”，且您的数据表中只有一个时间数据项，您可以直接选择“重要日期”。 <br>
                </div>`,
            },
            {
                value: categoryTemplateValueType_NEW.DATE,
                label: categoryTemplateValueTypeMap_NEW[categoryTemplateValueType_NEW.DATE],
                desc: `<div class='cascader-title'>仅用于记录的常规日期</div>
                <div class='cascader-desc'>
                用于记录跟绩效所属周期无关的其他日期信息。<br>
                </div>`,
            },
        ],
    },
    {
        value: "op5",
        label: "文件类",
        children: [
            {
                value: categoryTemplateValueType_NEW.FILE,
                label: categoryTemplateValueTypeMap_NEW[categoryTemplateValueType_NEW.FILE],
                desc: `<div class='cascader-title'>允许用户进行文件上传</div>
                <div class='cascader-desc'>
                用于教师录入时上传各类佐证材料，支持常见文件格式。<br>
                <div class='cascader-img'><img src='${FILE}'/></div>
                `,
            },
        ],
    },
];
