<template>
    <div class="be-verified-team">
        <GeneralDataDisplay>
            <template #top>
                <div class="top">
                    <div>
                        <CustomSearch
                            v-model="searchKeyword"
                            placeholder="输入关键字检索待确认申请"
                            @search="handleSearch"
                            @clear="refreshAll" />
                    </div>
                </div>
            </template>

            <template #content>
                <div class="content">
                    <DataTable
                        :search-keyword="searchKeyword"
                        :is-local-search="false"
                        :is-load-first="isAutoLoadFirst"
                        ref="dataTableRef"
                        @itemClick="loadDetail"
                        :load-more-func="loadMoreFunc"
                        @load-complete="handleLoadComplete">
                        <!-- datatype:资料类型：0邀请，1资料申请 -->
                        <!-- type:资料类型：0立项资料，1建设成果 -->
                        <template #title="{ item }: { item: TmsProjectRequestDto }">
                            <div class="title">
                                {{
                                    item.dataType === 0
                                        ? "加入团队邀请"
                                        : item.type === 0
                                          ? "立项资料共享申请"
                                          : "建设成果共享申请"
                                }}
                            </div>
                        </template>
                        <template #desc="{ item }: { item: TmsProjectRequestDto }">
                            <div>邀请团队：{{ item.projectName }}</div>
                            <div>邀请人：{{ item.participantName }}</div>
                        </template>
                        <template #detail="{ selectedItem }">
                            <Detail :chosen-item="chosenItem" :selected-item="detailData" @refresh="refreshAll" />
                        </template>
                    </DataTable>
                </div>
            </template>
        </GeneralDataDisplay>
    </div>
</template>

<script setup lang="ts">
import GeneralDataDisplay from "@/components/GeneralDataDisplay/index.vue";
import DataTable from "@/components/DataTable/index.vue";
import Detail from "./detail.vue";
import CustomSearch from "@/components/CustomSearch/index.vue";
import { TmsProjectRequestDto } from "@/apis/types";
import { tmsProjectRequestGetSharedList } from "@/apis/tmsProjectRequestController";
import { tmsProjectSelectProjectById } from "@/apis/tmsProjectController";
import { useRoute } from "vue-router";

const dataTableRef = ref();

const searchKeyword = ref("");

const detailData = ref();

const chosenItem = ref<TmsProjectRequestDto>();

const route = useRoute();

// 处理搜索
const handleSearch = () => {
    detailData.value = null;
    chosenItem.value = null;
    if (dataTableRef.value) {
        dataTableRef.value.search();
    }
};

// 加载详情
const loadDetail = (item: TmsProjectRequestDto) => {
    // 如果当前选中项和之前选中项相同，则不重新加载
    if (item.id === detailData.value?.id) return;
    detailData.value = null; // 清空之前的详情数据
    chosenItem.value = null;
    chosenItem.value = item;
    tmsProjectSelectProjectById({ params: { id: item.projectId } }).then((res) => {
        detailData.value = res.data;
    });
};

// 刷新列表
const refreshAll = (id?: number) => {
    detailData.value = null;
    chosenItem.value = null;
    if (id) {
        tmsProjectSelectProjectById({ params: { id: id } }).then((res) => {
            detailData.value = res.data;
        });
    }
    if (dataTableRef.value) {
        dataTableRef.value.refreshAll();
    }
};

// 加载更多函数
const loadMoreFunc = (pageNum: number, pageSize: number, searchKeyword?: string) => {
    return tmsProjectRequestGetSharedList({
        params: {
            status: 1,
            pageNum,
            pageSize,
            keyword: searchKeyword.trim(),
        },
    });
};


const isAutoLoadFirst = computed(() => {
    return !(route.query.queryListItemId && route.query.queryIndex)
})

// type: 审核待办：0，录入待办-待补充：1，录入待办-审核退回：2，项目团队待办：3
// 数据加载完成后的处理
const handleLoadComplete = (data) => {
    const queryIndex = Number(route.query.queryIndex);

    // 如果有索引参数，模拟点击对应索引的项目
    if (queryIndex !== undefined && queryIndex !== null) {
        // 延迟一点执行，确保列表已经完全渲染
        nextTick(() => {
            dataTableRef.value.simulateClick(Number(queryIndex));
        });
    } 
};

</script>

<style scoped lang="scss">
.be-verified-team {
    .top {
        padding: 20px;
        background: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .content {
        .title {
            font-weight: bold;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
</style>
