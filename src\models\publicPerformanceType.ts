/**
 * 公共标识符枚举
 */
export enum publicPerformanceType {
    /** 项目负责人 */
    PROJECT_MAIN = 1,
    /** 项目名称 */
    PROJECT_NAME = 2,
    /** 项目状态 */
    PROJECT_STATUS = 4,
    /** 级别 */
    GRADE = 5,
    /** 时间 */
    DATE = 6,
    /** 参与人 */
    PROJECT_PARTICIPATE = 7,
}

/**
 * publicPerformanceType 的中文描述映射
 */
export const publicPerformanceTypeMap: Record<publicPerformanceType, string> = {
    [publicPerformanceType.PROJECT_MAIN]: '项目负责人',
    [publicPerformanceType.PROJECT_NAME]: '项目名称',
    [publicPerformanceType.PROJECT_STATUS]: '项目状态',
    [publicPerformanceType.GRADE]: '级别',
    [publicPerformanceType.DATE]: '时间', // 时间可以根据需要细化
    [publicPerformanceType.PROJECT_PARTICIPATE]: '参与人',
};
