/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 根据ID获取资源详情 GET /ums/umsResource/${param0} */
export async function umsResourceId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsResourceidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultUmsResource_>(`/ums/umsResource/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 添加后台资源 POST /ums/umsResource/create */
export async function umsResourceCreate({
  body,
  options,
}: {
  body: API.UmsResource_;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsResource/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据ID删除后台资源 POST /ums/umsResource/delete/${param0} */
export async function umsResourceDeleteId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsResourceDeleteidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultVoid_>(`/ums/umsResource/delete/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 分页模糊查询后台资源 GET /ums/umsResource/list */
export async function umsResourceList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsResourceListParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageUmsResource_>(
    '/ums/umsResource/list',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 查询所有后台资源 GET /ums/umsResource/listAll */
export async function umsResourceListAll({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListUmsResource_>('/ums/umsResource/listAll', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 修改后台资源 POST /ums/umsResource/update/${param0} */
export async function umsResourceUpdateId({
  params,
  body,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsResourceUpdateidParams;
  body: API.UmsResource_;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultVoid_>(`/ums/umsResource/update/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}
