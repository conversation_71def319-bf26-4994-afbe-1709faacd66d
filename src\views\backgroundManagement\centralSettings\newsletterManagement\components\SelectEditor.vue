<template>
    <div class="editor-top">
        <QuillEditor
            ref="myQuillEditor"
            class="myQuillEditor editor-quill"
            v-model:content="editorContent"
            :options="editorOption"
            @textChange="onEditorChange"
            @selection-change="onSelectionChange"
            @ready="onEditorReady"
            contentType="html"
            :enable="true"
            toolbar="" />
        <div class="editor-top-button"><slot name="editor-top-button"> </slot></div>
    </div>
</template>

<script>
import { ref, computed, onMounted, defineComponent, watch, onBeforeUnmount } from "vue";
import { QuillEditor } from "@vueup/vue-quill";
import "@vueup/vue-quill/dist/vue-quill.snow.css";
import SelfSelect from "./quill-select";
import Quill from "quill";

export default defineComponent({
    name: "SelectEditor",
    components: {
        QuillEditor,
    },
    props: {
        // 下拉框选项列表
        userList: {
            type: Array,
            default: () => [],
        },
        // 初始内容
        modelValue: {
            type: String,
            default: "",
        },
        // 显示工具栏
        showToolbar: {
            type: Boolean,
            default: true,
        },
    },
    emits: ["update:modelValue", "editor-ready", "content-change"],
    setup(props, { emit }) {
        // 编辑器内容
        const editorContent = ref("");
        const size = ref(0);
        const currentIndex = ref(0);
        const selectionsInfo = ref([]);
        const myQuillEditor = ref(null);
        const testInput = ref("");
        const placeholderText = ref("");

        // 监听props.modelValue的变化，自动更新编辑器内容
        watch(
            () => props.modelValue,
            (newValue) => {
                if (newValue !== placeholderText.value) {
                    testInput.value = newValue;
                    console.log("新的值" + testInput.value);
                    // restoreContent();
                }
            }
        );

        // 监听userList，更新Quill全局数据
        watch(
            () => props.userList,
            (newList) => {
                // 确保全局数据已正确设置
                if (!Quill.selectData) {
                    Quill.selectData = {};
                }
                Quill.selectData.userList = newList;
                
                console.log("用户列表已更新:", newList);
                
                // 如果已有内容，重新渲染下拉框
                if (testInput.value) {
                    setTimeout(() => {
                        restoreContent();
                    }, 0);
                }
            },
            { immediate: true }
        );

        const editorOption = ref({
            theme: "snow",
            modules: {
                toolbar: false,
                clipboard: {
                    matchVisual: false,
                    matchers: [], // 清空默认匹配器
                },
            },
            readOnly: false,
        });

        // 获取编辑器实例
        const editor = computed(() => {
            return myQuillEditor.value ? myQuillEditor.value.getQuill() : null;
        });

        // 注册自定义Blot
        const addSelectAttr = () => {
            // 使用全局标记检查是否已注册格式
            if (!window.QuillFormatsRegistered) {
                window.QuillFormatsRegistered = {};
            }
            
            if (window.QuillFormatsRegistered["selectSection"]) {
                console.log("SelectSection format already registered");
                return; // 已注册则直接返回
            }

            let BlockEmbed = Quill.import("blots/embed");
            class SelectBlot extends BlockEmbed {
                static create(value) {
                    let node = super.create("span");
                    node.className = "el-cascader";
                    node.setAttribute("value", value.value || "");
                    node.setAttribute("width", value.width || "100%");
                    node.setAttribute("type", value.type);
                    node.setAttribute("data-placeholder", `<specialBlock>${value.value}</specialBlock>`);

                    if (!value.type) {
                        console.error('数据类型"type"缺失');
                    }
                    
                    // 确保全局数据已初始化
                    if (!Quill.selectData) {
                        Quill.selectData = {};
                    }
                    
                    let list = Quill.selectData[`${value.type}List`];
                    value.list = list;
                    
                    if (!value.list) {
                        console.warn(`${value.type}List 为空，将延迟初始化下拉框`);
                        // 标记此节点需要延迟初始化，等待数据加载
                        node.setAttribute("data-needs-init", "true");
                        return node;
                    }
                    
                    // 避免重复初始化
                    if (!node.querySelector('.select-input-value')) {
                        new SelfSelect(node, value);
                    }
                    
                    return node;
                }
                
                static value(node) {
                    let type = node.getAttribute("type");
                    let list = Quill.selectData && Quill.selectData[`${type}List`];
                    return {
                        value: node.getAttribute("value"),
                        width: node.getAttribute("width"),
                        type,
                        list,
                        placeholder: node.getAttribute("data-placeholder"),
                    };
                }
            }
            SelectBlot.blotName = "selectSection";
            SelectBlot.tagName = "span";
            Quill.register(SelectBlot);

            // 标记格式已注册
            window.QuillFormatsRegistered["selectSection"] = true;
        };

        const insertSeclect = () => {
            seclectHandler();
        };

        const seclectHandler = (value) => {
            const quill = editor.value;
            if (!quill) return;

            // 获取选中位置
            let cursorPosition = 0;
            const selection = quill.getSelection();

            if (selection) {
                cursorPosition = selection.index;
            } else {
                cursorPosition = currentIndex.value;
            }

            // 防止编辑器自动添加p标签导致换行
            const currentFormat = quill.getFormat(cursorPosition);

            // 插入自定义组件
            quill.insertEmbed(cursorPosition, "selectSection", {
                type: "user",
                value: value || "",
            });

            // 恢复原来的格式，防止格式变化
            quill.formatText(cursorPosition, 1, currentFormat);

            // 设置光标位置
            quill.setSelection(cursorPosition + 1);
            currentIndex.value = cursorPosition + 1;
        };

        const onSelectionChange = ({ range, oldRange, source }) => {
            if (!range) {
                onEditorBlur();
            } else {
                onEditorFocus();
                if (range) {
                    currentIndex.value = range.index;
                }
            }
        };

        const onEditorBlur = () => {
            // 失焦逻辑
        };

        const onEditorFocus = () => {
            // 聚焦逻辑
        };

        const onEditorChange = ({ html, text, delta, oldDelta, source }) => {
            // 保存完整的html内容
            console.log(editorContent.value, "editorContent.value");
            editorContent.value = html;

            // 获取所有下拉框的选中值和位置信息
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, "text/html");
            const cascaders = doc.querySelectorAll(".el-cascader");

            // 保存下拉框信息
            const selectionsInfo1 = Array.from(cascaders).map((cascader) => {
                return {
                    value: cascader.getAttribute("value"),
                    type: cascader.getAttribute("type"),
                    text: cascader.querySelector(".input-inner")?.textContent || "",
                };
            });

            selectionsInfo.value = selectionsInfo1;

            // 获取标识符文本并发送更新
            placeholderText.value = getTextWithPlaceholders();
            
            // 记录处理后的文本内容用于调试
            console.log("处理后的文本内容:", placeholderText.value);

            // 发送事件通知父组件内容已更新
            emit("update:modelValue", placeholderText.value);
            emit("content-change", {
                html,
                placeholderText: placeholderText.value,
                selectionsInfo: selectionsInfo.value,
            });
        };

        // 使用XML解析方式提取文本
        const getTextWithPlaceholders = () => {
            const html = myQuillEditor.value ? myQuillEditor.value.getHTML() : editorContent.value;
            
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;
            
            const cascaderElements = tempDiv.querySelectorAll('.el-cascader');
            const cascaderValues = Array.from(cascaderElements).map(el => el.getAttribute('value') || '');
            
            let plainText = '';
            let cascaderIndex = 0;
            
            function processNode(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    if (node.classList && node.classList.contains('el-cascader')) {
                        plainText += `%%MARKER_${cascaderIndex}%%`;
                        cascaderIndex++;
                        return;
                    }
                    
                    if (node.classList && (
                        node.classList.contains('select-input-value') ||
                        node.classList.contains('el-cascader__dropdown') ||
                        node.classList.contains('input-wraper')
                    )) {
                        return;
                    }
                    
                    if (node.tagName === 'P' || node.tagName === 'p') {
                        let isLastP = true;
                        let nextSibling = node.nextSibling;
                        
                        while (nextSibling) {
                            if (nextSibling.tagName === 'P' || nextSibling.tagName === 'p') {
                                isLastP = false;
                                break;
                            }
                            nextSibling = nextSibling.nextSibling;
                        }
                        
                        for (let i = 0; i < node.childNodes.length; i++) {
                            processNode(node.childNodes[i]);
                        }
                        
                        if (!isLastP) {
                            plainText += '\n';
                        }
                        return;
                    }
                    
                    for (let i = 0; i < node.childNodes.length; i++) {
                        processNode(node.childNodes[i]);
                    }
                } else if (node.nodeType === Node.TEXT_NODE) {
                    // Strip zero-width spaces from text content
                    plainText += (node.textContent || '').replace(/\u200B/g, '');
                }
            }
            
            for (let i = 0; i < tempDiv.childNodes.length; i++) {
                processNode(tempDiv.childNodes[i]);
            }
            
            for (let i = 0; i < cascaderValues.length; i++) {
                plainText = plainText.replace(`%%MARKER_${i}%%`, `<specialBlock>${cascaderValues[i]}</specialBlock>`);
            }
            
            return plainText;
        };

        // 将标识符转换为完整HTML
        const restoreContent = () => {
            let content1 = testInput.value;
            const lines = content1.split("\n");
            let htmlParts = [];

            console.log("恢复内容:", content1);

            const oldDropdowns = document.querySelectorAll(".ql-el-cascader__dropdown");
            oldDropdowns.forEach(dropdown => {
                if (dropdown.parentNode) {
                    dropdown.parentNode.removeChild(dropdown);
                }
            });

            lines.forEach((line) => {
                if (!line.trim()) {
                    htmlParts.push("<p><br></p>");
                    return;
                }

                let lineHtml = "<p>";
                let lastIndex = 0;
                const regex = /<specialBlock>([^<]*)<\/specialBlock>/g;
                let match;

                while ((match = regex.exec(line)) !== null) {
                    const textBefore = line.substring(lastIndex, match.index);
                    if (textBefore) {
                        // Strip zero-width spaces from textBefore
                        lineHtml += textBefore.replace(/\u200B/g, '');
                    }

                    const value = match[1];
                    // Add the span and a single, controlled zero-width space
                    lineHtml += `<span class="el-cascader" value="${value}" width="100%" type="user" contenteditable="false"></span>&#8203;`;

                    lastIndex = match.index + match[0].length;
                }

                if (lastIndex < line.length) {
                    const remainingText = line.substring(lastIndex);
                    // Strip zero-width spaces from remainingText
                    lineHtml += remainingText.replace(/\u200B/g, '');
                }

                lineHtml += "</p>";
                htmlParts.push(lineHtml);
            });

            const htmlContent = htmlParts.join("");
            console.log("重建的HTML内容:", htmlContent);

            // 设置HTML内容前清空编辑器
            if (myQuillEditor.value) {
                myQuillEditor.value.setHTML(htmlContent);
            }

            // 延迟初始化下拉框，确保DOM已完全渲染
            setTimeout(() => {
                const cascaders = document.querySelectorAll(".el-cascader");
                console.log(`找到${cascaders.length}个下拉框元素`);
                
                // 清除已有的下拉框，避免重复
                cascaders.forEach(cascader => {
                    // 移除现有的input元素，避免重复
                    const existingInputs = cascader.querySelectorAll('.select-input-value');
                    existingInputs.forEach(input => {
                        cascader.removeChild(input);
                    });
                    
                    const value = cascader.getAttribute("value");
                    console.log(`初始化下拉框，值:${value}`);
                    
                    // 确保用户列表存在
                    if (!Quill.selectData || !Quill.selectData.userList) {
                        console.error("用户列表未加载");
                        return;
                    }
                    
                    // 创建新的下拉框
                    new SelfSelect(cascader, {
                        type: "user",
                        value: value,
                        list: Quill.selectData.userList,
                    });
                });
            }, 10); // 增加延迟，确保DOM完全渲染
        };

        // 编辑器就绪事件
        const onEditorReady = (quill) => {
            const Block = Quill.import("blots/block");
            Block.tagName = "p";
            Quill.register(Block, true);

            quill.root.addEventListener("click", (e) => {
                // 获取编辑器内容
                const delta = quill.getContents();
                const length = quill.getLength();

                // 计算最后一个内容的位置
                let lastPosition = 0;
                delta.ops.forEach((op) => {
                    if (typeof op.insert === "string") {
                        lastPosition += op.insert.length;
                    } else if (typeof op.insert === "object") {
                        lastPosition += 1;
                    }
                });
                
                // 确保位置包括文档末尾的换行符
                if (lastPosition < length - 1) {
                    lastPosition = length - 1;
                }
                
                // 获取最后一个位置的边界框
                const lastBounds = quill.getBounds(lastPosition);

                // 获取点击位置相对于编辑器的坐标
                const clickY = e.clientY - quill.root.getBoundingClientRect().top;
                const clickX = e.clientX - quill.root.getBoundingClientRect().left;

                // 判断点击是否在最后一个内容之后的空白区域
                if (clickY > lastBounds.bottom || (clickY > lastBounds.top && clickX > lastBounds.right)) {
                    // 设置光标到最后
                    setTimeout(() => {
                        quill.setSelection(lastPosition, 0);
                    }, 0);
                }
            });

            // 导入Delta类
            const Delta = Quill.import("delta");

            // 覆盖默认粘贴行为，强制只粘贴纯文本
            quill.clipboard.addMatcher(Node.ELEMENT_NODE, (node, delta) => {
                // 提取纯文本内容
                const plainText = node.innerText || node.textContent || "";

                // 返回正确的Delta实例
                return new Delta().insert(plainText);
            });

            // 发送编辑器就绪事件
            emit("editor-ready", quill);

            // 如果有初始值，设置内容
            if (props.modelValue) {
                testInput.value = props.modelValue;
                console.log("初始化内容:", props.modelValue);
                restoreContent();
            }
        };

        onMounted(() => {
            // 注册自定义格式
            addSelectAttr();
            
            // 监听组件销毁，清理所有下拉框
            onBeforeUnmount(() => {
                cleanupDropdowns();
            });
        });

        // 添加清理下拉框的方法
        const cleanupDropdowns = () => {
            // 查找可能存在的所有下拉框元素
            const dropdowns = document.querySelectorAll('.ql-el-cascader__dropdown');
            dropdowns.forEach(dropdown => {
                if (dropdown.parentNode) {
                    dropdown.parentNode.removeChild(dropdown);
                }
            });
            
            // 检查Quill实例上是否有存储的下拉框实例
            const editor = myQuillEditor.value?.getQuill();
            if (editor?.root) {
                const cascaders = editor.root.querySelectorAll('.el-cascader');
                cascaders.forEach(cascader => {
                    if (cascader._selectInstances) {
                        cascader._selectInstances.forEach(instance => {
                            if (typeof instance.cleanup === 'function') {
                                instance.cleanup();
                            }
                        });
                        cascader._selectInstances = [];
                    }
                });
            }
        };

        // 暴露方法给父组件
        return {
            editorContent,
            currentIndex,
            editorOption,
            selectionsInfo,
            placeholderText,
            testInput,
            myQuillEditor,
            insertSeclect,
            onEditorChange,
            onSelectionChange,
            onEditorReady,
            restoreContent,
            getTextWithPlaceholders, // 暴露此方法给父组件
            editor, // 暴露编辑器实例
            cleanupDropdowns, // 暴露清理方法
        };
    },
    methods: {
        // 公共方法，可以被父组件通过ref调用
        getEditor() {
            return this.myQuillEditor;
        },
        getPlaceholderText() {
            return this.placeholderText;
        },
        setContent(content) {
            this.testInput = content;
            this.restoreContent();
        },
        // 添加清理方法，可从父组件调用
        cleanupAllDropdowns() {
            this.cleanupDropdowns();
        }
    },
    // 添加组件销毁钩子
    beforeUnmount() {
        this.cleanupDropdowns();
    }
});
</script>

<style lang="scss">
.ql-editor {
    font-size: 14px !important;
    font-weight: 400;
    line-height: 26px !important;
    word-break: break-all;
    color: #606266;
    /* 增加编辑器高度 */
    white-space: pre-wrap !important;
    /* 允许显示换行 */
}

/* 确保编辑器中的元素正确显示 */
:deep(.ql-editor p) {
    white-space: normal !important;
    margin: 0;
    display: block !important;
    /* 确保段落是块级元素 */
    min-height: 1em;
    /* 给空段落一定高度 */
}

:deep(.ql-editor br) {
    display: block !important;
    /* 确保换行符生效 */
    content: "" !important;
    margin-top: 0.5em !important;
}

:deep(.ql-editor div.ql-el-cascader) {
    display: inline-block !important;
    vertical-align: middle;
}

/* 修复Quill编辑器容器样式 */
:deep(.ql-container) {
    overflow: visible !important;
    display: block !important;
}
</style>

<style lang="scss" scoped>
.editor-top {
    width: 100%;
    display: flex;
    // 移除 align-items: center，让子元素自然拉伸
    // align-items: center;
    height: 70px; // 设置一个具体高度

    :deep(.editor-quill) {
        flex: 1;
        height: 100%; // 确保编辑器占满高度
        max-width: calc(100% - 105px);

        // 确保Quill编辑器内部元素也充满高度
        .ql-container,
        .ql-editor {
            height: 100%;

            &::before {
                height: calc(100% - 24px);
                display: flex;
                align-items: center;
            }

            p {
                max-width: 55vw;
                overflow-x: auto;
                overflow-y: hidden;
                height: 100%;
                display: flex;
                align-items: center;
                white-space: nowrap;
                // line-height: 100%;
            }
        }
    }

}

:deep(.select-input-value) {
    display: -webkit-inline-box;
    overflow: hidden;
    width: 160px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    height: 40px;
    line-height: 40px;
    color: #303133;
    background: #f5f5f5;
    cursor: pointer;

    i {
        display: block;
        width: 14px;
        height: 14px;
        // margin-top: 13px;
        transition: transform 0.3s;

        &::after {
            display: block;
            width: 14px;
            height: 14px;
            transform: rotate(90deg);
            -ms-transform: rotate(90deg);
            /* IE 9 */
            -moz-transform: rotate(90deg);
            /* Firefox */
            -webkit-transform: rotate(90deg);
            /* Safari 和 Chrome */
            -o-transform: rotate(90deg);
            content: "";
            background: url(./icon/arrow.png) no-repeat center;
            background-size: 14px;
        }
    }

    .is-reverse {
        transform: rotate(180deg);
    }

    &.is-focus {
        border: 1px solid #409eff;
    }

    span {
        line-height: 40px;
        display: inline-block;
        padding: 0 10px;
        width: 140px;
        overflow: hidden; //超出的文本隐藏
        text-overflow: ellipsis; //用省略号显示
        white-space: nowrap; //不换行
    }

    .input-wraper {
        display: inline-flex;
    }

    .input-inner {
        text-align: left;
        display: inline-block !important;

        &:empty::before {
            content: "请选择";
            line-height: 1.5;
            font-size: 14px;
        }

        &:focus:before {
            content: none;
        }
    }

    .data-list-wraper {
        display: none;
    }
}

* {
    margin: 0;
    padding: 0;
    list-style: none;
}

:deep(.ql-el-cascader) {
    display: -webkit-inline-box;
    position: relative;
    line-height: 40px;
    margin: 0 3px;

    span[contenteditable="false"] {
        // display: block;
        // height: 40px;
    }

    li {
        &::before {
            content: "";
        }
    }
}

:deep(.ql-editor.ql-blank::before) {
    font-style: normal;
}
</style>
