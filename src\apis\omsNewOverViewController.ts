/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 根据参数获取数据的数量 POST /oms/new/overView/getByParam */
export async function newOverViewGetByParam({
  body,
  options,
}: {
  body: API.OmsCountStaticsParamDto[];
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsPicChartDataResult_>(
    '/oms/new/overView/getByParam',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 根据参数获取数据的数量 数据源于个人 POST /oms/new/overView/getByPersonAndParam */
export async function newOverViewGetByPersonAndParam({
  body,
  options,
}: {
  body: API.OmsCountStaticsParamDto[];
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsPicChartDataResult_>(
    '/oms/new/overView/getByPersonAndParam',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 根据参数获取数据的费用(区间计数) POST /oms/new/overView/getCostByParam */
export async function newOverViewGetCostByParam({
  body,
  options,
}: {
  body: API.OmsCountSectionStaticsDto[];
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsPicChartDataResult_>(
    '/oms/new/overView/getCostByParam',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 根据参数获取数据的费用(区间计数) 数据源于个人 POST /oms/new/overView/getCostByPersonAndParam */
export async function newOverViewGetCostByPersonAndParam({
  body,
  options,
}: {
  body: API.OmsCountSectionStaticsDto[];
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsPicChartDataResult_>(
    '/oms/new/overView/getCostByPersonAndParam',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
