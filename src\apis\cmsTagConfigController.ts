/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 批量关联数据表 POST /cms/cmsTagConfig/batchRelateCategory */
export async function cmsTagConfigBatchRelateCategory({
  body,
  options,
}: {
  body: API.BatchRelateCategoryParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>(
    '/cms/cmsTagConfig/batchRelateCategory',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 根据字段组合type获取分值组合列表 GET /cms/cmsTagConfig/getCombinationListByType */
export async function cmsTagConfigGetCombinationListByType({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsTagConfigGetCombinationListByTypeParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsTagConfigCombinationResp_>(
    '/cms/cmsTagConfig/getCombinationListByType',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取配置信息以及得分信息 提供给未录入的数据（审核中） GET /cms/cmsTagConfig/getDetailAndScore/${param0} */
export async function cmsTagConfigGetDetailAndScoreId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsTagConfigGetDetailAndScoreidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultCmsTagDetailAndScoreDto_>(
    `/cms/cmsTagConfig/getDetailAndScore/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}

/** 获取配置信息以及得分信息 提供给已录入的数据（审核通过，不需要审核） GET /cms/cmsTagConfig/getDetailAndScoreData/${param0} */
export async function cmsTagConfigGetDetailAndScoreDataId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsTagConfigGetDetailAndScoreDataidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultCmsTagDetailAndScoreDto_>(
    `/cms/cmsTagConfig/getDetailAndScoreData/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}

/** 获取已关联的数据表 GET /cms/cmsTagConfig/getRelateCategoryList */
export async function cmsTagConfigGetRelateCategoryList({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListLong_>(
    '/cms/cmsTagConfig/getRelateCategoryList',
    {
      method: 'GET',
      ...(options || {}),
    }
  );
}

/** 根据绩效规则id和数据表id获取标签分值配置信息 GET /cms/cmsTagConfig/getTagConfig */
export async function cmsTagConfigGetTagConfig({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsTagConfigGetTagConfigParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCmsTagConfig_>(
    '/cms/cmsTagConfig/getTagConfig',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据大类id获取标签配置信息 GET /cms/cmsTagConfig/getTagConfigByCategoryId */
export async function cmsTagConfigGetTagConfigByCategoryId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsTagConfigGetTagConfigByCategoryIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsTagConfigResp_>(
    '/cms/cmsTagConfig/getTagConfigByCategoryId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 新增或修改 POST /cms/cmsTagConfig/saveOrUpdate */
export async function cmsTagConfigSaveOrUpdate({
  body,
  options,
}: {
  body: API.CmsTagConfigParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/cms/cmsTagConfig/saveOrUpdate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
