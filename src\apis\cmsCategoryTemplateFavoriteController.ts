/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 删除收藏数据表 POST /cms/cmsCategoryTemplateFavorite/delete/${param0} */
export async function cmsCategoryTemplateFavoriteDeleteId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryTemplateFavoriteDeleteidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultVoid_>(
    `/cms/cmsCategoryTemplateFavorite/delete/${param0}`,
    {
      method: 'POST',
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}

/** 获取收藏的数据表列表 GET /cms/cmsCategoryTemplateFavorite/List */
export async function cmsCategoryTemplateFavoriteList({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListLong_>(
    '/cms/cmsCategoryTemplateFavorite/List',
    {
      method: 'GET',
      ...(options || {}),
    }
  );
}

/** 添加收藏数据表 POST /cms/cmsCategoryTemplateFavorite/save/${param0} */
export async function cmsCategoryTemplateFavoriteSaveId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCategoryTemplateFavoriteSaveidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultVoid_>(
    `/cms/cmsCategoryTemplateFavorite/save/${param0}`,
    {
      method: 'POST',
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}
