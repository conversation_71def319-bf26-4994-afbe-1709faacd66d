/**
 * 员工类型：专任教师、行政人员、教辅人员、辅导员、其他。
 */
export enum EmployeeType {
    /** 专任教师 */
    FULL_TIME_TEACHER = 0,
    /** 行政人员 */
    ADMINISTRATIVE = 1,
    /** 教辅人员 */
    TEACHING_ASSISTANT = 2,
    /** 辅导员 */
    COUNSELOR = 3,
    /** 其他 */
    OTHER = 4,
}

// 生成对应Map
export const EmployeeTypeMap: Record<EmployeeType, string> = {
    [EmployeeType.FULL_TIME_TEACHER]: "专任教师",
    [EmployeeType.ADMINISTRATIVE]: "行政人员",
    [EmployeeType.TEACHING_ASSISTANT]: "教辅人员",
    [EmployeeType.COUNSELOR]: "辅导员",
    [EmployeeType.OTHER]: "其他",
}
