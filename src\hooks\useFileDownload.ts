import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { categoryTemplateValueType_NEW } from '@/enums/categoryTemplate/categoryTemplateValueType'

/**
 * 文件下载 hooks
 * 提供统一的文件下载功能，支持多种数据源格式
 */
export function useFileDownload() {
    // 文件下载弹窗状态
    const fileDownloadDialogVisible = ref(false)
    const fileListString = ref('')

    /**
     * 从字段列表和行数据中提取文件路径（资料库页面使用）
     * @param row 行数据
     * @param fieldList 字段列表
     * @param repeatModelDownload 是否为重复数据下载模式
     */
    const downloadFromFieldList = (row: any, fieldList: any[], repeatModelDownload: boolean = false) => {
        let fileFields = []
        
        if (repeatModelDownload) {
            fileFields = row.values.filter((field: any) => field.value.startsWith('/cdmp'))
        } else {
            // 找到所有类型为 FILE 的数据项
            fileFields = fieldList.filter((field: any) => {
                return field.type === categoryTemplateValueType_NEW.FILE
            })
        }

        // 检查是否存在文件数据项
        if (fileFields.length === 0) {
            ElMessage.warning('该行没有文件！')
            return false
        }

        // 收集所有文件路径
        const allFileUrls: string[] = []

        if (repeatModelDownload) {
            // 重复数据模式：直接从 values 中获取以 /cdmp 开头的文件
            fileFields.forEach((field: any) => {
                if (field.value && field.value.trim().length > 0) {
                    const fileUrls = field.value
                        .split(',')
                        .map((url: string) => url.trim())
                        .filter((url: string) => url.length > 0 && url.startsWith('/cdmp'))
                    allFileUrls.push(...fileUrls)
                }
            })
        } else {
            // 普通模式：从 row.values 中获取对应字段的文件
            fileFields.forEach((field: any) => {
                const fieldValue = row.values.find((val: any) => val.categoryTemplateValueId === field.id)?.value
                if (fieldValue && fieldValue.trim().length > 0) {
                    const fileUrls = fieldValue
                        .split(',')
                        .map((url: string) => url.trim())
                        .filter((url: string) => url.length > 0 && url.startsWith('/'))
                    allFileUrls.push(...fileUrls)
                }
            })
        }

        return processFileUrls(allFileUrls)
    }

    /**
     * 从单个字段中提取文件路径（项目资料库页面使用）
     * @param row 行数据
     * @param fieldName 字段名称，默认为 'proofMaterial'
     */
    const downloadFromSingleField = (row: any, fieldName: string = 'proofMaterial') => {
        const fieldValue = row[fieldName]
        
        // 检查是否存在文件数据
        if (!fieldValue || !fieldValue.trim()) {
            ElMessage.warning('该行没有文件！')
            return false
        }

        // 收集所有文件路径
        const fileUrls = fieldValue
            .split(',')
            .map((url: string) => url.trim())
            .filter((url: string) => url.length > 0 && url.startsWith('/'))

        return processFileUrls(fileUrls)
    }

    /**
     * 处理文件URL数组，检查并打开弹窗
     * @param fileUrls 文件URL数组
     */
    const processFileUrls = (fileUrls: string[]) => {
        // 检查是否有文件数据
        if (fileUrls.length === 0) {
            ElMessage.warning('该行没有文件！')
            return false
        }

        // 设置文件列表字符串并打开弹窗
        fileListString.value = fileUrls.join(',')
        fileDownloadDialogVisible.value = true
        return true
    }

    /**
     * 通用的文件下载处理函数
     * @param options 下载选项
     */
    const handleDownload = (options: {
        row: any
        fieldList?: any[]
        fieldName?: string
        repeatModelDownload?: boolean
        mode?: 'fieldList' | 'singleField'
    }) => {
        const { row, fieldList, fieldName = 'proofMaterial', repeatModelDownload = false, mode = 'singleField' } = options

        if (mode === 'fieldList' && fieldList) {
            return downloadFromFieldList(row, fieldList, repeatModelDownload)
        } else {
            return downloadFromSingleField(row, fieldName)
        }
    }

    return {
        // 状态
        fileDownloadDialogVisible,
        fileListString,
        
        // 方法
        handleDownload,
        downloadFromFieldList,
        downloadFromSingleField,
        processFileUrls
    }
} 