/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 清空已读 POST /mms/mmsMessage/deleteAll */
export async function mmsMessageDeleteAll({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/mms/mmsMessage/deleteAll', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 获取当前用户通知列表 GET /mms/mmsMessage/list */
export async function mmsMessageList({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListMmsMessage_>('/mms/mmsMessage/list', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 分页获取当前用户未读通知列表 GET /mms/mmsMessage/listMessage */
export async function mmsMessageListMessage({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.mmsMessageListMessageParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageMmsMessage_>(
    '/mms/mmsMessage/listMessage',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 消息已读 POST /mms/mmsMessage/messageRead/${param0} */
export async function mmsMessageMessageReadId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.mmsMessageMessageReadidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultVoid_>(
    `/mms/mmsMessage/messageRead/${param0}`,
    {
      method: 'POST',
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}
