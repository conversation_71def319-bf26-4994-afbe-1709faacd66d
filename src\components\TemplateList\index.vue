<template>
    <div class="right-list-item" v-for="item in classifyList" :key="item.id" :id="item.name">
        <div class="title-wrapper">
            <div class="title">{{ item.name }}</div>
        </div>
        <div v-if="getTemplatesForClassify(item.id).length > 0" class="templates-grid">
            <div
                v-for="template in getTemplatesForClassify(item.id)"
                :key="template.id"
                class="template-card"
                :data-category-id="item.id">
                <div v-if="template.templateName" class="template-item" @click="$emit('choose-entry', template)">
                    <div class="template-icon-left">
                        <img src="@/assets/templateIcon.png" alt="template" class="icon-image" />
                    </div>
                    <div class="template-content">
                        <span>{{ template.templateName }}</span>
                    </div>
                    <div @click.stop="handleCollectWithEvent(template, $event)" class="template-icon-star">
                        <img :src="getStarImage(template.id)" class="star-icon" />
                    </div>
                </div>
            </div>
        </div>
        <div v-else>
            <div>
                <p class="empty-text">暂无数据</p>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { CmsCategoryTemplate_, CmsClassify_ } from "@/apis/types";

const props = defineProps<{
    classifyList: CmsClassify_[];
    templateList: CmsCategoryTemplate_[];
    getStarImage: (id: number) => string;
}>();

const emit = defineEmits<{
    (e: "choose-entry", template: CmsCategoryTemplate_): void;
    (e: "collect", template: CmsCategoryTemplate_, event: Event): void;
}>();

// 获取分类下的模板
const getTemplatesForClassify = (classifyId: number) => {
    return props.templateList.filter((temp) => temp.classify === classifyId);
};

const handleCollectWithEvent = (template: CmsCategoryTemplate_, event: Event) => {
    emit("collect", template, event);
};
</script>

<style scoped lang="scss">
@use "@/assets/styles/favorite.scss";

.right-list-item {
    margin-bottom: 40px;
}

.title-wrapper {
    border-left: 4px solid #1890FF;
    padding-left: 10px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.title {
    font-size: 14px;
    font-weight: 500;
    color: #1890FF;
    line-height: 28px;
}

.templates-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-left: 40px;
}

.template-card {
    width: 240px;
    background: #FFFFFF;
    border: 1px solid #F0F0F0;
    border-radius: 6px;
    overflow: hidden;
}

.template-item {
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 17px;
    min-height: 44px;
}

.template-icon-left {
    flex-shrink: 0;
}

.icon-image {
    width: 20px;
    height: 20px;
}

.template-content {
    flex: 1;
    color: #333333;
    font-size: 14px;
    line-height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.template-icon-star {
    cursor: pointer;
    flex-shrink: 0;
}

.star-icon {
    width: 18px;
    height: 18px;
}

.empty-text {
    font-size: 13px;
    color: #6b7995;
    // text-align: center;
    margin: 20px 0;
    margin-left: 40px;
}
</style>
