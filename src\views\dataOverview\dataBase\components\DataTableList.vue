<template>
    <div class="con-table" v-loading="loading">
        <el-table
            header-row-class-name="table-header"
            v-if="tableData.length > 0"
            ref="multipleTableRef"
            border
            :data="tableData"
            style="width: 100%"
            max-height="calc(100vh - 340px)"
            @selection-change="handleSelectionChange"
            @sort-change="sortChange"
            @scroll="$emit('tableScroll', $event)"
            empty-text="暂无数据...">
            <el-table-column type="selection" width="55" align="center" label="全选" v-if="isExport" />
            <!-- 隐藏文件类型字段 -->
            <template v-for="(field, i) in fieldList">
                <el-table-column
                    v-if="field.type !== categoryTemplateValueType_NEW.FILE"
                    :fixed="i == 0 ? true : false"
                    :key="field.id"
                    :prop="field.value"
                    :label="field.value"
                    :filters="isFilterable(field) ? getFieldFilters(field) : undefined"
                    :filter-method="isFilterable(field) ? filterHandler : undefined"
                    :sortable="isSortable(field) ? 'custom' : false"
                    show-overflow-tooltip
                    align="center"
                    min-width="150">
                    <template #default="scope">
                        <FieldValueDisplay
                            :field="field"
                            :row-data="scope.row"
                            :person-list="allPersonListWithDisabled"
                            :dict-list="dictList"
                            :org-list="allDependentMajors" />
                    </template>
                </el-table-column>
            </template>

            <el-table-column fixed="right" label="操作" min-width="156" align="center">
                <template #default="scope">
                    <div class="flex-center" style="gap: 12px">
                        <div
                            style="color: #1677ff; cursor: pointer"
                            v-if="hasBackendPermission"
                            type="primary"
                            @click="$emit('downloadMaterial', scope.row)">
                            下载材料
                        </div>
                        <div
                            style="color: #1677ff; cursor: pointer"
                            type="primary"
                            @click="$emit('viewDetail', scope.row)">
                            查看详情
                        </div>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-empty
            v-if="
                selectedTableDetail?.templateType === categoryTemplateType['院情数据'] &&
                availableBatches.length === 0
            "
            description="该数据表暂无有效批次" />
        <el-empty v-else-if="tableData.length === 0" description="该数据表暂无数据" />
    </div>
</template>

<script setup lang="ts">
import { categoryTemplateValueType_NEW } from "@/enums/categoryTemplate/categoryTemplateValueType";
import { categoryTemplateType } from "@/enums/categoryTemplate/categoryTemplateType";
import { CmsCategoryTemplate_, CmsCategoryTemplateValue10 } from "@/apis/types";
import { getFieldName } from "@/utils/getNames";
import type { TableColumnCtx, TableInstance } from "element-plus";
import FieldValueDisplay from "@/components/FieldValueDisplay/index.vue";

interface Props {
    loading: boolean;
    tableData: any[];
    fieldList: CmsCategoryTemplateValue10[];
    selectedTableDetail?: CmsCategoryTemplate_;
    availableBatches: string[];
    isExport: boolean;
    hasBackendPermission: boolean;
    allPersonListWithDisabled: any[];
    dictList: any[];
    allDependentMajors: any[];
    filterConfigs: Record<number, any>;
}

interface Emits {
    (e: 'selectionChange', val: CmsCategoryTemplateValue10[]): void;
    (e: 'sortChange', payload: { prop: string; order: string }): void;
    (e: 'tableScroll', payload: { scrollLeft: number; scrollTop: number }): void;
    (e: 'downloadMaterial', row: any): void;
    (e: 'viewDetail', row: any): void;
    (e: 'toggleSelection', rows?: any[]): void;
    (e: 'clearSelection'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 表格ref
const multipleTableRef = ref<TableInstance>();

// 处理选择数据
const handleSelectionChange = (val: CmsCategoryTemplateValue10[]) => {
    emit('selectionChange', val);
};

// 判断数据项是否可筛选
const isFilterable = (field: CmsCategoryTemplateValue10): boolean => {
    if (field.categoryPublicType && props.filterConfigs[field.categoryPublicType]) {
        return true;
    }

    switch (field.type) {
        case categoryTemplateValueType_NEW.ISN:
        case categoryTemplateValueType_NEW.LEVEL:
        case categoryTemplateValueType_NEW.ENUM_SINGLE:
        case categoryTemplateValueType_NEW.ENUM_MULTI:
        case categoryTemplateValueType_NEW.DEPENDENT_MAJOR:
        case categoryTemplateValueType_NEW.PROJECT_STATUS:
            return true;
        default:
            return false;
    }
};

// 判断数据项是否可排序
const isSortable = (field: CmsCategoryTemplateValue10): boolean => {
    switch (field.type) {
        case categoryTemplateValueType_NEW.INTEGER:
        case categoryTemplateValueType_NEW.DOUBLE:
        case categoryTemplateValueType_NEW.MONEY:
        case categoryTemplateValueType_NEW.DATE:
        case categoryTemplateValueType_NEW.PROJECT_DATE:
            return true;
        default:
            return false;
    }
};

// 获取数据项的过滤器配置
const getFieldFilters = (field: CmsCategoryTemplateValue10) => {
    if (field.categoryPublicType && props.filterConfigs[field.categoryPublicType]) {
        const uniqueValues = new Set(
            props.tableData
                .map((row) => row.values.find((val) => val.categoryTemplateValueId === field.id)?.value)
                .filter(Boolean)
        );

        const valueNames = Array.from(uniqueValues).map((value) => ({
            id: value,
            name: getFieldName(props.dictList, value),
        }));

        const enumFilters = props.filterConfigs[field.categoryPublicType]();

        return enumFilters.map((filter) => {
            const matchingValue = valueNames.find((v) => v.name === filter.text);
            return {
                text: filter.text,
                value: matchingValue?.id || filter.value,
            };
        });
    }

    switch (field.type) {
        case categoryTemplateValueType_NEW.ISN:
            return [
                { text: "是", value: "1" },
                { text: "否", value: "0" },
            ];
        case categoryTemplateValueType_NEW.LEVEL:
        case categoryTemplateValueType_NEW.ENUM_SINGLE:
        case categoryTemplateValueType_NEW.PROJECT_STATUS:
            const uniqueValues = new Set(
                props.tableData
                    .map((row) => row.values.find((val) => val.categoryTemplateValueId === field.id)?.value)
                    .filter(Boolean)
            );

            const filters = Array.from(uniqueValues).map((value) => ({
                text: getFieldName(props.dictList, value),
                value: value,
            }));
            return filters;
        case categoryTemplateValueType_NEW.ENUM_MULTI:
            if (field.tag) {
                const matchingDictItems = props.dictList.filter((dict) => dict.type === field.tag);
                return matchingDictItems.map((dict) => ({
                    text: dict.name,
                    value: dict.id.toString(),
                }));
            }
            const multiUniqueValues = new Set<string>();
            props.tableData.forEach((row) => {
                const cellValue = row.values.find((val) => val.categoryTemplateValueId === field.id)?.value;
                if (cellValue) {
                    cellValue.split(",").forEach((value) => {
                        if (value.trim()) {
                            multiUniqueValues.add(value.trim());
                        }
                    });
                }
            });

            return Array.from(multiUniqueValues).map((value) => ({
                text: getFieldName(props.dictList, value),
                value: value,
            }));
        case categoryTemplateValueType_NEW.DEPENDENT_MAJOR:
            const majorUniqueValues = new Set<string>();
            props.tableData.forEach((row) => {
                const cellValue = row.values.find((val) => val.categoryTemplateValueId === field.id)?.value;
                if (cellValue) {
                    cellValue.split(",").forEach((value) => {
                        if (value.trim()) {
                            majorUniqueValues.add(value.trim());
                        }
                    });
                }
            });

            return Array.from(majorUniqueValues).map((id) => {
                const org = props.allDependentMajors.find((org) => org.id.toString() === id.trim());
                return {
                    text: org ? org.name : id,
                    value: id,
                };
            });
        default:
            return undefined;
    }
};

// 过滤器处理方法
const filterHandler = (value: any, row: any, column: TableColumnCtx<any>) => {
    const field = props.fieldList.find((field) => field.value === column.property);
    if (!field) return true;

    const cellValue = row.values.find((val) => val.categoryTemplateValueId === field.id)?.value;

    if (!cellValue || cellValue.trim() === "") {
        return false;
    }

    if (field.type === categoryTemplateValueType_NEW.ISN) {
        return cellValue === value;
    }

    if (
        field.type === categoryTemplateValueType_NEW.ENUM_MULTI ||
        field.type === categoryTemplateValueType_NEW.DEPENDENT_MAJOR
    ) {
        const cellValues = cellValue.split(",").map((v) => v.trim());
        return cellValues.includes(value);
    }

    return cellValue === value;
};

// 表格自定义排序方法
const sortChange = ({ prop, order }: { prop: string; order: string }) => {
    emit('sortChange', { prop, order });
};

// 暴露方法给父组件
const toggleRowSelection = (row: any, selected?: boolean) => {
    multipleTableRef.value?.toggleRowSelection(row, selected);
};

const clearSelection = () => {
    multipleTableRef.value?.clearSelection();
};

defineExpose({
    toggleRowSelection,
    clearSelection,
});
</script>

<style scoped lang="scss">
// 表头背景色
:deep(.table-header) {
    th {
        background-color: #edf1fa !important;
        color: rgba(0, 0, 0, 0.88);
        font-size: 14px;
        font-weight: 500;
    }
}

.con-table {
    position: relative;
}
</style>