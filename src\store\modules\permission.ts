import auth from "@/plugins/auth";
import router, { constantRoutes, dynamicRoutes } from "@/router";
import Layout from "@/layout/index.vue";
import { defineStore } from "pinia";
import ParentView from "@/components/ParentView/index.vue";
import { umsAdminMenuTree } from "@/apis/umsAdminController";
// 匹配views里面所有的.vue文件
const modules = import.meta.glob("./../../views/**/*.vue");
const usePermissionStore = defineStore("permission", {
    state: () => ({
        routes: Array<any>(),
        addRoutes: Array<any>(),
        defaultRoutes: Array<any>(),
        topbarRouters: Array<any>(),
        sidebarRouters: Array<any>(),
    }),
    actions: {
        setRoutes(routes: Array<any>) {
            this.addRoutes = routes;
            this.routes = constantRoutes.concat(routes);
        },
        setDefaultRoutes(routes: Array<any>) {
            this.defaultRoutes = constantRoutes.concat(routes);
        },
        setTopbarRoutes(routes: Array<any>) {
            this.topbarRouters = routes;
        },
        setSidebarRouters(routes: Array<any>) {
            this.sidebarRouters = routes;
        },
        generateRoutes() {
            this.clearAllRoutes();
            return new Promise((resolve, reject) => {
                // 向后端请求路由数据
                umsAdminMenuTree({})
                    .then((res) => {
                        let sdata = JSON.parse(JSON.stringify(res.data));
                        let rdata = JSON.parse(JSON.stringify(res.data));
                        const defaultData = JSON.parse(JSON.stringify(res.data));
                        // console.table(rdata);

                        const sidebarRoutes = filterAsyncRouter(sdata);

                        const rewriteRoutes = filterAsyncRouter(rdata, false, true);

                        const defaultRoutes = filterAsyncRouter(defaultData);
                        const asyncRoutes = filterDynamicRoutes(dynamicRoutes);
                        asyncRoutes.forEach((route) => {
                            router.addRoute(route);
                        });
                        this.setRoutes(rewriteRoutes);
                        this.setSidebarRouters(constantRoutes.concat(sidebarRoutes));
                        this.setDefaultRoutes(sidebarRoutes);
                        this.setTopbarRoutes(defaultRoutes);
                        resolve(rewriteRoutes);
                    })
                    .catch((err) => {
                        reject(err);
                    });
            });
        },
        clearAllRoutes() {
            // 清除路由实例中的动态路由
            const remainingRouteNames = router.getRoutes()
                .map(route => route.name)
                .filter(name => name && !constantRoutes.some(item => item.name === name));
                
            remainingRouteNames.forEach(name => router.removeRoute(name as string));
            
            // 清空store中的路由数据
            this.routes = [];
            this.addRoutes = [];
            this.defaultRoutes = [];
            this.topbarRouters = [];
            this.sidebarRouters = [];
        },
    },
});

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap: Array<any>, lastRouter = false, type = false) {
    return asyncRouterMap.filter((route) => {
        if (type && route.children) {
            route.children = filterChildren(route.children);
        }
        if (route.component) {
            // Layout ParentView 组件特殊处理
            if (route.component === "Layout") {
                route.component = Layout;
            } else if (route.component === "ParentView") {
                route.component = ParentView;
            } else {
                route.component = loadView(route.component);
            }
        }
        if (route.children != null && route.children && route.children.length) {
            route.children = filterAsyncRouter(route.children, route, type);
        } else {
            delete route["children"];
            delete route["redirect"];
        }
        return true;
    });
}

function filterChildren(childrenMap: Array<any>, lastRouter: false | any = false) {
    var children: Array<any> = [];
    // console.table(childrenMap);
    childrenMap.forEach((el, index) => {
        if (el.children && el.children.length) {
            if (el.component === "ParentView" && !lastRouter) {
                // el.children.forEach((c: any) => {
                //     if (c.children && c.children.length) {
                //         children = children.concat(filterChildren(c.children, c));
                //         return;
                //     }
                //     children.push(c);
                //     console.table(children);
                // });
                // return;
            }
        }
        // if (lastRouter) {
        //     el.path = lastRouter.path + "/" + el.path;
        // }
        children = children.concat(el);
    });
    return children;
}

// 动态路由遍历，验证是否具备权限
export function filterDynamicRoutes(routes: Array<any>) {
    const res: Array<any> = [];
    routes.forEach((route) => {
        if (route.permissions) {
            if (auth.hasPermiOr(route.permissions)) {
                res.push(route);
            }
        } else if (route.roles) {
            if (auth.hasRoleOr(route.roles)) {
                res.push(route);
            }
        }
    });
    return res;
}

export const loadView = (view: string) => {
    let res;
    view = view.replace("/", "");
    for (const path in modules) {
        const dir = path.split("views/")[1].split(".vue")[0];
        if (dir === view) {
            res = () => modules[path]();
        }
    }
    return res;
};

export default usePermissionStore;
