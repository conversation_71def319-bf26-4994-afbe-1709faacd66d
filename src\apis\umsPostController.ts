/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 根据ID获取岗位详情 GET /ums/post/${param0} */
export async function postId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.postidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultUmsPost_>(`/ums/post/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 添加岗位 POST /ums/post/create */
export async function postCreate({
  body,
  options,
}: {
  body: API.UmsPostParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultBoolean_>('/ums/post/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除岗位 POST /ums/post/delete/${param0} */
export async function postDeleteId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.postDeleteidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultBoolean_>(`/ums/post/delete/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 批量删除岗位 POST /ums/post/delete/batch */
export async function postDeleteBatch({
  body,
  options,
}: {
  body: number[];
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultBoolean_>('/ums/post/delete/batch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询岗位 GET /ums/post/list */
export async function postList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.postListParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageUmsPost_>('/ums/post/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取所有岗位 GET /ums/post/listAll */
export async function postListAll({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListUmsPost_>('/ums/post/listAll', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 修改岗位 POST /ums/post/update */
export async function postUpdate({
  body,
  options,
}: {
  body: API.UmsPostParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultBoolean_>('/ums/post/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改岗位状态 POST /ums/post/updateStatus/${param0} */
export async function postUpdateStatusId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.postUpdateStatusidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultBoolean_>(`/ums/post/updateStatus/${param0}`, {
    method: 'POST',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}
