<template>
    <div class="wrapper">
        <!-- 新闻详情 -->
        <div class="box">
            <div class="top">
                <div class="name">
                    <svg width="37" height="37" viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M18.5204 0.000369235C28.7738 -0.0646081 37.0298 8.45522 36.9999 18.5553C36.9713 28.5046 28.6516 37.4143 17.6432 36.9517C7.88746 36.5397 -0.181432 28.285 0.00310372 18.0056C0.179842 8.15373 8.48135 -0.0347185 18.5204 0.000369235ZM26.5853 19.9276C26.5853 17.3727 26.5931 14.8178 26.5853 12.2603C26.5853 10.6307 25.7185 9.78466 24.0993 9.78466H10.6477C8.9284 9.78466 8.07849 10.6086 8.07719 12.3032C8.072 17.3913 8.072 22.4791 8.07719 27.5664C8.07719 29.0686 8.95568 29.9315 10.4658 29.9315H24.0408C25.7601 29.9315 26.5827 29.1141 26.584 27.3922C26.5866 24.9023 26.5871 22.4141 26.5853 19.9276ZM27.469 27.0634C28.8258 26.684 29.3352 25.9822 29.3365 24.5501C29.3365 19.5469 29.3365 14.544 29.3365 9.54164C29.3365 9.34671 29.33 9.15178 29.3118 8.95815C29.2247 8.01857 28.7023 7.39869 27.842 7.079C27.5361 6.97619 27.2147 6.92738 26.892 6.93475C22.3436 6.92522 17.7952 6.92522 13.2468 6.93475C11.9291 6.93475 11.0675 7.63261 10.8621 8.88407H11.5678C15.7926 8.88407 20.0148 8.91786 24.2397 8.86978C26.2007 8.84639 27.5262 10.2642 27.4976 12.0784C27.4197 16.7983 27.4703 21.5209 27.4703 26.2434L27.469 27.0634Z"
                            fill="#478CAE" />
                        <path
                            d="M17.2625 18.1498C15.0532 18.1498 12.844 18.1563 10.6426 18.1498C9.86284 18.1498 9.50806 17.8288 9.52496 17.2077C9.54055 16.6125 9.93822 16.2889 10.6842 16.2876C15.0766 16.2876 19.4691 16.2876 23.8603 16.2798C24.4464 16.2798 24.8258 16.502 24.9727 17.0491C25.1286 17.6235 24.6569 18.1381 23.9473 18.1459C22.1527 18.1654 20.3567 18.1563 18.5607 18.1589H17.2612L17.2625 18.1498Z"
                            fill="#478CAE" />
                        <path
                            d="M17.1883 21.4039C15.1091 21.4039 13.0328 21.4039 10.9596 21.4039C10.7427 21.4062 10.5257 21.3966 10.3098 21.3753C9.79781 21.322 9.5496 21.0075 9.5236 20.5137C9.49501 19.9679 9.8186 19.682 10.3033 19.5494C10.4528 19.525 10.6048 19.5202 10.7556 19.5351H23.7316C23.8614 19.528 23.9916 19.528 24.1214 19.5351C24.6153 19.591 25.0311 20.0303 24.9869 20.4916C24.9388 20.9958 24.6802 21.3571 24.1305 21.3688C23.0285 21.3922 21.9213 21.3935 20.8232 21.3974C19.6133 21.4065 18.4021 21.4026 17.1883 21.4039Z"
                            fill="#478CAE" />
                        <path
                            d="M17.2546 26.0355C19.4638 26.0355 21.6666 26.0446 23.8719 26.029C24.4229 26.029 24.7933 26.2447 24.9622 26.7567C25.113 27.2129 24.792 27.7379 24.3125 27.8588C24.1645 27.8917 24.0131 27.9065 23.8615 27.9029C19.4491 27.9029 15.0376 27.9029 10.6269 27.9029C9.88228 27.9029 9.5379 27.6027 9.5249 27.0101C9.51191 26.4176 9.91477 26.0407 10.6373 26.0394C12.8426 26.0355 15.048 26.0355 17.2546 26.0355Z"
                            fill="#478CAE" />
                        <path
                            d="M17.2611 24.6567C15.2026 24.6567 13.1446 24.6567 11.087 24.6567C10.8479 24.6567 10.61 24.6567 10.3722 24.6359C9.83422 24.5956 9.54182 24.2941 9.52882 23.7587C9.51453 23.1869 9.84721 22.8945 10.3787 22.7905C10.4431 22.7847 10.5079 22.7847 10.5724 22.7905C15.0142 22.7905 19.4556 22.7905 23.8966 22.7905C24.5464 22.7905 24.9947 23.1895 24.9895 23.7314C24.9843 24.2733 24.614 24.6099 23.9499 24.6528C23.7771 24.6632 23.6029 24.6619 23.4301 24.6619L17.2611 24.6567Z"
                            fill="#478CAE" />
                        <rect x="12.25" y="11.8704" width="10.0065" height="2.859" rx="1.4295" fill="#478CAE" />
                    </svg>
                    <span>新闻详情</span>
                </div>
            </div>
            <div class="content">
                <div class="back-btn" @click="goBack">
                    <el-button color="#8BA0C9">返回</el-button>
                </div>
                <div class="news-detail" v-if="newsInfo">
                    <div class="news-header">
                        <h1 class="news-title">{{ newsInfo.title }}</h1>
                        <div class="news-meta">
                            <span>发布时间：{{ formatDate(newsInfo.publishedAt) }}</span>
                            <span>作者：{{ newsInfo.authorName || 0 }}</span>
                        </div>
                    </div>
                    <div class="news-content" v-html="newsInfo.content"></div>
                </div>
                <div class="loading" v-else>
                    <el-empty description="加载中..." v-if="loading"></el-empty>
                    <el-empty description="暂无数据" v-else></el-empty>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { nmsNewsGetPublishById } from "@/apis/nmsNewsController";
import { NmsNews_ } from "@/apis/types";

const route = useRoute();
const router = useRouter();
const newsId = ref(route.query.id as string);
const newsInfo = ref<NmsNews_>(null);
const loading = ref(true);

function fetchNewsDetail() {
    if (!newsId.value) {
        loading.value = false;
        return;
    }

    loading.value = true;
    nmsNewsGetPublishById({
        params: {
            id: Number(newsId.value),
        },
    })
        .then((res) => {
            newsInfo.value = res.data;
            loading.value = false;
        })
        .catch((err) => {
            console.error(err);
            loading.value = false;
        });
}

function formatDate(dateString: string | null) {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
}

function goBack() {
    router.back();
}

onMounted(() => {
    fetchNewsDetail();
});
</script>

<style scoped lang="scss">
.wrapper {
    .box {
        .top {
            background: linear-gradient(360deg, #fafcff 0%, #d7e1f4 100%);
            box-shadow:
                0 4px 6px -1px rgba(0, 0, 0, 0.05),
                0 2px 4px -1px rgba(0, 0, 0, 0.02);
            z-index: 5;
            position: relative;

            .name {
                margin: 0px 40px;
                padding: 30px 0;
                display: flex;
                align-items: center;
                width: 200px;
                border-bottom: 8px #8ba0c9 solid;
                padding-bottom: 10px;

                span {
                    text-shadow: 0px 4px 4px #5f6d8640;
                    color: #23346d;
                    font-size: 20px;
                    font-weight: 700;
                    letter-spacing: 0.1em;
                    text-align: left;
                    margin-left: 15px;
                }
            }
        }

        .content {
            background: #fff;
            padding: 20px 40px;
            min-height: 60vh;

            .back-btn {
                margin-bottom: 20px;
                text-align: left;

                button {
                    letter-spacing: 3px;
                    color: #fff;
                    font-size: 16px;
                }
            }

            .news-detail {
                .news-header {
                    border-bottom: 1px solid #d2dbec;
                    padding-bottom: 15px;
                    margin-bottom: 20px;

                    .news-title {
                        font-size: 24px;
                        font-weight: 700;
                        color: #23346d;
                        margin-bottom: 15px;
                        letter-spacing: 0.1em;
                        text-align: center;
                    }

                    .news-meta {
                        display: flex;
                        justify-content: center;
                        color: #6c7cb2;
                        font-size: 14px;
                        gap: 30px;
                    }
                }

                .news-content {
                    padding: 20px 0;
                    color: #23346d;
                    font-size: 16px;
                    line-height: 1.8;
                    letter-spacing: 0.05em;
                    min-height: 300px;

                    :deep(img) {
                        max-width: 100%;
                        height: auto;
                        margin: 10px 0;
                    }

                    :deep(p) {
                        margin-bottom: 15px;
                    }
                }
            }

            .loading {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 400px;
            }
        }
    }
}
</style>
