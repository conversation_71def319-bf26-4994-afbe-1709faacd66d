import { ref, computed, type Ref } from "vue";
import { ElMessage } from "element-plus";
import type { CmsCategoryTemplate_ } from "@/apis/types";
import StarCollected from "@/assets/star_collected.png";
import StarCollect from "@/assets/star_collect.png";
import {
    cmsCategoryTemplateFavoriteList,
    cmsCategoryTemplateFavoriteSaveId,
    cmsCategoryTemplateFavoriteDeleteId,
} from "@/apis/cmsCategoryTemplateFavoriteController";

/**
 * 数据表收藏功能Hook
 * @param templateList 数据表列表
 */
export function useFavoriteTemplate(templateList: Ref<CmsCategoryTemplate_[]>) {
    // 收藏列表 ID 数组
    const favoriteList = ref<number[]>([]);

    // 已收藏的数据表列表
    const favoriteTemplates = computed(() => {
        return templateList.value.filter((template) => favoriteList.value.includes(template.id));
    });

    // 获取收藏图标
    const getStarImage = (templateId: number) => {
        return favoriteList.value.includes(templateId) ? StarCollected : StarCollect;
    };

    // 收藏/取消收藏处理函数
    const handleCollect = (template: CmsCategoryTemplate_, event?: Event) => {
        // 检查是否已收藏
        const isAlreadyFavorite = favoriteList.value.includes(template.id);

        // 添加点击动画效果
        if (event) {
            const starIcon = event.target as HTMLElement;
            starIcon.classList.add("star-click");

            setTimeout(() => {
                starIcon.classList.remove("star-click");
            }, 300);
        }

        if (isAlreadyFavorite) {
            // 如果已收藏，则取消收藏
            cmsCategoryTemplateFavoriteDeleteId({
                params: {
                    id: template.id,
                },
            }).then((res) => {
                if (res.code === 200) {
                    ElMessage.success("已取消收藏");
                    // 从收藏列表中移除
                    const index = favoriteList.value.indexOf(template.id);
                    if (index > -1) {
                        favoriteList.value.splice(index, 1);
                    }
                }
            });
        } else {
            // 如果未收藏，则添加收藏
            cmsCategoryTemplateFavoriteSaveId({
                params: {
                    id: template.id,
                },
            }).then((res) => {
                if (res.code === 200) {
                    ElMessage.success("收藏成功");
                    // 添加到收藏列表
                    favoriteList.value.push(template.id);
                }
            });
        }
    };

    // 初始加载收藏列表
    const loadFavoriteList = () => {
        cmsCategoryTemplateFavoriteList({}).then((res) => {
            favoriteList.value = res.data;
        });
    };

    return {
        favoriteList,
        favoriteTemplates,
        getStarImage,
        handleCollect,
        loadFavoriteList,
    };
}
