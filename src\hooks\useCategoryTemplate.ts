import { ref, computed, onMounted } from "vue";
import { cmsCategoryTemplateList, cmsCategoryTemplateListByAdmin } from "@/apis/cmsCategoryTemplateController";
import { CmsCategoryTemplate_ } from "@/apis/types";

export function useCategoryTemplateList() {
    const templateList = ref<CmsCategoryTemplate_[]>([]);

    /**
     * 根据筛选条件过滤列表
     * @param filterFn 筛选函数
     */
    const getFilteredList = (filterFn: (template: CmsCategoryTemplate_) => boolean) => {
        return computed(() => {
            return templateList.value.filter(filterFn);
        });
    };

    /**
     * 获取大类列表(管理员权限，获取全部)
     */
    const fetchTemplateList = async () => {
        try {
            const res = await cmsCategoryTemplateListByAdmin({});
            templateList.value = res.data;
        } catch (error) {
            console.error("加载大类列表失败:", error);
        }
    };

    /**
     * 获取大类列表(教师权限)
     */
    const fetchTemplateListByTeacher = async () => {
        try {
            const res = await cmsCategoryTemplateList({});
            templateList.value = res.data;
        } catch (error) {
            console.error("加载大类列表失败:", error);
        }
    }

    const getTemplateNameById = (id: number) => {
        return templateList.value.find((template) => template.id === id)?.templateName;
    }

    // 自动加载数据
    onMounted(fetchTemplateList);

    return { 
        getFilteredList,
        templateList, 
        fetchTemplateList,
        fetchTemplateListByTeacher,
        getTemplateNameById
    };
}
