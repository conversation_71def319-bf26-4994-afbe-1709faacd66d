<template>
    <div class="con-top">
        <div class="con-top-left">
            <div>
                <span class="desc">查看数据表：</span>
                <el-cascader
                    popper-class="my-cascader-popper"
                    :model-value="selectedTable"
                    :options="tableOptions"
                    @change="handleTableChange"
                    :show-all-levels="false" />
            </div>
            <div
                v-if="
                    selectedTableDetail?.templateType === categoryTemplateType['院情数据'] &&
                    selectedTableDetail.batchType != null
                ">
                <span class="desc">选择批次：</span>
                <el-date-picker
                    :disabled="!selectedBatch"
                    :model-value="selectedBatch"
                    type="month"
                    :clearable="false"
                    :placeholder="
                        selectedTableDetail?.templateType === categoryTemplateType['院情数据'] &&
                        availableBatches.length === 0
                            ? '无'
                            : '选择年+月'
                    "
                    value-format="YYYY-MM"
                    :disabled-date="disabledDate"
                    @update:model-value="handleBatchChange">
                </el-date-picker>
            </div>
        </div>
        <div class="con-top-right">
            <el-button type="danger" @click="handleDeleteBatch" v-if="isShowDelete" plain>删除批次</el-button>
            <el-button v-if="isExport" plain @click="handleExportCancel">取消选择</el-button>
            <el-button
                :disabled="tableData.length === 0"
                v-if="!isExport"
                type="primary"
                @click="handleExportStart">
                导出数据
            </el-button>
            <el-button
                :disabled="multipleSelection.length === 0"
                v-else
                type="primary"
                @click="handleExportSelected">
                导出选中数据
            </el-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { CascaderOption } from "element-plus/es/components/cascader-panel/src/types";
import { categoryTemplateType } from "@/enums/categoryTemplate/categoryTemplateType";
import { CmsCategoryTemplate_ } from "@/apis/types";

interface Props {
    selectedTable: [number, number] | [];
    selectedTableDetail?: CmsCategoryTemplate_;
    selectedBatch: string;
    availableBatches: string[];
    tableOptions: CascaderOption[];
    isExport: boolean;
    isShowDelete: boolean;
    tableData: any[];
    multipleSelection: any[];
}

interface Emits {
    (e: 'update:selectedTable', value: [number, number] | []): void;
    (e: 'update:selectedBatch', value: string): void;
    (e: 'update:isExport', value: boolean): void;
    (e: 'tableChange', value: any): void;
    (e: 'deleteBatch'): void;
    (e: 'exportStart'): void;
    (e: 'exportCancel'): void;
    (e: 'exportSelected'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 禁用日期
const disabledDate = (time: Date) => {
    if (!props.availableBatches || props.availableBatches.length === 0) {
        return true;
    }

    const isYearOnly = props.availableBatches[0].length === 4;

    if (isYearOnly) {
        const yearStr = time.getFullYear().toString();
        return !props.availableBatches.includes(yearStr);
    } else {
        const monthStr = time.getMonth() + 1;
        const yearMonthStr = `${time.getFullYear()}-${monthStr.toString().padStart(2, "0")}`;
        return !props.availableBatches.includes(yearMonthStr);
    }
};

// 处理表格变化
const handleTableChange = (value: any) => {
    emit('update:selectedTable', value);
    emit('tableChange', value);
};

// 处理批次变化
const handleBatchChange = (value: string) => {
    emit('update:selectedBatch', value);
};

// 处理删除批次
const handleDeleteBatch = () => {
    emit('deleteBatch');
};

// 处理开始导出
const handleExportStart = () => {
    emit('update:isExport', true);
    emit('exportStart');
};

// 处理取消导出
const handleExportCancel = () => {
    emit('update:isExport', false);
    emit('exportCancel');
};

// 处理导出选中
const handleExportSelected = () => {
    emit('exportSelected');
};
</script>

<style scoped lang="scss">
.desc {
    color: #1677ff;
    font-size: 14px;
}

.con-top {
    display: flex;
    padding: 24px 0 10px 0;
    justify-content: space-between;
    
    .con-top-left {
        display: flex;
        gap: 41px;
    }
}
</style>