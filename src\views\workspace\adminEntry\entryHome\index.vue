<template>
    <div class="entry-home" v-if="!isEntrying">
        <div class="left">
            <div class="classify-list">
                <el-anchor type="underline" :container="containerRef" @click="handleClick">
                    <el-anchor-link v-for="item in classifyList" :href="`#${item.name}`">
                        {{ item.name }}
                    </el-anchor-link>
                </el-anchor>
            </div>
        </div>
        <div class="right" ref="containerRef">
            <div class="right-list">
                <!-- 搜索区域 -->
                <div>
                    <el-input style="width: 400px" size="large" v-model="searchKeyword" placeholder="输入关键字检索">
                        <template #append>
                            <el-button :icon="Search" />
                        </template>
                    </el-input>
                </div>

                <!-- 收藏区域 -->
                <div class="favorite-section">
                    <FavoriteTemplateList
                        :favoriteTemplates="favoriteTemplates"
                        :getStarImage="getStarImage"
                        @choose-entry="handleChooseEntry"
                        @collect="handleCollect" />
                </div>

                <!-- 分类区域 -->
                <div class="template-section">
                    <TemplateList
                        :classifyList="classifyList"
                        :templateList="templateList"
                        :getStarImage="getStarImage"
                        @choose-entry="handleChooseEntry"
                        @collect="handleCollect" />
                </div>
            </div>
        </div>
        <!-- =========================== 选择录入方式弹窗 =========================== -->
        <el-dialog
            v-model="chooseEntrydialogVisible"
            title="请选择录入方式"
            width="600"
            @close="
                chooseEntrydialogVisible = false;
                fileList = [];
            "
            align-center>
            <div class="dialog-content">
                <div class="dialog-description">根据您的需求，可以选择不同的录入方式。</div>

                <div class="entry-options">
                    <!-- 当selectedTemplate.batchType 为1时，手动录入按钮不可用 -->
                    <div
                        class="entry-option-card"
                        :class="{ disabled: selectedTemplate.batchType == 1 }"
                        @click="selectedTemplate.batchType != 1 ? openEntrying() : null">
                        <div class="card-header">
                            <div class="card-title">手动录入</div>
                        </div>
                        <div class="card-content">手动进行单条数据的录入</div>
                    </div>

                    <div class="entry-option-card" @click="handleChooseFile">
                        <div class="card-header">
                            <div class="card-title">批量导入</div>
                        </div>
                        <div class="card-content">使用Excel表进行批量数据的导入</div>
                    </div>
                </div>
            </div>

            <template #footer>
                <div class="dialog-footer">
                    <el-button
                        class="cancel-btn"
                        @click="
                            chooseEntrydialogVisible = false;
                            fileList = [];
                        ">
                        取 消
                    </el-button>
                    <el-button type="primary" class="confirm-btn" @click="chooseEntrydialogVisible = false">
                        确 定
                    </el-button>
                </div>
            </template>
        </el-dialog>
        <!-- =========================== 选择录入方式弹窗 =========================== -->

        <!-- =========================== 选择导入文件弹窗 =========================== -->
        <el-dialog
            v-model="chooseFileDialogVisible"
            title="请选择导入文件"
            width="500"
            @close="fileList = []"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            align-center>
            <div>
                <!-- 仅当为院情表且已有批次才显示 -->
                <div
                    style="margin-bottom: 10px"
                    v-if="
                        selectedTemplate.batchType === 1 &&
                        selectedTemplate.templateType === categoryTemplateType['院情数据']
                    ">
                    <span>请选择导入批次：</span>
                    <el-date-picker
                        v-model="selectedBatch"
                        type="month"
                        placeholder="请选择日期"
                        value-format="YYYY-MM" />
                </div>
                <el-upload
                    drag
                    ref="uploadRef"
                    :on-exceed="handleExceed"
                    :limit="1"
                    v-model:file-list="fileList"
                    :on-change="handleFileChange"
                    action=""
                    :auto-upload="false">
                    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                    <div class="el-upload__text">拖动文件至此处或 <em>点击上传</em></div>
                    <template #tip>
                        <div class="el-upload__tip text-red">
                            最多选择一个文件，再次选择会覆盖。文件大小不能超过100MB。
                        </div>
                    </template>
                </el-upload>
            </div>
            <template #footer>
                <el-button
                    @click="
                        selectedBatch = null;
                        fileList = [];
                        chooseFileDialogVisible = false;
                    "
                    >取消</el-button
                >
                <el-button type="primary" @click="handleUpload">上传</el-button>
            </template>
        </el-dialog>
        <!-- =========================== 选择导入文件弹窗 =========================== -->

        <!-- =========================== 校验导入文件过程弹窗 =========================== -->
        <el-dialog
            v-model="checkFileDialogVisible"
            :show-close="false"
            width="600"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            align-center>
            <el-dialog
                v-model="checkFailDialogVisible"
                width="600"
                style="max-height: 500px; overflow-y: auto"
                append-to-body
                :show-close="false"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                align-center>
                <template #header="{ close, titleId, titleClass }">
                    <div>
                        <h4 :id="titleId" :class="titleClass">文件校验未通过</h4>
                    </div>
                </template>
                <div>
                    <div style="font-size: 16px; font-weight: bold">
                        <span>文件校验未通过</span>
                        <span v-if="checkFailInfo?.code == 1"> - 表头校验失败</span>
                        <span v-if="checkFailInfo?.code == 2"> - 数据项校验失败（有重复）</span>
                        <span v-if="checkFailInfo?.code == 3"> - 校验失败，数据类型错误，缺少归属人，缺少项目名称</span>
                        <span v-if="checkFailInfo?.code == 4"> - 自定义值不存在</span>
                    </div>
                    <div style="margin: 10px 0; line-height: 30px; letter-spacing: 1px">
                        您的文件 <span style="font-weight: bold">{{ fileList[0]?.name }}</span> 未通过
                        <span style="font-weight: bold">"{{ selectedTemplate?.templateName }}"</span
                        >数据校验，以下行与平台数据库不匹配。这可能是您选择了错误的文件，请核对后重新上传文件。
                    </div>
                    <div v-for="item in checkFailInfo?.errorList" style="margin: 10px 0; line-height: 30px">
                        <div
                            v-if="
                                item.repetitionRow[0].rowId && item.repetitionRow && item.repetitionRow[0].rowId >= 0
                            ">
                            第{{ item.repetitionRow[0].rowId }}行：
                        </div>
                        <div v-for="error in item.repetitionRow[0].values" :key="error.id">
                            错误信息：{{ error.value }}
                        </div>
                    </div>
                    <div style="margin-top: 20px">
                        <strong
                            >提示：
                            您可以返回修改表格并再次上传，或联系平台管理员获取帮助，以确保数据项（表头）符合要求。</strong
                        >
                    </div>
                </div>
                <template #footer>
                    <div style="display: flex; justify-content: center">
                        <el-button type="primary" @click="hanldeCloseAll">我知道了</el-button>
                    </div>
                </template>
            </el-dialog>
            <el-dialog
                v-model="checkSuccessDialogVisible"
                width="600"
                style="max-height: 500px; overflow-y: auto"
                append-to-body
                :show-close="false"
                align-center>
                <template #header="{ close, titleId, titleClass }">
                    <div class="my-header">
                        <img style="height: 24px" src="../../../../assets/checkSuccess.png" alt="校验成功" />
                        <div :id="titleId" :class="titleClass">校验已通过</div>
                    </div>
                </template>
                <div
                    style="margin-left: 35px"
                    v-if="selectedTemplate.templateType === categoryTemplateType['院情数据']"
                    class="simple-text-normal">
                    您的文件已通过数据校验，所有数据均符合导入要求。该数据表类型为院情数据，点击"确认"后，数据将立即导入平台中。
                    <br />
                    <br />
                    您可以在"管理员录入中心-资料库"查看已导入的数据。
                </div>
                <div v-else>
                    您的文件已通过数据校验，所有数据均符合导入要求。该数据表类型为
                    <span style="font-weight: bold">{{ categoryTemplateType[selectedTemplate.templateType] }}</span
                    >，点击 "确认导入" 后，数据将分发至对应教师处，经由教师核对补充后提交审核。
                    <div style="margin-top: 20px">
                        您可以在"管理员录入中心-录入记录"查看本次导入记录。对应教师将在你点击"确认导入"后收到通知提示。
                    </div>
                </div>
                <template #footer>
                    <div>
                        <el-button plain @click="hanldeCloseAll">取消导入</el-button>
                        <el-button type="primary" @click="submitImport">确认导入</el-button>
                    </div>
                </template>
            </el-dialog>
            <template #header="{ close, titleId, titleClass }">
                <div class="my-header" v-if="progressStatus === ''">
                    <h4 :id="titleId" :class="titleClass">正在校验导入文件...</h4>
                </div>
            </template>
            <div class="check-file-content">
                <div v-if="progressStatus === 'exception'">
                    <div class="simple-text-bold">校验失败</div>
                    <div>点击“下一步”继续</div>
                </div>
                <div v-if="progressStatus === 'success'">
                    <div class="simple-text-bold">校验完成</div>
                    <div>点击“下一步”继续</div>
                </div>
                <el-progress :stroke-width="8" :percentage="checkProgress" :status="progressStatus" />
            </div>
            <template #footer>
                <div>
                    <el-button
                        v-if="progressStatus === 'exception'"
                        @click="checkFailDialogVisible = true"
                        type="primary"
                        >下一步</el-button
                    >
                    <el-button v-else @click="checkSuccessDialogVisible = true" type="primary">下一步</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- =========================== 校验导入文件过程弹窗 =========================== -->
    </div>
    <div class="entry-home" v-else>
        <DataEntry
            :selected-template="selectedTemplate"
            @back="handleBack"
            @show-performance-rules="showPerformanceRules" />
        <!-- =========================== 查看已关联绩效规则弹窗 =========================== -->
        <PerformanceRulesDialog
            v-model="performanceRulesDialogVisible"
            :performance-rules-list="performanceRulesList"
            :show-change-rules-button="false" />
        <!-- =========================== 查看已关联绩效规则弹窗 =========================== -->
    </div>
</template>

<script setup lang="ts">
import PerformanceRulesDialog from "@/components/PerformanceRulesDialog/index.vue";
import { useCategoryTemplateList } from "@/hooks/useCategoryTemplate";
import { cmsClassifyList } from "@/apis/cmsClassifyController";
import {
    CmsCategoryTemplate_,
    CmsClassify_,
    CmsTag_,
    CmsTagConfigResp,
    CommonResultRowImportErrorMessage_,
    RowImportErrorMessage,
    UmsPerson_,
} from "@/apis/types";

import {
    dayjs,
    ElLoading,
    ElMessage,
    ElMessageBox,
    genFileId,
    UploadFiles,
    UploadInstance,
    UploadProps,
    UploadRawFile,
} from "element-plus";
import {
    cmsCategoryRowCheckData,
    cmsCategoryRowCheckPerformance,
    cmsCategoryRowGetOwnerByStatus,
    cmsCategoryRowImportDataFile,
    cmsCategoryRowImportPerformanceFile,
} from "@/apis/cmsCategoryRowController";
import { categoryTemplateType } from "@/enums/categoryTemplate/categoryTemplateType";
import { cmsTagCategoryEntityGetTagTreeListByEntityId } from "@/apis/cmsTagCategoryEntityController";
import { useDataEntryStore } from "@/store/modules/dataEntry";
import DataEntry from "@/components/DataEntry/index.vue";
import { useRoute, useRouter } from "vue-router";
import { useCheckFailInfoStore } from "@/store/modules/checkFailInfo";
import { ref, watch, onMounted, onUnmounted } from "vue";
import { useFavoriteTemplate } from "@/hooks/useFavoriteTemplate";
import { useTemplateHeight } from "@/hooks/useTemplateHeight";
import FavoriteTemplateList from "@/components/FavoriteTemplateList/index.vue";
import TemplateList from "@/components/TemplateList/index.vue";
import { Search, Close } from "@element-plus/icons-vue";
import { useCustomDialog } from "@/components/CustomDialog";

const customDialog = useCustomDialog();

const route = useRoute();
const router = useRouter();

// 搜索关键字
const searchKeyword = ref("");

// 大类归类列表
const classifyList = ref<CmsClassify_[]>([]);

// 数据录入状态
const dataEntryStore = useDataEntryStore();

// 校验失败信息
const checkFailInfoStore = useCheckFailInfoStore();

const { templateList } = useCategoryTemplateList();

const selectedTemplate = ref<CmsCategoryTemplate_ | null>(null);

// 锚点容器
const containerRef = ref<HTMLElement | null>(null);

// 选择录入方式弹窗
const chooseEntrydialogVisible = ref(false);
// 选择导入文件弹窗
const chooseFileDialogVisible = ref(false);

// 选择导入批次
const selectedBatch = ref<string | null>(null);

// 导入的文件
const fileList = ref([]);

// 是否正在录入
const isEntrying = ref(false);

// 文件列表引用
const fileListRefs = ref<UploadFiles[]>([]);

// 校验导入文件过程弹窗
const checkFileDialogVisible = ref(false);

// 校验失败弹窗
const checkFailDialogVisible = ref(false);

// 校验成功弹窗
const checkSuccessDialogVisible = ref(false);

// 添加进度条相关变量
const checkProgress = ref(0);
const progressStatus = ref<"success" | "exception" | "warning" | "">("");
const progressTimer = ref<ReturnType<typeof setInterval> | null>(null);
const progressSpeed = ref(500); // 进度增长间隔时间(ms)
const progressStep = ref(5); // 每次增长步长

// 开始进度增长
const startProgress = () => {
    // 清除可能存在的定时器
    if (progressTimer.value) {
        clearInterval(progressTimer.value);
    }

    const timer = setInterval(() => {
        if (checkProgress.value < 99) {
            // 根据当前进度动态调整步长，使进度增长速度逐渐变慢
            const remainingProgress = 99 - checkProgress.value;
            const step = Math.min(progressStep.value, Math.max(1, remainingProgress / 10));
            checkProgress.value = Number(Math.min(99, checkProgress.value + step).toFixed(2));
        } else {
            // 达到99%时停止定时器
            if (progressTimer.value) {
                clearInterval(progressTimer.value);
                progressTimer.value = null;
            }
        }
    }, progressSpeed.value);
    progressTimer.value = timer;
};

// 停止进度增长
const stopProgress = () => {
    if (progressTimer.value) {
        clearInterval(progressTimer.value);
        progressTimer.value = null;
    }
};

const checkFailInfo = ref<RowImportErrorMessage | null>(null);

// 关闭所有弹窗
const hanldeCloseAll = () => {
    selectedBatch.value = null;
    fileList.value = [];
    checkFileDialogVisible.value = false;
    checkSuccessDialogVisible.value = false;
    checkFailDialogVisible.value = false;
    chooseFileDialogVisible.value = false;
    chooseEntrydialogVisible.value = false;
};

// 选择录入方式
const handleChooseEntry = (template: CmsCategoryTemplate_) => {
    // 查询当前用户数据表某个状态是否有数据
    cmsCategoryRowGetOwnerByStatus({
        params: {
            approvalStatus: [0, 4],
            rowTypes: ["IMPORT", "INSERT"],
            templateId: template.id,
        },
    }).then((res) => {
        if (res.data) {
            customDialog.confirm({
                title: "提示",
                message: `您的「待补充」中仍有未处理的「${template.templateName}」数据。为避免重复录入，请先确认您当前要录入的内容是否跟「待补充」中的数据重复。如您已经确认，可继续录入操作。`,
                type: "info",
                alignCenter: true,
                onConfirm: () => {
                    chooseEntrydialogVisible.value = true;
                    selectedTemplate.value = template;
                },
            });
        } else {
            chooseEntrydialogVisible.value = true;
            selectedTemplate.value = template;
        }
    });
};

// 选择批量导入
const handleChooseFile = () => {
    chooseEntrydialogVisible.value = false;
    chooseFileDialogVisible.value = true;
};

// 阻止锚点跳转
const handleClick = (e) => {
    e.preventDefault();
};

// 添加一个变量保存当前处理的文件
const currentFile = ref<File | null>(null);

// 处理上传文件
const handleUpload = async () => {
    if (
        selectedTemplate.value?.templateType === categoryTemplateType["院情数据"] &&
        selectedTemplate.value?.batchType === 1 &&
        !selectedBatch.value
    ) {
        ElMessage.error("请选择导入批次");
        return;
    }

    // 重置进度和状态
    checkProgress.value = 0;
    progressStatus.value = "";
    checkFailInfo.value = null;
    stopProgress(); // 确保之前的定时器被清除

    // 保存文件信息
    const fileToUpload = fileList.value[0];
    if (!fileToUpload?.raw) {
        ElMessage.error("请选择文件");
        return;
    }
    currentFile.value = fileToUpload.raw;

    try {
        checkFileDialogVisible.value = true;
        startProgress(); // 开始进度增长

        // 第一步校验
        let firstCheckResult: CommonResultRowImportErrorMessage_ | null = null;
        if (
            selectedTemplate.value?.templateType === categoryTemplateType["工作数据"] ||
            selectedTemplate.value?.templateType === categoryTemplateType["成果数据"]
        ) {
            firstCheckResult = await cmsCategoryRowCheckPerformance({
                params: { id: selectedTemplate.value?.id },
                body: {},
                file: fileToUpload.raw,
            });
        } else {
            firstCheckResult = await cmsCategoryRowCheckData({
                params: {
                    id: selectedTemplate.value?.id,
                    batchNumber: selectedBatch.value,
                },
                body: {},
                file: fileToUpload.raw,
            });
        }

        // code错误码
        // 操作状态：0：检验成功，1：表头校验失败，2：数据项校验失败（有重复），3：校验失败，数据类型错误，缺少归属人，缺少项目名称
        // 4：自定义值不存在，5：其他错误
        if (firstCheckResult.data.code == 2) {
            checkProgress.value = 100;
            stopProgress(); // 停止进度增长
            progressStatus.value = "exception";
            checkFailInfo.value = firstCheckResult.data;
            customDialog.confirm({
                title: "数据重复",
                message: '当前导入的数据与数据库中已存在的记录高度重复。点击"查看详情"进行后续操作',
                type: "warning",
                alignCenter: true,
                confirmButtonText: "查看详情",
                onConfirm: () => {
                    // 存储到pinia中
                    checkFailInfoStore.setCheckFailInfo(firstCheckResult.data);
                    checkFailInfoStore.setCheckFailFile(fileToUpload);
                    checkFailDialogVisible.value = true;
                    router.push("/dataOverview/dataBase");
                },
                onCancel: () => {
                    // 清除校验失败信息
                    checkFailInfoStore.clearCheckFailInfo();
                    hanldeCloseAll();
                },
            });
            return;
        } else if (firstCheckResult.data.code != 0) {
            console.log("失败", firstCheckResult);
            stopProgress(); // 停止进度增长
            checkProgress.value = 100;
            progressStatus.value = "exception";
            checkFailInfo.value = firstCheckResult.data;
            return;
        }
        console.log("成功");
        // 全部成功
        stopProgress(); // 停止进度增长
        checkProgress.value = 100;
        progressStatus.value = "success";
        ElMessage.success("文件校验成功");

        chooseFileDialogVisible.value = false;
        checkSuccessDialogVisible.value = true;
    } catch (error) {
        ElMessage.error("上传失败");
        stopProgress(); // 停止进度增长
        checkProgress.value = 100;
        progressStatus.value = "exception";
    }
};

// 确认导入
const submitImport = async () => {
    if (!currentFile.value) {
        ElMessage.error("文件不存在");
        return;
    }
    const loadingInstance = ElLoading.service({
        lock: true,
        text: "正在导入文件，请勿关闭页面...",
        background: "rgba(0, 0, 0, 0.7)",
    });
    try {
        if (
            selectedTemplate.value?.templateType === categoryTemplateType["工作数据"] ||
            selectedTemplate.value?.templateType === categoryTemplateType["成果数据"]
        ) {
            const res = await cmsCategoryRowImportPerformanceFile({
                params: { id: selectedTemplate.value?.id },
                body: {},
                file: currentFile.value,
            });
            if (res.code === 200) {
                ElMessage.success("导入成功");
                loadingInstance.close();
                hanldeCloseAll();
                currentFile.value = null; // 清空当前文件
            }
        } else {
            const res = await cmsCategoryRowImportDataFile({
                params: {
                    id: selectedTemplate.value?.id,
                    batchNumber: selectedBatch.value,
                },
                body: {},
                file: currentFile.value,
            });
            if (res.code === 200) {
                ElMessage.success("导入成功");
                loadingInstance.close();
                hanldeCloseAll();
                currentFile.value = null; // 清空当前文件
            }
        }
    } catch (error: any) {
        ElMessage.error(`导入失败: ${error?.message || "未知错误"}`);
        loadingInstance.close();
    }
};

// 返回
const handleBack = () => {
    isEntrying.value = false;
    dataEntryStore.setEnteringData(false);
    selectedTemplate.value = null;
    chooseEntrydialogVisible.value = false;
};

// 打开录入
const openEntrying = () => {
    isEntrying.value = true;
    dataEntryStore.setEnteringData(true);
    chooseEntrydialogVisible.value = false;
};

// 查看已关联绩效规则弹窗
const performanceRulesDialogVisible = ref(false);

// 已关联绩效规则列表
const performanceRulesList = ref<CmsTag_[]>([]);

// 显示绩效规则弹窗
function showPerformanceRules(templateId: number) {
    performanceRulesDialogVisible.value = true;
    // 获取标签树结构
    cmsTagCategoryEntityGetTagTreeListByEntityId({ params: { categoryEntityId: templateId } }).then((res) => {
        performanceRulesList.value = res.data;
    });
}

const uploadRef = ref<UploadInstance>();

// 处理文件超出
const handleExceed: UploadProps["onExceed"] = (files) => {
    uploadRef.value!.clearFiles();
    const file = files[0] as UploadRawFile;
    file.uid = genFileId();
    uploadRef.value!.handleStart(file);
};

// 在 el-upload 组件中添加 on-change 属性
const handleFileChange: UploadProps["onChange"] = (uploadFile) => {
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (uploadFile.raw.size > maxSize) {
        ElMessage.error("文件大小不能超过100MB，文件名为：" + uploadFile.raw.name);
        // 从文件列表中移除该文件
        const index = fileList.value.indexOf(uploadFile);
        if (index > -1) {
            fileList.value.splice(index, 1);
        }
        // 如果是多文件上传组件的文件
        fileListRefs.value.forEach((list) => {
            const index = list.indexOf(uploadFile);
            if (index > -1) {
                list.splice(index, 1);
            }
        });
    }
};

// 使用收藏功能hook
const { favoriteTemplates, getStarImage, handleCollect, loadFavoriteList } = useFavoriteTemplate(templateList);

// 使用模板高度调整hook
const { adjustTemplateHeights, setupLifecycle } = useTemplateHeight(classifyList, templateList);

// 监听路由参数t的变化，刷新页面
watch(
    () => route.query.t,
    () => {
        // 执行刷新逻辑
        isEntrying.value = false;
    }
);

// 监听窗口大小变化，重新调整高度
onMounted(() => {
    // 获取大类归类列表
    cmsClassifyList({}).then((res) => {
        classifyList.value = res.data;
        adjustTemplateHeights();
    });

    // 加载收藏列表
    loadFavoriteList();

    // 设置生命周期钩子
    setupLifecycle();
});

onUnmounted(() => {
    stopProgress();
    // 移除窗口调整事件监听器
    window.removeEventListener("resize", adjustTemplateHeights);
});
</script>

<style scoped lang="scss">
@use "../../../../assets/styles/mixins.scss" as mix;
@use "../../../../assets/styles/favorite.scss";

.simple-text-bold {
    color: var(--system-color-text-regular);
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
}

.simple-text-normal {
    color: var(--system-color-text-regular);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
}

.my-header {
    display: flex;
    align-items: center;
    gap: 10px;
}

.check-file-content {
    div {
        margin: 10px 0;
    }
}

// 去掉进度条末端空白
:deep(.el-progress__text) {
    min-width: 0;
}

:deep(.el-anchor__marker) {
    // display: none;
    height: 20px !important;
    width: 3px !important;
}
:deep(.el-anchor__link.is-active) {
    color: #1677ff !important;
}
:deep(.el-anchor__link) {
    color: rgba(0, 0, 0, 0.88) !important;
}

:deep(.el-anchor__item) {
    margin: 10px 0;
}

$padding: 0 30px;

// 每行最低高度
$min-height: 70px;

:global(.confirm-button) {
    background-color: #586fbb !important;
    border-color: #586fbb !important;
}

.star {
    &::before {
        content: "*";
        color: #ff8061;
        font-size: 14px;
        margin-right: 5px;
    }
}

.desc {
    font-size: 14px;
    color: #818181;
}

.per-title {
    font-size: 20px;
    font-weight: 700;
    line-height: 19.8px;
    letter-spacing: 0.1em;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #6b7995;
    padding-left: 15px;
    position: relative;
    margin-left: 30px;

    &::before {
        content: "";
        position: absolute;
        left: 0;
        width: 4px; // 左侧线条宽度
        height: 20px;
        background-color: #23346d;
    }
}

.entry-home {
    margin-top: 20px;
    background-color: #fff;
    height: calc(100vh - 220px);
    overflow-y: auto;
    display: flex;
    .left {
        margin-left: 10px;
        width: 188px;
        height: 100%;
    }
    .right {
        height: 100%;
        flex: 1;
        overflow: auto;

        .right-list {
            padding: 40px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        // 收藏区域样式
        .favorite-section {
            background: #ebf1ff;
            border-radius: 8px;
            padding: 10px;
        }

        // 分类区域样式
        .template-section {
            background: #f9fbff;
            border-radius: 8px;
            padding: 10px 10px 20px 10px;
        }
    }
    // 旧样式已替换为新的弹窗样式
}

.dialog-content {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .dialog-description {
        color: rgba(0, 0, 0, 0.88);
        font-size: 14px;
        line-height: 22px;
        margin-right: 16px;
    }

    .entry-options {
        display: flex;
        flex-direction: row;
        gap: 24px;
        .entry-option-card {
            background: #f5f5f5;
            border: 1px solid #f0f0f0;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            flex-grow: 1;
            flex-basis: 256px;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover:not(.disabled) {
                border-color: #1677ff;
                box-shadow: 0 2px 8px rgba(22, 119, 255, 0.1);
            }

            &.disabled {
                user-select: none;
                background: #f5f5f5;
                border-color: #d9d9d9;
                cursor: not-allowed;
                opacity: 0.6;

                .card-title {
                    color: rgba(0, 0, 0, 0.25);
                }

                .card-content {
                    color: rgba(0, 0, 0, 0.25);
                }
            }

            .card-header {
                display: flex;
                align-items: center;
                padding: 12px 24px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.06);

                .card-title {
                    color: rgba(0, 0, 0, 0.88);
                    font-size: 16px;
                    font-weight: 600;
                    line-height: 24px;
                }
            }

            .card-content {
                color: #000000;
                font-size: 14px;
                line-height: 22px;
                margin: 24px;
            }
        }
    }
}
</style>
