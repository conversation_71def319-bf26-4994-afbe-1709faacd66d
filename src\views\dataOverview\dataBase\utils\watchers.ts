import { watch, WatchStopHandle } from 'vue';
import { CmsCategoryTemplate_ } from "@/apis/types";
import { cmsCategoryRowGetAllBatchNumber } from "@/apis/cmsCategoryRowController";
import { ElMessage } from "element-plus";
import { isCollegeDataType, DATA_CONSTANTS } from "./constants";

/**
 * 创建统一的数据监听器
 * 合并原来分散的watch逻辑，减少重复监听
 */
export const createDataWatchers = (
    templateList: any,
    classifyList: any, 
    selectedTable: any,
    selectedBatch: any,
    selectedTableDetail: any,
    loadTableData: (templateId: number, batchNumber?: string, pageNum?: number) => Promise<void>,
    setAvailableBatches: (batches: string[]) => void,
    setSelectedBatch: (batch: string) => void,
    setTableData: (data: any[]) => void,
    resetPagination: () => void,
    clearTableSelection: () => void,
    setExportMode: (mode: boolean) => void,
    buildTableOptions: (classifyList: any[], templateList: any[]) => void
) => {
    const stopHandles: WatchStopHandle[] = [];

    // 1. 监听模板和分类列表变化，构建表格选项
    const stopTemplateWatch = watch(
        [templateList, classifyList], 
        ([newTemplateList, newClassifyList]) => {
            if (newTemplateList.length > 0 && newClassifyList.length > 0) {
                buildTableOptions(newClassifyList, newTemplateList);
            }
        }
    );
    stopHandles.push(stopTemplateWatch);

    // 2. 监听选中表格变化
    const stopTableWatch = watch(
        selectedTable,
        async (newVal) => {
            if (!newVal || newVal.length < 2) {
                selectedTableDetail.value = undefined;
                setTableData([]);
                return;
            }

            const templateId = newVal[1];
            const template = templateList.value.find((item: any) => item.id === templateId);
            
            if (!template) return;
            
            selectedTableDetail.value = template;
            resetPagination();

            // 如果是院情数据，需要先加载批次
            if (isCollegeDataType(template.templateType)) {
                try {
                    const batchRes = await cmsCategoryRowGetAllBatchNumber({
                        params: { templateId: template.id },
                    });

                    setAvailableBatches(batchRes.data);
                    if (batchRes.data.length > 0) {
                        setSelectedBatch(batchRes.data[0]);
                    } else {
                        ElMessage.warning("当前数据表没有可用批次");
                        setTableData([]);
                        setSelectedBatch("");
                    }
                } catch (error) {
                    console.error("批次加载失败：", error);
                    ElMessage.error("批次加载失败");
                }
            } else {
                // 非院情数据直接加载数据
                await loadTableData(templateId);
            }
        },
        { immediate: true }
    );
    stopHandles.push(stopTableWatch);

    // 3. 监听批次变化，重新加载数据
    const stopBatchWatch = watch(
        selectedBatch,
        async (newBatch, oldBatch) => {
            if (!selectedTableDetail.value || !newBatch) {
                return;
            }

            // 避免重复加载
            if (oldBatch !== undefined && newBatch === oldBatch) {
                return;
            }

            await loadTableData(selectedTableDetail.value.id, newBatch, 1);
            clearTableSelection();
            setExportMode(false);
        },
        { flush: "post" }
    );
    stopHandles.push(stopBatchWatch);

    // 返回清理函数
    return () => {
        stopHandles.forEach(stop => stop());
    };
};

/**
 * 创建重复数据相关的监听器
 */
export const createDuplicateDataWatchers = (
    checkFailInfo: any,
    router: any,
    initErrorItemStates: (state: number) => void
) => {
    const stopHandles: WatchStopHandle[] = [];

    const stopFailInfoWatch = watch(
        () => checkFailInfo.value,
        (newVal) => {
            if (newVal && router.currentRoute.value.query.rowId) {
                initErrorItemStates(2);
            } else {
                initErrorItemStates(1);
            }
        },
        { immediate: true }
    );
    stopHandles.push(stopFailInfoWatch);

    return () => {
        stopHandles.forEach(stop => stop());
    };
};

/**
 * 创建详情相关的监听器
 */
export const createDetailWatchers = (
    isShowDetail: any,
    selectedItem: any,
    getDetailAndScore: (rowId: number) => void,
    getRecordList: (rowId: number) => Promise<void>
) => {
    const stopHandles: WatchStopHandle[] = [];

    const stopDetailWatch = watch(
        isShowDetail,
        async (newVal) => {
            if (newVal) {
                getDetailAndScore(selectedItem.value.id);
                await getRecordList(selectedItem.value.id);
            }
        }
    );
    stopHandles.push(stopDetailWatch);

    return () => {
        stopHandles.forEach(stop => stop());
    };
};