/**
 * 级别
 */
export enum gradeOptions {
    /** 国际级 */
    INTERNATIONAL = 0,
    /** 国家级 */
    NATIONAL = 1,
    /** 部级 */
    MINISTRY = 2,
    /** 省级 */
    PROVINCIAL = 3,
    /** 厅级 */
    DEPARTMENT = 4,
    /** 市级 */
    CITY = 5,
    /** 区局级 */
    AREA = 6,
    /** 校级 */
    SCHOOL = 7,
    /** 院级 */
    COLLEGE = 8,
    /** 其他 */
    OTHER = 9,
}

// 生成对应Map
export const gradeOptionsMap: Record<gradeOptions, string> = {
    [gradeOptions.INTERNATIONAL]: "国际级",
    [gradeOptions.NATIONAL]: "国家级",
    [gradeOptions.MINISTRY]: "部级",
    [gradeOptions.PROVINCIAL]: "省级",
    [gradeOptions.DEPARTMENT]: "厅级",
    [gradeOptions.CITY]: "市级",
    [gradeOptions.AREA]: "区局级",
    [gradeOptions.SCHOOL]: "校级",
    [gradeOptions.COLLEGE]: "院级",
    [gradeOptions.OTHER]: "其他",
}


