<template>
    <div v-if="selectedItem" v-loading="loading" class="detail-content">
        <div class="title">{{ selectedItem.projectName }}</div>
        <div class="content">
            <el-tabs :before-leave="handleTabClick" type="card" style="height: 100%">
                <el-tab-pane label="项目信息">
                    <div class="project-info">
                        <div style="display: flex; justify-content: end; gap: 16px; padding: 0 8px">
                            <el-button v-if="isAdministrator" @click="handleOpenPermissionSettingDialog">
                                权限设置
                            </el-button>
                            <el-button v-if="isAdministrator" @click="editProjectDialogVisible = true">
                                编辑团队信息
                            </el-button>
                            <el-button v-if="isAdministrator" type="danger" plain @click="deleteTeam">
                                解散团队
                            </el-button>
                        </div>

                        <!-- 基本信息 -->
                        <div class="basic-info-section">
                            <div class="section-title">
                                <div class="title-indicator"></div>
                                <span>基本信息</span>
                            </div>
                            <div class="info-table">
                                <div class="info-row">
                                    <div class="info-label">团队负责人</div>
                                    <div class="info-content">{{ selectedItem.projectLeaderName }}</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">项目状态</div>
                                    <div class="info-content project-status-content">
                                        <div class="status-item">
                                            立项时间：{{ selectedItem.projectInitiationDate || "暂无" }}
                                        </div>
                                        <div class="status-item">
                                            预计结项时间：{{ selectedItem.projectExpectedEndDate || "暂无" }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 团队成员 -->
                        <div class="team-members-section">
                            <div class="section-title">
                                <div class="title-indicator"></div>
                                <span>团队成员</span>
                                <el-button plain @click="inviteMemberDialogVisible = true" style="margin-left: 10px">
                                    <img style="height: 14px; margin-right: 8px" src="@/assets/addPerson_2.png" />
                                    邀请成员
                                </el-button>
                            </div>
                            <div class="members-grid">
                                <!-- 已加入成员 -->
                                <div
                                    class="member-card"
                                    v-for="item in selectedItem.memberList"
                                    :key="'member-' + item.id">
                                    <div class="member-card-inner">
                                        <div v-if="isAdministrator" class="delete-button">
                                            <el-tooltip :hide-after="0" content="移除成员" placement="top">
                                                <div>
                                                    <el-popconfirm
                                                        title="确定要移除该成员吗？"
                                                        @confirm="deletePerson(item)"
                                                        placement="top">
                                                        <template #reference>
                                                            <img
                                                                style="height: 24px"
                                                                src="@/assets/delete_2.png"
                                                                alt="delete" />
                                                        </template>
                                                    </el-popconfirm></div
                                            ></el-tooltip>
                                        </div>
                                        <div class="member-avatar-new" :style="{ background: getAvatarColor(item.id) }">
                                            <span v-if="!item.avatar" class="avatar-text">{{
                                                item.employeeName.charAt(0)
                                            }}</span>
                                            <img v-else :src="filesBaseUrl + item.avatar" />
                                        </div>
                                        <div class="member-name">{{ item.employeeName }}</div>
                                        <div class="member-status joined">已加入</div>
                                    </div>
                                </div>

                                <!-- 已邀请成员 -->
                                <div
                                    class="member-card"
                                    v-for="item in selectedItem.beingProcessedList"
                                    :key="'pending-' + item.id"
                                    v-if="isAdministrator">
                                    <div class="member-card-inner">
                                        <div class="delete-button">
                                            <el-tooltip :hide-after="0" content="撤回邀请" placement="top">
                                                <div>
                                                    <el-popconfirm
                                                        title="确定要撤回该邀请吗？"
                                                        @confirm="revokeInvitation(item)"
                                                        placement="top">
                                                        <template #reference>
                                                            <svg
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                                                width="24"
                                                                height="24"
                                                                viewBox="0 0 24 24">
                                                                <defs>
                                                                    <clipPath id="clipPath1724274801">
                                                                        <path
                                                                            d="M0 0L24 0L24 24L0 24L0 0Z"
                                                                            fill-rule="nonzero"
                                                                            transform="matrix(1 0 0 1 0 0)" />
                                                                    </clipPath>
                                                                </defs>
                                                                <g clip-path="url(#clipPath1724274801)">
                                                                    <circle
                                                                        cx="12"
                                                                        cy="12"
                                                                        r="12"
                                                                        transform="matrix(1 0 0 1 0 0)"
                                                                        fill="rgb(107, 114, 128)" />
                                                                    <path
                                                                        d="M12 0C18.6268 0 24 5.37321 24 12C24 18.6268 18.6268 24 12 24C5.37321 24 0 18.6268 0 12C0 5.37321 5.37321 0 12 0ZM15.428 7.36125L15.427 7.36125L15.4248 7.36286L12 10.7879L8.57518 7.36286C8.57411 7.36152 8.57357 7.36125 8.57304 7.36125C8.57242 7.36106 8.57177 7.36106 8.57116 7.36125C8.57036 7.36125 8.56982 7.36152 8.56875 7.36259L7.36286 8.56848C7.36221 8.56916 7.36175 8.56999 7.36152 8.57089C7.36133 8.5715 7.36133 8.57216 7.36152 8.57277L7.36152 8.5733C7.36197 8.57392 7.36251 8.57446 7.36313 8.57491L10.7879 12L7.36286 15.4248C7.36152 15.4259 7.36125 15.4264 7.36125 15.427C7.36106 15.4276 7.36106 15.4282 7.36125 15.4288C7.36125 15.4296 7.36152 15.4302 7.36259 15.4312L8.56848 16.6371C8.56916 16.6378 8.56999 16.6383 8.57089 16.6385C8.5715 16.6387 8.57216 16.6387 8.57277 16.6385C8.5733 16.6385 8.57384 16.6382 8.57491 16.6371L12 13.2121L15.4248 16.6371C15.4259 16.6382 15.4264 16.6385 15.427 16.6385C15.4276 16.6387 15.4282 16.6387 15.4288 16.6385C15.4296 16.6385 15.4302 16.6382 15.4312 16.6371L16.6371 15.4312C16.6378 15.4306 16.6383 15.4297 16.6385 15.4288C16.6387 15.4282 16.6387 15.4276 16.6385 15.427L16.6385 15.4264C16.6381 15.4258 16.6377 15.4253 16.6371 15.4248L13.2121 12L16.6371 8.57518C16.6382 8.57411 16.6385 8.57357 16.6385 8.57304C16.6387 8.57242 16.6387 8.57177 16.6385 8.57116C16.6385 8.57036 16.6382 8.56982 16.6371 8.56875L15.4312 7.36286C15.4306 7.36221 15.4297 7.36175 15.4288 7.36152C15.4282 7.36133 15.4276 7.36133 15.427 7.36152L15.428 7.36125Z"
                                                                        fill-rule="evenodd"
                                                                        transform="matrix(1 0 0 1 0 0)"
                                                                        fill="rgb(243, 244, 246)" />
                                                                </g>
                                                            </svg>
                                                        </template>
                                                    </el-popconfirm>
                                                </div>
                                            </el-tooltip>
                                        </div>
                                        <div class="member-avatar-new" :style="{ background: getAvatarColor(item.id) }">
                                            <span v-if="!item.avatar" class="avatar-text">{{
                                                item.employeeName.charAt(0)
                                            }}</span>
                                            <img v-else :src="filesBaseUrl + item.avatar" />
                                        </div>
                                        <div class="member-name">{{ item.employeeName }}</div>
                                        <div class="member-status invited">已邀请</div>
                                    </div>
                                </div>
                            </div>
                            <div
                                v-if="
                                    selectedItem.memberList.length === 0 && selectedItem.beingProcessedList.length === 0
                                "
                                style="margin-left: 30px; color: #6b7995">
                                暂无成员
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
                <!-- ============================== BEGIN 项目资料库 ============================== -->
                <el-tab-pane v-if="isAdministrator" label="项目资料库">
                    <project-library v-if="selectedItem" :project-id="selectedItem.id" ref="projectLibraryRef" />
                </el-tab-pane>
                <!-- ============================== END 项目资料库 ============================== -->

                <!-- ============================== BEGIN 立项资料申请 ============================== -->
                <el-tab-pane v-if="isAdministrator" label="立项资料申请" style="overflow: scroll">
                    <project-application
                        v-if="selectedItem"
                        :project-id="selectedItem.id"
                        ref="projectApplicationRef" />
                </el-tab-pane>
                <!-- ============================== END 立项资料申请 ============================== -->

                <!-- ============================== BEGIN 成果池 ============================== -->
                <el-tab-pane v-if="isAdministrator" label="成果池">
                    <project-pool v-if="selectedItem" :project-id="selectedItem.id" ref="projectPoolRef" />
                </el-tab-pane>
                <!-- ============================== END 成果池 ============================== -->
            </el-tabs>
        </div>
    </div>
    <div v-else class="detail-content">
        <el-empty description="请从左侧选择一个项目查看详情。" />
    </div>
    <!-- 邀请成员弹窗 -->
    <el-dialog
        v-model="inviteMemberDialogVisible"
        title="邀请成员"
        width="30%"
        @close="inviteMemberForm.memberId = null"
        align-center>
        <el-form :model="inviteMemberForm" label-width="120px">
            <div style="margin-bottom: 10px; font-size: 15px; color: #6b7995">
                在此处选择你要邀请的成员（可输入关键字检索）
            </div>
            <el-select clearable filterable v-model="inviteMemberForm.memberId" placeholder="请选择成员">
                <el-option
                    v-for="item in inviteMemberOptions"
                    :key="item.value"
                    :label="item.employeeName"
                    :value="item.id" />
            </el-select>
        </el-form>
        <template #footer>
            <el-button @click="inviteMemberDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="inviteMember">邀请</el-button>
        </template>
    </el-dialog>
    <!-- 邀请成员弹窗 -->

    <!-- 编辑项目弹窗 -->
    <el-dialog v-model="editProjectDialogVisible" title="编辑项目基本信息" width="500" align-center>
        <el-form :model="editProjectForm" :rules="editProjectRules" ref="editProjectFormRef" label-position="top">
            <el-form-item label="项目名称" :label-width="formLabelWidth" prop="projectName">
                <el-input v-model="editProjectForm.projectName" />
            </el-form-item>
            <el-form-item label="项目简介" :label-width="formLabelWidth" prop="projectBrief">
                <el-input type="textarea" v-model="editProjectForm.projectBrief" />
            </el-form-item>
            <el-form-item label="执行负责人" :label-width="formLabelWidth" prop="leaderId">
                <el-select v-model="editProjectForm.leaderId" placeholder="请选择">
                    <el-option
                        v-for="item in selectedItem?.memberList"
                        :key="item.id"
                        :label="item.employeeName"
                        :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="立项时间" :label-width="formLabelWidth" prop="projectInitiationDate">
                <el-date-picker
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    v-model="editProjectForm.projectInitiationDate"
                    type="date"
                    placeholder="请选择立项时间" />
            </el-form-item>
            <el-form-item label="结项时间" :label-width="formLabelWidth" prop="projectExpectedEndDate">
                <el-date-picker
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    v-model="editProjectForm.projectExpectedEndDate"
                    type="date"
                    placeholder="请选择结项时间" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="editProjectDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitUpdateForm(editProjectFormRef)"> 确认 </el-button>
            </div>
        </template>
    </el-dialog>
    <!-- 编辑项目弹窗 -->

    <!-- 权限设置弹窗 -->
    <el-dialog v-model="permissionSettingDialogVisible" title="权限设置" width="30%" align-center>
        <el-form :model="permissionSettingForm" label-width="120px" ref="permissionSettingFormRef">
            <el-form-item label="项目管理员" :label-width="formLabelWidth" prop="administrators">
                <el-select multiple v-model="permissionSettingForm.administrators" placeholder="请选择">
                    <el-option
                        v-for="item in selectedItem?.memberList"
                        :key="item.id"
                        :label="item.employeeName"
                        :value="item.id" />
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="permissionSettingDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitPermissionSettingForm(permissionSettingFormRef)"> 确认 </el-button>
        </template>
    </el-dialog>
    <!-- 权限设置弹窗 -->
</template>

<script setup lang="ts">
import { filesBaseUrl } from "@/utils/filesBaseUrl";
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance, TabPaneName } from "element-plus";
import useUserStore from "@/store/modules/user";
import {
    tmsProjectChangePosition,
    tmsProjectDeleteMember,
    tmsProjectDeleteProject,
    tmsProjectUpdateProject,
} from "@/apis/tmsProjectController";
import {
    tmsProjectInvitationInvitePerson,
    tmsProjectInvitationRevokeInvite,
} from "@/apis/tmsProjectInvitationController";
import { umsPersonAll, umsPersonAllPersonList } from "@/apis/umsPersonController";
import { TmsProject0, TmsUpdateAdministratorDto } from "@/apis";
import defaultAvatar from "@/assets/defaultAvatar.png";
import ProjectLibrary from "./components/ProjectLibrary.vue";
import ProjectPool from "./components/ProjectPool.vue";
import ProjectApplication from "./components/ProjectApplication.vue";
import { useCustomDialog } from "@/components/CustomDialog/useCustomDialog";

const CustomDialog = useCustomDialog();

const emit = defineEmits(["refresh"]);
const userStore = useUserStore();

// 编辑项目基本信息弹窗
const editProjectDialogVisible = ref(false);

// 权限设置弹窗
const permissionSettingDialogVisible = ref(false);

// 权限设置表单ref
const permissionSettingFormRef = ref();

const formLabelWidth = "120px";

// 编辑项目基本信息表单
const editProjectForm = ref({
    projectName: null,
    projectBrief: null,
    leaderId: null,
    projectInitiationDate: null,
    projectExpectedEndDate: null,
});

// 编辑项目基本信息表单规则
const editProjectRules = ref({
    projectName: [{ required: true, message: "请输入项目名称", trigger: "blur" }],
    projectLeader: [{ required: true, message: "请输入执行负责人", trigger: "blur" }],
    projectGroupLeader: [{ required: true, message: "请输入执行组长", trigger: "blur" }],
    projectStatus: [{ required: true, message: "请选择项目状态", trigger: "blur" }],
});

// 权限设置表单
const permissionSettingForm = ref<TmsUpdateAdministratorDto>({
    administrators: [],
    projectId: null,
});

// 编辑项目基本信息表单ref
const editProjectFormRef = ref();

// 权限设置表单提交
const submitPermissionSettingForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    await formEl.validate((valid, fields) => {
        if (valid) {
            tmsProjectChangePosition({
                body: {
                    administrators: permissionSettingForm.value.administrators,
                    projectId: selectedItem.id,
                },
            }).then((res: any) => {
                if (res.code === 200) {
                    ElMessage.success("权限设置成功！");
                    permissionSettingDialogVisible.value = false;
                    emit("refresh", selectedItem.id);
                }
            });
        }
    });
};

// 打开权限设置弹窗
const handleOpenPermissionSettingDialog = () => {
    permissionSettingForm.value.projectId = selectedItem.id;
    permissionSettingForm.value.administrators = selectedItem.executorList.map((item: any) => item.id);
    permissionSettingDialogVisible.value = true;
};

// 邀请成员弹窗
const inviteMemberDialogVisible = ref(false);

const inviteMemberForm = ref({
    memberId: null,
});

// 邀请成员选项
const inviteMemberOptions = ref([]);

const loading = ref(false);
const { selectedItem } = defineProps<{
    selectedItem: TmsProject0;
}>();

// 组件引用
const projectLibraryRef = ref();
const projectPoolRef = ref();

// 处理tab切换事件
const handleTabClick = (activeName: TabPaneName) => {
    // 如果当前立项时间为空，则禁止跳转至1标签
    if (activeName === "1") {
        if (selectedItem.projectInitiationDate === null) {
            ElMessage.warning("当前项目缺少立项时间，请先完善！");
            return false;
        }
    }
    return true;
};

// 监听页面事件
watch(
    () => selectedItem,
    (newVal) => {
        if (newVal) {
            loading.value = true;
            permissionSettingForm.value.administrators = selectedItem.executorList.map((item: any) => item.id);
            editProjectForm.value.leaderId = selectedItem.leaderId;
            editProjectForm.value.projectName = selectedItem.projectName;
            editProjectForm.value.projectBrief = selectedItem.projectBrief;
            editProjectForm.value.projectInitiationDate = selectedItem.projectInitiationDate;
            editProjectForm.value.projectExpectedEndDate = selectedItem.projectExpectedEndDate;

            // 等子组件完成数据加载后结束loading状态
            setTimeout(() => {
                loading.value = false;
            }, 500);
        }
    },
);

// 打开邀请人员弹窗后，加载全部人员列表
watch(
    () => inviteMemberDialogVisible.value,
    () => {
        if (inviteMemberDialogVisible.value && !inviteMemberOptions.value.length) {
            umsPersonAllPersonList({}).then((res: any) => {
                inviteMemberOptions.value = res.data;

                // 筛选inviteMemberOptions：移除当前用户和待加入成员
                inviteMemberOptions.value = inviteMemberOptions.value.filter((person: any) => {
                    // 移除当前用户
                    if (person.id === userStore.userId) {
                        return false;
                    }

                    // 移除已在团队中的成员
                    const isExistingMember = selectedItem.memberList.some((member: any) => member.id === person.id);
                    if (isExistingMember) {
                        return false;
                    }

                    // 移除待加入成员
                    const isPendingMember = selectedItem.beingProcessedList.some(
                        (member: any) => member.id === person.id,
                    );
                    if (isPendingMember) {
                        return false;
                    }

                    return true;
                });
            });
        }
    },
);

// 判断当前用户是否为项目管理员
const isAdministrator = computed(() => {
    return permissionSettingForm.value.administrators.includes(userStore.userId);
});

// 删除成员
const deletePerson = (item: any) => {
    tmsProjectDeleteMember({
        body: {
            projectId: selectedItem.id,
            memberId: item.id,
        },
    }).then((res: any) => {
        if (res.code === 200) {
            ElMessageBox.alert("移除成员成功！", "", { type: "success" });
        }
        emit("refresh", selectedItem.id);
    });
};

// 邀请成员
const inviteMember = () => {
    tmsProjectInvitationInvitePerson({
        body: {
            projectId: selectedItem.id,
            participant: inviteMemberForm.value.memberId,
        },
    }).then((res: any) => {
        if (res.code === 200) {
            ElMessage.success("邀请成员成功！");
        }
        inviteMemberDialogVisible.value = false;
        emit("refresh", selectedItem.id);
    });
};

// 修改项目团队信息
const submitUpdateForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    await formEl.validate((valid, fields) => {
        if (valid) {
            tmsProjectUpdateProject({
                body: {
                    id: selectedItem.id,
                    projectBrief: editProjectForm.value.projectBrief,
                    leaderId: editProjectForm.value.leaderId,
                    projectName: editProjectForm.value.projectName,
                    projectInitiationDate: editProjectForm.value.projectInitiationDate,
                    projectExpectedEndDate: editProjectForm.value.projectExpectedEndDate,
                },
            }).then((res: any) => {
                if (res.code === 200) {
                    ElMessage.success("修改项目团队信息成功！");
                    editProjectDialogVisible.value = false;
                    emit("refresh", selectedItem.id);
                }
            });
        }
    });
};

// 删除团队
const deleteTeam = () => {
    // 弹窗提示是否删除
    CustomDialog.confirm({
        title: "确认解散团队吗？",
        message:
            "团队解散后，当前“项目资料库”，“立项资料”，“成果池”中的内容将被一并删除，所有当前共享的文件将失去下载权限，该操作不可撤销。如您已充分知晓后果，请点击“确定”。",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        alignCenter: true,
        onConfirm: () => {
            tmsProjectDeleteProject({
                body: {
                    id: selectedItem.id,
                },
            }).then((res: any) => {
                if (res.code === 200) {
                    ElMessage.success("删除团队成功！");
                    emit("refresh");
                }
            });
        },
    });
};

// 根据用户ID生成头像背景颜色
const getAvatarColor = (userId: number) => {
    const colors = ["#45B7D1", "#54A0FF", "#FF9F43", "#9AD572", "#FD79A8", "#FDCB6E", "#6C5CE7", "#A29BFE"];
    return colors[userId % colors.length];
};

// 撤回邀请
const revokeInvitation = (item: any) => {
    tmsProjectInvitationRevokeInvite({
        body: {
            projectId: selectedItem.id,
            personId: item.id,
        },
    }).then((res: any) => {
        if (res.code === 200) {
            ElMessage.success("撤回邀请成功！");
        }
        emit("refresh", selectedItem.id);
    });
};
</script>

<style scoped lang="scss">
@use "../../../../assets/styles/mixins.scss" as mix;

/* :global(.el-tabs__content) {
    overflow: scroll;
} */

.bold {
    font-weight: bold;
}
.pagination {
    @extend .flex-end;
    margin-top: 10px;
    margin-right: 20px;
}

.flex-end {
    display: flex;
    justify-content: end;
}

.project-info {
    height: 100%;
    /* height: calc(100vh - 410px); */
    overflow-y: auto;
}

// 基本信息区域样式
.basic-info-section {
    background: #ffffff;
    padding-top: 5.5px;
}

.section-title {
    display: flex;
    align-items: center;
    min-height: 21px;
    margin: 21.5px 0;

    .title-indicator {
        border-left: 3px solid #1677ff;
        padding-left: 8px;
        margin-right: 8px;
        height: 20px;
        display: flex;
        align-items: center;
    }

    span {
        color: #000000;
        line-height: 20px;
        width: 56px;
        min-height: 20px;
        font-size: 14px;
    }
}

.info-table {
    display: flex;
    flex-direction: column;
}

.info-row {
    display: flex;
    flex-direction: row;

    &:first-child .info-label {
        border-top: 1px solid rgba(0, 0, 0, 0.06);
        border-top-left-radius: 8px;
    }

    &:first-child .info-content {
        border-top: 1px solid rgba(0, 0, 0, 0.06);
        border-top-right-radius: 8px;
    }

    &:last-child .info-label {
        border-bottom-left-radius: 8px;
    }

    &:last-child .info-content {
        border-bottom-right-radius: 8px;
    }
}

.info-label {
    background: #f9fbff;
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-right: none;
    width: 160px;
    padding: 16px 24px;
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.88);
    font-weight: 600;
    line-height: 22px;
    flex-shrink: 0;
}

.info-content {
    background: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.06);
    padding: 16px 24px;
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.88);
    line-height: 22px;
    flex-grow: 1;

    &.project-status-content {
        display: flex;
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
        padding: 16px 24px;

        .status-item {
            min-height: 22px;
        }
    }
}

// 团队成员区域样式
.team-members-section {
    margin-top: 0.5px;
}

.members-grid {
    margin-top: 0.5px;
    gap: 16px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.member-card {
    width: 160px;
    background: #ffffff;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    position: relative;
    transition: box-shadow 0.3s ease;

    &:hover {
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.05);
        border: 1px solid #dddddd;
        .delete-button {
            opacity: 1;
        }
    }
}

.member-card-inner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 32px 48px 16px 48px;
    position: relative;
}

.delete-button {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    background: #f5f5f5;
    border-radius: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 10;

    &:hover {
        background: #eeeeee;
    }
}

.member-avatar-new {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    overflow: hidden;
    background: #45b7d1;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .avatar-text {
        color: #ffffff;
        font-size: 24px;
        font-weight: 700;
        line-height: 37.7px;
        text-align: center;
    }
}

.member-name {
    color: #000000;
    line-height: 22px;
    text-align: center;
    font-size: 14px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    max-width: 100%;
}

.member-status {
    border-radius: 4px;
    padding: 0 12px;
    font-size: 12px;
    line-height: 20px;
    text-align: center;
    min-height: 20px;

    &.joined {
        background: #f6ffed;
        color: #52c41a;
    }

    &.invited {
        background: #e6f4ff;
        color: #1677ff;
    }
}

.color-box {
    background-color: #f7f9fe;
    padding: 10px;
    color: #6b7995;
    margin-top: 10px;
    .member-list {
        margin-left: 30px;
        display: flex;
        gap: 30px;
        flex-wrap: wrap;
        .member {
            // border: 1px solid #bec5d7;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            .member-avatar {
                width: 50px;
                height: 50px;
                position: relative;

                img {
                    width: 100%;
                    height: 100%;
                }

                .delete-badge {
                    position: absolute;
                    top: -5px;
                    right: -5px;
                    width: 16px;
                    height: 16px;
                    cursor: pointer;
                    z-index: 2;
                }

                &.edit-mode::after {
                    content: "";
                    position: absolute;
                    top: -5px;
                    right: -5px;
                    width: 16px;
                    height: 16px;
                    background-color: #ff4d4f;
                    border-radius: 50%;
                }

                &.edit-mode::before {
                    content: "";
                    position: absolute;
                    top: 2px;
                    right: -1px;
                    width: 8px;
                    height: 2px;
                    background-color: white;
                    z-index: 1;
                }
            }
        }
    }
}

.detail-content {
    height: 100%;
    overflow: hidden;
    padding: 20px;
    .title {
        height: 30px;

        font-size: 24px;
        font-weight: 700;
        letter-spacing: 0.1em;
        color: #23346d;
        margin-bottom: 10px;
    }

    .content {
        height: 100%;
        :global(.el-tabs__content) {
            overflow: scroll;
        }
        /* height: calc(100vh - 300px); */
        /* overflow-y: auto; */
    }
}
</style>
