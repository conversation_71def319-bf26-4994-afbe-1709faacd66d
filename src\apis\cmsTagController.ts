/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 删除标签 POST /cms/cmsTag/delete/${param0} */
export async function cmsTagDeleteId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsTagDeleteidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultVoid_>(`/cms/cmsTag/delete/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 根据标签id获取标签详情 GET /cms/cmsTag/getById */
export async function cmsTagGetById({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsTagGetByIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCmsTagResp_>('/cms/cmsTag/getById', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取标签树列表 GET /cms/cmsTag/getTagList */
export async function cmsTagGetTagList({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsTagNode_>('/cms/cmsTag/getTagList', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 根据标签id获取标签的树状结构 GET /cms/cmsTag/getTagTreeById */
export async function cmsTagGetTagTreeById({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsTagGetTagTreeByIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCmsTagNode_>('/cms/cmsTag/getTagTreeById', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 添加标签信息 POST /cms/cmsTag/save */
export async function cmsTagSave({
  body,
  options,
}: {
  body: API.CmsTagParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/cms/cmsTag/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改标签信息 POST /cms/cmsTag/update */
export async function cmsTagUpdate({
  body,
  options,
}: {
  body: API.CmsTagUpdateParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListString_>('/cms/cmsTag/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
