/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 通过 POST /cms/cmsCheck/agree */
export async function cmsCheckAgree({
  body,
  options,
}: {
  body: API.CmsCheckAgreeParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/cms/cmsCheck/agree', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询根据状态查询个人审批 一般查询需要待审批 GET /cms/cmsCheck/getByStatus */
export async function cmsCheckGetByStatus({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCheckGetByStatusParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageCmsCheck_>(
    '/cms/cmsCheck/getByStatus',
    {
      method: 'GET',
      params: {
        // status has a default value: 0
        status: '0',
        // types has a default value: 0
        types: '0',
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 查询行数据审批记录 GET /cms/cmsCheck/getRecord */
export async function cmsCheckGetRecord({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCheckGetRecordParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListCmsCheck_>('/cms/cmsCheck/getRecord', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询待重新赋分列表 GET /cms/cmsCheck/getToBeReassignedList */
export async function cmsCheckGetToBeReassignedList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCheckGetToBeReassignedListParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageCmsToBeReassignedDto_>(
    '/cms/cmsCheck/getToBeReassignedList',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 已读审核数据 只会出现一次，且当前数据在资料库出现造成重复 POST /cms/cmsCheck/read */
export async function cmsCheckRead({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.cmsCheckReadParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/cms/cmsCheck/read', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 拒绝 只需要传row_id, description POST /cms/cmsCheck/reject */
export async function cmsCheckReject({
  body,
  options,
}: {
  body: API.CmsCheck_;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/cms/cmsCheck/reject', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分发审核 只需要传check_id(分配给谁)， row_id POST /cms/cmsCheck/send */
export async function cmsCheckSend({
  body,
  options,
}: {
  body: API.CmsCheck_;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/cms/cmsCheck/send', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 退回审核 只需要传row_id POST /cms/cmsCheck/sendBack */
export async function cmsCheckSendBack({
  body,
  options,
}: {
  body: API.CmsCheck_;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/cms/cmsCheck/sendBack', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
