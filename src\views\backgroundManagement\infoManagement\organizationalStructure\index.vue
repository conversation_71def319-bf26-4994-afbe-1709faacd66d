<template>
    <div class="organizational-structure">
        <div class="header">
            <div>信息工程学院</div>
            <div><el-button type="primary" @click="handleAddDept(null)">新建组织</el-button></div>
        </div>
        <div class="content">
            <el-table
                height="100%"
                v-loading="loading"
                :data="deptList"
                style="width: 100%"
                row-key="id"
                border
                default-expand-all
                :indent="0">
                <el-table-column prop="name" label="组织架构名称" min-width="200" />
                <el-table-column label="状态" width="120" >
                    <template #default="scope">
                        <el-switch
                            :disabled="scope.row.id === -1"
                            v-model="scope.row.status"
                            inline-prompt
                            class="custom-switch"
                            active-text="启用"
                            inactive-text="禁用"
                            :active-value="1"
                            :inactive-value="0"
                            @change="handleStatusChange(scope.row)" />
                    </template>
                </el-table-column>
                <el-table-column label="操作" >
                    <template #default="scope">
                        <el-button type="primary" text @click="handleEditDept(scope.row)">编辑</el-button>
                        <el-button
                            :disabled="scope.row.id === -1"
                            type="primary"
                            text
                            @click="handleDeleteDept(scope.row)"
                            >删除</el-button
                        >
                        <el-button v-if="scope.row.pid !== -1" type="primary" text @click="handleAddDept(scope.row.id)"
                            >添加子组织</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 添加/编辑部门对话框 -->
        <el-dialog v-model="dialogVisible" :title="dialogTitle" width="500px" append-to-body destroy-on-close>
            <el-form ref="deptFormRef" :model="deptForm" :rules="deptFormRules" label-width="100px" label-position="top">
                <el-form-item label="部门名称" prop="name">
                    <el-input :disabled="deptForm.id === -1" v-model="deptForm.name" placeholder="请输入部门名称" />
                </el-form-item>
                <el-form-item label="显示排序" prop="deptSort">
                    <el-input-number v-model="deptForm.deptSort" :min="0" style="width: 100%" />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group :disabled="deptForm.id === -1" v-model="deptForm.status">
                        <el-radio :label="1">启用</el-radio>
                        <el-radio :label="0">禁用</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" :loading="submitLoading" @click="submitDeptForm">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import { ElMessage, ElMessageBox, FormInstance } from "element-plus";
import { umsDeptListTree, umsDeptCreate, umsDeptUpdate, umsDeptDeleteById } from "@/apis/umsDeptController";
import type { UmsDept_ } from "@/apis/types";
 
// 部门列表数据
const deptList = ref<UmsDept_[]>([]);
const loading = ref(false);

// 获取部门树形数据
const getDeptList = async () => {
    loading.value = true;
    try {
        const res = await umsDeptListTree({});
        if (res.code === 200 && res.data) {
            deptList.value = res.data;
        } else {
            ElMessage.error(res.message || "获取部门列表失败");
        }
    } catch (error) {
        console.error("获取部门列表出错:", error);
        ElMessage.error("获取部门列表出错");
    } finally {
        loading.value = false;
    }
};

// 对话框相关
const dialogVisible = ref(false);
const dialogTitle = ref("");
const deptFormRef = ref<FormInstance>();
const submitLoading = ref(false);
const deptForm = reactive<UmsDept_>({
    name: "",
    pid: 0,
    status: 1,
    deptSort: 999,
});

// 表单验证规则
const deptFormRules = {
    name: [{ required: true, message: "请输入部门名称", trigger: "blur" }],
};

// 重置表单
const resetForm = () => {
    deptForm.id = undefined;
    deptForm.name = "";
    deptForm.pid = 0;
    deptForm.status = 1;
    deptForm.deptSort = 0;
    deptFormRef.value?.resetFields();
};

// 处理添加部门
const handleAddDept = (pid: number | null) => {
    resetForm();
    dialogTitle.value = "添加部门";
    if (pid !== null) {
        deptForm.pid = pid;
    }
    dialogVisible.value = true;
};

// 处理编辑部门
const handleEditDept = (row: UmsDept_) => {
    resetForm();
    dialogTitle.value = "编辑部门";
    Object.assign(deptForm, row);
    dialogVisible.value = true;
};

// 处理删除部门
const handleDeleteDept = (row: UmsDept_) => {
    ElMessageBox.confirm(`确定要删除部门【${row.name}】吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    })
        .then(async () => {
            try {
                const res = await umsDeptDeleteById({ params: { id: row.id! } });
                if (res.code === 200) {
                    ElMessage.success("删除成功");
                    getDeptList();
                } else {
                    ElMessage.error(res.message || "删除失败");
                }
            } catch (error) {
                console.error("删除部门出错:", error);
                ElMessage.error("删除部门出错");
            }
        })
        .catch(() => {});
};

// 处理状态变更
const handleStatusChange = async (row: UmsDept_) => {
    try {
        const res = await umsDeptUpdate({
            body: {
                id: row.id,
                status: row.status,
            },
        });
        if (res.code === 200) {
            ElMessage.success(`${row.status === 1 ? "启用" : "禁用"}成功`);
        } else {
            // 恢复原状态
            row.status = row.status === 1 ? 0 : 1;
            ElMessage.error(res.message || "操作失败");
        }
    } catch (error) {
        // 恢复原状态
        row.status = row.status === 1 ? 0 : 1;
        console.error("更新部门状态出错:", error);
        ElMessage.error("更新部门状态出错");
    }
};

// 提交表单
const submitDeptForm = async () => {
    if (!deptFormRef.value) return;
    try {
        await deptFormRef.value.validate();
        submitLoading.value = true;

        if (deptForm.id) {
            // 编辑部门
            const res = await umsDeptUpdate({ body: deptForm });
            if (res.code === 200) {
                ElMessage.success("更新成功");
                dialogVisible.value = false;
                getDeptList();
            } else {
                ElMessage.error(res.message || "更新失败");
            }
        } else {
            // 添加部门
            const res = await umsDeptCreate({ body: deptForm });
            if (res.code === 200) {
                ElMessage.success("添加成功");
                dialogVisible.value = false;
                getDeptList();
            } else {
                ElMessage.error(res.message || "添加失败");
            }
        }
    } catch (error) {
        console.error("表单提交出错:", error);
    } finally {
        submitLoading.value = false;
    }
};

onMounted(() => {
    getDeptList();
});
</script>

<style scoped lang="scss">
:deep(.el-button + .el-button) {
    margin-left: 0;
}

.custom-switch {
    scale: 1.1;
}

.organizational-structure {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;

    .header {
        background-color: #fff;
        display: flex;
        padding: 16px 32px;
        gap: 10px;
        align-items: center;
        justify-content: space-between;
        div:nth-child(1) {
            color: #23346d;
            font-size: 20px;
            font-weight: 700;
            letter-spacing: 1px;
        }
    }

    .content {
        border-radius: 4px;
        height: 100%;
        position: relative;
        :deep(.el-table) {
            position: absolute;
            th {
                background-color: #edf1fa !important;
                color: rgba(0, 0, 0, 0.88);
                font-weight: 500;
            }
            .el-table__inner-wrapper:after {
                width: 0;
            }
        }
    }
}
</style>
