<template>
    <div class="entrying-content" v-loading="loading">
        <div v-if="templateDetail?.id" class="right-scroll">
            <div class="header-bar">
                <div>
                    {{ templateDetail.templateName }}
                    <el-button
                        @click="handleShowPerformanceRules(templateDetail.id)"
                        v-if="templateDetail.templateType != categoryTemplateType['院情数据']"
                        type="primary"
                        style="margin-left: 15px"
                        >查看已关联绩效规则</el-button
                    >
                </div>
                <div v-if="isShowBackButton">
                    <el-button @click="handleBack">返回</el-button>
                </div>
            </div>
            <div class="right-content">
                <div v-if="isAutoFetchData && isShowImportHistory">
                    <div class="con-title">
                        <span>导入历史</span>
                    </div>
                    <div class="approval-history">
                        <div class="history-item">
                            <div class="history-label">导入负责人：</div>
                            <div class="history-value">
                                {{ getPersonName(allPersonListWithDisabled, String(selectedTemplate.createdBy)) }}
                            </div>
                        </div>
                        <div class="history-item">
                            <div class="history-label">导入时间：</div>
                            <div class="history-value">
                                {{ dayjs(selectedTemplate.createdAt).format("YYYY/MM/DD") }}
                            </div>
                        </div>
                    </div>
                </div>
                <slot name="customContent"></slot>

                <!-- 录入信息标题 -->
                <div class="section-title">
                    <span>录入信息</span>
                </div>
                <!-- 字段列表容器 -->
                <div class="fields-container">
                    <div
                        v-if="fieldsList && importValueParam.values.length > 0"
                        v-for="(v, index) in fieldsList"
                        :key="v.id"
                        class="field-card">
                        <!-- 字段头部 -->
                        <div class="field-header" :class="{ star: v.isRequired === 1 }">
                            <div class="field-header-content">
                                <!-- 字段名称 -->
                                <div class="field-name" :class="{ 'is-category-public-field': v.categoryPublicType }">
                                    {{ v.value }}
                                </div>
                                <!-- 信息图标 -->
                                <div v-if="v.description" class="info-icon">
                                    <el-tooltip placement="right" color="#fff" :content="v.description">
                                        <div class="info-circle"></div>
                                    </el-tooltip>
                                </div>
                            </div>
                        </div>

                        <!-- 字段内容 -->
                        <div class="field-content">
                            <div class="inputs">
                                <div
                                    v-if="
                                        v.type === categoryTemplateValueType_NEW.STRING ||
                                        v.type === categoryTemplateValueType_NEW.INTEGER ||
                                        v.type === categoryTemplateValueType_NEW.DOUBLE ||
                                        v.type === categoryTemplateValueType_NEW.PROJECT_NAME ||
                                        v.type === categoryTemplateValueType_NEW.STUDENT
                                    "
                                    class="input-container">
                                    <div>
                                        <el-input
                                            :class="{
                                                'empty-value':
                                                    isHighlightEmptyValue && importValueParam.values[index].value == '',
                                            }"
                                            style="width: 25vw"
                                            v-model="importValueParam.values[index].value"
                                            :disabled="isReadOnly"
                                            placeholder="请输入" />
                                    </div>
                                    <div
                                        class="error-description"
                                        v-if="recordList && recordList.length > 0 && getFieldErrorDescription(v.id)">
                                        <strong>审核人批注：</strong>{{ getFieldErrorDescription(v.id) }}
                                    </div>
                                </div>
                                <div v-if="v.type === categoryTemplateValueType_NEW.MONEY" class="input-container">
                                    <div>
                                        <el-input
                                            :class="{
                                                'empty-value':
                                                    isHighlightEmptyValue && importValueParam.values[index].value == '',
                                            }"
                                            :disabled="isReadOnly"
                                            v-model="importValueParam.values[index].value"
                                            placeholder="请输入金额"
                                            :formatter="(value) => `￥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                            :parser="(value) => value.replace(/\￥\s?|(,*)/g, '')" />
                                    </div>
                                    <div
                                        class="error-description"
                                        v-if="recordList && recordList.length > 0 && getFieldErrorDescription(v.id)">
                                        <strong>审核人批注：</strong>{{ getFieldErrorDescription(v.id) }}
                                    </div>
                                </div>
                                <div
                                    v-if="
                                        v.type === categoryTemplateValueType_NEW.DATE ||
                                        v.type === categoryTemplateValueType_NEW.PROJECT_DATE
                                    "
                                    class="input-container">
                                    <div>
                                        <el-date-picker
                                            :disabled="isReadOnly"
                                            :class="{
                                                'empty-value':
                                                    isHighlightEmptyValue && importValueParam.values[index].value == '',
                                            }"
                                            v-model="importValueParam.values[index].value"
                                            value-format="YYYY-MM-DD"
                                            type="date"
                                            placeholder="请选择日期" />
                                    </div>
                                    <div
                                        class="error-description"
                                        v-if="recordList && recordList.length > 0 && getFieldErrorDescription(v.id)">
                                        <strong>审核人批注：</strong>{{ getFieldErrorDescription(v.id) }}
                                    </div>
                                </div>
                                <div v-if="v.type === categoryTemplateValueType_NEW.FILE" class="input-container">
                                    <div>
                                        <el-button
                                            :class="{
                                                'empty-value':
                                                    isHighlightEmptyValue && importValueParam.values[index].value == '',
                                            }"
                                            :disabled="isReadOnly"
                                            type="primary"
                                            @click="openUploadDialog(index)"
                                            style="margin-top: 10px; width: 150px; z-index: 50">
                                            {{ fileListRefs[index].length > 0 ? "文件列表" : "用户未上传文件" }}
                                            <el-icon class="el-icon--right"><Upload /></el-icon>
                                        </el-button>
                                        <div v-if="fileListRefs[index].length > 0" class="file-list">
                                            <el-upload
                                                :on-preview="handlePreview"
                                                class="dataEntry-upload"
                                                :disabled="isReadOnly"
                                                :auto-upload="false"
                                                multiple
                                                v-model:file-list="fileListRefs[index]"
                                                :on-remove="handleFileRemove"
                                                action="">
                                            </el-upload>
                                        </div>
                                    </div>

                                    <!-- 集成上传弹窗 -->
                                    <FileUploadDialog
                                        v-model="uploadDialogVisible[index]"
                                        :title="`选择${v.value}文件`"
                                        :accept="'*'"
                                        :max-size="100"
                                        :max-count="10"
                                        :initial-file-urls="getFileUrlsString(index)"
                                        :selected-files="fileListRefs[index]"
                                        @selected="(files) => handleFilesSelected(files, index)" />

                                    <div
                                        class="error-description"
                                        v-if="recordList && recordList.length > 0 && getFieldErrorDescription(v.id)">
                                        <strong>审核人批注：</strong>{{ getFieldErrorDescription(v.id) }}
                                    </div>
                                </div>
                                <div
                                    v-if="v.type === categoryTemplateValueType_NEW.PERSON_SINGLE"
                                    class="input-container">
                                    <div>
                                        <PersonSelector
                                            v-model="importValueParam.values[index].value"
                                            :is-disabled="isReadOnly"
                                            :out-placeholder="'请选择人员（可输入姓名搜索）'"
                                            :person-list="allPersonList"
                                            :is-filterable="true" />
                                    </div>
                                    <div
                                        class="error-description"
                                        v-if="recordList && recordList.length > 0 && getFieldErrorDescription(v.id)">
                                        <strong>审核人批注：</strong>{{ getFieldErrorDescription(v.id) }}
                                    </div>
                                </div>
                                <div
                                    v-if="
                                        v.type === categoryTemplateValueType_NEW.PERSON_MULTI ||
                                        v.type === categoryTemplateValueType_NEW.PROJECT_PARTICIPATE ||
                                        v.type === categoryTemplateValueType_NEW.PROJECT_MAIN
                                    "
                                    class="input-container">
                                    <div style="z-index: 0">
                                        <MultiPersonSelector
                                            v-model="importValueParam.values[index].value"
                                            :is-disabled="isReadOnly"
                                            :out-placeholder="'选择人员（可输入姓名搜索）'"
                                            :person-list="getFilteredPersonList(index)"
                                            :is-filterable="true" />
                                    </div>
                                    <div
                                        class="error-description"
                                        v-if="recordList && recordList.length > 0 && getFieldErrorDescription(v.id)">
                                        <strong>审核人批注：</strong>{{ getFieldErrorDescription(v.id) }}
                                    </div>
                                </div>
                                <div
                                    v-if="v.type === categoryTemplateValueType_NEW.DEPENDENT_MAJOR"
                                    class="input-container">
                                    <div>
                                        <el-select
                                            :class="{
                                                'empty-value-selector':
                                                    isHighlightEmptyValue && importValueParam.values[index].value == '',
                                            }"
                                            filterable
                                            :disabled="isReadOnly"
                                            multiple
                                            v-model="importValueParam.values[index].value"
                                            style="width: 100%; min-width: 300px"
                                            placeholder="请选择依托专业（多选）">
                                            <el-option
                                                v-for="item in activeDependentMajors"
                                                :key="item.id"
                                                :label="item.name"
                                                :value="item.id" />
                                        </el-select>
                                    </div>
                                    <div
                                        class="error-description"
                                        v-if="recordList && recordList.length > 0 && getFieldErrorDescription(v.id)">
                                        <strong>审核人批注：</strong>{{ getFieldErrorDescription(v.id) }}
                                    </div>
                                </div>
                                <div
                                    v-if="
                                        v.type === categoryTemplateValueType_NEW.ENUM_SINGLE ||
                                        v.type === categoryTemplateValueType_NEW.LEVEL ||
                                        v.type === categoryTemplateValueType_NEW.PROJECT_STATUS
                                    "
                                    class="input-container">
                                    <div>
                                        <el-select
                                            :class="{
                                                'empty-value-selector':
                                                    isHighlightEmptyValue && importValueParam.values[index].value == '',
                                            }"
                                            filterable
                                            :disabled="isReadOnly"
                                            v-model="importValueParam.values[index].value"
                                            style="width: 300px"
                                            placeholder="请选择">
                                            <el-option
                                                v-for="item in v.type === categoryTemplateValueType_NEW.LEVEL
                                                    ? getSortedLevelOptions(v.dictList)
                                                    : v.dictList"
                                                :key="item.id"
                                                :label="item.name"
                                                :value="item.id" />
                                        </el-select>
                                    </div>
                                    <div
                                        class="error-description"
                                        v-if="recordList && recordList.length > 0 && getFieldErrorDescription(v.id)">
                                        <strong>审核人批注：</strong>{{ getFieldErrorDescription(v.id) }}
                                    </div>
                                </div>
                                <div v-if="v.type === categoryTemplateValueType_NEW.ENUM_MULTI" class="input-container">
                                    <div>
                                        <el-select
                                            :class="{
                                                'empty-value-selector':
                                                    isHighlightEmptyValue && importValueParam.values[index].value == '',
                                            }"
                                            filterable
                                            :disabled="isReadOnly"
                                            multiple
                                            v-model="importValueParam.values[index].value"
                                            style="width: 300px"
                                            placeholder="请选择">
                                            <el-option
                                                v-for="item in v.dictList"
                                                :key="item.id"
                                                :label="item.name"
                                                :value="item.id" />
                                        </el-select>
                                    </div>
                                    <div
                                        class="error-description"
                                        v-if="recordList && recordList.length > 0 && getFieldErrorDescription(v.id)">
                                        <strong>审核人批注：</strong>{{ getFieldErrorDescription(v.id) }}
                                    </div>
                                </div>
                                <div v-if="v.type === categoryTemplateValueType_NEW.ISN" class="input-container">
                                    <div>
                                        <el-radio-group
                                            :disabled="isReadOnly"
                                            v-model="importValueParam.values[index].value">
                                            <el-radio value="1" size="large">是</el-radio>
                                            <el-radio value="0" size="large">否</el-radio>
                                        </el-radio-group>
                                    </div>
                                    <div
                                        class="error-description"
                                        v-if="recordList && recordList.length > 0 && getFieldErrorDescription(v.id)">
                                        <strong>审核人批注：</strong>{{ getFieldErrorDescription(v.id) }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else>
                        <el-empty description="暂无数据项" />
                    </div>
                </div>

                <!-- 绩效评分标题 -->
                <div class="section-title" style="margin-top: 20px" v-if="isSowPerformanceRating && !isReadOnly">
                    <span>绩效评分</span>
                </div>
                <div v-if="isSowPerformanceRating && !isReadOnly" class="performance-rating-container">
                    <div class="performance-header">
                        <div class="header-text">分配得分比例：</div>
                        <div class="help-icon">
                            <span>?</span>
                        </div>
                    </div>

                    <div class="performance-table-container">
                        <div class="table-header">
                            <div class="header-cell">角色</div>
                            <div class="header-cell">姓名</div>
                            <div class="header-cell">分配得分比例</div>
                        </div>

                        <div v-if="performanceRatingData.length === 0" class="empty-data">
                            <div class="empty-row">
                                <div class="data-cell">-</div>
                                <div class="data-cell">请先选择人员！</div>
                                <div class="data-cell">-</div>
                            </div>
                        </div>

                        <div v-else>
                            <div v-for="(row, index) in performanceRatingData" :key="index" class="data-row">
                                <div class="data-cell">{{ row.role }}</div>
                                <div class="data-cell">{{ row.name }}</div>
                                <div class="data-cell input-cell">
                                    <div class="input-wrapper">
                                        <el-input-number
                                            :class="
                                                Math.abs(totalScorePercent - 100) > 0.01
                                                    ? 'total-score-input-alert'
                                                    : ''
                                            "
                                            v-model="row.proportion"
                                            :min="0"
                                            :max="getMaxAvailableScore(row)"
                                            :precision="0"
                                            controls-position="right"
                                            placeholder="请输入百分比"
                                            class="performance-input">
                                        </el-input-number>
                                        <div class="percent-suffix">%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="total-display">
                        <span class="total-text">当前总计: {{ totalScorePercent }}%</span>
                    </div>

                    <div v-if="Math.abs(totalScorePercent - 100) > 0.01" class="warning-alert">
                        <div class="warning-icon"><img src="@/assets/dialog_icons/warning.png" alt="warning" /></div>
                        <span>分配比例总和必须等于100%</span>
                    </div>
                    <div v-else class="success-alert">
                        <div class="success-icon"><img src="@/assets/dialog_icons/success.png" alt="success" /></div>
                        <span>分配比例总和已等于100%</span>
                    </div>
                </div>

                <PerformanceScore
                    v-if="isShowPerformanceRating"
                    :detail-and-score="detailAndScore"
                    :is-already-passed="true" />

                <div style="display: flex; justify-content: center; margin-top: 20px">
                    <el-tooltip
                        v-if="!isAllRequiredFieldsFilled && !isReadOnly"
                        content="请完整填写所有必填数据项！"
                        placement="top">
                        <el-button disabled @click="handleSubmit" color="#D9D9D9" style="color: #fff" type="primary"
                            >提交录入</el-button
                        >
                    </el-tooltip>
                    <el-button
                        v-else-if="!isReadOnly"
                        @click="handleSubmit"
                        color="#1677FF"
                        style="color: #fff"
                        type="primary"
                        >提交录入</el-button
                    >
                </div>
            </div>
        </div>
        <div v-else>
            <el-empty description="请在左侧选择内容" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { handlePreview } from "@/utils/fileDownload";
import PerformanceScore from "@/components/PerformanceScore/index.vue";
import { getPersonName } from "@/utils/getNames";
import { ref, computed, watch } from "vue";
import { ElMessage, ElMessageBox, ElLoading, UploadProps, dayjs } from "element-plus";
import { Upload } from "@element-plus/icons-vue";
import PersonSelector from "@/components/SinglePersonSelector/index.vue";
import MultiPersonSelector from "@/components/MultiPersonSelector/index.vue";
import FileUploadDialog from "@/components/FileUploadDialog/index.vue";
import { categoryTemplateValueType_NEW } from "@/enums/categoryTemplate/categoryTemplateValueType";
import { categoryTemplateType } from "@/enums/categoryTemplate/categoryTemplateType";
import { publicPerformanceType } from "@/models/publicPerformanceType";
import { tagAllocateType } from "@/enums/tag/tagConfig";
import { useDataEntryStore } from "@/store/modules/dataEntry";
import { cmsCategoryTemplateGetById } from "@/apis/cmsCategoryTemplateController";
import { cmsCategoryTemplateValueGetValueByTemplateId } from "@/apis/cmsCategoryTemplateValueController";
import {
    cmsTagConfigGetDetailAndScoreDataId,
    cmsTagConfigGetTagConfigByCategoryId,
} from "@/apis/cmsTagConfigController";
import { cmsTagCategoryEntityGetTagTreeListByEntityId } from "@/apis/cmsTagCategoryEntityController";
import { cmsCategoryRowConstraintCreateOrUpdate, cmsCategoryRowCreateOrUpdate } from "@/apis/cmsCategoryRowController";
import { minioUploads } from "@/apis/fmsMinioController";
import { cmsCategoryRowGetById } from "@/apis/cmsCategoryRowController";
import type {
    CmsCategoryTemplate_,
    CmsCategoryTemplateValue10,
    CmsTagConfigResp,
    OmsOrganization_,
    UmsPerson_,
    CmsTag_,
    CmsCheck_,
    CmsTagDetailAndScoreDto,
} from "@/apis/types";
import type { FormRules, UploadFiles, UploadUserFile } from "element-plus";
import { usePersons } from "@/hooks/usePersons";
import { useDependentMajors } from "@/hooks/useDependentMajors";
import { getSortedLevelOptions } from "@/utils/enums";
import { PermissionEnum } from "@/enums/roles/authorCards";
import auth from "@/plugins/auth";
import { useRouter } from "vue-router";

interface importParam {
    id?: number;
    categoryTemplateId: number;
    properties: {
        id?: number;
        /** 用户id */
        personId?: number;
        /** 人员类型（1：项目负责人，7：参与人员） */
        personType?: number;
        /** 占比 */
        proportion?: number;
        /** 行id */
        rowId?: number;
    }[];
    values: {
        /** 对应表头数据项id */
        categoryTemplateValueId?: number;
        /** 公共数据项code，用于查询 */
        code?: string;
        id?: number;
        /** 行id */
        rowId?: number;
        /** 补充值 */
        tag?: string;
        /** 值 */
        value?: any;
    }[];
}

const router = useRouter();

const props = withDefaults(
    defineProps<{
        /** 选中的模板 */
        selectedTemplate: CmsCategoryTemplate_ | null;
        /** 审核历史 */
        recordList?: CmsCheck_[] | null;
        /** 是否显示返回按钮 */
        isShowBackButton?: boolean;
        /** 行id */
        rowId?: number;
        /** 是否自动获取行数据 */
        isAutoFetchData?: boolean;
        /** 是否只读 */
        isReadOnly?: boolean;
        /** 是否显示导入历史 */
        isShowImportHistory?: boolean;
        /** 是否高亮空值 */
        isHighlightEmptyValue?: boolean;
        /** 是否显示绩效评分 */
        isShowPerformanceRating?: boolean;
        /** 是否强制使用不包含被禁用的人列表 */
        isForcedWithoutDisabled?: boolean;
    }>(),
    {
        /** 审核历史 */
        recordList: null,
        /** 是否显示返回按钮 */
        isShowBackButton: true,
        /** 行id */
        rowId: undefined,
        /** 是否自动获取行数据 */
        isAutoFetchData: false,
        /** 是否只读 */
        isReadOnly: false,
        /** 是否显示导入历史 */
        isShowImportHistory: false,
        /** 是否高亮空值 */
        isHighlightEmptyValue: false,
        /** 是否显示绩效评分 */
        isShowPerformanceRating: false,
        /** 是否强制使用不包含被禁用的人列表 */
        isForcedWithoutDisabled: false,
    },
);

const emit = defineEmits<{
    (e: "back"): void;
    (e: "showPerformanceRules", templateId: number): void;
    (e: "refresh"): void;
}>();

// 是否正在加载
const loading = ref(false);

// 数据录入状态
const dataEntryStore = useDataEntryStore();

// 全部人员列表
// 当自动获取数据时，获取所有人员列表(包含已被禁用的人员)
// 当强制使用不包含被禁用的人员列表时，则获取人员列表(不包含已被禁用的人员)
const { allPersonList, allPersonListWithDisabled } = usePersons(
    props.isAutoFetchData && !props.isForcedWithoutDisabled,
);

// 依托专业列表
const { activeDependentMajors } = useDependentMajors();

// 大类数据项列表
const fieldsList = ref<CmsCategoryTemplateValue10[]>([]);

// 大类详情
const templateDetail = ref<CmsCategoryTemplate_>({} as CmsCategoryTemplate_);

// 导入数据参数
const importValueParam = ref<importParam>({
    categoryTemplateId: -1,
    properties: [],
    values: [],
});

// 文件列表引用
const fileListRefs = ref<UploadFiles[]>([]);

// 标签配置列表
const tagConfigList = ref<CmsTagConfigResp[]>([]);

// 已关联绩效规则列表
const performanceRulesList = ref<CmsTag_[]>([]);

// 是否显示绩效评分
const isSowPerformanceRating = ref(false);

// 绩效评分数据
const performanceRatingData = ref<
    { role: string; name: string; proportion: number; personId: number; personType: 1 | 7 }[]
>([]);

// 弹窗显示状态数组
const uploadDialogVisible = ref<boolean[]>([]);

// 返回
const handleBack = () => {
    // 重置文件列表
    fileListRefs.value = [];
    dataEntryStore.setEnteringData(false);
    emit("back");

    // 延迟重置数据，确保页面跳转完成后再清空
    setTimeout(() => {
        // 重置文件列表
        fileListRefs.value = [];
        // 重置所有数据
        importValueParam.value = {
            categoryTemplateId: -1,
            properties: [],
            values: [],
        };
    }, 100);
};

// 显示绩效规则弹窗
const handleShowPerformanceRules = (templateId: number) => {
    emit("showPerformanceRules", templateId);
};

// 判断必填数据项是否已全部填完
const isAllRequiredFieldsFilled = computed(() => {
    let flag = true;
    for (let i = 0; i < fieldsList.value.length; i++) {
        // 如果是必填数据项，且不是文件类型
        if (fieldsList.value[i].isRequired === 1 && fieldsList.value[i].type != categoryTemplateValueType_NEW.FILE) {
            if (importValueParam.value.values[i].value === "" || importValueParam.value.values[i].value === null) {
                flag = false;
            }
        }
        // 如果是必填数据项，且是文件类型
        else if (
            fieldsList.value[i].isRequired === 1 &&
            fieldsList.value[i].type === categoryTemplateValueType_NEW.FILE
        ) {
            if (fileListRefs.value[i].length === 0) {
                flag = false;
            }
        }
    }
    return flag;
});

// 提交录入
const handleSubmit = async () => {
    // 如果需要绩效评分但总分不等于 100%，阻止提交
    if (isSowPerformanceRating.value && Math.abs(totalScorePercent.value - 100) > 0.01) {
        ElMessage.error("绩效评分的总和必须等于100%");
        return;
    }

    // 弹窗提示
    const confirm = await ElMessageBox.confirm("确定要提交录入吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info",
    });

    if (confirm != "confirm") {
        return;
    }

    // 创建 loading 实例
    const loading = ElLoading.service({
        lock: true,
        text: "正在提交录入数据，请稍后...",
        background: "rgba(237, 243, 255, 0.7)",
    });

    try {
        // 先保存一份绩效评分数据的副本
        const performanceDataCopy = JSON.parse(JSON.stringify(performanceRatingData.value));

        // 转换数组类型的值为字符串
        for (const element of importValueParam.value.values) {
            if (Array.isArray(element.value)) {
                element.value = element.value.toString();
            }
        }

        // 收集所有需要上传的文件和它们对应的数据项索引
        const allFiles: { file: any; fieldIndex: number }[] = [];
        // 用于存储每个数据项的上传结果
        const fieldUrls: { [key: number]: string[] } = {};

        // 如果是自动获取数据模式，需要区分已上传文件和新增文件
        if (props.isAutoFetchData) {
            // 遍历所有数据项的文件列表
            for (let i = 0; i < fileListRefs.value.length; i++) {
                if (fileListRefs.value[i].length > 0) {
                    // 初始化该字段的URL数组
                    fieldUrls[i] = [];

                    // 遍历该字段的所有文件
                    (fileListRefs.value[i] as unknown as any[]).forEach((item) => {
                        // 如果是已上传文件(status为success)，直接使用其URL
                        if (item.status === "success") {
                            fieldUrls[i].push(item.url);
                        }
                        // 如果是新增文件(status为ready)，添加到待上传列表
                        else if (item.raw) {
                            allFiles.push({
                                file: item.raw,
                                fieldIndex: i,
                            });
                        }
                    });
                }
            }
        } else {
            // 原有逻辑：遍历所有数据项的文件
            for (let i = 0; i < fileListRefs.value.length; i++) {
                if (fileListRefs.value[i].length > 0) {
                    // 将每个数据项的所有文件添加到总文件列表中
                    (fileListRefs.value[i] as unknown as any[]).forEach((item) => {
                        allFiles.push({
                            file: item.raw,
                            fieldIndex: i,
                        });
                    });
                }
            }
        }

        // 如果有新文件需要上传
        if (allFiles.length > 0) {
            // 分批上传文件，每批3个文件
            const batchSize = 3;
            for (let i = 0; i < allFiles.length; i += batchSize) {
                const batch = allFiles.slice(i, i + batchSize);
                const formData = new FormData();

                // 将这一批的文件添加到 FormData
                batch.forEach((item) => {
                    formData.append("files", item.file);
                });

                // 上传这一批文件
                const res = await minioUploads({
                    body: formData as any,
                    options: {
                        headers: {
                            "Content-Type": "multipart/form-data",
                        },
                    },
                });

                // 处理返回的URL，将它们添加到对应数据项的结果中
                res.data.forEach((urlData, index) => {
                    const fieldIndex = batch[index].fieldIndex;
                    if (!fieldUrls[fieldIndex]) {
                        fieldUrls[fieldIndex] = [];
                    }
                    fieldUrls[fieldIndex].push(urlData.url);
                });

                // 更新上传进度
                const progress = Math.min(((i + batchSize) / allFiles.length) * 100, 100);
                loading.setText(`已完成 ${progress.toFixed(0)}% 的文件上传...`);
            }
        }

        // 将收集到的URL设置到对应的数据项中
        for (const fieldIndex in fieldUrls) {
            importValueParam.value.values[fieldIndex].value = fieldUrls[fieldIndex].join(",");
        }

        // 使用保存的副本来添加绩效评分数据
        for (const element of performanceDataCopy) {
            importValueParam.value.properties.push({
                personId: element.personId,
                personType: element.personType,
                proportion: element.proportion,
            });
        }

        // 所有文件上传完成后，提交数据
        const res = await cmsCategoryRowCreateOrUpdate({
            body: importValueParam.value,
        });

        if (res.data && res.data.length > 0) {
            // 检测到有重复数据，提示用户是否强制录入？
            ElMessageBox.confirm(
                "当前录入的数据与数据库中的记录高度重复，若您确认表内数据无误，可点击下方确认提交。",
                "提示",
                {
                    confirmButtonText: "确认提交",
                    cancelButtonText: "取消",
                    type: "warning",
                },
            ).then(() => {
                // 强制录入
                cmsCategoryRowConstraintCreateOrUpdate({
                    body: importValueParam.value,
                }).then((res) => {
                    if (res.code === 200) {
                        ElMessage.success("强制录入成功！");

                        // 设置数据录入状态为false
                        dataEntryStore.setEnteringData(false);

                        // 如果是自动获取行数据模式，则刷新页面
                        if (props.isAutoFetchData) {
                            emit("refresh");
                        } else {
                            emit("back");
                        }

                        // 延迟重置数据，确保页面跳转完成后再清空
                        setTimeout(() => {
                            // 重置所有数据
                            importValueParam.value = {
                                categoryTemplateId: -1,
                                properties: [],
                                values: [],
                            };
                            fileListRefs.value = [];
                        }, 100);
                    }
                });
            });
        } else if (res.code === 200) {
            ElMessage.success("录入成功！");

            // 设置数据录入状态为false
            dataEntryStore.setEnteringData(false);

            // 如果是自动获取行数据模式，则刷新页面
            if (props.isAutoFetchData) {
                emit("refresh");
            } else {
                // 根据用户拥有的权限进行跳转
                if (auth.hasPermi(PermissionEnum.TEACHER_ENTRY)) {
                    router.push("/workspace/teacherEntry/entryRecord");
                } else if (auth.hasPermi(PermissionEnum.ADMIN_ENTRY)) {
                    router.push("/workspace/adminEntry/entryRecord");
                }
            }

            // 延迟重置数据，确保页面跳转完成后再清空
            setTimeout(() => {
                // 重置所有数据
                importValueParam.value = {
                    categoryTemplateId: -1,
                    properties: [],
                    values: [],
                };
                fileListRefs.value = [];
            }, 100);
        }
    } catch (error) {
        // 清空绩效评分部分数据
        importValueParam.value.properties = [];
    } finally {
        loading.close();
    }
};

// 文件移除处理函数
const handleFileRemove: UploadProps["onRemove"] = (file) => {
    // 找到文件所在的列表
    for (let i = 0; i < fileListRefs.value.length; i++) {
        const index = fileListRefs.value[i].indexOf(file);
        if (index !== -1) {
            fileListRefs.value[i].splice(index, 1);
            return;
        }
    }
};

// 初始化弹窗状态
watch(
    () => fieldsList.value,
    (newFields) => {
        if (newFields) {
            uploadDialogVisible.value = new Array(newFields.length).fill(false);
        }
    },
    { immediate: true },
);

// 获取文件URL字符串
function getFileUrlsString(index: number): string {
    if (!fileListRefs.value[index]) return "";

    return fileListRefs.value[index]
        .filter((file: any) => file.url)
        .map((file: any) => file.url)
        .join(",");
}

// 获取过滤后的人员列表（排除了已在其他多选人员字段中选择的人员）
function getFilteredPersonList(currentFieldIndex: number) {
    let sourceList = props.isAutoFetchData ? allPersonListWithDisabled.value : allPersonList.value;

    // 如果开启强制获取少数人员列表，则使用不包含被禁用的人员列表
    if (props.isForcedWithoutDisabled) {
        sourceList = allPersonList.value;
    }

    if (!sourceList || sourceList.length === 0) return [];

    // 检查当前字段的类型是否为项目负责人或参与人
    const currentPublicType = fieldsList.value[currentFieldIndex]?.categoryPublicType;

    // 如果不是项目负责人(1)或参与人(7)类型，直接返回原始列表
    if (
        currentPublicType !== publicPerformanceType.PROJECT_MAIN &&
        currentPublicType !== publicPerformanceType.PROJECT_PARTICIPATE
    ) {
        return sourceList;
    }

    // 收集所有已选择的人员ID（排除当前字段）
    const selectedPersonIds: (number | string)[] = [];

    importValueParam.value.values.forEach((valueItem, fieldIndex) => {
        // 跳过当前字段和非多选人员类型字段
        if (fieldIndex === currentFieldIndex || !fieldsList.value[fieldIndex]) return;

        // 只考虑项目负责人和参与人字段类型
        const fieldPublicType = fieldsList.value[fieldIndex].categoryPublicType;
        if (
            fieldPublicType !== publicPerformanceType.PROJECT_MAIN &&
            fieldPublicType !== publicPerformanceType.PROJECT_PARTICIPATE
        )
            return;

        // 将已选择的人员ID添加到数组中
        if (valueItem.value) {
            const ids = Array.isArray(valueItem.value)
                ? valueItem.value
                : valueItem.value.toString().split(",").filter(Boolean);

            ids.forEach((id) => {
                if (id) selectedPersonIds.push(typeof id === "string" ? Number(id) : id);
            });
        }
    });

    // 返回过滤后的列表
    return sourceList.filter((person) => !selectedPersonIds.includes(person.id));
}

// 根据字段ID获取错误描述
function getFieldErrorDescription(fieldId: number): string {
    if (!props.recordList || props.recordList.length === 0) return "";

    // 获取最新的审核记录
    const latestRecord = props.recordList[props.recordList.length - 1];

    try {
        // 解析JSON字符串
        const descriptions = JSON.parse(latestRecord.description || "[]");

        // 查找当前字段的错误描述
        const fieldError = descriptions.find((item: any) => Number(item.fieldId) === fieldId);

        // 返回错误描述或空字符串
        return fieldError?.description || "";
    } catch (error) {
        console.error("解析错误描述失败:", error);
        return "";
    }
}

// 打开上传弹窗
function openUploadDialog(index: number) {
    uploadDialogVisible.value[index] = true;
}

// 处理弹窗选择完成事件
function handleFilesSelected(files: UploadUserFile[], index: number) {
    if (!files || files.length === 0) return;

    // 已有URL文件（status为success的文件）
    const existingUrlFiles = files.filter((file) => file.url && file.status === "success");

    // 新选择的文件（有raw属性的文件）
    const newFiles = files.filter((file) => file.raw);

    // 创建新的文件列表
    const newFileList = [...existingUrlFiles, ...newFiles];

    // 更新文件列表
    fileListRefs.value[index] = newFileList as any;

    // 触发响应式更新
    importValueParam.value = { ...importValueParam.value };
}

// 添加计算属性检查分数总和
const totalScorePercent = computed(() => {
    return performanceRatingData.value.reduce((sum, item) => sum + (item.proportion || 0), 0);
});

// 添加一个计算属性来获取剩余可分配的分数
const getMaxAvailableScore = computed(() => (currentRow: (typeof performanceRatingData.value)[0]) => {
    const currentTotal = performanceRatingData.value.reduce((sum, item) => {
        // 不计算当前行的分数
        if (item === currentRow) {
            return sum;
        }
        return sum + (item.proportion || 0);
    }, 0);

    // 返回剩余可分配的分数
    return 100 - currentTotal;
});

// 监听人员数据项的变化并更新绩效评分表格数据
watch(
    () => importValueParam.value.values,
    (newValues) => {
        // 如果是在提交过程中，不执行重置操作
        if (!isSowPerformanceRating.value || importValueParam.value.properties.length > 0) return;

        const performanceData: {
            role: string;
            name: string;
            proportion: number;
            personId: number;
            personType: 1 | 7;
        }[] = [];

        // 遍历所有数据项
        fieldsList.value.forEach((field, index) => {
            // 检查是否是参与人或负责人数据项
            if (
                field.categoryPublicType === publicPerformanceType.PROJECT_MAIN ||
                field.categoryPublicType === publicPerformanceType.PROJECT_PARTICIPATE
            ) {
                // 先检查newValues[index]是否存在
                if (!newValues[index]) return;

                const value = newValues[index].value;
                if (!value) return;

                // 将字符串转换为数组
                const personIds = Array.isArray(value) ? value : value.split(",").filter(Boolean);

                // 为每个选中的人员创建表格行数据
                personIds.forEach((personId) => {
                    // 查找人员，不受过滤影响
                    let person: UmsPerson_ | undefined;
                    let sourceList = props.isAutoFetchData ? allPersonListWithDisabled.value : allPersonList.value;
                    // 确保使用正确的人员列表
                    if (props.isForcedWithoutDisabled) {
                        sourceList = allPersonList.value;
                    }

                    person = sourceList.find((p) => p.id === Number(personId));

                    if (person) {
                        performanceData.push({
                            role: field.value,
                            name: person.employeeName,
                            proportion: 0,
                            personId: person.id,
                            personType: field.categoryPublicType as 1 | 7,
                        });
                    }
                });
            }
        });

        // 更新表格数据
        performanceRatingData.value = performanceData;
    },
    { deep: true },
);

// 监听大类详情变化
watch(
    templateDetail,
    (newVal) => {
        if (newVal.id != -1 && newVal.id != null) {
            if (newVal.templateType === categoryTemplateType["院情数据"]) {
                isSowPerformanceRating.value = false;
                return;
            } else {
                isSowPerformanceRating.value = true;
            }
            // 获取标签配置
            cmsTagConfigGetTagConfigByCategoryId({ params: { categoryId: newVal.id } }).then((res) => {
                if (res.data.length < 1) {
                    isSowPerformanceRating.value = false;
                    return;
                }
                tagConfigList.value = res.data;
                tagConfigList.value.forEach((v) => {
                    if (v.allocateType === tagAllocateType.PROJECT_SCORE) {
                        // 如果分配模式为项目制记分，且赋分方式为平台计算，则显示绩效评分
                        isSowPerformanceRating.value = true;
                        return;
                    } else {
                        isSowPerformanceRating.value = false;
                    }
                });
            });
            // 获取标签树结构
            cmsTagCategoryEntityGetTagTreeListByEntityId({ params: { categoryEntityId: newVal.id } }).then((res) => {
                performanceRulesList.value = res.data;
            });
        }
    },
    { deep: true },
);

// 从URL创建文件对象，用于回显已上传的文件
function mockFileFromUrl(url: string): Promise<any> {
    return new Promise((resolve) => {
        // 从URL中提取文件名
        const fileName = url.substring(url.lastIndexOf("/") + 1);

        // 创建一个符合UploadFile结构的对象
        const mockFile = {
            name: decodeURIComponent(fileName),
            url: url,
            status: "success",
        };

        resolve(mockFile);
    });
}

// 获取行数据并回显
async function fetchRowData() {
    if (!props.rowId || !props.isAutoFetchData) return;

    try {
        loading.value = true;
        // 重置所有表单字段的值
        importValueParam.value.values.forEach((item) => {
            item.value = "";
            item.tag = "";
        });

        const res = await cmsCategoryRowGetById({ params: { id: props.rowId } });

        if (res.code === 200 && res.data) {
            // 将获取到的数据设置到表单中
            const rowData = res.data;

            // 设置行id
            importValueParam.value.id = rowData.id;

            // 确保已经加载了数据项列表
            if (fieldsList.value.length > 0 && importValueParam.value.values.length > 0) {
                // 遍历行数据中的values，填充到importValueParam中
                rowData.values?.forEach((rowValue) => {
                    const index = importValueParam.value.values.findIndex(
                        (v) => v.categoryTemplateValueId === rowValue.categoryTemplateValueId,
                    );

                    if (index > -1) {
                        const field = fieldsList.value[index];
                        let processedValue: any = rowValue.value;

                        // 处理文件类型的字段，将URL字符串转换为文件列表
                        if (field.type === categoryTemplateValueType_NEW.FILE && processedValue) {
                            // 保存原始URL到value中
                            importValueParam.value.values[index].value = processedValue;

                            // 处理文件回显
                            const urls = processedValue.split(",").filter(Boolean);

                            if (urls.length > 0) {
                                // 为每个URL创建模拟文件对象
                                Promise.all(urls.map((url) => mockFileFromUrl(url))).then((mockFiles) => {
                                    // 更新文件列表引用
                                    fileListRefs.value[index] = mockFiles;
                                });
                            }
                        }
                        // 处理多选类型的数据项，将字符串转换为数组
                        else if (
                            field.type === categoryTemplateValueType_NEW.PERSON_MULTI ||
                            field.type === categoryTemplateValueType_NEW.ENUM_MULTI ||
                            field.type === categoryTemplateValueType_NEW.DEPENDENT_MAJOR ||
                            field.type === categoryTemplateValueType_NEW.PROJECT_PARTICIPATE ||
                            field.type === categoryTemplateValueType_NEW.PROJECT_MAIN
                        ) {
                            // 如果值不为空，则将逗号分隔的字符串转换为数组
                            if (processedValue && typeof processedValue === "string") {
                                processedValue = processedValue
                                    .split(",")
                                    .filter(Boolean)
                                    .map((id) => {
                                        // 对于ENUM_MULTI，需要根据字典列表判断ID类型
                                        if (field.type === categoryTemplateValueType_NEW.ENUM_MULTI) {
                                            // 检查字典列表中第一项的ID类型，如果是数字则转换为数字类型
                                            if (field.dictList && field.dictList.length > 0) {
                                                const firstDictItem = field.dictList[0];
                                                if (typeof firstDictItem.id === "number") {
                                                    return Number(id);
                                                }
                                            }
                                            return id; // 如果无法判断，默认保持原样
                                        }
                                        // 对于PERSON_MULTI类型，需要特殊处理非数字ID（如"aaa"）
                                        if (
                                            field.type === categoryTemplateValueType_NEW.PERSON_MULTI ||
                                            field.type === categoryTemplateValueType_NEW.PROJECT_PARTICIPATE ||
                                            field.type === categoryTemplateValueType_NEW.PROJECT_MAIN
                                        ) {
                                            // 如果可以转为数字且不是NaN，则转为数字；否则保留原始字符串
                                            const numId = Number(id);
                                            return isNaN(numId) ? id : numId;
                                        }
                                        // 其他类型转为数字
                                        return Number(id);
                                    });
                            } else if (!processedValue) {
                                processedValue = [];
                            }
                        }
                        // 处理单选类型的数据项，确保类型正确
                        else if (field.type === categoryTemplateValueType_NEW.PERSON_SINGLE) {
                            // 对于单选人员，确保是数字类型
                            processedValue = processedValue ? Number(processedValue) : "";
                        }
                        // 处理自定义单选和级别数据项
                        else if (
                            field.type === categoryTemplateValueType_NEW.ENUM_SINGLE ||
                            field.type === categoryTemplateValueType_NEW.LEVEL ||
                            field.type === categoryTemplateValueType_NEW.PROJECT_STATUS
                        ) {
                            // 检查字典项目的ID类型，保持一致
                            if (processedValue && field.dictList && field.dictList.length > 0) {
                                const firstDictItem = field.dictList[0];
                                // 如果字典ID是数字类型，则转换为数字
                                if (typeof firstDictItem.id === "number") {
                                    processedValue = Number(processedValue);
                                }
                                // 否则保持原样(字符串)
                            }
                        }

                        importValueParam.value.values[index].value = processedValue;
                    }
                });

                // 初始化绩效评分数据 - 如果需要
                // 注意：因为API返回的具体结构未知，我们暂时跳过绩效评分的处理
                // 若API有返回绩效评分数据，需要在此处理
            }
        } else {
            ElMessage.warning("获取数据失败或数据不存在");
        }
    } catch (error) {
        console.error("获取行数据失败:", error);
        ElMessage.error("获取数据失败: " + error);
    } finally {
        setTimeout(() => {
            loading.value = false;
        }, 500);
    }
}

// 抽取模板详情获取逻辑为单独函数
async function fetchTemplateDetails(templateId: number) {
    loading.value = true;
    try {
        const [res1, res2] = await Promise.all([
            cmsCategoryTemplateGetById({ params: { id: templateId } }),
            cmsCategoryTemplateValueGetValueByTemplateId({ params: { id: templateId } }),
        ]);

        templateDetail.value = res1.data;
        fieldsList.value = res2.data;

        // 初始化文件列表数组
        fileListRefs.value = new Array(fieldsList.value.length).fill([]);

        importValueParam.value.categoryTemplateId = templateId;
        importValueParam.value.values = fieldsList.value.map((v) => ({
            categoryTemplateValueId: v.id,
            value: "",
            tag: "",
        }));

        if (templateDetail.value.templateType != categoryTemplateType["院情数据"]) {
            // 获取标签配置
            const tagConfigRes = await cmsTagConfigGetTagConfigByCategoryId({ params: { categoryId: templateId } });
            if (tagConfigRes.data.length > 0) {
                tagConfigList.value = tagConfigRes.data;
                tagConfigList.value.forEach((v) => {
                    if (v.allocateType === tagAllocateType.PROJECT_SCORE) {
                        // 如果分配模式为项目制记分，且赋分方式为平台计算，则显示绩效评分
                        isSowPerformanceRating.value = true;
                    }
                });
            }
        }

        // 如果需要自动获取数据且有rowId，获取行数据
        if (props.isAutoFetchData && props.rowId) {
            await fetchRowData();
        }
    } catch (error) {
        console.error("加载模板详情失败:", error);
        ElMessage.error("加载模板详情失败: " + error);
    } finally {
        loading.value = false;
    }
}

// 配置信息和得分信息
const detailAndScore = ref<CmsTagDetailAndScoreDto>({});

// 获取配置信息以及得分信息
const getDetailAndScore = (rowId: number) => {
    // 已录入的数据
    cmsTagConfigGetDetailAndScoreDataId({ params: { id: rowId } }).then((res) => {
        detailAndScore.value = res.data;
    });
};

// 监听选中的模板变化
watch(
    () => props.selectedTemplate?.id,
    async (newTemplateId) => {
        if (newTemplateId) {
            await fetchTemplateDetails(newTemplateId);
            if (props.isShowPerformanceRating) {
                getDetailAndScore(props.rowId);
            }
        }
    },
    { immediate: true },
);

// 监听 rowId 变化
watch(
    () => props.rowId,
    async (newRowId) => {
        if (newRowId && props.isAutoFetchData && fieldsList.value.length > 0) {
            await fetchRowData();
        }
    },
    { immediate: true },
);
</script>

<style scoped lang="scss">
@use "../../assets/styles/templateFieldsDetail.scss";

$padding: 0 30px;

// 每行最低高度
$min-height: 70px;

:global(.dataEntry-upload .el-upload-list.el-upload-list--text li) {
    margin-top: -20px;
    height: 30px;
    display: flex;
    align-items: center;
}

.empty-value-selector {
    border: 1px solid red;
    border-radius: 5px;
}

.empty-value {
    border: 1px solid red;
    border-radius: 5px;
    :deep(.el-input__wrapper) {
        box-shadow: none !important;
    }
    :deep(.el-input__inner) {
        box-shadow: none !important;
    }
}

.total-score-input-alert {
    border: 1px solid red;
    border-radius: 5px;
    :deep(.el-input__wrapper) {
        box-shadow: none !important;
    }
    :deep(.el-input__inner) {
        box-shadow: none !important;
    }
}

.approval-history {
    padding-left: 60px;
    .history-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .history-label {
            color: #23346d;
            font-size: 15px;
            font-weight: bold;
        }

        .history-value {
            color: #23346d;
            font-size: 14px;
        }
    }
}

.star {
    &::before {
        content: "*";
        color: #ff8061;
        font-size: 14px;
        margin-right: 5px;
    }
}

.desc {
    font-size: 14px;
    color: #818181;
}

.entrying-content {
    height: 100%;
    position: relative;
    background: #fff;
    width: 100%;
}

/* 绩效评分容器样式 */
.performance-rating-container {
    background: #f9fbff;
    border-radius: 8px;
    padding: 33px 16px 32px 16px;
    margin: 20px 0;
    width: 100%;

    .performance-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 17px;

        .header-text {
            color: #23346d;
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            width: 97px;
            min-height: 20px;
        }

        .help-icon {
            background: #b9bed1;
            border-radius: 9px;
            width: 18px;
            height: 18px;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0.5px 6px;

            span {
                color: #ffffff;
                font-size: 12px;
                font-weight: 700;
                line-height: 17px;
                text-align: center;
            }
        }
    }

    .performance-table-container {
        background: white;
        border-radius: 8px;
        overflow: hidden;

        .table-header {
            display: flex;
            background: #f6f6f6;

            .header-cell {
                flex: 1;
                padding: 13.5px;
                text-align: center;
                color: rgba(0, 0, 0, 0.88);
                font-weight: 500;
                font-size: 14px;
                line-height: 21px;
                min-height: 21px;
                display: flex;
                justify-content: center;
                align-items: center;

                &:first-child {
                    border-radius: 8px 0 0 0;
                }

                &:last-child {
                    border-radius: 0 8px 0 0;
                }
            }
        }

        .data-row,
        .empty-row {
            display: flex;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
                border-bottom: none;
            }

            .data-cell {
                flex: 1;
                padding: 17px;
                text-align: center;
                background: #ffffff;
                color: rgba(0, 0, 0, 0.88);
                font-size: 14px;
                line-height: 21px;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 22px;

                &.input-cell {
                    padding: 12px 16px;

                    .input-wrapper {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 0;

                        .performance-input {
                            width: 228.5px !important;
                            border-radius: 2px 0 0 2px !important;

                            // 在未触发警告时，添加透明边框保持尺寸一致
                            &:not(.total-score-input-alert) {
                                border: 1px solid transparent !important;
                            }

                            :deep(.el-input__wrapper) {
                                border-radius: 2px 0 0 2px !important;
                                border-right: none !important;
                                border-color: #d9d9d9 !important;
                                padding: 1px 12px !important;
                                min-height: 30px !important;
                            }

                            :deep(.el-input__inner) {
                                color: #bfbfbf;
                                line-height: 22px;
                                text-align: left;
                            }

                            :deep(.el-input-number__increase),
                            :deep(.el-input-number__decrease) {
                                border-left: 1px solid #d9d9d9;
                                background: #ffffff;
                                color: #000;
                            }
                        }

                        .percent-suffix {
                            background: #fafafa;
                            border: 1px solid #d9d9d9;
                            border-left: none;
                            border-radius: 0 2px 2px 0;
                            width: 34px;
                            height: 32px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            color: rgba(0, 0, 0, 0.85);
                            font-size: 14px;
                            line-height: 22px;
                        }
                    }
                }
            }
        }
    }

    .total-display {
        display: flex;
        justify-content: flex-end;
        margin-top: 32px;
        padding-right: 16px;

        .total-text {
            color: #ff4d4f;
            font-weight: 700;
            font-size: 14px;
            line-height: 22px;
            min-height: 22px;
        }
    }

    .warning-alert,
    .success-alert {
        border-radius: 2px;
        padding: 9px 16px;
        margin-top: 32px;
        display: flex;
        align-items: center;
        gap: 8px;

        span {
            color: rgba(0, 0, 0, 0.85);
            font-size: 14px;
            line-height: 22px;
        }
    }

    .warning-alert {
        background: #fffbe6;
        border: 1px solid #ffe58f;
    }

    .success-alert {
        background: #f0f9eb;
        border: 1px solid #b7eb8f;
    }

    .warning-icon,
    .success-icon {
        display: flex;
        justify-content: center;
        align-items: center;

        img {
            width: 14px;
            height: 14px;
        }
    }
}
</style>
