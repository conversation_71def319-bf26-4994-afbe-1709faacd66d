import type { GenerateServiceProps } from "openapi-ts-request";
import { APIDataType } from "openapi-ts-request/dist/generator/type";

function convertApiPath(path) {
    const parts = path.split('/').filter(p => p); // 分割路径并过滤空值
    if (parts.length < 1) return '';
    
    // 移除第一个模块名后处理剩余部分
    const transformed = parts.slice(1)
        .map(segment => 
            segment.charAt(0).toUpperCase() + segment.slice(1)
        )
        .join('');
    
    // 确保首字母小写
    return transformed.charAt(0).toLowerCase() + transformed.slice(1);
}


export default {
    // schemaPath: './openapi.json', // 本地openapi文件
    serversPath: "./src/apis", // 接口存放路径
    schemaPath: "http://*************:8090/v2/api-docs",
    requestLibPath: "@/utils/request.ts",
    hook: {
        customFunctionName(data: APIDataType, prefix: string) {
            return convertApiPath(data.path);
        },
    },
} as GenerateServiceProps;
