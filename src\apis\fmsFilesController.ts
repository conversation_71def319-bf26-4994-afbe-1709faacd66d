/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 删除文件关联 POST /fms/fmsFiles/delete */
export async function fmsFilesDelete({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.fmsFilesDeleteParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/fms/fmsFiles/delete', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页获取文件关联列表 GET /fms/fmsFiles/list */
export async function fmsFilesList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.fmsFilesListParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageFmsFiles_>('/fms/fmsFiles/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 添加或修改文件关联信息 POST /fms/fmsFiles/saveOrUpdate */
export async function fmsFilesSaveOrUpdate({
  body,
  options,
}: {
  body: API.FmsFiles_;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/fms/fmsFiles/saveOrUpdate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
