<template>
    <div class="performance-score">
        <div class="header">
            <div class="header-title">
                {{
                    personId ? getPersonName(allPersonListWithDisabled, personId.toString()) + "的绩效得分" : "我的绩效"
                }}
            </div>
        </div>
        <div class="content" v-loading="loading">
            <div class="content-data-picker">
                <div>
                    <el-button v-if="personId" @click="handleBack">返回</el-button>
                </div>
                <div class="year-picker">
                    选择时间：
                    <el-date-picker
                        format="YYYY年度"
                        :clearable="false"
                        @change="changeYear"
                        style="width: 150px"
                        v-model="nowYear"
                        type="year"
                        placeholder="Pick a year" />
                </div>
            </div>
            <div class="content-data">
                <div class="content-header" v-drag-scroll>
                    <div class="each-item" v-for="(item, index) in overViewData" :key="index">
                        <div class="each-item-content">
                            <div class="left-item">{{ item.name }}</div>
                            <div class="right-item" :class="getScoreColorClass(item.name)">{{ item.score }}</div>
                        </div>
                    </div>
                </div>

                <div class="table">
                    <el-table
                        :header-cell-style="{
                            backgroundColor: '#EDF1FA',
                            color: '#394857',
                            fontSize: '14px',
                            fontWeight: '400',
                            height: '48px',
                        }"
                        :style="{ borderRadius: '6px 6px 0px 0px' }"
                        :cell-style="{ border: 'none', height: '40px' }"
                        :indent="24"
                        cell-class-name="performance-cell"
                        :data="scoreTree"
                        default-expand-all
                        style="width: 100%"
                        height="100%"
                        row-key="id">
                        <el-table-column align="center" width="200" fixed type="" prop="maxScore" label="最终得分">
                            <template #default="scope">
                                <div class="score-box" v-if="scope.row.score === 0">
                                    <div class="score-left" :class="getScoreColorClass(scope.row.tagName)">
                                        {{ scope.row.actualScore }}
                                    </div>
                                    <div class="score-right" v-if="scope.row.maxScore !== null">
                                        /{{ scope.row.maxScore }}
                                    </div>
                                </div>
                                <div style="display: flex; justify-content: end; width: 60%" v-else>
                                    <span style="color: #3b82f6; font-weight: 700"> {{ scope.row.score }}</span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" prop="tagName" label="绩效规则" min-width="500">
                            <template #default="scope">
                                <div :class="scope.row.tagLevel === null ? 'rowData-cell' : 'tagName-cell'">
                                    <div>{{ scope.row.tagName }}</div>
                                    <span v-if="scope.row.tagRules">
                                        <el-tooltip placement="right" popper-class="tag-tooltip" effect="light">
                                            <template #content>
                                                <div class="popper-content">
                                                    <div class="popper-title">绩效规则详情</div>
                                                    <div
                                                        class="popper-text"
                                                        style="margin: 5px 0"
                                                        v-for="(item, i) in scope.row.tagRules"
                                                        :key="i">
                                                        {{ i + 1 }}：{{ item }}
                                                    </div>
                                                </div>
                                            </template>
                                            <div class="flex-center" v-if="scope.row.tagRules">
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                                    width="16"
                                                    height="16"
                                                    viewBox="0 0 16 16">
                                                    <defs>
                                                        <clipPath id="clipPath0218148765">
                                                            <path
                                                                d="M0 0L16 0L16 16L0 16L0 0Z"
                                                                fill-rule="nonzero"
                                                                transform="matrix(1 0 0 1 0 0)" />
                                                        </clipPath>
                                                    </defs>
                                                    <g clip-path="url(#clipPath0218148765)">
                                                        <path
                                                            d="M8 0C3.58214 0 0 3.58214 0 8C0 12.4179 3.58214 16 8 16C12.4179 16 16 12.4179 16 8C16 3.58214 12.4179 0 8 0ZM8.57143 11.8571C8.57143 11.9357 8.50714 12 8.42857 12L7.57143 12C7.49286 12 7.42857 11.9357 7.42857 11.8571L7.42857 7C7.42857 6.92143 7.49286 6.85714 7.57143 6.85714L8.42857 6.85714C8.50714 6.85714 8.57143 6.92143 8.57143 7L8.57143 11.8571ZM8 5.71429C7.7757 5.70971 7.56213 5.61739 7.40513 5.45714C7.24812 5.2969 7.16018 5.08149 7.16018 4.85714C7.16018 4.6328 7.24812 4.41739 7.40513 4.25714C7.56213 4.0969 7.7757 4.00458 8 4C8.2243 4.00458 8.43787 4.0969 8.59488 4.25714C8.75189 4.41739 8.83982 4.6328 8.83982 4.85714C8.83982 5.08149 8.75189 5.2969 8.59488 5.45714C8.43787 5.61739 8.2243 5.70971 8 5.71429Z"
                                                            fill-rule="nonzero"
                                                            transform="matrix(1 0 0 1 0 0)"
                                                            fill="rgb(0, 0, 0)"
                                                            fill-opacity="0.25" />
                                                    </g>
                                                </svg>
                                            </div>
                                        </el-tooltip>
                                    </span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="countScore" label="得分明细" align="center" width="400">
                            <template #default="scope">
                                <div class="progress-container" style="gap: 10px">
                                    <div class="progress-wrapper">
                                        <OverflowableProgress
                                            v-if="scope.row.score === 0 && scope.row.maxScore !== null"
                                            :percentage="computedPercentage(scope.row.countScore, scope.row.maxScore)"
                                            :maxValue="100"
                                            normalColor="#1677FF"
                                            overflowColor="#FF4D4F"
                                            :textInside="false"
                                            :stroke-width="6"
                                            width="180px">
                                        </OverflowableProgress>
                                    </div>
                                    <div
                                        class="progress-end"
                                        v-if="scope.row.score === 0 && scope.row.maxScore !== null">
                                        <!-- 外置文本 -->
                                        <div class="progress-text">
                                            <span>{{ scope.row.countScore }}/{{ scope.row.maxScore }}</span>
                                        </div>
                                        <!-- 如果溢出则提示 -->
                                        <span
                                            v-if="
                                                (scope.row.countScore / scope.row.maxScore) * 100 > 100 &&
                                                scope.row.maxScore !== null
                                            ">
                                            <el-tooltip placement="top" popper-class="alert-tooltip" effect="light">
                                                <template #content>
                                                    <div class="popper-content">
                                                        <div class="popper-title">分数超出上限</div>
                                                        <div class="popper-text">
                                                            您在该指标的绩效总分有溢出，原本得分为<span
                                                                style="color: #3b82f6; font-weight: 700"
                                                                >{{ scope.row.countScore }}</span
                                                            >分，该项指标的分数上限为<span
                                                                style="color: #3b82f6; font-weight: 700"
                                                                >{{ scope.row.maxScore }}</span
                                                            >分。
                                                            <br />
                                                            <br />
                                                            您的该项指标的绩效已经被记录为<span
                                                                style="color: #3b82f6; font-weight: 700"
                                                                >{{ scope.row.maxScore }}</span
                                                            >分。
                                                        </div>
                                                    </div>
                                                </template>
                                                <div
                                                    class="flex-center"
                                                    v-if="(scope.row.countScore / scope.row.maxScore) * 100 > 100">
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        xmlns:xlink="http://www.w3.org/1999/xlink"
                                                        width="16"
                                                        height="16"
                                                        viewBox="0 0 16 16">
                                                        <defs>
                                                            <clipPath id="clipPath0218148765">
                                                                <path
                                                                    d="M0 0L16 0L16 16L0 16L0 0Z"
                                                                    fill-rule="nonzero"
                                                                    transform="matrix(1 0 0 1 0 0)" />
                                                            </clipPath>
                                                        </defs>
                                                        <g clip-path="url(#clipPath0218148765)">
                                                            <path
                                                                d="M8 0C3.58214 0 0 3.58214 0 8C0 12.4179 3.58214 16 8 16C12.4179 16 16 12.4179 16 8C16 3.58214 12.4179 0 8 0ZM8.57143 11.8571C8.57143 11.9357 8.50714 12 8.42857 12L7.57143 12C7.49286 12 7.42857 11.9357 7.42857 11.8571L7.42857 7C7.42857 6.92143 7.49286 6.85714 7.57143 6.85714L8.42857 6.85714C8.50714 6.85714 8.57143 6.92143 8.57143 7L8.57143 11.8571ZM8 5.71429C7.7757 5.70971 7.56213 5.61739 7.40513 5.45714C7.24812 5.2969 7.16018 5.08149 7.16018 4.85714C7.16018 4.6328 7.24812 4.41739 7.40513 4.25714C7.56213 4.0969 7.7757 4.00458 8 4C8.2243 4.00458 8.43787 4.0969 8.59488 4.25714C8.75189 4.41739 8.83982 4.6328 8.83982 4.85714C8.83982 5.08149 8.75189 5.2969 8.59488 5.45714C8.43787 5.61739 8.2243 5.70971 8 5.71429Z"
                                                                fill-rule="nonzero"
                                                                transform="matrix(1 0 0 1 0 0)"
                                                                fill="rgb(0, 0, 0)"
                                                                fill-opacity="0.25" />
                                                        </g>
                                                    </svg>
                                                </div>
                                            </el-tooltip>
                                        </span>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getPersonName } from "@/utils/getNames";
import {
    pmsScoreGetMyScoreTotal,
    pmsScoreGetScoreListByYearAndPersonId,
    pmsScoreGetScoreTotalByPersonId,
    pmsScoreGetScoreTreeByYearAndPersonId,
    pmsScoreGetScoreTreeByYear,
    pmsScoreMajorGetScoreTotalByPersonIdAndDept,
    pmsScoreMajorGetScoreTreeByYearAndPersonId,
} from "@/apis/pmsScoreController";
import { CmsTagNode_, PmsScoreTotalDto } from "@/apis/types";
import OverflowableProgress from "@/components/OverflowableProgress/index.vue";
import { usePersons } from "@/hooks/usePersons";

// 组件属性定义
const props = defineProps<{
    /**
     * 人员ID，用于查询指定人员的绩效得分
     * 如果不提供，则查询当前登录用户的绩效得分
     */
    personId?: number | string;
    /**
     * 默认年份
     */
    defaultYear?: Date;
    /**
     * 是否为专业绩效得分
     */
    isProfessional?: boolean;
    /**
     * 自定义返回逻辑
     */
    customBack?: () => void;
}>();

// 组件事件
const emit = defineEmits<{
    (e: "back"): void;
}>();

// 当前人员id，可能从props接收，也可能是undefined
const personId = ref<number | undefined>(props.personId ? Number(props.personId) : undefined);

// 获取所有人员列表(包含已被禁用的人员)
const { allPersonListWithDisabled } = usePersons(true);

// 获取当前年份
const nowYear = ref(props.defaultYear || new Date());

// 绩效得分概览
const overViewData = ref<PmsScoreTotalDto[]>([]);

// 绩效得分树
const scoreTree = ref<CmsTagNode_[]>([]);

// 加载状态
const loading = ref(false);

/**
 * 获取绩效数据
 */
const fetchData = async () => {
    loading.value = true;
    if (personId.value && !props.isProfessional) {
        // 有人员id，且 不是专业绩效得分，则查询人员信息和获取人员绩效得分树
        const [res1, res2] = await Promise.all([
            pmsScoreGetScoreTotalByPersonId({
                params: { year: nowYear.value.getFullYear(), personId: personId.value },
            }),
            pmsScoreGetScoreTreeByYearAndPersonId({
                params: { year: nowYear.value.getFullYear(), personId: personId.value },
            }),
        ]);
        overViewData.value = res1.data;
        scoreTree.value = res2.data;
        loading.value = false;
    } else if (props.isProfessional && personId.value) {
        // 有人员id，且 为专业绩效得分，则查询人员信息和获取人员绩效得分树
        const [res1, res2] = await Promise.all([
            pmsScoreMajorGetScoreTotalByPersonIdAndDept({
                params: { year: nowYear.value.getFullYear(), personId: personId.value },
            }),
            pmsScoreMajorGetScoreTreeByYearAndPersonId({
                params: { year: nowYear.value.getFullYear(), personId: personId.value },
            }),
        ]);
        overViewData.value = res1.data;
        scoreTree.value = res2.data;
        loading.value = false;
    } else {
        // 没有人员id，则查询我的绩效得分和获取绩效得分树
        const [res1, res2] = await Promise.all([
            pmsScoreGetMyScoreTotal({ params: { year: nowYear.value.getFullYear() } }),
            pmsScoreGetScoreTreeByYear({ params: { year: nowYear.value.getFullYear() } }),
        ]);
        overViewData.value = res1.data;
        scoreTree.value = res2.data;
        loading.value = false;
    }
};

const computedPercentage = (countScore, maxScore) => {
    if (countScore > 0 && maxScore === null) return 100;
    return (countScore / maxScore) * 100;
};

/**
 * 年份变化事件处理
 */
const changeYear = (val: Date) => {
    nowYear.value = new Date(val);
    fetchData();
};

/**
 * 返回按钮点击事件处理
 */
const handleBack = () => {
    if (props.customBack) {
        props.customBack();
    } else {
        emit("back");
    }
};

/**
 * 根据名称判断得分颜色类名
 * @param name 指标名称
 * @returns 颜色类名
 */
const getScoreColorClass = (name?: string) => {
    if (!name) return "";

    if (name.includes("加分指标")) {
        return "score-positive";
    } else if (name.includes("减分指标")) {
        return "score-negative";
    }

    return "";
};

// 组件挂载时获取数据
onMounted(() => {
    fetchData();
});
</script>

<style scoped lang="scss">
// 隐藏表格展开图标
:deep(.el-table__expand-icon .el-icon) {
    display: none !important;
}

// 自定义展开图标
:deep(.el-table__expand-icon) {
    position: relative;
    z-index: 2;
    margin-right: 8px;
    display: inline-block; // 确保 transform 有效

    // 设置旋转中心为元素中心（可按需要调整）
    transform-origin: 75% 50%;

    &.el-table__expand-icon--expanded {
        transform: rotate(90deg);
    }

    &::before {
        content: "";
        position: absolute;
        z-index: 99;
        left: 100%;
        top: 50%;
        transform: translate(-50%, -50%);
        border: 5px solid transparent;
        border-left-color: #000;
    }
}

// 修改tooltip样式
:global(.tag-tooltip) {
    width: 541px;
    background: var(--Component-Popover-popoverBg, #ffffff);
    box-shadow:
        0px 9px 28px 8px rgba(0, 0, 0, 0.05),
        0px 3px 6px -4px rgba(0, 0, 0, 0.12),
        0px 6px 16px 0px rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    padding: 12px;
}

:global(.alert-tooltip) {
    background: var(--Component-Popover-popoverBg, #ffffff);
    box-shadow:
        0px 9px 28px 8px rgba(0, 0, 0, 0.05),
        0px 3px 6px -4px rgba(0, 0, 0, 0.12),
        0px 6px 16px 0px rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    padding: 12px;
    width: 240px;
}
// 更改表格第一列的cell为左对齐
:deep(.performance-cell div) {
    display: flex;
    align-items: center;
}
// 去掉表格最底部的线
:deep(.el-table__inner-wrapper:before) {
    height: 0;
}

:deep(td.performance-cell:has(.rowData-cell)) {
    padding: 0 !important;
}
:deep(td.performance-cell:has(.rowData-cell) .cell) {
    height: 39px;
}

.rowData-cell {
    color: rgba(0, 0, 0, 0.66);
    font-family: "Alibaba PuHuiTi 3.0";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 1.571 */
}

.flex-center {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    gap: 10px;
}
.tagName-cell {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    gap: 10px;
    padding-left: 23px;

    color: rgba(0, 0, 0, 0.88);
    font-family: "Alibaba PuHuiTi 3.0";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 1.571 */
}

.performance-score {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 24px 24px 0;
    gap: 24px;

    background: #ffffff;
    border-radius: 6px 6px 0px 0px;
    .header {
        height: 80px;

        display: flex;
        align-items: center;
        justify-content: center;
        background: #f9fbff;

        .header-title {
            color: #23346d;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 20px;
            font-style: normal;
            font-weight: 500;
            line-height: 34px; /* 1.7 */
            letter-spacing: 2.4px;
            text-align: center;
        }
    }
    .content {
        flex: 1;
        height: 0;
        background-color: #fff;
        display: flex;
        flex-direction: column;
        gap: 10px;

        .content-data-picker {
            display: flex;
            justify-content: space-between;
            padding: 0 16px;
            .year-picker {
                padding-left: 10px;

                color: #1677ff;
                font-family: "Alibaba PuHuiTi 3.0";
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
            }
        }
        .content-data {
            flex: 1;
            height: 0;
            display: flex;
            padding: 24px 16px;
            gap: 16px;
            flex-direction: column;
            align-items: center;

            background: #f9fbff;

            .content-header {
                display: flex;
                gap: 8px;
                align-items: center;
                flex-shrink: 0;

                overflow-x: auto;
                white-space: nowrap;
                width: 100%;
                user-select: none;
                &::-webkit-scrollbar {
                    height: 6px;
                }
                &::-webkit-scrollbar-thumb {
                    background-color: #d9d9d9;
                    border-radius: 3px;
                    cursor: pointer;
                }
                &::-webkit-scrollbar-track {
                    background-color: #f5f5f5;
                }
                .each-item {
                    display: flex;
                    width: 300.8px;
                    height: 88px;
                    padding: 1px 24px;
                    gap: 10px;
                    flex-direction: column;
                    justify-content: center;
                    align-items: flex-start;
                    flex-shrink: 0;

                    background: #ffffff;
                    border: 1px solid #f0f0f0;
                    border-radius: 8px;

                    .each-item-content {
                        height: 54px;
                        display: flex;
                        gap: 4px;
                        flex-direction: column;
                        align-items: flex-start;
                        flex-shrink: 0;
                    }

                    // 注释：原来的最后一个为减分指标的样式已被动态样式替代
                    .left-item {
                        height: 22px;
                        color: rgba(0, 0, 0, 0.45);
                        font-family: "Roboto";
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 22px; /* 1.571 */
                    }
                    .right-item {
                        height: 28px;
                        color: #1677ff;
                        font-family: "Roboto";
                        font-size: 24px;
                        font-style: normal;
                        font-weight: 400;

                        // 加分样式 - 绿色
                        &.score-positive {
                            color: #52c41a;
                        }

                        // 减分样式 - 红色
                        &.score-negative {
                            color: #ff4d4f;
                        }
                    }
                }
            }
            .table {
                flex: 1;
                height: 0;
                width: 100%;
                position: relative;
                :deep(.el-table) {
                    position: absolute;
                }
                .score-box {
                    display: flex;
                    justify-content: end;
                    width: 60%;

                    font-family: "Alibaba PuHuiTi 3.0";
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 22px; /* 1.571 */

                    .score-left {
                        color: #3b82f6;

                        // 加分样式 - 绿色
                        &.score-positive {
                            color: #52c41a;
                        }

                        // 减分样式 - 红色
                        &.score-negative {
                            color: #ff4d4f;
                        }
                    }
                    .score-right {
                        color: rgba(0, 0, 0, 0.88);
                    }
                }

                .progress-container {
                    display: flex;
                    .progress-wrapper {
                        width: 260px;
                        text-align: right;
                        display: flex;
                        justify-content: flex-end;
                    }

                    .progress-end {
                        flex: 1;
                        width: 0;
                        display: flex;
                        gap: 10px;
                        .progress-text {
                            color: var(--Global-Colors-Neutral-Text-colorText, rgba(0, 0, 0, 0.88));
                            font-family: "Alibaba PuHuiTi 3.0";
                            font-size: 12px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 20px; /* 1.667 */
                        }
                    }
                }
            }
        }
    }
}

.popper-content {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .popper-title {
        color: var(--Global-Colors-Neutral-Text-colorTextHeading, rgba(0, 0, 0, 0.88));
        font-family: "Alibaba PuHuiTi 3.0";
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 1.571 */
    }
    .popper-text {
        color: var(--Component-Popover-popoverColor, rgba(0, 0, 0, 0.88));
        font-family: "Alibaba PuHuiTi 3.0";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 1.571 */
    }
}
</style>
