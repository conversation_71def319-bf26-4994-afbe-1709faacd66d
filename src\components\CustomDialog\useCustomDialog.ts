import { createApp, h, ref, nextTick, type App } from "vue";
import { ElMessage, ElDialog, ElButton, ElIcon } from "element-plus";
import { Close } from "@element-plus/icons-vue";
import CustomDialog from "./index.vue";
import type { CustomDialogOptions, CustomDialogInstance, CustomDialogAction } from "./types";

// 引入本地图标
import successIcon from '@/assets/dialog_icons/success.png';
import errorIcon from '@/assets/dialog_icons/error.png';
import warningIcon from '@/assets/dialog_icons/warning.png';
import infoIcon from '@/assets/dialog_icons/info.png';

class CustomDialogService {
    private instances: App[] = [];

    private create(options: CustomDialogOptions): Promise<CustomDialogAction> {
        return new Promise((resolve) => {
            const visible = ref(false);
            const container = document.createElement("div");
            document.body.appendChild(container);

            const handleAction = async (action: CustomDialogAction) => {
                try {
                    if (action === "confirm" && options.onConfirm) {
                        await options.onConfirm();
                    } else if (action === "cancel" && options.onCancel) {
                        await options.onCancel();
                    } else if (action === "close" && options.onClose) {
                        await options.onClose();
                    }
                } catch (error) {
                    console.error("Dialog action error:", error);
                    ElMessage.error("操作失败，请重试");
                    return;
                }

                visible.value = false;

                // 延迟销毁，等待动画完成
                setTimeout(() => {
                    const index = this.instances.indexOf(app);
                    if (index > -1) {
                        this.instances.splice(index, 1);
                    }
                    app.unmount();
                    if (container.parentNode) {
                        container.parentNode.removeChild(container);
                    }
                }, 300);

                resolve(action);
            };

            const app = createApp({
                render() {
                    return h(CustomDialog as any, {
                        modelValue: visible.value,
                        "onUpdate:modelValue": (value: boolean) => {
                            visible.value = value;
                        },
                        onConfirm: () => handleAction("confirm"),
                        onCancel: () => handleAction("cancel"),
                        onClose: () => handleAction("close"),
                        ...options,
                    });
                },
            });

            // 注册 ElementPlus 组件
            app.component("ElDialog", ElDialog);
            app.component("ElButton", ElButton);
            app.component("ElIcon", ElIcon);
            app.component("Close", Close);

            this.instances.push(app);
            app.mount(container);

            nextTick(() => {
                visible.value = true;
            });
        });
    }

    // 成功类型对话框
    success(options: string | CustomDialogOptions): Promise<CustomDialogAction> {
        const config: CustomDialogOptions = typeof options === "string" ? { message: options } : options;

        const finalConfig: CustomDialogOptions = {
            type: 'success',
            iconColor: "#52C41A",
            showCancelButton: false,
            ...config,
        };

        // 如果用户没有明确设置iconUrl，则使用默认图标
        if (!("iconUrl" in config)) {
            finalConfig.iconUrl = successIcon;
        }

        return this.create(finalConfig);
    }

    // 警告类型对话框
    warning(options: string | CustomDialogOptions): Promise<CustomDialogAction> {
        const config: CustomDialogOptions = typeof options === "string" ? { message: options } : options;

        const finalConfig: CustomDialogOptions = {
            type: 'warning',
            iconColor: "#FAAD14",
            showCancelButton: true,
            ...config,
        };

        // 如果用户没有明确设置iconUrl，则使用默认图标
        if (!("iconUrl" in config)) {
            finalConfig.iconUrl = warningIcon;
        }

        return this.create(finalConfig);
    }

    // 错误类型对话框
    error(options: string | CustomDialogOptions): Promise<CustomDialogAction> {
        const config: CustomDialogOptions = typeof options === "string" ? { message: options } : options;

        const finalConfig: CustomDialogOptions = {
            type: 'error',
            iconColor: "#FF4D4F",
            showCancelButton: false,
            ...config,
        };

        // 如果用户没有明确设置iconUrl，则使用默认图标
        if (!("iconUrl" in config)) {
            finalConfig.iconUrl = errorIcon;
        }

        return this.create(finalConfig);
    }

    // 信息类型对话框
    info(options: string | CustomDialogOptions): Promise<CustomDialogAction> {
        const config: CustomDialogOptions = typeof options === "string" ? { message: options } : options;

        const finalConfig: CustomDialogOptions = {
            type: 'info',
            iconColor: "#1677FF",
            showCancelButton: false,
            ...config,
        };

        // 如果用户没有明确设置iconUrl，则使用默认图标
        if (!("iconUrl" in config)) {
            finalConfig.iconUrl = infoIcon;
        }

        return this.create(finalConfig);
    }

    // 确认类型对话框
    confirm(options: string | CustomDialogOptions): Promise<CustomDialogAction> {
        const config: CustomDialogOptions = typeof options === "string" ? { message: options } : options;

        const finalConfig: CustomDialogOptions = {
            type: 'confirm',
            showClose: true,
            showCancelButton: true,
            showConfirmButton: true,
            dialogClass: 'confirm-dialog',
            ...config,
        };

        return this.create(finalConfig);
    }

    // 自定义对话框
    open(options: CustomDialogOptions): Promise<CustomDialogAction> {
        return this.create(options);
    }

    // 关闭所有对话框
    closeAll() {
        this.instances.forEach((app) => {
            app.unmount();
        });
        this.instances = [];
    }
}

export const CustomDialogProvider = new CustomDialogService();

// 导出 hook 函数
export function useCustomDialog() {
    return {
        success: CustomDialogProvider.success.bind(CustomDialogProvider),
        warning: CustomDialogProvider.warning.bind(CustomDialogProvider),
        error: CustomDialogProvider.error.bind(CustomDialogProvider),
        info: CustomDialogProvider.info.bind(CustomDialogProvider),
        confirm: CustomDialogProvider.confirm.bind(CustomDialogProvider),
        open: CustomDialogProvider.open.bind(CustomDialogProvider),
        closeAll: CustomDialogProvider.closeAll.bind(CustomDialogProvider),
    };
}
