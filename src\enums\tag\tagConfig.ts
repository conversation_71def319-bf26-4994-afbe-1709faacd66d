/** 分配模式 */
export enum tagAllocateType {
    /** 项目制记分 */
    "PROJECT_SCORE" = 0,
    /** 人头制计分 */
    "HEAD_SCORE" = 1,
    /** 金额累计制计分 */
    "AMOUNT_SCORE" = 2
}

export const tagAllocateTypeMap = {
    [tagAllocateType.PROJECT_SCORE]: "项目制记分",
    [tagAllocateType.HEAD_SCORE]: "人头制计分",
    [tagAllocateType.AMOUNT_SCORE]: "金额累计制计分"
};

/** 赋分方式 */
export enum tagScoringMethod {
    /** 手动输入 */
    "MANUAL" = 0,
    /** 平台计算 */
    "AUTO" = 1
}

export const tagScoringMethodMap = {
    [tagScoringMethod.MANUAL]: "手动输入",
    [tagScoringMethod.AUTO]: "平台计算"
};

/** 计算方式 */
export enum tagCalculationMethod {
    /** 数据项组合计算分值 */
    "DATA_ITEM_COMBINATION" = 0,
    /** 固定分值 */
    "FIXED_SCORE" = 1,
    /** 数据项等值分值 */
    "DATA_ITEM_EQUAL_SCORE" = 2
}

export const tagCalculationMethodMap = {
    [tagCalculationMethod.DATA_ITEM_COMBINATION]: "数据项组合计算分值",
    [tagCalculationMethod.FIXED_SCORE]: "固定分值",
    [tagCalculationMethod.DATA_ITEM_EQUAL_SCORE]: "数据项等值分值"
}; 
