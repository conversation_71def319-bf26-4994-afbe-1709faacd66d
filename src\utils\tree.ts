// 递归遍历树结构，生成路径和底层节点内容
export const traverseTree = (node, parentPath) => {
    // 如果节点为空，返回空数组
    if (node == null) {
        return [];
    }
    const currentPath = parentPath ? `${parentPath} > ${node.tagName}` : node.tagName;
    // 如果是底层节点，返回路径（不包含当前节点的 tagName）和内容
    if (!node.children || node.children.length === 0) {
        return [
            {
                id: node.id,
                parentPath: parentPath, // 仅显示到上一级路径
                oldTagName: node.oldTagName,
                content: node.tagName,
                isLeaf: true,
            },
        ];
    }
    // 如果有子节点，递归处理每个子节点
    return node.children.flatMap((child) => traverseTree(child, currentPath));
};

// 计算已关联绩效规则中，叶子节点数量
export const getLeafNodesCount = (nodes: any[]): number => {
    if (!nodes) return 0;
    let count = 0;
    nodes.forEach(node => {
        if (!node.children || node.children.length === 0) {
            count++;
        } else {
            count += getLeafNodesCount(node.children);
        }
    });
    return count;
};