export const enum PermissionEnum {
    ALL = "所有人",
    ADMIN_ENTRY = "管理员录入权限卡",
    NEWS_ADMIN = "新闻管理员权限卡",
    COLLEGE_DATA = "全院数据权限卡",
    MAJOR_DATA = "专业数据权限卡",
    DIGITAL_ARCHIVE = "数字档案袋权限卡",
    TEACHER_ENTRY = "教师录入权限卡",
    COLLEGE_PERFORMANCE = "学院绩效统计权限卡",
    MAJOR_PERFORMANCE = "专业绩效统计权限卡",
    BACKEND_MANAGEMENT = "信息管理权限卡",
    CHECK_PERMISSION = "审核权限卡",
    CONTROL_PERMISSION = "中控设置权限卡",
    ADMIN_PERMISSION = "超级管理权限卡",
}

export const PermissionColors: Record<PermissionEnum, string> = {
    [PermissionEnum.ALL]: "#0078D7", 
    [PermissionEnum.ADMIN_ENTRY]: "#FF6011",
    [PermissionEnum.NEWS_ADMIN]: "#E8A701", 
    [PermissionEnum.COLLEGE_DATA]: "#87D068",
    [PermissionEnum.MAJOR_DATA]: "#4868E0", 
    [PermissionEnum.DIGITAL_ARCHIVE]: "#34C8C7", 
    [PermissionEnum.TEACHER_ENTRY]: "#25AEEB", 
    [PermissionEnum.COLLEGE_PERFORMANCE]: "#846FD2", 
    [PermissionEnum.MAJOR_PERFORMANCE]: "#E34194", 
    [PermissionEnum.BACKEND_MANAGEMENT]: "#697697",
    [PermissionEnum.CHECK_PERMISSION]: "#548255", 
    [PermissionEnum.CONTROL_PERMISSION]: "#9E6525", 
    [PermissionEnum.ADMIN_PERMISSION]: "#C11C1C", 
};

export const PermissionIds: Record<PermissionEnum, number> = {
    [PermissionEnum.ALL]: 1,
    [PermissionEnum.ADMIN_ENTRY]: 2,
    [PermissionEnum.NEWS_ADMIN]: 3,
    [PermissionEnum.COLLEGE_DATA]: 4,
    [PermissionEnum.MAJOR_DATA]: 5,
    [PermissionEnum.DIGITAL_ARCHIVE]: 6,
    [PermissionEnum.TEACHER_ENTRY]: 7,
    [PermissionEnum.COLLEGE_PERFORMANCE]: 8,
    [PermissionEnum.MAJOR_PERFORMANCE]: 9,
    [PermissionEnum.BACKEND_MANAGEMENT]: 10,
    [PermissionEnum.CHECK_PERMISSION]: 12,
    [PermissionEnum.CONTROL_PERMISSION]: 13,
    [PermissionEnum.ADMIN_PERMISSION]: 14,
};

export const PermissionDescription: Record<PermissionEnum, string> = {
    [PermissionEnum.ALL]: `
<ul>
    <li>访问和使用基础功能。</li>
    <li>适用对象：所有用户。</li>
</ul>`,
    [PermissionEnum.ADMIN_ENTRY]: `
<ul>
    <li>录入系统关键数据，管理核心资源。</li>
    <li>适用对象：系统管理员。</li>
</ul>`,
    [PermissionEnum.NEWS_ADMIN]: `
<ul>
    <li>管理新闻内容，包括创建、编辑和删除。</li>
    <li>适用对象：新闻管理员。</li>
</ul>`,
    [PermissionEnum.COLLEGE_DATA]: `
<ul>
    <li>查看和管理学院层级的数据。</li>
    <li>适用对象：学院管理员。</li>
</ul>`,
    [PermissionEnum.MAJOR_DATA]: `
<ul>
    <li>查看和管理专业层级的数据。</li>
    <li>适用对象：专业管理员。</li>
</ul>`,
    [PermissionEnum.DIGITAL_ARCHIVE]: `
<ul>
    <li>访问和管理数字档案数据。</li>
    <li>适用对象：档案管理员。</li>
</ul>`,
    [PermissionEnum.TEACHER_ENTRY]: `
<ul>
    <li>录入教师相关信息和数据。</li>
    <li>适用对象：教师或数据录入人员。</li>
</ul>`,
    [PermissionEnum.COLLEGE_PERFORMANCE]: `
<ul>
    <li>统计和查看学院绩效数据。</li>
    <li>适用对象：学院管理人员。</li>
</ul>`,
    [PermissionEnum.MAJOR_PERFORMANCE]: `
<ul>
    <li>统计和查看专业绩效数据。</li>
    <li>适用对象：专业管理人员。</li>
</ul>`,
    [PermissionEnum.BACKEND_MANAGEMENT]: `
<ul>
    <li>访问后台页面，执行审核和配置操作。拥有平台大部分权限，访问核心功能。</li>
    <li>适用对象：需要进行平台管理的用户。</li>
</ul>`,
    [PermissionEnum.CHECK_PERMISSION]: `
<ul>
    <li>审核数据录入、审核数据、审核绩效等操作。</li>
    <li>适用对象：审核人员。</li>
</ul>`,
    [PermissionEnum.CONTROL_PERMISSION]: `
<ul>
    <li>控制中控页面，进入中控页面操作系统。</li>
    <li>适用对象：管理人员。</li>
</ul>`,
    [PermissionEnum.ADMIN_PERMISSION]: `
<ul>
    <li>拥有平台所有权限，可以访问所有功能。</li>
    <li>适用对象：超级管理员。</li>
</ul>`,
};
