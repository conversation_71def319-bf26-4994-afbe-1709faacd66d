import { defineConfig } from "vite";
import path from "path";
import vue from "@vitejs/plugin-vue";
import AutoImport from "unplugin-auto-import/vite";
import { codeInspectorPlugin } from "code-inspector-plugin";
import vueDevTools from "vite-plugin-vue-devtools";
// https://vite.dev/config/
export default defineConfig({
    plugins: [
        vue(),
        AutoImport({ imports: ["vue", "vue-router", "pinia"], dts: true }),
        codeInspectorPlugin({
            bundler: "vite",
        }),
        vueDevTools(),
    ],
    resolve: {
        // https://cn.vitejs.dev/config/#resolve-alias
        alias: {
            // 设置路径
            "~": path.resolve(__dirname, "./"),
            // 设置别名
            "@": path.resolve(__dirname, "./src"),
        },
        // https://cn.vitejs.dev/config/#resolve-extensions
        extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"],
    },
    // 处理sass报错
    css: {
        preprocessorOptions: {
            scss: {
                api: "modern-compiler",
            },
        },
    },
});
