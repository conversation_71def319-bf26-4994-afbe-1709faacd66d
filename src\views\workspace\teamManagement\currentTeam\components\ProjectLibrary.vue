<template>
    <div class="project-info">
        <div class="section-title">
            <div class="title-indicator"></div>
            <span>立项资料</span>
        </div>
        <div class="table">
            <el-table
                header-row-class-name="table-header"
                empty-text="暂无数据"
                show-overflow-tooltip
                :data="projectInfoData"
                border
                style="width: 100%">
                <el-table-column
                    prop="owner"
                    label="资料所属"
                    align="center"
                    :filters="personFilterList"
                    :filter-method="personFilterHandler">
                    <template #default="scope">
                        {{ getPersonName(allPersonListWithDisabled, String(scope.row.owner)) }}
                    </template>
                </el-table-column>
                <el-table-column prop="templateName" label="数据表" align="center" />
                <el-table-column prop="projectName" label="项目名称" align="center" />
                <el-table-column
                    prop="projectStatus"
                    label="项目状态"
                    align="center"
                    :filters="projectStatusList"
                    :filter-method="projectStatusFilterHandler">
                    <template #default="scope">
                        <div>
                            {{ getStatusDescriptionById(scope.row.projectStatus) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="grade"
                    label="级别"
                    align="center"
                    :filters="gradeFilterList"
                    :filter-method="gradeFilterHandler">
                    <template #default="scope">
                        <div>
                            {{ getFieldName(dictList, scope.row.grade) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="projectTime"
                    label="成果完成时间"
                    align="center"
                    sortable
                    :sort-method="
                        (a, b) => {
                            return a.projectTime - b.projectTime;
                        }
                    " />
                <el-table-column prop="proofMaterial" label="佐证材料" align="center">
                    <template #default="scope">
                        <div style="color: #1677ff; cursor: pointer" @click="handleDownload(scope.row)">下载材料</div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center">
                    <template #default="scope">
                        <div style="color: #1677ff; cursor: pointer" @click="handleDetail(scope.row)">查看详情</div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="pagination">
            <el-pagination
                size="small"
                layout="prev, pager, next,jumper"
                :current-page="infoPagination.currentPage"
                :page-size="infoPagination.pageSize"
                :total="infoPagination.total"
                @current-change="handleInfoPageChange"
                prev-text="上一页"
                next-text="下一页" />
        </div>

        <div class="section-title">
            <div class="title-indicator"></div>
            <span>建设成果</span>
        </div>
        <div class="table">
            <el-table
                header-row-class-name="table-header"
                empty-text="暂无数据"
                show-overflow-tooltip
                :data="projectAchievementsData"
                border
                ref="achievementsTableRef"
                style="width: 100%">
                <el-table-column
                    prop="owner"
                    label="资料所属"
                    align="center"
                    :filters="personFilterList"
                    :filter-method="personFilterHandler">
                    <template #default="scope">
                        {{ getPersonName(allPersonListWithDisabled, String(scope.row.owner)) }}
                    </template>
                </el-table-column>
                <el-table-column prop="templateName" label="数据表" align="center" />
                <el-table-column prop="projectName" label="项目名称" align="center" />
                <el-table-column
                    prop="projectStatus"
                    label="项目状态"
                    align="center"
                    :filters="projectStatusList"
                    :filter-method="projectStatusFilterHandler">
                    <template #default="scope">
                        <div>
                            {{ getStatusDescriptionById(scope.row.projectStatus) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="grade"
                    label="级别"
                    align="center"
                    :filters="gradeFilterList"
                    :filter-method="gradeFilterHandler">
                    <template #default="scope">
                        <div>
                            {{ getFieldName(dictList, scope.row.grade) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="projectTime"
                    label="成果完成时间"
                    align="center"
                    sortable
                    :sort-method="
                        (a, b) => {
                            return a.projectTime - b.projectTime;
                        }
                    " />
                <el-table-column prop="proofMaterial" label="佐证材料" align="center">
                    <template #default="scope">
                        <div style="color: #1677ff; cursor: pointer" @click="handleDownload(scope.row)">下载材料</div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center">
                    <template #default="scope">
                        <div style="color: #1677ff; cursor: pointer" @click="handleDetail(scope.row)">查看详情</div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="pagination">
            <el-pagination
                size="small"
                layout="prev, pager, next,jumper"
                :current-page="achievementsPagination.currentPage"
                :page-size="achievementsPagination.pageSize"
                :total="achievementsPagination.total"
                @current-change="handleAchievementsPageChange"
                prev-text="上一页"
                next-text="下一页" />
        </div>

        <!-- 文件下载弹窗 -->
        <FileDownloadDialog v-model="fileDownloadDialogVisible" :file-list-string="fileListString" />
    </div>
</template>

<script setup lang="ts">
import { CmsAchievement } from "@/apis";
import { tmsProjectSharedGetValuesByProjectId } from "@/apis/tmsProjectSharedController";
import { getPersonName, getFieldName } from "@/utils/getNames";
import { usePersons } from "@/hooks/usePersons";
import { useDict } from "@/hooks/useDict";
import { useProjectStatusFilter, useGradeFilter, usePersonFilter } from "@/utils/filterUtils";
import FileDownloadDialog from "@/components/FileDownloadDialog/index.vue";
import type { TableInstance } from "element-plus";
import { nextTick } from "vue";
import { useFileDownload } from "@/hooks/useFileDownload";

// 属性定义
const props = defineProps<{
    projectId: string | number;
}>();

const { allPersonListWithDisabled } = usePersons(true);
const { dictList } = useDict();

// 使用提取出的筛选功能
const { projectStatusList, projectStatusFilterHandler, getStatusDescriptionById } = useProjectStatusFilter(dictList);
const { gradeFilterList, gradeFilterHandler } = useGradeFilter(dictList);

// 立项信息数据、分页信息
const projectInfoData = ref<CmsAchievement[]>([]);
const infoPagination = ref({
    currentPage: 1,
    pageSize: 5,
    total: 0,
});

// 建设成果数据、分页信息
const projectAchievementsData = ref<CmsAchievement[]>([]);
const achievementsPagination = ref({
    currentPage: 1,
    pageSize: 5,
    total: 0,
});

const achievementsTableRef = ref<TableInstance | null>(null);

// 使用文件下载hooks
const { fileDownloadDialogVisible, fileListString, handleDownload: handleFileDownload } = useFileDownload();

// 使用人员筛选功能
const { personFilterList, personFilterHandler } = usePersonFilter(projectInfoData, allPersonListWithDisabled);

// 下载材料功能 - 使用统一的hooks
const handleDownload = (row: any) => {
    handleFileDownload({
        row,
        fieldName: 'proofMaterial',
        mode: 'singleField'
    });
};

// 查看详情功能（占位符）
const handleDetail = (row: any) => {
    // TODO: 实现查看详情功能
    console.log("查看详情", row);
};

// 获取立项资料表格数据
const fetchProjectInfoData = async () => {
    if (!props.projectId) return;
    const { currentPage, pageSize } = infoPagination.value;
    const res = await tmsProjectSharedGetValuesByProjectId({
        body: {
            pageNum: currentPage,
            pageSize: pageSize,
            projectId: Number(props.projectId),
            status: 1,
            type: 0,
        },
    });
    projectInfoData.value = res.data.records;
    infoPagination.value.total = res.data.total;
};

// 获取建设成果表格数据
const fetchAchievementsData = async () => {
    if (!props.projectId) return;
    const { currentPage, pageSize } = achievementsPagination.value;
    const res = await tmsProjectSharedGetValuesByProjectId({
        body: {
            pageNum: currentPage,
            pageSize: pageSize,
            projectId: Number(props.projectId),
            status: 1,
            type: 1,
        },
    });
    projectAchievementsData.value = res.data.records;
    achievementsPagination.value.total = res.data.total;

    // 清除表格筛选
    nextTick(() => {
        if (achievementsTableRef.value) {
            achievementsTableRef.value.clearFilter();
        }
    });
};

// 立项资料分页切换事件
const handleInfoPageChange = (newPage: number) => {
    infoPagination.value.currentPage = newPage;
    fetchProjectInfoData();
};

// 建设成果分页切换事件
const handleAchievementsPageChange = (newPage: number) => {
    achievementsPagination.value.currentPage = newPage;
    fetchAchievementsData();
};

// 暴露给父组件的方法
defineExpose({
    fetchProjectInfoData,
    fetchAchievementsData,
});

// 监听projectId变化
watch(
    () => props.projectId,
    (newVal) => {
        if (newVal) {
            fetchProjectInfoData();
            fetchAchievementsData();
        }
    },
    { immediate: true }
);

// 同时监听表格数据和字典数据
watchEffect(() => {
    if (dictList.value?.length > 0) {
        if (projectInfoData.value?.length > 0) {
            projectInfoData.value = [...projectInfoData.value];
        }
        if (projectAchievementsData.value?.length > 0) {
            projectAchievementsData.value = [...projectAchievementsData.value];
        }
    }
});
</script>

<style scoped lang="scss">
.section-title {
    display: flex;
    align-items: center;
    min-height: 21px;
    margin: 21.5px 0;

    .title-indicator {
        border-left: 3px solid #1677ff;
        padding-left: 8px;
        margin-right: 8px;
        height: 20px;
        display: flex;
        align-items: center;
    }

    span {
        color: #000000;
        line-height: 20px;
        width: 56px;
        min-height: 20px;
        font-size: 14px;
    }
}

:deep(.table-header) {
    th {
        background-color: #edf1fa;
        color: rgba(0, 0, 0, 0.88);
        font-size: 14px;
        font-weight: 500;
    }
}

.pagination {
    display: flex;
    justify-content: end;
    margin-top: 10px;
    margin-right: 20px;
    margin-bottom: 20px;
}
</style>
