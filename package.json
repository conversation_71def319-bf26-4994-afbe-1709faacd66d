{"name": "comprehensive-data-management-platform-web-next", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build:dev": "vite build --mode dev", "build:prod": "vite build --mode prod", "build:docker": "vite build --mode docker", "preview": "vite preview", "openapi": "openapi-ts"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@element-plus/icons-vue": "^2.3.1", "@popperjs/core": "^2.11.8", "@types/qs": "^6.9.18", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "^12.2.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "@wangeditor/plugin-upload-attachment": "^1.1.0", "ant-design-vue": "^4.2.6", "axios": "^1.7.7", "crypto-js": "^4.2.0", "element-plus": "^2.9.4", "js-cookie": "^3.0.5", "mitt": "^3.0.1", "mutation-events": "github:mfreed7/mutation-events-polyfill", "nprogress": "^0.2.0", "pinia": "^2.2.6", "qs": "^6.14.0", "quill": "^1.3.7", "sass": "^1.81.0", "sass-loader": "^16.0.3", "vue": "^3.5.12", "vue-draggable-plus": "^0.6.0", "vue-router": "^4.4.5"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/node": "^22.9.1", "@types/quill": "^2.0.14", "@vitejs/plugin-vue": "^5.1.4", "code-inspector-plugin": "^0.20.16", "openapi-ts-request": "^1.1.2", "typescript": "~5.6.2", "unplugin-auto-import": "^0.18.5", "vite": "^5.4.10", "vite-plugin-vue-devtools": "^7.7.6", "vue-tsc": "^2.1.8"}}