<template>
    <div class="account-management">
        <div class="account-management-header">
            <div class="account-management-header-left">
                <CustomSearch
                    v-model="searchKeyword"
                    placeholder="输入关键字检索"
                    @search="handleSearch"
                    @clear="handleClear" />
            </div>
            <div class="account-management-header-right">
                <el-button type="primary" @click="openDialog('add')" class="add-button"> 新建账号 </el-button>
            </div>
        </div>

        <div class="account-management-table">
            <el-table
                :data="tableData"
                style="width: 100%"
                v-loading="tableLoading"
                :header-cell-style="{
                    backgroundColor: '#EDF1FA',
                    color: 'rgba(0, 0, 0, 0.88)',
                    fontWeight: '500',
                    height: '47px',
                    borderBottom: '1px solid #EDF1FA',
                }"
                :cell-style="{
                    borderBottom: '1px solid #EDF1FA',
                    padding: '16px',
                }"
                :row-style="{ backgroundColor: '#FFFFFF' }"
                height="100%">
                <el-table-column prop="username" label="账号" width="320" />
                <el-table-column prop="userNick" label="姓名" width="320" />
                <el-table-column prop="roles" label="权限身份" :filters="roleFilters" :filter-method="filterByRole">
                    <template #default="scope">
                        <div v-for="role in scope.row.roles" :key="role.id">{{ role.name }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                    <template #default="scope">
                        <div class="action-buttons">
                            <el-button type="primary" link @click="handleEdit(scope.row)" class="action-button">
                                编辑
                            </el-button>
                            <el-button type="primary" link @click="handleDelete(scope.row)" class="action-button">
                                删除
                            </el-button>
                            <el-switch
                                size="large"
                                v-model="scope.row.status"
                                :active-value="1"
                                :inactive-value="0"
                                :before-change="() => handleBeforeEnableOrDisable(scope.row)"
                                inline-prompt
                                active-text="启用"
                                inactive-text="禁用" />
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 新增/编辑人员抽屉 -->
        <CustomDrawer
            v-model="dialogVisible"
            :title="dialogType === 'add' ? '新建账号' : '编辑账号'"
            :confirm-text="dialogType === 'add' ? '确定' : '保存'"
            @confirm="submitForm"
            @close="
                () => {
                    dialogVisible = false;
                    resetForm();
                }
            ">
            <el-form ref="formRef" :model="formData" :rules="formRules" label-position="top">
                <el-form-item label="教职工号" prop="username">
                    <el-input
                        v-model="formData.username"
                        placeholder="请输入教职工号"
                        :disabled="dialogType === 'edit'" />
                </el-form-item>
                <el-form-item label="姓名" prop="userNick">
                    <el-input v-model="formData.userNick" placeholder="请输入姓名" />
                </el-form-item>
                <el-form-item label="权限身份" prop="roleId">
                    <el-select v-model="formData.roleId" placeholder="请选择权限身份" style="width: 100%">
                        <el-option v-for="item in rolesData" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
            </el-form>
        </CustomDrawer>
    </div>
</template>

<script setup lang="ts">
import { ElMessage, FormInstance } from "element-plus";
import CustomSearch from "@/components/CustomSearch/index.vue";
import CustomDrawer from "@/components/CustomDrawer";
import { UmsAdmin_, UmsRole_, umsRoleGetAllByEnable } from "@/apis";
import {
    umsAdminCreate,
    umsAdminList,
    umsAdminUpdate,
    umsAdminDeleteById,
    umsAdminEnableOrDisAble,
} from "@/apis/umsAdminController";
import { useCustomDialog } from "@/components/CustomDialog";

const customDialog = useCustomDialog();

const searchKeyword = ref("");

const tableLoading = ref(false);
const tableData = ref<UmsAdmin_[]>([]);
const originalTableData = ref<UmsAdmin_[]>([]);

const handleSearch = (keyword: string) => {
    if (keyword) {
        // 从原始数据中过滤
        tableData.value = originalTableData.value.filter((item) => {
            return item.username?.includes(keyword) || item.userNick?.includes(keyword);
        });
    } else {
        // 如果搜索关键词为空，恢复原始数据
        tableData.value = [...originalTableData.value];
    }
};

const handleClear = () => {
    // 清空搜索时恢复原始数据
    tableData.value = [...originalTableData.value];
};

// 获取表格数据
const getTableData = () => {
    tableLoading.value = true;
    umsAdminList({})
        .then((res) => {
            tableData.value = res.data;
            originalTableData.value = res.data; // 保存原始数据
        })
        .finally(() => {
            tableLoading.value = false;
        });
};

const rolesData = ref<UmsRole_[]>([]);

// 获取权限身份数据
const getRolesData = () => {
    umsRoleGetAllByEnable({}).then((res) => {
        rolesData.value = res.data;
        // 更新过滤器选项
        updateRoleFilters();
    });
};
// 弹窗相关
const dialogVisible = ref(false);
const dialogType = ref<"add" | "edit">("add");
const formRef = ref<FormInstance>();
const formData = ref({
    id: undefined as number | undefined,
    username: "",
    userNick: "",
    roleId: undefined as number | undefined,
});

// 表单验证规则
const formRules = {
    username: [{ required: true, message: "请输入教职工号", trigger: "blur" }],
    userNick: [{ required: true, message: "请输入姓名", trigger: "blur" }],
    roleId: [{ required: true, message: "请选择权限身份", trigger: "change" }],
};

// 打开弹窗
const openDialog = (type: "add" | "edit") => {
    dialogType.value = type;
    dialogVisible.value = true;
};

// 编辑人员
const handleEdit = (row: UmsAdmin_) => {
    openDialog("edit");
    formData.value = {
        id: row.id,
        username: row.username,
        userNick: row.userNick,
        roleId: row.roles && row.roles.length > 0 ? row.roles[0].id : undefined,
    };
};

// 删除人员
const handleDelete = (row: UmsAdmin_) => {
    customDialog.confirm({
        title: "确认删除账号吗？",
        message: `删除账号后，拥有该账号的用户将无法再登陆该平台，该操作不可撤销。如您已知晓后果，可点击“确定”删除账号。`,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        alignCenter: true,
        confirmButtonProps: {
            type: "danger",
        },
        onConfirm: () => {
            umsAdminDeleteById({
                params: {
                    id: row.id,
                },
            }).then(() => {
                getTableData();
            });
        },
    });
};

// 启用/禁用前的处理
const handleBeforeEnableOrDisable = (row: UmsAdmin_) => {
    if (row.status === 1) {
        customDialog.confirm({
            title: "确认禁用账号吗？",
            message: "禁用账号后，拥有该账号的用户将无法再登陆该平台。如您已知晓后果，可点击“确定”禁用账号。",
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            alignCenter: true,
            onConfirm: () => {
                return new Promise<boolean>((resolve) => switchAccEnableOrDisable(row, resolve));
            },
        });
    } else {
        return new Promise<boolean>((resolve) => switchAccEnableOrDisable(row, resolve));
    }
};

// 禁用/启用账号 返回promise
const switchAccEnableOrDisable = (row: UmsAdmin_, resolve: (value: boolean) => void) => {
    umsAdminEnableOrDisAble({
        params: {
            id: row.id,
        },
    })
        .then((res) => {
            if (res.code === 200) {
                ElMessage.success("操作成功");
                getTableData();
                resolve(true);
            } else {
                resolve(false);
            }
        })
        .catch(() => {
            resolve(false);
        });
};

// 重置表单
const resetForm = () => {
    formRef.value?.resetFields();
    formData.value = {
        id: undefined,
        username: "",
        userNick: "",
        roleId: undefined,
    };
};

// 提交表单
const submitForm = async () => {
    if (!formRef.value) return;

    await formRef.value.validate(async (valid) => {
        if (valid) {
            try {
                if (dialogType.value === "add") {
                    // 新增人员
                    await umsAdminCreate({
                        body: {
                            employeeId: formData.value.username,
                            employeeName: formData.value.userNick,
                            roleId: formData.value.roleId,
                        },
                    });
                    ElMessage.success("新增成功");
                } else {
                    // 编辑人员
                    await umsAdminUpdate({
                        body: {
                            id: formData.value.id,
                            employeeName: formData.value.userNick,
                            roleId: formData.value.roleId,
                        },
                    });
                    ElMessage.success("编辑成功");
                }
                dialogVisible.value = false;
                getTableData();
            } catch (error) {
                console.error(error);
                ElMessage.error("操作失败");
            }
        }
    });
};

// 角色过滤器选项
const roleFilters = ref<{ text: string; value: string }[]>([]);

// 更新角色过滤器选项
const updateRoleFilters = () => {
    const allRoles = new Set<string>();
    tableData.value.forEach((item) => {
        if (item.roles && Array.isArray(item.roles)) {
            item.roles.forEach((role) => {
                if (role.name) {
                    allRoles.add(role.name);
                }
            });
        }
    });

    roleFilters.value = Array.from(allRoles).map((roleName) => ({
        text: roleName,
        value: roleName,
    }));
};

// 角色过滤方法
const filterByRole = (value: string, row: UmsAdmin_) => {
    if (!row.roles || !Array.isArray(row.roles)) {
        return false;
    }
    return row.roles.some((role) => role.name === value);
};

onMounted(() => {
    getTableData();
    getRolesData();
});
</script>

<style scoped lang="scss">
.account-management {
    height: 100%;
    display: flex;
    flex-direction: column;
    /* height: calc(100vh - 170px); */
    .account-management-table {
        flex: 1;
        height: 0;
    }

    &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 32px;
        background: #ffffff;
        border-radius: 6px;
        height: 64px;
        box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);

        &-left {
            display: flex;
            align-items: center;
        }

        &-right {
            .add-button {
                background: #1677ff;
                border: 1px solid #1677ff;
                border-radius: 6px;
                height: 32px;
                padding: 5px 16px;
                box-shadow: 0px 2px 0px rgba(5, 145, 255, 0.1);
                font-size: 14px;
                font-weight: 400;

                &:hover {
                    background: #4c9bff;
                    border-color: #4c9bff;
                }
            }
        }
    }

    &-table {
        margin-top: 12px;
        background: #ffffff;
        border-radius: 6px;
        padding: 0;
        box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
        overflow: hidden;

        :deep(.el-table) {
            border: none;

            .el-table__header-wrapper {
                .el-table__header {
                    th {
                        background: #edf1fa;
                        border-bottom: 1px solid #edf1fa;
                        color: rgba(0, 0, 0, 0.88);
                        font-weight: 500;
                        font-size: 14px;
                        line-height: 22px;
                        padding: 12.5px 16px;

                        &:first-child {
                            border-left: none;
                        }

                        &:last-child {
                            border-right: none;
                        }
                    }
                }
            }

            .el-table__body-wrapper {
                .el-table__body {
                    tr {
                        background: #ffffff;

                        td {
                            border-bottom: 1px solid #edf1fa;
                            border-left: 1px solid #edf1fa;
                            color: rgba(0, 0, 0, 0.88);
                            font-size: 14px;
                            line-height: 22px;
                            padding: 25px 16px;

                            &:first-child {
                                border-left: none;
                            }

                            &:last-child {
                                border-right: none;
                            }
                        }

                        &:hover td {
                            background: #fafafa;
                        }
                    }
                }
            }
        }
    }
}

.action-buttons {
    display: flex;
    align-items: center;
    gap: 12px;

    .action-button {
        color: #1677ff;
        font-size: 14px;
        line-height: 22px;
        padding: 0;
        height: auto;
        min-width: 28px;
        margin-left: 0;

        &:hover {
            color: #4c9bff;
        }
    }
}

.dialog-confirm-button {
    background: #1677ff;
    border-color: #1677ff;

    &:hover {
        background: #4c9bff;
        border-color: #4c9bff;
    }
}
</style>
