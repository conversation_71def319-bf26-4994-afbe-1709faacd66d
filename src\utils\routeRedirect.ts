import { PermissionIds, PermissionEnum } from "@/enums/roles/authorCards";
import useUserStore from "@/store/modules/user";

// 页面路径与权限ID的映射配置
interface RoutePermissionConfig {
    path: string;
    permissionId: number;
    priority: number; // 优先级，数字越小优先级越高
}

// 路由组配置 - 将相关路由分组，并配置其权限要求
const ROUTE_GROUPS: Record<string, RoutePermissionConfig[]> = {
    // 学院绩效统计路由组
    "/workspace/CollegePerformanceStatistics": [
        {
            path: "/workspace/CollegePerformanceStatistics/collegeStatistics",
            permissionId: PermissionIds[PermissionEnum.COLLEGE_PERFORMANCE],
            priority: 1,
        },
        {
            path: "/workspace/CollegePerformanceStatistics/professionalStatistics",
            permissionId: PermissionIds[PermissionEnum.MAJOR_PERFORMANCE],
            priority: 2,
        },
    ],
    // 后台管理-信息管理路由组
    "/backgroundManagement/infoManagement": [
        {
            path: "/backgroundManagement/infoManagement/accountManagement",
            permissionId: PermissionIds[PermissionEnum.BACKEND_MANAGEMENT],
            priority: 1,
        },
        {
            path: "/backgroundManagement/infoManagement/organizationalStructure",
            permissionId: PermissionIds[PermissionEnum.BACKEND_MANAGEMENT],
            priority: 2,
        },
        {
            path: "/backgroundManagement/infoManagement/permissionGroup",
            permissionId: PermissionIds[PermissionEnum.BACKEND_MANAGEMENT],
            priority: 3,
        },
    ],
    // 后台管理-中央设置路由组
    "/backgroundManagement/centralSettings": [
        {
            path: "/backgroundManagement/centralSettings/tableManagement",
            permissionId: PermissionIds[PermissionEnum.CONTROL_PERMISSION],
            priority: 1,
        },
        {
            path: "/backgroundManagement/centralSettings/newsletterManagement",
            permissionId: PermissionIds[PermissionEnum.CONTROL_PERMISSION],
            priority: 2,
        },
        {
            path: "/backgroundManagement/centralSettings/performanceRuleManagement",
            permissionId: PermissionIds[PermissionEnum.CONTROL_PERMISSION],
            priority: 3,
        },
    ],
    // 工作台-团队管理路由组
    "/workspace/teamManagement": [
        {
            path: "/workspace/teamManagement/currentTeam",
            permissionId: PermissionIds[PermissionEnum.ALL], // 所有人都能访问当前团队
            priority: 1, // 设置为最高优先级，默认跳转到此页面
        },
        {
            path: "/workspace/teamManagement/beVerifiedTeam",
            permissionId: PermissionIds[PermissionEnum.ALL],
            priority: 2,
        },
    ],
    // 工作台-管理员审核路由组
    "/workspace/adminApproval": [
        {
            path: "/workspace/adminApproval/pendingApproval",
            permissionId: PermissionIds[PermissionEnum.CHECK_PERMISSION],
            priority: 1,
        },
        {
            path: "/workspace/adminApproval/approvalRecord",
            permissionId: PermissionIds[PermissionEnum.CHECK_PERMISSION],
            priority: 2,
        },
        {
            path: "/workspace/adminApproval/pendingReassigned",
            permissionId: PermissionIds[PermissionEnum.CHECK_PERMISSION],
            priority: 3,
        },
    ],
    // 工作台-管理员录入路由组
    "/workspace/adminEntry": [
        {
            path: "/workspace/adminEntry/entryHome",
            permissionId: PermissionIds[PermissionEnum.ADMIN_ENTRY],
            priority: 1,
        },
        {
            path: "/workspace/adminEntry/entryRecord",
            permissionId: PermissionIds[PermissionEnum.ADMIN_ENTRY],
            priority: 2,
        },
        {
            path: "/workspace/adminEntry/adminToBeUpdate",
            permissionId: PermissionIds[PermissionEnum.ADMIN_ENTRY],
            priority: 3,
        },
        {
            path: "/workspace/adminEntry/toBeRecordHistory",
            permissionId: PermissionIds[PermissionEnum.ADMIN_ENTRY],
            priority: 4,
        },
    ],
    // 工作台-教师录入路由组
    "/workspace/teacherEntry": [
        {
            path: "/workspace/teacherEntry/entryHome",
            permissionId: PermissionIds[PermissionEnum.TEACHER_ENTRY],
            priority: 1,
        },
        {
            path: "/workspace/teacherEntry/entryRecord",
            permissionId: PermissionIds[PermissionEnum.TEACHER_ENTRY],
            priority: 2,
        },
        {
            path: "/workspace/teacherEntry/toBeUpdate",
            permissionId: PermissionIds[PermissionEnum.TEACHER_ENTRY],
            priority: 3,
        },
        {
            path: "/workspace/teacherEntry/toBeRecordHistory",
            permissionId: PermissionIds[PermissionEnum.TEACHER_ENTRY],
            priority: 4,
        },
    ],
    // 工作台-新闻管理路由组
    "/workspace/newsManagement": [
        {
            path: "/workspace/newsManagement/newsDraft",
            permissionId: PermissionIds[PermissionEnum.ALL],
            priority: 1,
        },
        {
            path: "/workspace/newsManagement/pendingNews",
            permissionId: PermissionIds[PermissionEnum.NEWS_ADMIN],
            priority: 2,
        },
        {
            path: "/workspace/newsManagement/publishedNews",
            permissionId: PermissionIds[PermissionEnum.NEWS_ADMIN],
            priority: 3,
        },
    ],
    // 后台管理-权限管理路由组
    "/backgroundManagement/permissionManagement": [
        {
            path: "/backgroundManagement/permissionManagement/userList",
            permissionId: PermissionIds[PermissionEnum.BACKEND_MANAGEMENT],
            priority: 1,
        },
    ],
};

/**
 * 检查用户是否拥有指定权限
 * @param permissionId 权限ID
 * @param userPermissions 用户权限数组
 * @returns 是否拥有权限
 */
function hasPermission(permissionId: number, userPermissions: any[]): boolean {
    return userPermissions.some((permission) => permission.id === permissionId);
}

/**
 * 根据用户权限获取动态重定向路径
 * @param parentPath 父路由路径
 * @returns 重定向路径或null（如果没有权限访问任何子路由）
 */
export function getDynamicRedirectPath(parentPath: string): string | null {
    const userStore = useUserStore();
    const userPermissions = userStore.permissions || [];

    // 获取该父路由下的路由配置
    const routeConfigs = ROUTE_GROUPS[parentPath];
    if (!routeConfigs || routeConfigs.length === 0) {
        console.warn(`未找到路径 ${parentPath} 的路由配置`);
        return null;
    }

    // 过滤出用户有权限访问的路由，并按优先级排序
    const accessibleRoutes = routeConfigs
        .filter((config) => {
            // 如果权限ID为ALL，则表示所有人都能访问，直接放行
            if (config.permissionId === PermissionIds[PermissionEnum.ALL]) {
                return true;
            }
            
            // 如果用户拥有超级管理员权限，可以访问所有路由
            if (hasPermission(PermissionIds[PermissionEnum.ADMIN_PERMISSION], userPermissions)) {
                return true;
            }
            
            // 检查用户是否有对应的权限
            return hasPermission(config.permissionId, userPermissions);
        })
        .sort((a, b) => a.priority - b.priority);

    // 返回优先级最高的可访问路由
    if (accessibleRoutes.length > 0) {
        return accessibleRoutes[0].path;
    }

    // 如果没有任何可访问的路由，返回null
    console.warn(`用户没有访问路径 ${parentPath} 下任何子路由的权限`);
    return null;
}

/**
 * 获取所有需要动态重定向的路由配置
 * @returns 动态路由配置数组
 */
export function getDynamicRedirectRoutes() {
    return Object.keys(ROUTE_GROUPS).map((parentPath) => ({
        path: parentPath,
        redirect: () => {
            const redirectPath = getDynamicRedirectPath(parentPath);
            // 如果没有权限访问任何子路由，重定向到404页面或首页
            return redirectPath || "/404";
        },
    }));
}

/**
 * 添加新的路由组配置
 * @param parentPath 父路由路径
 * @param configs 路由配置数组
 */
export function addRouteGroup(parentPath: string, configs: RoutePermissionConfig[]) {
    ROUTE_GROUPS[parentPath] = configs;
}
