<template>
    <div class="top-nav">
        <div>
            <el-button type="text" @click="logout" style="color: #fff">退出登录</el-button>
        </div>
        <router-link to="/index">
            <div class="icon-button">
                <el-icon size="24" color="#ffffff">
                    <HomeFilled />
                </el-icon>
            </div>
        </router-link>
        <Notification />
        <div class="icon">
            <div class="icon-button">
                <el-icon size="24" color="#ffffff">
                    <User />
                </el-icon>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import useUserStore from "@/store/modules/user";
import Notification from "@/components/Notification/index.vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";

const router = useRouter();

// 退出登录
const logout = () => {
    useUserStore()
        .logOut()
        .then(() => {
            ElMessage.success("退出登录成功！");
            router.push("/login");
        })
        .catch((error) => {
            console.error(error);
        });
};
</script>

<style scoped lang="scss">
.icon-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.25);
    border-radius: 20px;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
        background: rgba(255, 255, 255, 0.35);
    }
}

.top-nav {
    position: relative;
    display: flex;
    z-index: 3;
    align-items: center;
    width: 100%;
    // margin-left: 32%; // 设置整体偏移
    justify-content: end;
    height: 64px;
    gap: 32px;
    padding: 0 32px;
    background-color: #334155;
    color: #fff;

    .icon {
        height: 64px;
        display: flex;
        align-items: center;
    }
}
</style>
