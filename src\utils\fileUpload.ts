import type { UploadUserFile, UploadRawFile } from "element-plus";
import { ElMessage } from "element-plus";
import { minioUploads } from "@/apis/fmsMinioController";

/**
 * 使用实例：
 * 
 * 上传文件列表
 * const urls = await uploadFiles(fileList);
 * 
 * 单文件上传
 * const input = document.querySelector('input[type="file"]');
 * if (input.files.length > 0) {
 *  const url = await uploadSingleFile(input.files[0]);
 * }
 * 
 * 启用分片上传：
 * const urls = await uploadFiles(fileList, {
 * chunkOptions: {
 *    enabled: true,
 *    chunkSize: 2 * 1024 * 1024, // 2MB分片
 *    concurrency: 3, // 并发上传3个分片
 *    onProgress: (progress) => {
 *      console.log(`上传进度: ${progress}%`);
 *    }
 *  }
 * });
 * 
 * 
 * 上传文件列表并设置请求头
 * const urls = await uploadFiles(fileList, {
 *  headers: {
 *    'Authorization': 'Bearer token'
 *  },
 *  onProgress: (progress) => {
 *    console.log(`总进度: ${progress}%`);
 *  },
 *  onSuccess: (urls) => {
 *    console.log('上传成功，URL列表:', urls);
 *  },
  onError: (error) => {
 *    console.error('上传失败:', error);
 *  }
 * });
 */

/**
 * 文件分片配置接口
 */
interface ChunkOptions {
    /** 是否启用分片上传 */
    enabled: boolean;
    /** 分片大小，默认为5MB */
    chunkSize?: number;
    /** 并发上传的分片数量，默认为3 */
    concurrency?: number;
    /** 分片上传进度回调 */
    onProgress?: (progress: number) => void;
}

/**
 * 文件上传配置接口
 */
interface UploadOptions {
    /** 请求头配置 */
    headers?: Record<string, string>;
    /** 分片上传配置 */
    chunkOptions?: ChunkOptions;
    /** 上传进度回调 */
    onProgress?: (progress: number) => void;
    /** 上传成功回调 */
    onSuccess?: (urls: string[]) => void;
    /** 上传失败回调 */
    onError?: (error: any) => void;
}

/**
 * 上传文件并获取URLs
 *
 * @param fileList 文件列表
 * @param options 上传配置选项
 * @returns 返回逗号分隔的URL字符串
 */
export async function uploadFiles(fileList: UploadUserFile[], options?: UploadOptions): Promise<string> {
    if (!fileList || fileList.length === 0) {
        return "";
    }

    try {
        // 只上传新文件（raw存在且url不存在的文件）
        const newFiles = fileList.filter((file) => file.raw && !file.url);

        // 如果没有新文件，直接返回现有文件的URL
        if (newFiles.length === 0) {
            return fileList
                .map((file) => file.url)
                .filter(Boolean)
                .join(",");
        }

        // 已有的文件URL
        const existingUrls = fileList.filter((file) => file.url).map((file) => file.url);

        // 判断是否启用分片上传
        if (
            options?.chunkOptions?.enabled &&
            newFiles.some((file) => file.raw && file.raw.size > (options.chunkOptions?.chunkSize || 5 * 1024 * 1024))
        ) {
            // 执行分片上传
            const newUrls = await uploadFilesWithChunks(newFiles, options);
            return [...existingUrls, ...newUrls].join(",");
        } else {
            // 执行普通上传
            const formData = new FormData();
            newFiles.forEach((file) => {
                if (file.raw) {
                    formData.append("files", file.raw);
                }
            });

            const res = await minioUploads({
                body: formData as any,
                options: {
                    headers: {
                        "Content-Type": "multipart/form-data",
                        ...options?.headers,
                    },
                },
            });

            if (res.data && res.data.length > 0) {
                // 合并新上传的文件URL和已有的文件URL
                const newUrls = res.data.map((item: any) => item.url);
                options?.onSuccess?.(newUrls);
                return [...existingUrls, ...newUrls].join(",");
            }
        }

        return existingUrls.join(",");
    } catch (error) {
        console.error("文件上传失败:", error);
        ElMessage.error("文件上传失败");
        options?.onError?.(error);

        // 出错时返回已有的文件URL
        return fileList
            .map((file) => file.url)
            .filter(Boolean)
            .join(",");
    }
}

/**
 * 分片上传文件
 *
 * @param files 文件列表
 * @param options 上传配置选项
 * @returns 返回上传后的URL数组
 */
async function uploadFilesWithChunks(files: UploadUserFile[], options?: UploadOptions): Promise<string[]> {
    // 分片大小，默认5MB
    const chunkSize = options?.chunkOptions?.chunkSize || 5 * 1024 * 1024;
    // 并发数，默认3个分片同时上传
    const concurrency = options?.chunkOptions?.concurrency || 3;

    const results: string[] = [];

    // 处理每个文件的上传
    for (const file of files) {
        if (!file.raw) continue;

        try {
            const fileSize = file.raw.size;
            const chunks: Blob[] = [];
            const totalChunks = Math.ceil(fileSize / chunkSize);

            // 切分文件
            for (let i = 0; i < totalChunks; i++) {
                const start = i * chunkSize;
                const end = Math.min(fileSize, start + chunkSize);
                chunks.push(file.raw.slice(start, end));
            }

            // 并发上传分片
            const uploadChunk = async (chunk: Blob, index: number) => {
                const formData = new FormData();
                // 添加文件信息
                formData.append("files", new File([chunk], file.raw!.name, { type: file.raw!.type }));
                formData.append("chunkIndex", index.toString());
                formData.append("totalChunks", totalChunks.toString());
                formData.append("fileName", file.raw!.name);

                const res = await minioUploads({
                    body: formData as any,
                    options: {
                        headers: {
                            "Content-Type": "multipart/form-data",
                            ...options?.headers,
                        },
                    },
                });

                // 更新进度
                if (options?.chunkOptions?.onProgress) {
                    const progress = ((index + 1) / totalChunks) * 100;
                    options.chunkOptions.onProgress(progress);
                }

                return res;
            };

            // 控制并发上传
            const results = [];
            for (let i = 0; i < totalChunks; i += concurrency) {
                const chunkPromises = [];
                for (let j = 0; j < concurrency && i + j < totalChunks; j++) {
                    chunkPromises.push(uploadChunk(chunks[i + j], i + j));
                }
                // 等待当前批次的分片上传完成
                const chunkResults = await Promise.all(chunkPromises);
                results.push(...chunkResults);
            }

            // 合并分片（这里假设服务端已经处理了分片合并）
            // 如果服务端需要额外的合并请求，这里需要添加合并逻辑

            // 获取合并后的URL（这里假设最后一个分片的响应包含了合并后的URL）
            if (results.length > 0 && results[results.length - 1].data && results[results.length - 1].data.length > 0) {
                results.push(results[results.length - 1].data[0].url);
            }
        } catch (error) {
            console.error(`文件 ${file.raw.name} 分片上传失败:`, error);
            ElMessage.error(`文件 ${file.raw.name} 分片上传失败`);
            options?.onError?.(error);
        }
    }

    return results;
}

/**
 * 单文件上传并获取URL
 *
 * @param file 单个文件
 * @param options 上传配置选项
 * @returns 返回上传后的URL
 */
export async function uploadSingleFile(file: File, options?: UploadOptions): Promise<string> {
    if (!file) {
        return "";
    }

    try {
        // 判断是否启用分片上传
        if (options?.chunkOptions?.enabled && file.size > (options.chunkOptions?.chunkSize || 5 * 1024 * 1024)) {
            // 创建一个符合UploadUserFile格式的对象
            const uploadFile: UploadUserFile = {
                name: file.name,
                uid: Date.now(), // 生成一个唯一ID
                raw: file as UploadRawFile,
            };
            // 执行分片上传
            const urls = await uploadFilesWithChunks([uploadFile], options);
            return urls[0] || "";
        } else {
            // 执行普通上传
            const formData = new FormData();
            formData.append("files", file);

            const res = await minioUploads({
                body: formData as any,
                options: {
                    headers: {
                        "Content-Type": "multipart/form-data",
                        ...options?.headers,
                    },
                },
            });

            if (res.data && res.data.length > 0) {
                const url = res.data[0].url;
                options?.onSuccess?.([url]);
                return url;
            }
        }

        return "";
    } catch (error) {
        console.error("文件上传失败:", error);
        ElMessage.error("文件上传失败");
        options?.onError?.(error);
        return "";
    }
}
