import { filesBaseUrl } from "@/utils/filesBaseUrl";
import { ElMessage } from "element-plus";

/**
 * 下载文件（普通下载方式）
 * @param filePath 文件路径
 * @param fileName 文件名
 * @returns Promise<void>
 */
export const downloadFile = async (filePath: string, fileName: string): Promise<void> => {
    if (!filePath.startsWith("/")) return;

    const fullUrl = `${filesBaseUrl}${filePath}`;

    try {
        // 统一使用 fetch API 下载文件
        console.log("开始下载：", fullUrl);

        const response = await fetch(fullUrl);
        if (!response.ok) throw new Error(`下载失败: ${response.status}`);

        const blob = await response.blob();
        const blobUrl = window.URL.createObjectURL(blob);

        const link = document.createElement("a");
        link.href = blobUrl;
        link.download = fileName;
        link.style.display = "none";

        document.body.appendChild(link);
        link.click();

        // 清理资源
        setTimeout(() => {
            document.body.removeChild(link);
            window.URL.revokeObjectURL(blobUrl);
        }, 100);
    } catch (error) {
        console.error("文件下载失败:", error);
        ElMessage.error("文件下载失败");
    }
};

/**
 * 判断是否为图片数据项
 * @param value 图片数据项
 * @returns
 */
export const isImageField = (value: string) => {
    if (!value) return false;
    const fileUrls = value.split(",");
    const imageExtensions = [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp"];
    return fileUrls.every((url) => {
        const lowerUrl = url.toLowerCase();
        return imageExtensions.some((ext) => lowerUrl.endsWith(ext));
    });
};

/**
 * 点击文件列表事件(用于el-upload组件中的文件列表)
 * @param file 文件对象
 */
export const handlePreview = async (file: { name: string; url: string }): Promise<void> => {
    if (!file?.url) {
        ElMessage.warning("文件链接不存在");
        return;
    }

    try {
        // 使用统一下载逻辑
        const response = await fetch(file.url);
        if (!response.ok) throw new Error(`下载失败: ${response.status}`);

        const blob = await response.blob();
        const blobUrl = window.URL.createObjectURL(blob);

        const link = document.createElement("a");
        link.href = blobUrl;
        link.download = file.name;
        link.click();

        window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
        console.error("文件预览下载失败:", error);
        ElMessage.error("文件下载失败");
    }
};

import { ElLoading } from "element-plus";
import { downloadFileRequest } from "@/utils/request";

// 定义通用接口
interface DownloadOptions {
    loadingText?: string;
    successText?: string;
    fileName?: string;
    fileType?: string;
}

// 文件类型枚举映射
const FILE_MIME_TYPES = {
    xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    csv: "text/csv",
    pdf: "application/pdf",
    docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    zip: "application/zip",
} as const;

// 图片扩展名集合
const IMAGE_EXTENSIONS = [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp"] as const;

/**
 * 通用文件下载函数（下载接口返回blob，支持自定义传入文件类型）
 * @param url 下载接口URL
 * @param data 请求参数
 * @param options 额外选项配置
 * @returns Promise<void>
 */
export async function commonDownloadFile(
    url: string,
    data: Record<string, any>,
    options?: DownloadOptions
): Promise<void> {
    const {
        loadingText = "正在导出数据，请稍候...",
        successText = "导出成功！请在浏览器下载文件中查看！",
        fileName = "下载文件",
        fileType = FILE_MIME_TYPES.xlsx,
    } = options || {};

    const loadingInstance = ElLoading.service({
        lock: true,
        text: loadingText,
        background: "rgba(0, 0, 0, 0.7)",
    });

    try {
        // 调用下载接口
        const res = await downloadFileRequest<Blob>(url, data);

        // 处理可能的 JSON 错误响应
        if (res.data instanceof Blob && res.data.type === "application/json") {
            const text = await res.data.text();
            const errorData = JSON.parse(text);
            throw new Error(errorData.message || "下载失败");
        }

        // 从 header 中提取文件名
        const contentDisposition = res.headers["content-disposition"];
        let downloadFileName = fileName;

        if (contentDisposition) {
            const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
            const matches = filenameRegex.exec(contentDisposition);
            if (matches && matches[1]) {
                downloadFileName = decodeURIComponent(matches[1].replace(/['"]/g, ""));
            }
        }

        // 创建Blob对象并下载
        const blob = new Blob([res.data], { type: fileType });
        const blobUrl = window.URL.createObjectURL(blob);

        const link = document.createElement("a");
        link.href = blobUrl;
        link.download = downloadFileName;
        link.click();

        setTimeout(() => window.URL.revokeObjectURL(blobUrl), 100);

        ElMessage.success(successText);
        return Promise.resolve();
    } catch (error) {
        console.error("下载失败:", error);
        ElMessage.error(error instanceof Error ? error.message : "下载失败");
        return Promise.reject(error);
    } finally {
        loadingInstance.close();
    }
}

/**
 * 根据文件名或URL判断文件类型
 * @param fileName 文件名或URL
 * @returns 文件类型信息
 */
export function getFileTypeInfo(fileName: string): {
    extension: string;
    isImage: boolean;
    mimeType: string;
} {
    const extension = fileName.split(".").pop()?.toLowerCase() || "";
    const isImage = IMAGE_EXTENSIONS.some((ext) => ext.includes(extension));

    // 获取MIME类型
    let mimeType = "";
    if (isImage) {
        mimeType = `image/${extension === "jpg" ? "jpeg" : extension}`;
    } else {
        mimeType = FILE_MIME_TYPES[extension as keyof typeof FILE_MIME_TYPES] || "application/octet-stream";
    }

    return {
        extension,
        isImage,
        mimeType,
    };
}
