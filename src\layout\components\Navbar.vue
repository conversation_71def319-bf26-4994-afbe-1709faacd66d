<template>
    <div class="custom-navbar">
        <div class="nav-menu">
            <div
                v-for="(route, index) in routeList"
                :key="index"
                class="nav-item"
                :class="{ 'is-active': isActive(route.path) }"
                @click="handleSelect(route.path)">
                {{ route.title }}
                <div v-if="isActive(route.path)" class="active-indicator"></div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useRouter, useRoute, RouteLocationMatched } from "vue-router";
import { ElMessageBox } from "element-plus";
import { useDataEntryStore } from "@/store/modules/dataEntry";
import { truncatePathToLevel } from "@/utils/pathUtils";

const router = useRouter();
const route = useRoute();
const dataEntryStore = useDataEntryStore();

const currentRoute = computed(() => router.currentRoute.value);

// 当前导航菜单列表
const routeList = ref<RouteLocationMatched[]>([]);

// 监听路由变化，更新导航菜单
watch(
    route,
    (newRoute) => {
        const matched = router.currentRoute.value.matched;
        const parentRoute = matched.find((item) => {
            const pathSegments = item.path.split("/").filter((v) => v !== "");
            return pathSegments.length === 2;
        });

        if (parentRoute?.children) {
            routeList.value = parentRoute.children as RouteLocationMatched[];
        } else {
            routeList.value = [];
        }
    },
    { immediate: true },
);

// 判断菜单项是否激活
const isActive = (path: string) => {
    return route.path === path;
};

// 处理菜单选择
const handleSelect = async (path: string) => {
    // 获取当前路由的前三级路径
    const currentBase = truncatePathToLevel(currentRoute.value.path, 3);
    // 获取目标路径的前三级路径
    const targetBase = truncatePathToLevel(path, 3);

    if (dataEntryStore.isEnteringData) {
        try {
            ElMessageBox.confirm(
                "检测到您正在输入数据，此时切换页面将会丢失所有未保存的内容。是否确认跳转到新页面？",
                "提示",
                {
                    confirmButtonText: "确认跳转",
                    cancelButtonText: "取消",
                    type: "warning",
                },
            ).then(() => {
                dataEntryStore.isEnteringData = false;

                if (currentBase === targetBase) {
                    console.log("准备刷新");
                    const timestamp = new Date().getTime();
                    // 使用带时间戳的replace方式强制刷新
                    router
                        .replace({
                            path: path,
                            query: {
                                t: timestamp,
                                // 添加随机参数确保每次路由都不同
                                _: Math.random().toString(36).substr(2),
                            },
                        })
                        .then(() => {
                            // 强制刷新当前路由
                            router.replace({ path: path, query: {} });
                        });
                } else {
                    router.push(path);
                }
            });
        } catch {
            // 用户取消切换，不做任何操作
        }
    } else {
        router.push(path);
    }
};
</script>

<style lang="scss" scoped>
.custom-navbar {
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    width: 100%;
    font-size: 14px;
    font-weight: 400;
    font-style: normal;
    height: 47px;
    box-sizing: border-box;

    .nav-menu {
        display: flex;
        align-items: stretch;
        gap: 40px;
        flex-direction: row;
    }

    .nav-item {
        height: 47px;
        position: relative;
        display: flex;
        align-items: center;
        line-height: 22px;
        min-height: 22px;
        cursor: pointer;
        transition: color 0.3s ease;
        user-select: none;
        color: rgba(0, 0, 0, 0.65);
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 0;
        border: none;
        background: transparent;

        &:hover {
            color: #1677ff;
        }

        &.is-active {
            color: #1677ff;
            font-weight: 500;
        }

        .active-indicator {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background-color: #1677ff;
        }
    }
}
</style>
