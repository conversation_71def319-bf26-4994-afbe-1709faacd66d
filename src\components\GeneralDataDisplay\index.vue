<template>
    <div class="wrapper">
        <div class="wrapper-top">
            <slot name="top"></slot>
        </div>
        <div class="wrapper-content">
            <slot name="content"></slot>
        </div>
    </div>
</template>

<script setup lang="ts"></script>

<style scoped lang="scss">
.wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
    .wrapper-content {
        margin-top: 12px;
        flex: 1;
        height: 0;
    }
}
</style>
