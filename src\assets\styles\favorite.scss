// 收藏图标样式
.template-icon-star {
    padding: 0 3px;
    margin-right: 10px;
    background-color: #fff;
    height: calc(100% - 2px);
    border-radius: 5%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease;

    &:hover {
        transform: scale(1.1);
    }
}

.star-icon {
    transition: all 0.3s ease;
}

.star-click {
    animation: star-pulse 0.3s ease;
}

.template-wrapper {
    transition: all 0.3s ease;

    &.favorite-item {
        animation: highlight-favorite 1s ease;
    }
}

/* 收藏列表动画 */
.favorite-list-enter-active,
.favorite-list-leave-active {
    transition: all 0.5s ease;
}

.favorite-list-enter-from {
    opacity: 0;
    transform: translateY(30px);
}

.favorite-list-leave-to {
    opacity: 0;
    transform: translateX(-30px);
}

/* 星星点击动画 */
@keyframes star-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.3);
    }
    100% {
        transform: scale(1);
    }
}

/* 收藏项目突出显示动画 */
@keyframes highlight-favorite {
    0% {
        background-color: rgba(255, 255, 200, 0.5);
    }
    100% {
        background-color: transparent;
    }
}
