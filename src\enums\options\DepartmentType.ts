/**
 * 所属部门：下拉菜单内容从组织架构中调用
 * 
 * 注意：实际部门数据应从组织架构API中动态获取，此枚举仅作为类型定义使用
 * 实际使用时应通过API获取组织架构数据来构建下拉菜单
 */
export interface DepartmentOption {
    /** 部门ID */
    id: string | number;
    /** 部门名称 */
    name: string;
    /** 上级部门ID */
    parentId?: string | number;
    /** 子部门列表 */
    children?: DepartmentOption[];
}

// 示例：用于类型定义，实际数据应从API获取
export const DepartmentTypeExample: DepartmentOption[] = [
    {
        id: 1,
        name: "学校",
        children: [
            {
                id: 2,
                name: "行政部门",
                parentId: 1,
                children: [
                    { id: 3, name: "人事处", parentId: 2 },
                    { id: 4, name: "教务处", parentId: 2 },
                ]
            },
            {
                id: 5,
                name: "教学单位",
                parentId: 1,
                children: [
                    { id: 6, name: "计算机学院", parentId: 5 },
                    { id: 7, name: "外国语学院", parentId: 5 },
                ]
            }
        ]
    }
]; 