import { cmsClassifyList } from "@/apis/cmsClassifyController";
import { CmsClassify_ } from "@/apis/types";

export function useClassify() {
    /**
     * 大类归类列表
     */
    const classifyList = ref<CmsClassify_[]>([]);

    /**
     * 获取大类归类列表
     */
    const fetchClassifyList = async () => {
        try {
            const res = await cmsClassifyList({});
            classifyList.value = res.data;
        } catch (error) {
            console.error("加载大类归类列表失败:", error);
        }
    };

    onMounted(() => {
        fetchClassifyList();
    });

    return {
        classifyList,
        fetchClassifyList
    };
}
