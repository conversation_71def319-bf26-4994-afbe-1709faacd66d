# v-drag-scroll 横向拖拽滚动指令

此指令提供了一种简单的方式来为任何需要横向滚动的元素添加拖拽滚动功能。当元素内容宽度超过容器宽度时，会自动启用拖拽滚动功能。

## 基本用法

只需将 `v-drag-scroll` 指令添加到需要支持拖拽滚动的元素上：

```vue
<template>
  <div class="scrollable-container" v-drag-scroll>
    <!-- 内容可能超过容器宽度 -->
    <div class="item" v-for="item in items" :key="item.id">{{ item.name }}</div>
  </div>
</template>
```

## 配置选项

你可以传递一个对象来自定义指令的行为：

```vue
<template>
  <div class="scrollable-container" v-drag-scroll="{ speed: 2 }">
    <!-- 内容 -->
  </div>
</template>
```

### 可用选项

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|------|
| speed | 滚动速度系数，值越大滚动越快 | number | 1.5 |

## 额外样式建议

为了提供更好的用户体验，建议为使用该指令的元素添加以下CSS样式：

```css
.scrollable-container {
  overflow-x: auto; /* 确保在不支持拖拽时也可以滚动 */
  user-select: none; /* 防止拖拽过程中选中文本 */
}

/* 自定义滚动条样式 */
.scrollable-container::-webkit-scrollbar {
  height: 6px;
}

.scrollable-container::-webkit-scrollbar-thumb {
  background-color: #d9d9d9;
  border-radius: 3px;
}

.scrollable-container::-webkit-scrollbar-track {
  background-color: #f5f5f5;
}
```

## 使用场景

- 水平菜单或标签列表，内容可能超出容器宽度
- 数据卡片横向展示
- 图片画廊
- 任何需要横向滚动的内容区域 