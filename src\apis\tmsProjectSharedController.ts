/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 分页获取项目共享列表 POST /tms/tmsProjectShared/getValuesByProjectId */
export async function tmsProjectSharedGetValuesByProjectId({
  body,
  options,
}: {
  body: API.TmsProjectSharedQueryParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageCmsAchievement_>(
    '/tms/tmsProjectShared/getValuesByProjectId',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
