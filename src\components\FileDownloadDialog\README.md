# FileDownloadDialog 文件下载弹窗组件

基于 CustomDialog 组件开发的文件下载弹窗，用于显示文件列表并支持单个文件下载功能。

## 功能特性

- 📁 解析文件列表字符串，显示文件信息
- ✅ 支持文件选择（复选框）
- 📥 单个文件下载功能
- 🎨 美观的表格布局，符合设计稿
- 📊 显示选中文件统计信息
- 🔄 为批量下载预留接口

## 基本用法

### 组件方式

```vue
<template>
    <FileDownloadDialog 
        v-model="dialogVisible"
        :file-list-string="fileListString"
    />
    <el-button @click="openDialog">打开文件下载弹窗</el-button>
</template>

<script setup>
import { ref } from 'vue';
import { FileDownloadDialog } from '@/components/FileDownloadDialog';

const dialogVisible = ref(false);
const fileListString = ref('/cdmp/20250708/5d636ccce92b41ac96d7f6482169b8fd::测试文件01.docx,/cdmp/20250708/e880c16fe293420c968a2b08d942b98b::测试文件02.docx');

const openDialog = () => {
    dialogVisible.value = true;
};
</script>
```

### Hook 方式

```vue
<template>
    <FileDownloadDialog 
        v-model="visible"
        :file-list-string="fileListString"
    />
    <el-button @click="handleOpenDialog">打开文件下载弹窗</el-button>
</template>

<script setup>
import { FileDownloadDialog, useFileDownloadDialog } from '@/components/FileDownloadDialog';

const { 
    visible, 
    fileListString, 
    openDialog, 
    updateFileList 
} = useFileDownloadDialog();

const handleOpenDialog = () => {
    const files = '/cdmp/20250708/5d636ccce92b41ac96d7f6482169b8fd::测试文件01.docx,/cdmp/20250708/e880c16fe293420c968a2b08d942b98b::测试文件02.docx';
    openDialog(files);
};
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| modelValue | 弹窗显示状态 | `boolean` | `false` |
| fileListString | 文件列表字符串 | `string` | `''` |

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:modelValue | 弹窗显示状态变化 | `(value: boolean)` |

## 文件列表格式

文件列表字符串格式：`文件路径::文件名,文件路径::文件名`

示例：
```
/cdmp/20250708/5d636ccce92b41ac96d7f6482169b8fd::测试文件01.docx,/cdmp/20250708/e880c16fe293420c968a2b08d942b98b::测试文件02.docx,/cdmp/20250708/281333148bc84908a43ee13aff7e9461::测试文件03.docx
```

- 使用 `,` 分隔多个文件
- 使用 `::` 分隔文件路径和文件名
- 文件路径部分会加上 `filesBaseUrl` 进行下载

## 文件图标

组件会根据文件扩展名自动显示对应的图标：

- Excel 文件（.xlsx, .xls, .csv）：Excel 图标
- Word 文件（.doc, .docx）：Word 图标  
- 图片文件（.jpg, .jpeg, .png, .gif, .bmp, .webp）：图片图标
- 其他文件：默认 ZIP 图标

## 下载功能

### 单个文件下载
- 点击每行的"下载"按钮可下载单个文件
- 使用项目中的 `downloadFile` 工具函数
- 自动添加 `filesBaseUrl` 前缀
- 包含下载进度提示和错误处理

### 批量下载功能
- 选择多个文件后，点击底部"批量下载 ZIP"按钮
- 自动将选中文件打包为ZIP格式下载
- 使用 `minioDownloadZip` API接口
- 支持任意数量文件的批量下载

### 全选功能
- 表头复选框支持全选/取消全选操作
- 支持半选状态显示（部分文件被选中时）
- 与批量下载联动，方便快速选择所有文件

## 样式特性

- 符合 Element Plus 设计规范
- 响应式布局，适配不同屏幕尺寸
- 悬停效果和选中状态提示
- 与项目整体风格保持一致

## 🎯 核心特性

| 特性 | 状态 | 说明 |
|------|------|------|
| 📁 文件列表解析 | ✅ 完成 | 支持格式：`/path::filename` |
| ✅ 文件选择功能 | ✅ 完成 | 多选复选框，实时统计 |
| 📥 单个文件下载 | ✅ 完成 | 点击下载按钮即可下载 |
| 🎨 UI 设计还原 | ✅ 完成 | 完美匹配设计稿效果 |
| 📊 文件信息显示 | ✅ 完成 | 自动获取文件大小、更新时间 |
| 📦 批量下载 | ✅ 完成 | 支持多文件打包ZIP下载 |
| 🔘 全选功能 | ✅ 完成 | 表头全选/取消全选，支持半选状态 |

## 注意事项

1. ✅ 文件大小和更新时间已通过 `minioGetFileInfos` API 自动获取
2. ✅ 批量下载功能已完成，支持多文件打包为ZIP下载
3. 📁 依赖项目中的 `downloadFile` 工具函数和 `filesBaseUrl` 配置
4. 🎨 基于 CustomDialog 组件，继承其所有特性
5. 📊 选中文件的总大小会实时计算并显示 