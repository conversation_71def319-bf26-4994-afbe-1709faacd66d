import { nextTick, watch, onMounted, onUnmounted, type Ref } from "vue";
import type { CmsClassify_ } from "@/apis/types";

/**
 * 模板高度调整Hook
 * @param classifyList 分类列表
 * @param templateList 模板列表
 */
export function useTemplateHeight(classifyList: Ref<CmsClassify_[]>, templateList: Ref<any[]>) {
    // 调整同类别模板高度
    const adjustTemplateHeights = () => {
        // 获取所有分类ID
        const categoryIds = Array.from(new Set(classifyList.value.map((item) => item.id)));

        // 为每个分类调整高度
        nextTick(() => {
            categoryIds.forEach((categoryId) => {
                // 获取特定分类的所有模板容器
                const templateWrappers = document.querySelectorAll(
                    `.template-wrapper[data-category-id="${categoryId}"]`
                );

                if (templateWrappers.length > 0) {
                    // 找出最高的模板
                    let maxHeight = 0;
                    templateWrappers.forEach((wrapper) => {
                        const template = wrapper.querySelector(".each-template");
                        if (template) {
                            maxHeight = Math.max(maxHeight, template.scrollHeight);
                        }
                    });

                    // 设置所有模板为相同的高度
                    templateWrappers.forEach((wrapper) => {
                        const template = wrapper.querySelector(".each-template");
                        if (template) {
                            (template as HTMLElement).style.height = `${maxHeight}px`;
                        }
                    });
                }
            });
        });
    };

    // 监听模板列表变化时调整高度
    watch(
        templateList,
        () => {
            adjustTemplateHeights();
        },
        { deep: true }
    );

    // 设置生命周期钩子
    const setupLifecycle = () => {
        // 添加窗口调整事件监听器
        window.addEventListener("resize", adjustTemplateHeights);

        // 卸载时移除监听器
        onUnmounted(() => {
            window.removeEventListener("resize", adjustTemplateHeights);
        });
    };

    return {
        adjustTemplateHeights,
        setupLifecycle,
    };
}
