<template>
    <el-dialog
        v-model="visible"
        width="700"
        class="performance-rules-dialog"
        :close-on-click-modal="closeOnClickModal"
        :close-on-press-escape="closeOnPressEscape"
        append-to-body
        :title="title || `已关联绩效规则 (${getLeafNodesCount(performanceRulesList)}条）`">

        <div v-if="performanceRulesList.length > 0">
            <div v-for="v in performanceRulesList" :key="v.id" style="background-color: #fffcf9; padding: 10px">
                <div v-for="(node, index) in traverseTree(v, '')" :key="node.id" style="margin-bottom: 20px">
                    <div
                        style="font-size: 14px; font-weight: bold; color: #23346d; margin-bottom: 5px"
                        v-if="node.isLeaf">
                        {{ index + 1 }}.{{ node.parentPath }}
                    </div>
                    <div style="font-size: 13px; letter-spacing: 0.1em" v-if="node.isLeaf">
                        {{ node.content }}
                    </div>
                </div>
            </div>
        </div>
        
        <div v-else>
            <div style="text-align: center; color: #c6c6c6; font-size: 14px">
                {{ emptyText || '暂无关联的绩效规则' }}
            </div>
        </div>
        
        <template #footer>
            <div class="dialog-footer">
                <el-button
                    v-if="showChangeRulesButton && performanceRulesList.length > 0"
                    type="primary"
                    @click="handleChangeRules">
                    {{ changeRulesButtonText || '更改绩效规则' }}
                </el-button>
                <el-button v-if="showCloseButton" @click="handleClose">
                    {{ closeButtonText || '关闭' }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { traverseTree, getLeafNodesCount } from '@/utils/tree';
import type { CmsTagNode_ } from '@/apis/types';
import { useRouter } from 'vue-router';

interface Props {
    modelValue?: boolean;
    performanceRulesList?: CmsTagNode_[];
    title?: string;
    emptyText?: string;
    showChangeRulesButton?: boolean;
    changeRulesButtonText?: string;
    changeRulesPath?: string;
    showCloseButton?: boolean;
    closeButtonText?: string;
    closeOnClickModal?: boolean;
    closeOnPressEscape?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    performanceRulesList: () => [],
    showChangeRulesButton: true,
    changeRulesPath: '/backgroundManagement/centralSettings/performanceRuleManagement',
    showCloseButton: true,
    closeOnClickModal: false,
    closeOnPressEscape: true,
});

const emit = defineEmits<{
    'update:modelValue': [value: boolean];
    'change-rules': [];
    'close': [];
}>();

const router = useRouter();

const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
});

const handleChangeRules = () => {
    emit('change-rules');
    if (props.changeRulesPath) {
        router.push(props.changeRulesPath);
    }
    visible.value = false;
};

const handleClose = () => {
    emit('close');
    visible.value = false;
};
</script>

<style scoped lang="scss">
// 样式已在src/assets/styles/modals.scss中定义
</style> 