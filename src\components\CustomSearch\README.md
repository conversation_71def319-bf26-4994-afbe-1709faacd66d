# CustomSearch 组件

一个可复用的搜索组件，包含输入框和搜索按钮。

## 功能特性

- 支持双向绑定 (`v-model`)
- 支持回车键搜索
- 支持清空功能
- 可自定义占位符和输入框宽度
- 一致的 UI 设计风格

## 使用方法

### 基本用法

```vue
<template>
    <CustomSearch
        v-model="searchKeyword"
        @search="handleSearch"
        @clear="handleClear" />
</template>

<script setup lang="ts">
import CustomSearch from '@/components/CustomSearch';

const searchKeyword = ref('');

const handleSearch = (keyword: string) => {
    console.log('搜索关键词:', keyword);
    // 执行搜索逻辑
};

const handleClear = () => {
    console.log('清空搜索');
    // 执行清空逻辑
};
</script>
```

### 自定义配置

```vue
<template>
    <CustomSearch
        v-model="searchKeyword"
        placeholder="请输入搜索内容"
        input-width="300px"
        @search="handleSearch"
        @clear="handleClear" />
</template>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|-------|
| modelValue | 搜索关键词 | `string` | `''` |
| placeholder | 输入框占位符 | `string` | `'输入关键字检索'` |
| inputWidth | 输入框宽度 | `string` | `'206px'` |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:modelValue | 搜索关键词变化时触发 | `(value: string)` |
| search | 点击搜索按钮或按回车键时触发 | `(keyword: string)` |
| clear | 清空搜索时触发 | `()` |

## 实例方法

| 方法名 | 说明 | 参数 |
|--------|------|------|
| handleClear | 清空搜索时触发的事件 | `() => void` |

## 使用示例

### 在表格中使用

```vue
<template>
    <div>
        <CustomSearch
            v-model="searchKeyword"
            @search="handleSearch"
            @clear="handleClear" />
        
        <el-table :data="filteredTableData">
            <!-- 表格列定义 -->
        </el-table>
    </div>
</template>

<script setup lang="ts">
import CustomSearch from '@/components/CustomSearch';

const searchKeyword = ref('');
const tableData = ref([]);
const originalTableData = ref([]);
const filteredTableData = ref([]);

const handleSearch = (keyword: string) => {
    if (keyword) {
        filteredTableData.value = originalTableData.value.filter(item => {
            return item.name.includes(keyword) || item.code.includes(keyword);
        });
    } else {
        filteredTableData.value = [...originalTableData.value];
    }
};

const handleClear = () => {
    filteredTableData.value = [...originalTableData.value];
};
</script>
```

## 时序说明

组件内部使用了 `nextTick` 来确保在清空搜索时，`v-model` 值的更新在 `clear` 事件触发之前完成。这样可以避免在清空搜索时网络请求仍然携带旧的搜索值的问题。

### 推荐的清空处理方式

```vue
<script setup lang="ts">
import { nextTick } from 'vue';

const handleClear = () => {
    // 如果需要立即执行刷新操作，使用 nextTick 确保搜索值已更新
    nextTick(() => {
        // 执行刷新或其他操作
        refreshData();
    });
};
</script>
```

## 样式定制

组件使用了 Sass 嵌套语法，可以通过以下方式进行样式定制：

```scss
:deep(.custom-search) {
    .search-container {
        // 自定义搜索容器样式
    }
    
    .search-button {
        // 自定义搜索按钮样式
    }
}
``` 