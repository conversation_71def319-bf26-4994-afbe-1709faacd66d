<template>
    <div class="project-application">
        <!-- 顶部标题 -->
        <div class="title-section">
            <h2 class="page-title">搜索立项资料</h2>
        </div>

        <!-- 搜索条件区域 -->
        <div class="search-section">
            <!-- 搜索条件表单 -->
            <div class="search-form">
                <div class="search-form-right">
                    <!-- 时间范围限定 -->
                    <div class="time-range-section">
                        <span class="simple-text">限定时间范围：</span>
                        <div class="time-range-container">
                            <div
                                class="time-range-item"
                                :class="{ active: timeRangeType === 'NULL' }"
                                @click="selectTimeRange('NULL')">
                                全部时间
                            </div>
                            <div
                                class="time-range-item"
                                :class="{ active: timeRangeType === 'ONE_YEAR' }"
                                @click="
                                    timeRangeType === 'ONE_YEAR' ? selectTimeRange('NULL') : selectTimeRange('ONE_YEAR')
                                ">
                                近一年
                            </div>
                            <div
                                class="time-range-item"
                                :class="{ active: timeRangeType === 'THREE_YEARS' }"
                                @click="
                                    timeRangeType === 'THREE_YEARS'
                                        ? selectTimeRange('NULL')
                                        : selectTimeRange('THREE_YEARS')
                                ">
                                近三年
                            </div>
                            <div
                                class="time-range-item"
                                :class="{ active: timeRangeType === 'CUSTOM' }"
                                @click="selectTimeRange('CUSTOM')">
                                自定义时间
                            </div>
                            <!-- 自定义时间日期选择器 -->
                            <div v-if="timeRangeType === 'CUSTOM'" class="custom-date-picker">
                                <el-date-picker
                                    style="width: 100%"
                                    v-model="customDateRange"
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    format="YYYY/MM/DD"
                                    value-format="YYYY-MM-DD"
                                    :disabled-date="disableFutureDates"
                                    :default-time="defaultTimes" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="search-form-left">
                    <div class="simple-text">添加搜索条件：</div>
                    <div>
                        <div class="search-condition-row" v-for="(item, index) in searchConditions" :key="index">
                            <div class="condition-item">
                                <el-select
                                    v-model="item.typeValue"
                                    class="condition-label"
                                    @change="handleConditionTypeChange(index)">
                                    <el-option
                                        v-for="option in getAvailableConditionTypes(index)"
                                        :key="option.value"
                                        :label="option.label"
                                        :value="option.value" />
                                </el-select>

                                <!-- 根据条件类型显示不同的输入控件 -->
                                <!-- 项目状态下拉菜单 -->
                                <el-select
                                    v-if="item.typeValue === publicPerformanceType.PROJECT_STATUS"
                                    v-model="item.value"
                                    class="condition-input"
                                    placeholder="请选择项目状态">
                                    <el-option
                                        v-for="option in projectStatusList"
                                        :key="option.value"
                                        :label="option.text"
                                        :value="option.text" />
                                </el-select>

                                <!-- 级别下拉菜单 -->
                                <el-select
                                    v-else-if="item.typeValue === publicPerformanceType.GRADE"
                                    v-model="item.value"
                                    class="condition-input"
                                    placeholder="请选择级别">
                                    <el-option
                                        v-for="option in gradeFilterList"
                                        :key="option.value"
                                        :label="option.text"
                                        :value="option.text" />
                                </el-select>

                                <!-- 负责人/参与人下拉菜单 -->
                                <el-select
                                    clearable
                                    filterable
                                    v-else-if="
                                        item.typeValue === publicPerformanceType.PROJECT_MAIN ||
                                        item.typeValue === publicPerformanceType.PROJECT_PARTICIPATE
                                    "
                                    v-model="item.value"
                                    class="condition-input"
                                    placeholder="请选择负责人/参与人">
                                    <el-option
                                        v-for="option in allPersonList"
                                        :key="option.id"
                                        :label="option.employeeName"
                                        :value="option.id" />
                                </el-select>

                                <!-- 其他条件类型使用文本输入框 -->
                                <el-input v-else v-model="item.value" placeholder="请输入" class="condition-input" />

                                <div class="operation-buttons">
                                    <el-button
                                        type="primary"
                                        circle
                                        @click="addCondition"
                                        v-if="hasAvailableConditionTypes"
                                        >+</el-button
                                    >
                                    <el-button
                                        type="primary"
                                        circle
                                        @click="removeCondition(index)"
                                        v-if="searchConditions.length > 1"
                                        >-</el-button
                                    >
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 搜索按钮 -->
            <div class="search-button-container">
                <el-button type="primary" class="search-btn" :disabled="isSearchDisabled" @click="handleSearch">
                    搜索
                </el-button>
            </div>
        </div>

        <!-- 搜索结果表格区域 -->
        <div class="search-result-section" v-if="showSearchResult && searchResultData.length > 0" v-loading="loading">
            <el-table :data="searchResultData" border style="width: 100%">
                <el-table-column
                    prop="owner"
                    label="资料所属"
                    align="center"
                    :filters="personFilterList"
                    :filter-method="personFilterHandler">
                    <template #default="scope">
                        {{ getPersonName(allPersonListWithDisabled, String(scope.row.owner)) }}
                    </template>
                </el-table-column>
                <el-table-column prop="templateName" label="数据表" align="center" />
                <el-table-column prop="projectName" label="项目名称" align="center" />
                <el-table-column
                    prop="projectStatus"
                    :filters="projectStatusList"
                    label="项目状态"
                    :filter-method="projectStatusFilterHandler"
                    width="100"
                    align="center">
                    <template #default="scope">
                        <div>
                            {{ getFieldName(dictList, scope.row.projectStatus) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="grade"
                    label="级别"
                    align="center"
                    :filters="gradeFilterList"
                    :filter-method="gradeFilterHandler">
                    <template #default="scope">
                        <div>
                            {{ getFieldName(dictList, scope.row.grade) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="projectTime" label="项目时间" align="center" sortable />
                <el-table-column prop="proofMaterial" label="佐证材料" align="center">
                    <template #default="scope">
                        <div style="color: #1677ff; cursor: pointer" @click="handleDownload(scope.row)">下载材料</div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" align="center" width="200">
                    <template #default="scope">
                        <el-button
                            :disabled="scope.row.isExist === 1 || scope.row.isExist === 2"
                            type="primary"
                            text
                            @click="requestAchievement(scope.row)">
                            <span v-if="scope.row.isExist === 0">申请成果</span>
                            <span v-else-if="scope.row.isExist === 1">申请中</span>
                            <span v-else>申请成功</span>
                        </el-button>
                        <el-button type="primary" text style="margin-left: 0px">查看详情</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    size="small"
                    background
                    layout="prev, pager, next"
                    prev-text="上一页"
                    next-text="下一页"
                    :total="totalItems"
                    :current-page="currentPage"
                    @current-change="handlePageChange"
                    @size-change="handleSizeChange" />
            </div>
        </div>
        <!-- 文件下载弹窗 -->
        <FileDownloadDialog v-model="fileDownloadDialogVisible" :file-list-string="fileListString" />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { publicPerformanceType, publicPerformanceTypeMap } from "@/models/publicPerformanceType";
import { cmsCategoryRowSearchProjectInitiationMaterials } from "@/apis/cmsCategoryRowController";
import { ElMessage, ElMessageBox } from "element-plus";
import type { PublicItemParam, SearchProjectInitiationMaterialsParam } from "@/apis/types";
import { usePersons } from "@/hooks/usePersons";
import { getPersonName, getFieldName } from "@/utils/getNames";
import { useDict } from "@/hooks/useDict";
import { tmsProjectRequestRequestShared } from "@/apis/tmsProjectRequestController";
import { useProjectStatusFilter, useGradeFilter, usePersonFilter } from "@/utils/filterUtils";
import { useFileDownload } from "@/hooks/useFileDownload";
import FileDownloadDialog from "@/components/FileDownloadDialog/index.vue";

const { allPersonListWithDisabled, allPersonList, getPersonList } = usePersons(true);

const { dictList } = useDict();

const props = defineProps({
    projectId: {
        type: Number,
        required: true,
    },
});

const { projectStatusList, projectStatusFilterHandler } = useProjectStatusFilter(dictList);
const { gradeFilterList, gradeFilterHandler } = useGradeFilter(dictList);

// 使用文件下载hooks
const { fileDownloadDialogVisible, fileListString, handleDownload: handleFileDownload } = useFileDownload();

// 下载材料功能 - 使用统一的hooks
const handleDownload = (row: any) => {
    handleFileDownload({
        row,
        fieldName: "proofMaterial",
        mode: "singleField",
    });
};

// 定义搜索条件接口
interface SearchCondition {
    typeValue: number;
    typeName: string;
    value: string | number;
}

// 获取所有公共标识符枚举（除了时间）
const allPerformanceTypes = computed(() => {
    return Object.values(publicPerformanceType)
        .filter((type) => typeof type === "number") // 过滤掉枚举的反向映射
        .filter((type) => type !== publicPerformanceType.DATE) // 过滤掉时间项
        .map((type) => ({
            value: type as number,
            label: publicPerformanceTypeMap[type as publicPerformanceType],
        }));
});

// 已选择的条件类型IDs
const selectedTypeIds = computed(() => searchConditions.map((item) => item.typeValue));

// 获取未被选择的条件类型选项
const getAvailableConditionTypes = (currentIndex: number) => {
    const currentValue = searchConditions[currentIndex]?.typeValue;
    return allPerformanceTypes.value.filter(
        (option) => !selectedTypeIds.value.includes(option.value) || option.value === currentValue,
    );
};

// 检查是否还有可用的条件类型
const hasAvailableConditionTypes = computed(() => {
    return allPerformanceTypes.value.length > searchConditions.length;
});

// 检查搜索按钮是否应该被禁用
const isSearchDisabled = computed(() => {
    // 检查时间范围是否为空（customDateRange为空，则代表没有选择时间范围）
    const isTimeRangeEmpty = !customDateRange.value && timeRangeType.value !== "NULL";

    // 检查搜索条件是否有空值
    const hasEmptyCondition = searchConditions.some((condition) => {
        return !condition.value || (typeof condition.value === "string" && condition.value.trim() === "");
    });

    // 当时间范围未选择或有空的搜索条件时，禁用搜索按钮
    return isTimeRangeEmpty || hasEmptyCondition;
});

// 默认只显示项目负责人
const searchConditions = reactive<SearchCondition[]>([
    {
        typeValue: publicPerformanceType.PROJECT_MAIN,
        typeName: publicPerformanceTypeMap[publicPerformanceType.PROJECT_MAIN],
        value: "",
    },
]);

// 添加新条件
const addCondition = () => {
    // 获取第一个未被选择的条件类型
    const availableType = allPerformanceTypes.value.find((option) => !selectedTypeIds.value.includes(option.value));

    if (availableType) {
        searchConditions.push({
            typeValue: availableType.value,
            typeName: availableType.label,
            value: "",
        });
    }
};

// 移除条件
const removeCondition = (index: number) => {
    searchConditions.splice(index, 1);
};

// 处理条件类型变化
const handleConditionTypeChange = (index: number) => {
    const typeId = searchConditions[index].typeValue;
    const typeName = publicPerformanceTypeMap[typeId as publicPerformanceType] || "";
    searchConditions[index].typeName = typeName;

    // 重置当前条件的值
    searchConditions[index].value = "";
};

// 时间范围类型
type TimeRangeType = "ONE_YEAR" | "THREE_YEARS" | "CUSTOM" | "NULL";
const timeRangeType = ref<TimeRangeType>("NULL");

// 自定义日期范围
const customDateRange = ref<[string, string] | null>(null);

// 默认时间 [00:00:00, 23:59:59]
const defaultTimes = [new Date(2000, 0, 1, 0, 0, 0), new Date(2000, 0, 1, 23, 59, 59)];

// 禁用未来日期
const disableFutureDates = (date: Date) => {
    return date > new Date();
};

// 选择时间范围类型
const selectTimeRange = (type: TimeRangeType) => {
    timeRangeType.value = type;

    // 根据类型计算时间范围
    const today = new Date();

    if (type === "ONE_YEAR") {
        // 近一年（今天往前算一年）
        const oneYearAgo = new Date();
        oneYearAgo.setFullYear(today.getFullYear() - 1);
        customDateRange.value = [formatDate(oneYearAgo), formatDate(today)];
    } else if (type === "THREE_YEARS") {
        // 近三年（今天往前算三年）
        const threeYearsAgo = new Date();
        threeYearsAgo.setFullYear(today.getFullYear() - 3);
        customDateRange.value = [formatDate(threeYearsAgo), formatDate(today)];
    } else if (type === "CUSTOM") {
        // 自定义时间，如果未设置，默认设为今天
        // customDateRange.value = [formatDate(today), formatDate(today)];
        // 自定义时间，默认设为空
        customDateRange.value = null;
    }
};

// 格式化日期为YYYY-MM-DD
const formatDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
};

// 加载状态
const loading = ref(false);
const setLoading = (status: boolean) => {
    loading.value = status;
};

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);

// 分页变化处理
const handlePageChange = (page: number) => {
    currentPage.value = page;
    handleSearch();
};

// 每页条数变化处理
const handleSizeChange = (size: number) => {
    pageSize.value = size;
    currentPage.value = 1; // 切换每页条数时重置为第一页
    handleSearch();
};

// u6a21u62dfu6570u636e
const searchResultData = ref<any[]>([]);

// u4f7fu7528u4ebau5458u7b5bu9009u529fu80fd
const { personFilterList, personFilterHandler } = usePersonFilter(searchResultData, allPersonListWithDisabled);

// 处理搜索按钮点击
const handleSearch = async () => {
    // 构建查询项数组
    const searchItems: PublicItemParam[] = [];

    // 添加普通查询条件
    searchConditions.forEach((condition) => {
        if (condition.value) {
            // 检查是否是项目负责人或项目参与人
            if (
                condition.typeValue === publicPerformanceType.PROJECT_MAIN ||
                condition.typeValue === publicPerformanceType.PROJECT_PARTICIPATE
            ) {
                // 获取人员ID对应的人名
                const personId = String(condition.value);
                const person = allPersonListWithDisabled.value.find((p) => String(p.id) === personId);
                if (person) {
                    searchItems.push({
                        code: condition.typeValue.toString(),
                        value: person.employeeName, // 使用人名替代ID
                    });
                }
            } else {
                // 其他条件直接使用原值
                searchItems.push({
                    code: condition.typeValue.toString(), // API要求code为字符串
                    value: condition.value.toString(),
                });
            }
        }
    });

    // 添加时间范围条件
    if (timeRangeType.value !== "NULL" && customDateRange.value) {
        searchItems.push({
            code: publicPerformanceType.DATE.toString(), // 时间的code为"6"
            startTime: customDateRange.value[0],
            endTime: customDateRange.value[1],
        });
    }

    // 构建完整查询参数
    const searchParams: SearchProjectInitiationMaterialsParam = {
        items: searchItems,
        pageParam: {
            pageSize: pageSize.value,
            pageNum: currentPage.value,
        },
        projectId: props.projectId,
    };

    // 调用搜索接口
    try {
        setLoading(true);
        const result = await cmsCategoryRowSearchProjectInitiationMaterials({
            body: searchParams,
        });

        if (result.code === 200) {
            // 更新表格数据和分页信息
            searchResultData.value = result.data.records;
            totalItems.value = result.data.total || 0;
        } else {
            // 处理错误
            ElMessage.error(result.message || "搜索失败");
        }
    } catch (error) {
        console.error("搜索请求出错:", error);
        ElMessage.error("搜索请求失败，请稍后重试");
    } finally {
        setLoading(false);
    }
};

// 成果池发送申请
const requestAchievement = function (row: any) {
    tmsProjectRequestRequestShared({
        body: {
            groupId: row.rowId,
            info: "申请",
            participant: row.owner,
            projectId: props.projectId,
            dataType: 0,
        },
    }).then((res: any) => {
        if (res.code === 200) {
            ElMessageBox.alert("申请已发送！请等待对方通过。", "", {
                confirmButtonText: "确定",
                type: "success",
            });
        }
        handleSearch();
    });
};

// 搜索结果是否显示
const showSearchResult = ref(true);

onMounted(() => {
    getPersonList();
});
</script>

<style scoped lang="scss">
.simple-text {
    font-size: 15px;
    color: #61636a;
    margin-right: 15px;
}

.pagination {
    display: flex;
    justify-content: end;
    margin-top: 10px;
    margin-right: 20px;
}

.project-application {
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    height: 100%;
    /* height: calc(100vh - 410px); */
    overflow-y: auto;
    .title-section {
        margin-bottom: 20px;

        .page-title {
            font-size: 18px;
            font-weight: 400;
            color: #23346d;
            margin: 0;
            padding: 10px 0;
        }
    }

    .search-section {
        margin-bottom: 30px;

        .search-form {
            display: flex;
            align-items: start;
            justify-content: space-between;
            .search-form-left {
                display: flex;
            }

            .search-form-right {
            }
            .search-condition-row {
                margin-bottom: 15px;
            }

            .condition-item {
                display: flex;
                align-items: center;

                .condition-label {
                    width: 8vw;
                    margin-right: 10px;
                }

                .condition-input {
                    flex: 1;
                    width: 12vw;
                }

                .operation-buttons {
                    margin-left: 15px;
                    display: flex;

                    .circle-btn {
                        width: 30px;
                        height: 30px;
                        padding: 0;
                        border-radius: 50%;
                        margin-right: 10px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                }
            }

            .time-range-section {
                width: 27vw;

                .time-range-container {
                    width: 420px;
                    display: inline-flex;
                    flex-wrap: wrap;
                    align-items: center;
                    gap: 10px;
                    margin-bottom: 10px;

                    .time-range-item {
                        font-size: 14px;
                        padding: 5px 10px;
                        border: 1px solid #d0d0d0;
                        border-radius: 5px;
                        text-align: center;
                        cursor: pointer;
                        transition: all 0.3s;
                        flex: 1;

                        &.active {
                            background-color: #d2e7ff;
                            border-color: #6c90bf;
                            color: #38587d;
                            font-weight: bold;
                        }
                    }
                }

                .custom-date-picker {
                    margin-top: 10px;
                    flex: 0 0 100%; /* 关键：不放大/不缩小/宽度100% */
                }
            }
        }
    }
    .search-button-container {
        margin-top: 30px;
        text-align: center;

        .search-btn {
            width: 80px;
        }
    }

    .search-result-section {
        margin-top: 20px;

        .operation-buttons-container {
            display: flex;
            justify-content: center;

            .detail-btn,
            .apply-btn {
                margin: 0 5px;
                background-color: #d2e7ff;
                border-color: #6c90bf;
                color: #38587d;
            }
        }
    }
}
</style>
