/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 接受或拒绝邀请 POST /tms/tmsProjectInvitation/acceptAndReject */
export async function tmsProjectInvitationAcceptAndReject({
  body,
  options,
}: {
  body: API.TmsAcceptRejectParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>(
    '/tms/tmsProjectInvitation/acceptAndReject',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 邀请人员 POST /tms/tmsProjectInvitation/invitePerson */
export async function tmsProjectInvitationInvitePerson({
  body,
  options,
}: {
  body: API.TmsProjectInvitationDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>(
    '/tms/tmsProjectInvitation/invitePerson',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 撤销邀请 POST /tms/tmsProjectInvitation/revoke/invite */
export async function tmsProjectInvitationRevokeInvite({
  body,
  options,
}: {
  body: API.TmsRevokeInviteDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>(
    '/tms/tmsProjectInvitation/revoke/invite',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
