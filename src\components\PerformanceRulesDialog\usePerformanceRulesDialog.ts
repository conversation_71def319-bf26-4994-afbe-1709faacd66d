import { createApp, h, ref, nextTick, type App } from "vue";
import { ElMessage, ElDialog, ElButton } from "element-plus";
import PerformanceRulesDialog from "./index.vue";
import type { PerformanceRulesDialogOptions, PerformanceRulesDialogAction } from "./types";

class PerformanceRulesDialogService {
    private instances: App[] = [];

    private create(options: PerformanceRulesDialogOptions): Promise<PerformanceRulesDialogAction> {
        return new Promise((resolve) => {
            const visible = ref(false);
            const container = document.createElement("div");
            document.body.appendChild(container);

            const handleAction = async (action: PerformanceRulesDialogAction) => {
                try {
                    if (action === "change-rules" && options.onChangeRules) {
                        await options.onChangeRules();
                    } else if (action === "close" && options.onClose) {
                        await options.onClose();
                    }
                } catch (error) {
                    console.error("PerformanceRulesDialog action error:", error);
                    ElMessage.error("操作失败，请重试");
                    return;
                }

                visible.value = false;

                // 延迟销毁，等待动画完成
                setTimeout(() => {
                    const index = this.instances.indexOf(app);
                    if (index > -1) {
                        this.instances.splice(index, 1);
                    }
                    app.unmount();
                    if (container.parentNode) {
                        container.parentNode.removeChild(container);
                    }
                }, 300);

                resolve(action);
            };

            const app = createApp({
                render() {
                    return h(PerformanceRulesDialog as any, {
                        modelValue: visible.value,
                        "onUpdate:modelValue": (value: boolean) => {
                            visible.value = value;
                        },
                        "onChange-rules": () => handleAction("change-rules"),
                        onClose: () => handleAction("close"),
                        ...options,
                    });
                },
            });

            // 注册 ElementPlus 组件
            app.component("ElDialog", ElDialog);
            app.component("ElButton", ElButton);

            this.instances.push(app);
            app.mount(container);

            nextTick(() => {
                visible.value = true;
            });
        });
    }

    // 显示绩效规则弹窗
    show(options: PerformanceRulesDialogOptions): Promise<PerformanceRulesDialogAction> {
        const finalConfig: PerformanceRulesDialogOptions = {
            showChangeRulesButton: true,
            showCloseButton: true,
            closeOnClickModal: false,
            closeOnPressEscape: true,
            ...options,
        };

        return this.create(finalConfig);
    }

    // 关闭所有弹窗
    closeAll() {
        this.instances.forEach((app) => {
            app.unmount();
        });
        this.instances = [];
    }
}

export const PerformanceRulesDialogProvider = new PerformanceRulesDialogService();

export function usePerformanceRulesDialog() {
    return {
        show: PerformanceRulesDialogProvider.show.bind(PerformanceRulesDialogProvider),
        closeAll: PerformanceRulesDialogProvider.closeAll.bind(PerformanceRulesDialogProvider),
    };
}
