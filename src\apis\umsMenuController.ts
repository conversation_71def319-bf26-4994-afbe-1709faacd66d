/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 根据ID获取菜单详情 GET /ums/umsMenu/${param0} */
export async function umsMenuId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsMenuidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultUmsMenu_>(`/ums/umsMenu/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 添加后台菜单 POST /ums/umsMenu/create */
export async function umsMenuCreate({
  body,
  options,
}: {
  body: API.UmsMenu1;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/ums/umsMenu/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据ID删除后台菜单 POST /ums/umsMenu/delete/${param0} */
export async function umsMenuDeleteId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsMenuDeleteidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultVoid_>(`/ums/umsMenu/delete/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 分页查询后台菜单 GET /ums/umsMenu/list/${param0} */
export async function umsMenuListParentId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsMenuListparentIdParams;
  options?: { [key: string]: unknown };
}) {
  const { parentId: param0, ...queryParams } = params;

  return request<API.CommonResultCommonPageUmsMenu_>(
    `/ums/umsMenu/list/${param0}`,
    {
      method: 'GET',
      params: {
        ...queryParams,
      },
      ...(options || {}),
    }
  );
}

/** 树形结构返回所有菜单列表 GET /ums/umsMenu/treeList */
export async function umsMenuTreeList({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListUmsMenu_>('/ums/umsMenu/treeList', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 修改后台菜单 POST /ums/umsMenu/update/${param0} */
export async function umsMenuUpdateId({
  params,
  body,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsMenuUpdateidParams;
  body: API.UmsMenu1;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultVoid_>(`/ums/umsMenu/update/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 修改菜单显示状态 POST /ums/umsMenu/updateHidden/${param0} */
export async function umsMenuUpdateHiddenId({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.umsMenuUpdateHiddenidParams;
  options?: { [key: string]: unknown };
}) {
  const { id: param0, ...queryParams } = params;

  return request<API.CommonResultVoid_>(`/ums/umsMenu/updateHidden/${param0}`, {
    method: 'POST',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}
