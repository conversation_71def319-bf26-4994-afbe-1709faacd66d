<template>
    <div v-if="selectedItem" class="detail-content">
        <div class="content" v-loading="detailLoading">
            <!-- 非编辑状态的内容区域 -->
            <div v-if="!isEdit" class="news-detail-container">
                <div class="news-header">
                    <div class="title">{{ selectedItem.title }}</div>
                    <div class="author">作者：{{ selectedItem.authorName }}</div>
                </div>
                <div class="news-content">
                    <div class="editor-content-view" v-html="selectedItem.content"></div>
                </div>
                <div class="btns">
                    <el-button type="primary" @click="handleSubmit">提交</el-button>
                    <el-button @click="handleEdit">编辑</el-button>
                    <el-button type="danger" plain @click="handleDelete">删除</el-button>
                </div>
            </div>

            <!-- 编辑状态的内容区域 -->
            <div v-if="isEdit">
                <div class="edit-box">
                    <Toolbar
                        style="border-bottom: 1px solid #ccc"
                        :editor="editorRef"
                        :defaultConfig="toolbarConfig"
                        :mode="mode" />
                    <div class="meta-info">
                        <div>
                            <label>标题：</label>
                            <el-input style="width: 400px" v-model="selectedItem.title" placeholder="请输入标题" />
                        </div>
                    </div>
                    <Editor
                        style="height: 270px; overflow-y: auto"
                        v-model="selectedItem.content"
                        :defaultConfig="editorConfig"
                        :mode="mode"
                        @onCreated="handleCreated" />
                </div>
                <div class="btns">
                    <el-button type="primary" @click="handleSave">保存</el-button>
                    <el-button @click="handleCancel">取消</el-button>
                </div>
            </div>
        </div>
    </div>
    <div v-else class="detail-content">
        <el-empty description="请从左侧选择一个新闻草稿查看详情。" />
    </div>
</template>

<script setup lang="ts">
import "@wangeditor/editor/dist/css/style.css";
import { IToolbarConfig } from "@wangeditor/editor";
import { NmsNews_ } from "@/apis/types";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { DomEditor } from "@wangeditor/editor";
import { filesBaseUrl } from "@/utils/filesBaseUrl";
import { minioUpload } from "@/apis/fmsMinioController";
import { nmsNewsDeleteDraft, nmsNewsEditDraft, nmsNewsSubmitDraft } from "@/apis/nmsNewsController";
import { ElMessage, ElMessageBox } from "element-plus";
import { AttachmentElement } from "@wangeditor/plugin-upload-attachment";
import { useCustomDialog } from "@/components/CustomDialog";

const customDialog = useCustomDialog();

const emit = defineEmits(["refresh"]);

// 选中的项目
const { selectedItem, detailLoading } = defineProps<{
    selectedItem: NmsNews_;
    detailLoading: boolean;
}>();

// 是否是编辑状态
const isEdit = ref(false);

// 编辑前的内容
const editContent = ref("");

// 编辑前的标题
const editTitle = ref("");

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef();

const mode = "default";

// 编辑器配置

const toolbarConfig: Partial<IToolbarConfig> = {
    excludeKeys: ["uploadImage", "insertVideo", "code"],
    toolbarKeys: [
        "fontFamily",
        "|",
        "bold",
        "italic",
        "underline",
        "|",
        "insertLink",
        "justifyLeft",
        "justifyCenter",
        "justifyRight",
        "|",
        "numberedList",
        "bulletedList",
        "insertImage",
        "|",
        "undo",
        "redo",
        {
            key: "group-more-style", // 必填，要以 group 开头
            title: "更多样式", // 必填
            iconSvg:
                "<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1024 1024'><path d='M176 416a112 112 0 1 0 0 224 112 112 0 0 0 0-224m0 64a48 48 0 1 1 0 96 48 48 0 0 1 0-96m336-64a112 112 0 1 1 0 224 112 112 0 0 1 0-224m0 64a48 48 0 1 0 0 96 48 48 0 0 0 0-96m336-64a112 112 0 1 1 0 224 112 112 0 0 1 0-224m0 64a48 48 0 1 0 0 96 48 48 0 0 0 0-96'></path></svg>", // 可选
            menuKeys: ["through", "clearStyle"], // 下级菜单 key ，必填
        },
    ],
    // 插入哪些菜单
    insertKeys: {
        index: 14, // 自定义插入的位置
        keys: ["uploadAttachment"], // "上传附件"菜单
    },
};

const editorConfig = {
    placeholder: "请输入内容...",
    // 在编辑器中，点击选中"附件"节点时，要弹出的菜单
    hoverbarKeys: {
        attachment: {
            menuKeys: ["downloadAttachment"], // "下载附件"菜单
        },
    },
    MENU_CONF: {
        // "上传附件"菜单的配置
        uploadAttachment: {
            server: "/fms/minio/upload", // 使用MinIO上传接口
            timeout: 30 * 1000,

            fieldName: "file", // 修改为MinIO API使用的字段名
            maxFileSize: 100 * 1024 * 1024, // 100M (根据API限制)

            onBeforeUpload(file: File) {
                console.log("onBeforeUpload", file);
                return file;
            },
            onProgress(progress: number) {
                console.log("onProgress", progress);
            },
            onSuccess(file: File, res: any) {
                console.log("onSuccess", file, res);
            },
            onFailed(file: File, res: any) {
                alert(res.message || "上传失败");
                console.log("onFailed", file, res);
            },
            onError(file: File, err: Error, res: any) {
                alert(err.message || "上传出错");
                console.error("onError", file, err, res);
            },

            // 自定义上传使用MinIO API
            customUpload(file: File, insertFn: Function) {
                console.log("customUpload", file);

                // 使用已导入的minioUpload函数
                return minioUpload({
                    body: {},
                    file: file,
                })
                    .then((res) => {
                        if (res && res.data && res.data.url) {
                            // 插入附件到编辑器
                            insertFn(file.name, `${filesBaseUrl}${res.data.url}`);
                            console.log("上传链接", `${filesBaseUrl}${res.data.url}`);
                            return "ok";
                        } else {
                            throw new Error("上传失败");
                        }
                    })
                    .catch((error) => {
                        console.error("Upload error:", error);
                        throw error;
                    });
            },

            // 插入到编辑器后的回调
            onInsertedAttachment(elem: AttachmentElement) {
                console.log("inserted attachment", elem);
            },
        },
        // 添加图片上传配置
        uploadImage: {
            server: "/fms/minio/upload",
            fieldName: "file",
            maxFileSize: 10 * 1024 * 1024, // 10M
            maxNumberOfFiles: 10,
            allowedFileTypes: ["image/*"],
            timeout: 30 * 1000,

            // 自定义上传
            customUpload(file: File, insertFn: Function) {
                // 使用已导入的minioUpload函数
                return minioUpload({
                    body: {},
                    file: file,
                })
                    .then((res) => {
                        if (res && res.data && res.data.url) {
                            // 插入图片到编辑器
                            insertFn(`${filesBaseUrl}${res.data.url}`, file.name);
                            return "ok";
                        } else {
                            throw new Error("上传失败");
                        }
                    })
                    .catch((error) => {
                        console.error("Upload error:", error);
                        throw error;
                    });
            },
        },
    },
};

// 插入链接
editorConfig.MENU_CONF["insertLink"] = {
    checkLink: customCheckLinkFn, // 也支持 async 函数
    parseLinkUrl: customParseLinkUrl, // 也支持 async 函数
};
// 更新链接
editorConfig.MENU_CONF["editLink"] = {
    checkLink: customCheckLinkFn, // 也支持 async 函数
    parseLinkUrl: customParseLinkUrl, // 也支持 async 函数
};

// 编辑
const handleEdit = () => {
    // 保存编辑前的内容
    editContent.value = selectedItem.content;
    editTitle.value = selectedItem.title;
    isEdit.value = true;
};

// 取消编辑
const handleCancel = () => {
    // 恢复编辑前的内容
    selectedItem.content = editContent.value;
    selectedItem.title = editTitle.value;
    isEdit.value = false;
};

// 保存
const handleSave = () => {
    // 保存编辑后的内容
    selectedItem.content = editorRef.value?.getHtml();
    isEdit.value = false;
    nmsNewsEditDraft({
        body: {
            id: selectedItem.id,
            content: selectedItem.content,
            title: selectedItem.title,
        },
    }).then((res) => {
        if (res.code === 200) {
            ElMessage.success("保存成功");
            emit("refresh", selectedItem.id);
        } else {
            ElMessage.error("保存失败");
        }
    });
};

// 删除
const handleDelete = () => {
    // 弹窗确认是否删除
    customDialog.confirm({
        title: "提示",
        message: "确定要删除该新闻吗？",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        onConfirm: () => {
            nmsNewsDeleteDraft({
                body: {
                    id: selectedItem.id,
                },
            }).then((res) => {
                if (res.code === 200) {
                    ElMessage.success("删除成功");
                    emit("refresh");
                } else {
                    ElMessage.error("删除失败");
                }
            });
        },
    });
};

// 提交
const handleSubmit = () => {
    // 弹窗确认是否提交
    customDialog.confirm({
        title: "提示",
        message: "确定要提交该新闻吗？",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info",
        onConfirm: () => {
            nmsNewsSubmitDraft({
                body: {
                    id: selectedItem.id,
                    title: selectedItem.title,
                    content: selectedItem.content,
                },
            }).then((res) => {
                if (res.code === 200) {
                    ElMessage.success("提交成功");
                    emit("refresh");
                } else {
                    ElMessage.error("提交失败");
                }
            });
        },
    });
};

// 组件销毁时，也及时销毁编辑器

onBeforeUnmount(() => {
    const editor = editorRef.value;
    if (editor === null) return;
});

// 创建编辑器
const handleCreated = (editor: any) => {
    editorRef.value = editor;
    console.log(editor.getAllMenuKeys());
    // 打印默认工具栏配置
    // console.log("默认工具栏配置:", DomEditor.getToolbar(editor));
};

// 自定义转换链接 url
function customParseLinkUrl(url: string): string {
    if (url.indexOf("http") !== 0) {
        return `http://${url}`;
    }
    return url;
}

// 自定义校验链接
function customCheckLinkFn(text: string, url: string): string | boolean | undefined {
    if (!url) {
        return;
    }

    // 校验链接是否符合链接要求
    const urlRegex = /^www\.[a-zA-Z0-9-]{2,}\.[a-zA-Z]{2,}$/;
    if (!urlRegex.test(url)) {
        ElMessage.error("链接格式不正确！");
        return "链接格式不正确！";
    }

    return true;
}

// 监听selectedItem，如果selectedItem发生变化，则将isEdit设置为false
watch(
    () => selectedItem,
    (newVal) => {
        if (newVal) {
            isEdit.value = false;
        }
    },
);
</script>

<style lang="scss">
.editor-content-view {
    color: #23346d;
    font-family: "Alibaba PuHuiTi 3.0", sans-serif;
    font-size: 14px;
    line-height: 21px;

    p,
    li {
        white-space: pre-wrap; /* 保留空格 */
        color: #23346d;
        line-height: 21px;
        margin-bottom: 21px;
    }

    span {
        color: #23346d;
        font-family: "Alibaba PuHuiTi 3.0", sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 21px;
    }

    blockquote {
        border-left: 8px solid #d0e5f2;
        padding: 10px 10px;
        margin: 10px 0;
        background-color: #f1f1f1;
        color: #23346d;
    }

    code {
        font-family: monospace;
        background-color: #eee;
        padding: 3px;
        border-radius: 3px;
        color: #23346d;
    }

    pre > code {
        display: block;
        padding: 10px;
    }

    table {
        border-collapse: collapse;
    }

    td,
    th {
        border: 1px solid #ccc;
        min-width: 50px;
        height: 20px;
        color: #23346d;
    }

    th {
        background-color: #f1f1f1;
    }

    ul,
    ol {
        padding-left: 20px;
        color: #23346d;
    }

    input[type="checkbox"] {
        margin-right: 5px;
    }
}
</style>

<style scoped lang="scss">
@use "../../../../assets/styles/mixins.scss" as mix;

.detail-content {
    height: 100%;
    padding: 24px;

    .content {
        background: #ffffff;
        border-radius: 6px 6px 0 0;
        padding: 24px;
        height: 100%;
        /* height: calc(100vh - 310px); */
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    // 新闻详情容器（非编辑状态）
    .news-detail-container {
        background: #f9fbff;
        border-radius: 6px;
        padding: 24px 25px;
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    // 新闻头部信息
    .news-header {
        border-bottom: 1px solid rgba(5, 5, 5, 0.06);

        .title {
            color: rgba(0, 0, 0, 0.88);
            font-size: 24px;
            font-weight: 600;
            line-height: 32px;
            letter-spacing: 1.2px;
            margin-bottom: 15px;
            text-overflow: ellipsis;
            overflow: hidden;
        }

        .author {
            color: #23346d;
            font-size: 14px;
            line-height: 21px;
            margin-bottom: 15px;
        }
    }

    // 新闻内容
    .news-content {
        margin-top: 15px;

        .editor-content-view {
            color: #23346d;
            font-size: 14px;
            line-height: 21px;

            p {
                margin-bottom: 21px;
                line-height: 21px;
            }

            span {
                color: #23346d;
                font-family: "Alibaba PuHuiTi 3.0", sans-serif;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 21px;
            }
        }
    }

    // 按钮组
    .btns {
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        margin-top: 16px;
    }

    // 编辑框样式（保持原有样式）
    .edit-box {
        margin-top: 10px;
        border: 1px solid #bec5d7;
        border-radius: 10px;

        .meta-info {
            background-color: #fff;
            border-bottom: 1px solid #bec5d7;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            gap: 10px;
            padding: 20px 10px;
        }
    }
}
</style>
