<template>
    <div id="wrapper">
        <div class="content">
            <div class="title">登录</div>
            <el-form
                ref="loginFormRef"
                style="max-width: 600px"
                :model="loginForm"
                :rules="rules"
                label-width="70px"
                :size="formSize"
                status-icon>
                <el-form-item label="账号" prop="username">
                    <el-input v-model="loginForm.username" />
                </el-form-item>
                <el-form-item label="密码" prop="password">
                    <el-input type="password" v-model="loginForm.password" @keyup.enter="submitForm(loginFormRef)" />
                </el-form-item>

                <div class="btns">
                    <el-button type="primary" @click="submitForm(loginFormRef)">登录</el-button>
                    <el-button @click="resetForm(loginFormRef)">重置</el-button>
                </div>
            </el-form>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, type ComponentSize, type FormInstance, type FormRules } from "element-plus";

import useUserStore from "@/store/modules/user";

const userStore = useUserStore();
const route = useRoute();
const router = useRouter();

interface loginForm {
    username: string;
    password: string;
}

const formSize = ref<ComponentSize>("default");
const loginFormRef = ref<FormInstance>();
const loginForm = ref<loginForm>({
    username: "",
    password: "",
});

const rules = reactive<FormRules<loginForm>>({
    username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
    password: [{ required: true, message: "请输入密码", trigger: "blur" }],
});

const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    await formEl.validate(async (valid) => {
        if (valid) {
            userStore
                .login(loginForm.value)
                .then(() => {
                    ElMessage.success("登录成功！");
                    router.push({ path: "/index" });
                })
                .catch((error) => {
                    console.error(error);
                });
        } else {
            // 校验失败 重置表单
        }
    });
};

const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
};
</script>

<style scoped lang="scss">
#wrapper {
    background: #edecec;
    min-width: 60%;
    height: 100vh;
    width: 100%;

    .content {
        position: absolute;
        height: 400px;
        width: 380px;
        background: #fff;
        border-radius: 5px;
        box-shadow: 2px 2px 6px #b4b4b4;
        padding: 30px;
        right: 10%;
        top: 20%;

        .title {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .btns {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}
</style>
