:root {
    --system-color-text-primary: rgba(0, 0, 0, 0.88);

    /* 主色调 */
    --el-color-primary: #1677ff;
    /* 圆角 */
    --el-border-radius-base: 6px;
    /* 文字颜色 */
    --el-text-color-regular: var(--system-color-text-primary);
}

* {
    box-sizing: border-box;
}

.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 弹窗内边距 */
.el-dialog {
    --el-dialog-padding-primary: 20px 24px;
}

/* 弹窗标题 */
.el-dialog__header {
    color: var(--system-color-text-primary);
    font-family: "Alibaba PuHuiTi 3.0";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
}

/* 弹窗内容 */
.el-dialog__body {
    padding: 8px 0;
}

/* 弹窗按钮 */
.el-dialog__headerbtn {
    margin-right: 10px;
    margin-top: 10px;
}

/* 抽屉样式 */
.el-drawer {
    /* 抽屉内边距 */
    --el-drawer-padding-primary: 0;

    /* 抽屉标题与内容间距 */
    .el-drawer__header {
        margin-bottom: 0;
    }
}

/* 描述列表label与value间距 */
.el-descriptions__label:not(.is-bordered-label) {
    margin-right: 8px;
}

.el-descriptions__label {
    font-weight: 600;
}
