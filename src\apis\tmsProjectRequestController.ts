/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 资料申请同意或拒绝 POST /tms/tmsProjectRequest/acceptAndReject */
export async function tmsProjectRequestAcceptAndReject({
  body,
  options,
}: {
  body: API.TmsRequestAcceptParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>(
    '/tms/tmsProjectRequest/acceptAndReject',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 获取行数据名称 GET /tms/tmsProjectRequest/getRowName */
export async function tmsProjectRequestGetRowName({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.tmsProjectRequestGetRowNameParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultString_>('/tms/tmsProjectRequest/getRowName', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询待验证团队列表 GET /tms/tmsProjectRequest/getSharedList */
export async function tmsProjectRequestGetSharedList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.tmsProjectRequestGetSharedListParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageTmsProjectRequestDto_>(
    '/tms/tmsProjectRequest/getSharedList',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 资料申请 POST /tms/tmsProjectRequest/requestShared */
export async function tmsProjectRequestRequestShared({
  body,
  options,
}: {
  body: API.TmsProjectRequestParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>(
    '/tms/tmsProjectRequest/requestShared',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  );
}
