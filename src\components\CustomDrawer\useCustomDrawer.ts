import { ref, reactive } from 'vue'
import type { CustomDrawerProps } from './types'

export interface UseCustomDrawerOptions extends Partial<CustomDrawerProps> {
    /**
     * 确认回调函数
     */
    onConfirm?: () => void | Promise<void>
    
    /**
     * 关闭回调函数
     */
    onClose?: () => void
    
    /**
     * 关闭前回调函数
     */
    onBeforeClose?: (done: () => void) => void
}

export function useCustomDrawer(options: UseCustomDrawerOptions = {}) {
    const visible = ref(false)
    
    const drawerProps = reactive({
        title: options.title || '',
        size: options.size || '378',
        direction: options.direction || 'rtl',
        modal: options.modal ?? true,
        lockScroll: options.lockScroll ?? true,
        appendToBody: options.appendToBody ?? true,
        closeOnClickModal: options.closeOnClickModal ?? true,
        closeOnPressEscape: options.closeOnPressEscape ?? true,
        cancelText: options.cancelText || '取消',
        confirmText: options.confirmText || '确定',
        drawerClass: options.drawerClass || ''
    })
    
    /**
     * 打开抽屉
     */
    const open = () => {
        visible.value = true
    }
    
    /**
     * 关闭抽屉
     */
    const close = () => {
        visible.value = false
        options.onClose?.()
    }
    
    /**
     * 确认操作
     */
    const confirm = async () => {
        try {
            await options.onConfirm?.()
            close()
        } catch (error) {
            // 如果确认操作失败，不关闭抽屉
            console.error('确认操作失败:', error)
        }
    }
    
    /**
     * 关闭前处理
     */
    const beforeClose = (done: () => void) => {
        if (options.onBeforeClose) {
            options.onBeforeClose(done)
        } else {
            done()
        }
    }
    
    /**
     * 更新抽屉属性
     */
    const updateProps = (newProps: Partial<UseCustomDrawerOptions>) => {
        Object.assign(drawerProps, newProps)
    }
    
    return {
        visible,
        drawerProps,
        open,
        close,
        confirm,
        beforeClose,
        updateProps
    }
}

// 简化版的快速创建函数
export function createDrawer(title: string, options: Omit<UseCustomDrawerOptions, 'title'> = {}) {
    return useCustomDrawer({ title, ...options })
}

// 特定用途的快速创建函数
export function createFormDrawer(title: string, onSubmit: () => void | Promise<void>) {
    return useCustomDrawer({
        title,
        confirmText: '保存',
        onConfirm: onSubmit
    })
}

export function createConfirmDrawer(title: string, onConfirm: () => void | Promise<void>) {
    return useCustomDrawer({
        title,
        confirmText: '确认',
        cancelText: '取消',
        onConfirm
    })
} 