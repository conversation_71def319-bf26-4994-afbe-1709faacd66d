<template>
    <div class="wrapper">
        <!-- 简讯通报 -->
        <div class="news-card">
            <div class="card-header">
                <div class="header-content">
                    <div class="title-section">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                            width="40"
                            height="40"
                            viewBox="0 0 40 40">
                            <defs>
                                <clipPath id="clipPath9820629959">
                                    <path
                                        d="M0 0L40 0L40 40L0 40L0 0Z"
                                        fill-rule="nonzero"
                                        transform="matrix(1 0 0 1 0 0)" />
                                </clipPath>
                            </defs>
                            <g clip-path="url(#clipPath9820629959)">
                                <defs>
                                    <mask id="mask8365641411" style="mask-type: alpha">
                                        <rect
                                            width="29.106371"
                                            height="29.106371"
                                            transform="matrix(0.854002 0.52027 -0.52027 0.854002 15.1432 0)"
                                            fill="rgb(0, 0, 0)" />
                                    </mask>
                                </defs>
                                <g mask="url(#mask8365641411)">
                                    <defs>
                                        <pattern
                                            id="pattern4185874665"
                                            patternContentUnits="userSpaceOnUse"
                                            width="1"
                                            height="1">
                                            <image
                                                width="320"
                                                height="320"
                                                transform="matrix(0.0909573 0 0 0.0909573 0 0)"
                                                xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAUAAAAFACAYAAADNkKWqAABjwklEQVR4AeyVfXCUx33Hv8/LHYeEXi0JYQlZCJCxkGWQQYAQDiE2ceyA29RDJrjUHU9bd9q4tGUYD390MpkMY9wk4yZu43TS1kw82LgQnGacmBoGUzAybxYgBAhJvAmZCEk+CUmcpNPz7PZ7styhDjEC33Oru9vPzI/dfQ49+/vts/tZAxqNB0gpzc88Mu/gNeLGgWEYQnVdmsRCC1AzZkal9mnYjAAjDw6KuJNyGNlU1lQ+87Ofxz9ga+SP/vEk9rPHOFVEdFdG2xCjixGGRAdnvjYylvxdcOxDO8dBhjP6/x0tSs1Y0QLU/A4UXURufkaA2rkbFmZwp8ygdGbyx0KYxt3sF/H3bNW5jhKRXyujjXGFuV1kvs1sW6jpFj7rHf0/YS1HzY1oASY5lF2ATQojHy4qKI25I7IzMIv9Ej4PqM4xCkTEeB5CtMAyT0DgNMVYz2ehSGgpJi9agEkEZWeySWfkUXaVFNx87oA5/KEUhlGoOr8YL8YgTKMJrmigFI9RigcpxRb+0kshhlSnp4kNWoAJzKjwsjGMIlio5mFfBMOs5Fefwee26vzGIUFGPdfpMKV4gGtWz3GHFmLiogWYYFB66WwK4WIh20cg8anwTNW5xSE9jONcw4Ns/5tXRhM+EaKjOjFNdNACjHMovIjY8uCgjO3j/KI1bMsZKapzSzAEo5VRxzXexetkb2RMGYZUJ6a5c7QA45BR6RXCRSXbJyBkDUyjmH1bdW5Jg5Q9MIx6nqB3+B3ehQ8tlGGv6rQ0t4cWYBxB8UWkt5DdbzKqGEWqc9KMEGI0QGIn4y3KsIkyDKlOSnNrtADHOZReJhzKzhiR3lJGMcNUnZfm99LLqOf3epNfaTf7LZShozopzc3RAhyHUHo2wijjAfojxkpI9gG/6rw0t4mU7TCMWva2wMJeijCoOiXN/0cLcBxB8WXDxTJ21vDg1PBRtuqcNFHB4UlrhBC/hDC38SproAyF6qQ0WoDKofRMNjN4RJ6CiSchUcqxrTovjWcE+dH38YJ7FRb2UIT9qhNKZrQAFUHx2ZTeQn6BP+dwOSOPYarOSxMzBhkN/P6v8qvvoAjbVSeUjGgBxhiKzw8Xj7LzHAyjio/SVefkYbEYHgoh7DoQ7F8b6Btpb8VEfwApvgkj/ZTAJBiWpboSL3EYrXDFVvjNn1GEF1UnlExoAcYIii+F4vsDdtcyKhgB1TndcS1OGMH+a+i63o2Pr/eiJ9SHzv7ukegbvE7R9aOXMTg8xHPtRGpHRHsuRTgG/8E0TViMCLZl854wkJ2aCb/tR05aFjInpuGu1AzclZKBrNQ05E7KQi7HgZS4vksEo4OxHRZeZs1NqhNKBrQAPYaHP0DxfYOddTzJ5XzkV53TGBPH9VAvLvdcRVt3B1q723E52I4rPR3ovt4DhzKLRERqYixW8wjbsmBRkrblwwSfH1My83A3Y9pdBSjIzEVhRt7IM8P2qV7R2yEIIbbCZ75EEbaoTiaR0QL0CIrPT/E9yRVeD4mI+GzVOX2e7Hr7e9DU2Yrmjk/iHKOXAvxUdPGIaZoUo00xTkA+JTgrfxqm50xlew+KsqdQiuP+LuphvA4LP6QIz6tOJhHRAvQA6civc2U3UnxlGIfik8LFpc42nG4/j2OXG9F89RKCfUG4fC6EUJ2e50SkaNs+FOcWoqLgXtxfMB3l+SUIpKSrTu330csPsxk+83sUYZfqZBIJLcAoIsNyIUy8yG41xpn4rgbbceTSaRxpPYXTHzUjNHg9KWQ3FigVWKaFwpwCPFA4C1XFZZhbWArDF1Cd2mfp4Yl9kXvsn5lzv+pkEgEtwCggpSzGsNgI01wF1eKTcqQRw4P48HIjPrjQgCMXTuLj3q5InqqXKm7w+/y4r6AUC4rLUV1SgbzMySOi5D+qU4twnvEdWNjKnBzVycQz4+JrxisUSjYcsRaG+fccTlIpPSkFBgb6ceB8PfafO4b61jMYCg+qXqKEoSCnAAumVeDLpfMwPbcQhuVTK8PIZWYYtZB4nlduLUUoVK9RPKIFeAdQfH64WMnNt5ErWKosD+EiFOpD7YWTeK/5KBoovWFnWPXyJDz5WflYNH0Olt1LGeZQhrZfpQzDjM2wuBeBNi3C20ML8Dag+ExutzJuthc4fJRhK7AewkMD+PByI3Y1HkTdhQYMhQdVL03SUpQ3FYunz8VX71uEvPS7YFi2Khle4WneCBM/pwT7Va9LvKAFOEYov0wI/CUknucwM+bzO2FcDrbj7Yb9ONBSh2BvUPWSaG7ANE3MnjoLj5ZVY3FJBSYEUnm6zFinIRh7+e8G+FBHETqq12W8owV4Cyg+Gw6quVIvcljFiN2ulhKDA304cOEk3jn1Ps60NUEIoXpJNLcgKy0bS0ofxGOza1CUPQWG7Yt1Cj28qH8MGy9Tgl2q12M8owX4OVB+OXDEWt7kf8vhpJjNK1x09HTiN6cO4L2zh9DJvib+sC0bc4rLsaLiIcwtKIUvkBrrFGopwg0UYS1F6Khej/GIFuBNoPhsOKjh6rzA4cKYzTscRnPnJew4/h4OnzuBgaGQ6qXQRAHKB/fkFeGx2TVYWjoPaakZkYexmr6XEvwnSvBl5tGlei3GG1qAn4Hyy4bAtyHkOm7S9FjMKcIDqGs7i1+e+B+cuNgAx9WXdaKSk5GLr5XX4OFZC5CXmcsTaMZq6lru63Xw4TBFKFSvw3hBC/AGZFjOgSF/yO5Sys/znSnCgzh86RS21e1G40dNEELvy2QhY1ImHimrxor7H4qlCIM88Rth4qeUYEj1GowHtACJlNIPF6vZfYGR7/V8EfEdovi21+3C2Y9a4ApX9RJoFJFJEX6FIlx5/xKKcDJPpOdHUnDD74BtPE8Jnlddv2qSXoCUXw7lFxHfnzD8ns7lDONEWyNeP7ITp9vOwnW1+DSfEBHh1x9Yisdn1yAzLTsWIozI7znDNn6junaVJLUAZVjOg4lX2J3n7UQCLe0X8NqhX+PYxVMYdsKqS9eMU3Izc/GNuQ9ThIvhC6R6PV0/DfB9noEfGIYRUl27CpJSgFJKEy7+mJ3v86bN83AidPZ0YMvRndjXeBgDQ0m5xzS3CWWEe/KK8K35X8OSmQ/CsGwvpxPcp7+CbTzHedtU1x5rkk6AlF86HHyHlf8VhwGv5nGHBrCjfi/eqtuF7r5u1WVr4hDbslBxz2w8U/0Epk+eFjGjd5MZaKAKnzV8Rq3qumNJUgmQ8iuCi39lZzk3k+nVPEfPH8e/vf8WLnW0qi5ZkwAEJkzE8vIleLrqMaSkZng5VQdjAyxsNgxDqK47FiSNAGVYVvHD/gwSFV7Nca0viFf2b0dt01EMO8OqS9YkGJOzJuOZxX+IJaXzYZie3d8hnpEfw8Z3KcFB1TV7TVIIUDrySTY/YtztyftdB++eqsWrtTtwrf+a6nI1CYxtWXiw5AH89UOrkEsheoTD+E9YWEsJdqmu2UsSWoBSShMCf8Mb7bscpnsxx9Xgb/HSntdxsvU0hBCqS9YkCemp6VizcCUer/gSDMv2YorIZn6fEnyaEryoul6vSFgBUn4B3mMbWeG3OfRH/f2ug7dP7MXPP/gv9A/0qy5Xk4SYhoGK4nL83bKnkJc12ZtJJJoY3zL8Rp3qer0gIQVI+aXDxb+wu5phRvv93b0f46U9W/DhueMQUqouV5PkpKem4y+WrMKyskUwzKhv98iBaqcq1hg+Y7fqWqNNwgmQ8suh/Lawu9yDl+NA81H8aPdr6Av1qS5Vo/k/DMPA4nvnY92ypxCYmBZ5EN0JpOzlO581bGOr6lqjSUIJkPIrpvzeZLcq2uILD4Xwyv7tePfkPgghVJeq0dyU3MxcrF/+p7h/6n3RlyAwSGOsh4mfULgJcQgSRoCUXwkcuY0fvTLa8mvtvIx/3LUZ5357QXWZGs0t8fv8WL1wBVZVPgLDNyHarw/fIEFHda1flIQQoAzLOfwgv2C3JKrvdR3sOXMIP933JvpDfarL1GhuiwUzK7H2y6uRlZbNkx7Foy6l4As3wcb3KMFB1XV+EeJegKPy28bujGi+Nzx4Hf/+wa/w62O74QqhukyN5o6Ykj0F65c/jfsKZvK0m9F8teDB2AS/GdcSjGsBjsgPchtMI3rykxJd1zrxg92v4cTFBtUlajRfmIkTUvDsl76J5WWLYNi+aL5aQGITbMStBONWgF7J7+yVZmza+R9o776qukSNJmpYpoUVlQ/jz6qfgOWfGM1XC7hiE/xmXEowLgU4Ij8T29iNmvykcLH3zCH8ZO8b6B/oV12iRuMJVTMrsf4razApNYOnP2rHX0BiE2zEnQTjToBSyhK42MVuSdTe6YTxxpF38MbBt+G4juoSNRpPmZZfjA1ffQZTcwqjJ0EpBUzjH2BiEyUoVNc4VuJKgJRfMRz5C360ypv9/r/slQ1wVeWdxp9zc4kx3Nx8GCCAYoAkfBRZrCzLImzd7opUEBHYTsfVqV3xoxYF6nScWQuKTDZB7Lh06weK2sIgoyJhgZQEIgQQSmIIuRpIIEKTmAgJmBBzm3vv+Xr3f1PcUauSnPec8yaX9zdzbl7IPc/7/E/O+R0rmGoIv93/NooDpaLHk0hcIz05HU+QBCeMGGdnrEpG+RVJ8Hf9RYL9RoAkv3QYKKTlFLsyI6EgVhWtx9HTAdHjSSSu47vah0f/5R7809h/sDNWpeMhxav8XvR8PaFfCJDk5yf5vUPLmXZltndcwDO7XkFtU53o8SQSYVwVn4BFMxZizqR/Jht47Ir9nI6fkQS3ip7vcvR5AZL8Ekh+r9LyHrsyW9rO4unCdahvaRA9nkQiHG+cFz+dPh8Lvn8rFFrbRCsY/k0ZoBwQPd930acFSPLzQMcaarmU/mnL66mhtQErdryI1vZW0eNJJH2GOE8c5k2+DffffJedEqxHHGYrinJC9Hzfhi1ScQwTj5H8Ftsqv+1SfhLJ1zFMAwUVRXjt0FYwQ7crNpOe4Y2MsWGi5/s2+qwAmc4WgmElLePtyKv/Qn4Xpfwkkm/CNE1sqyi2V4IMk2DgNZKgT/R830SfFCBT2RT6sZYOvx15Ufk9tUPKTyK5HIb9Eow6ZiZJcA1JMF70fH1egHSRRiAOr9JymB15DVH5bSf5tUv5SSQ9wSEJ3gcTi0XP9nUU0QW+DMnPT2+Kt2g5y468qPxWROV3UcpPIuktHo8Hd02ehftvvgtKnNeOyM/puFfxKttFz/ZlM/cJSH4e6HiKFjPtyGtpO4sVO6T8JBKrmKaJgooibDiygx5Q045IPx1r6VkfK3q2L+gzAoSBe6DgESgKd6f2jgt4unAdWtul/CQSHqIS3FL+RxQcLbFLgpn0rL9KEkwTPVuUPiFAprLJYGwNLRN4syKhIJ7Z9QrqWxpEjyWRxAS6oeON97egtKbMrsjpJME8kqBX9GzCC9BFSKeL8RKgDObNMtUQVhWtR21TneixYo6M1AzMyL4JE6/NwghaD/B4cTEcxMetn6C8oRrlpwNQtYjomhKH0HQN/73nD/BfPRDfz7yBHleFN/I+eu6jRn1d5FzcU/BA8ouni/ACLRdxZ+kq1u7bjOJAqciRYo5BKYOwaPoCzMiZDMUT963fi4Q68caRndhZ9R4MwxBdW+IQSYlJWL1gGUYOzuSXIGOtYMpsJV6pEDWPWAHq7D76sY6OeK4c08Dmsp3YeGibyHFijhtHTcR/zvwZfL6UHp9TVV+N3MJ1CIaCoutLHCIjLQNr5i9DespgO+IOIw53KopyQcQsHhGbRmEqm0Q/8sApP3qLoLSmDJuP7BQ1SkwyaeQNWDX74V7Jr/u8zAnIp4cjKTFJ9AgShzjXdg55xa8jEuq0I24adCxnjHlFzCJEgDRsGhT2G1pm8Mrv5Kd1eLF0M3RDFzFKTJKenI4nbv0p4q662tL5ozNGInfeEvgH+kWPInGIE5+cxAsHt4BpERvS2CIYmCdiDtcF2G16E4tpeQtv1oWO88gveh3BUNDtMWIWj6LgJ39/O1L811gPoYzsoaOw6s7FSPYlix5J4hB7PjyAtyv3gJkGX5CiJNJnHrlhlNszuC5A6JgOkz1OQ3PtrYb/gudKNuJce4vrI8Qyaf50zBw7hT+IJJgzNAsr73gEKb4U0WNJHGLTn7aj/EwAYIw3KgsGckmCCW72d1WANFw6FOTRw+HnyjF0vEYXPlBf7Wb9K4IbR4zDgISB9oSRBMcMy8ZTc36O1KRU0aNJHEDTNTxfsgGffvapHRKcTxJcSJ5wzUuubURDeaGbS2g5lTMIe2vKUHisxK3qVxRjM0baG0gSHDs8G8tnP0QSTBM9nsQBOoIdyN/9BtRIF29UPB2r6Mh0q7trAoSOaVA8S7kySH6N5z/BywfegmGarlW/UojzeDDIl2x/MElw3PAckuCDSPNLCcYidZ+exiuHCsBMgzcqEwZyGWMJbvR2RYA0TAoUrKaljycn+oZ5ds/vEezqdKO2xE4uSfDJHz0gJRij7ArsQ2lNWfSB541aSBKc50ZnxwVI8vPAxMO0nMIZhJcObsHps39247pckdDfCiFNdW4DkuD4a8dICcYopmnixdLNaL3YwhvlpWMV3Y/DnO7suAChYjwYnuDd61BdBXZ/dMDxulcyJgnwzIVmZzeREoxpgqEg/qvodTD+F2kWNPNJkqDXyb6OCpDKxyMOebRM4clp//wzrC3Z2P2GkThLZWMNmGk4u4mUYExzsrkOG8oL+YM8nv+AjmlOdnVUgDAwlz5n8UQwQ8fzezehs6vT0aqSv9JwvhGnzp52fqMvSTA1SUow1thaUYTappO8MQlQkMcYS3Cqp2MCpNJpYMilpZcnZ2egFEdPVzlVU/I1VE3FH8oKwXTV+c0uSXD57AdJgqmiR5fYSPQ+eq5kI9RwkDdqKkzc51RPxwQI3VxC9s7hiWhpO4sNf/pfmIw5VlPytwTqq/FOZYk7m5EExw3PwYrZD0kJxhjNF5qx/vB23hgPCWAlY2ywEx0dESCVzYTi+SVXhqHj+b1vIhjifoNIeolpmnjzyA7sPX7InQ1JgmNJgk/NeRgpvhTR40tspPij/aiiFyoXijIYBpY70c8RAUIzc+nTxxOx+/hhfNR4wpF6kssTUcP4n72bSIKH3dmQJDhmWDaevuMRJA9MFj2+xCZUTcXLB96BoYZ4ox5kjI21u5/tAmQqmwqP58c8GR2dbXjj8FaYpml3PUkvCEdC+O17G7HvhJsSzMIzdy6Gf6Bf9PgSm2hobcSmD4p4Y+JhYLXd3WwXICVGS3p5Il46uAUdwQ7bq0l6T0QNY23JBuytdk+COUNHI3feY/AlJokeX2IT2ypL0Egi5OR2prEf2tnLVgEync2hH9N4MirOVOHwqQo7a0kswhiDoeloO9eGVZt/h+JjB6L/6fzGJMGsjFHIn78Uvqt9oi+DxAZCkS68cOBtMEPjifFCwUq6L7129bJNgFQqnsrldpe0iBEJYf37BdB0za5aEotE5aeFNXS1d8HQDahaBPlbXkZx1UHXJDiaJLh6wS+lBGOE6sYa7Kst542ZCgPz7epkmwCp1EIwjLd8Pj1UWz8sRUNro22VJNaIyk+PaAh3hvFl1X1VgqYrXUZljJQSjBFM08TGIzvQ9ZcOnhgvFDxB92i8HZ1sESCVSaBSv+ouZ5HzF1tRULnHjjoSTkzDQPjz8Df+7q8SXEcSfJ8c6J4E8xcsgy8xSfSlkXByrr0Fbx8riUrDegjDRBiYb0cfWwTYXYZhgvWBTGyqKEJ7Z7stdSTW8MZ5oy8zRIIqvuv2VLUw8t9dhz0B9yQ4eshI5N21BP6BftGXScJJ4Yf70fRZM0+El47H6V5N4O3CLUAqkUgfj18qZYmPz/0ZB2rLIRGHx+PBoh/8GNcPuh66ql/2+6oaRp6bElQUZGWMwqo7H0XywGTRl0vCQbCrE29+sIvuG4MnZiIMzOPtwi3A7hKKMsHq6UzXsLGsEKFIF3cViXUGJw/C3Im3IHfuIxh9XVaPzvlCgrtdlGDO0NFYOfcXSPGlCL5iEh4O11XieNMpnoh4OpYxxhJ5QrgESJtHSyy5VMYSgaZaHKs/DolYbh0/DYp3ANLThuC5u5ch67rsHp0XlWD+JQmSBZ0vShIcMywLT9/xc6QmpQq+ahKrROi+eevobjBd44mZBAOzeAK4BHhp84lWTzfpIrz5QRE0XYVEHFcNuAq3jf/H///34Gsy8OzdS5E9IqdH53dLcMvLKK6KSpA5X7hbgtlYMfshKcF+TKDhOCoaqnki4un4BWMs3mqAZQHSpl76eJSWCVYzyugCnGg6CYlYJmV+D9ckD/rK/w0hCeb/5LGeS1CLXJLgQdckOHZ4DpZ3SzBNxGWTcKLpGt46ugeMXqCWYWwqdNxi9XTLAqRNp9JNOMXq6SYNvaVyDwzDgEQcColkzg0zvvF3GelDrUvQnfIYRxL89e0PIM0vJdgfqW0+hfLGE9YDFCURCu5njFlymaWTujdT8AAt/VZ7lzccx8nmjyERy6DkQbjp+gnf+ntrElyH4mP73RmAJDj+2jF48kdSgv0RwzDwTmUJTDXME/Ov0DDJyokeixtm0THTaltTDXUPbZgGJGK57Xs3Q4nzfud3ei/BMFa/+wqKKqUEJZentukkKj6p4YmgP7r571ZOtCZAHdHNBlttW0kD1zafgkQs3rg4zJkwo0ff7a0EI/RGX1PwKnZVlrozjJRgv8UwTbx77D0wLWI9xOOZzxgb4bgAaZM0OmuhVXkyTcW2wH6YNLRELDeN+jv4k1J7/P2oBFeTBLOuy+7R98OREJ4rWI8/HpUSlHw3NU2ncPzsGZ6IETAxq7cn9V5iBn4IhhyrLevONyBQXw2JeOZO/EGvzxlCEnz27qW9kuBvtr3mugR/ffuDSE2SEuwvaLqGgsA+MEO3GuEhL93LGEt0TIAU7qWPe2nptdKQmQa2Vu2Dbn1IiU0MSR2CG68bZ+3cazKwhiQ4+tqsHn0/HOlyXYLjhudg+eyoBFPd2VPCTcWZAJrazvJETIKOyb05oVcChIrxdHNNt9qu9eJ5lJ8OQNID/o+9cv+OqkrT8HtOikCSuiepW6oqSaUqVwiIeENksVrtFiJiyw/dirZtO+ClHUDG6R6H5qLTjuFij9M9PQ2C/gWIrjWzDOjM2Nx6OTIGGkQEBDRAIEgCSUjqkqqzZ1eFAIEknNqVOjtJ7Wetc1Kp8+1vv/XVqeekmR9WT4eUpWNeb6MS/N2Ty1BaVKaqnpcEV9Y9D7PerM2egpSI9ETwHwd3AkRhbaGHhAXJLEhOgDLm07OVKRoh+OjQHgTpD0HAFx0VX13NjIQkUqHAase//uwVlLh8qup7JbgZDY3aSbCyKIDVc1+ASW/SZk9BSuw48jk6Lren0uKHhBCv2mLVAqRNzbT6EdZUoWAnPj3yv8MzJUFK3FE2BcZhEkK+xYZ/+/mvUewsUVUfCgex/oPN2Na4Q5sPSyVY4Qrg9Ud+CWOeUZs9Bcx0dHXgf47uTaWFFzHMUlusWoCI4k4QVLOm2nPyIL6/9P2wDEmQGg9Puo96QR62fhZzAf70i3+Ex1Gsqj4uwbVb38H2fdpJsNzpx2/nLYYh16DNngJmth3ag1gkyLo8fmMvIITohleAEn5Cz9ksiUg0goZDu4d5TAIWXPku3OYuT0hhODGZ8rFp4Qq47R5V9eFICPVb4hLcSW8Qkv4PTj9vwOnDmz9eCn2OPv37CZj57nwTDp09kUqLaYigUk2hKgFSm7rpn1msaU61ncPh00eHdUgCNu6vvAuSbnxaehsMFry3aLVqCUZ64hLcgO37d2kmwTIqwTXzlwkJjnD+88vdIEqMdbkVOsxRU6hKgIjhbnouYYpCFPphdkFRlDSMSZAM2ePG46Hq6QkRpIs8gxmbF62Cx1Gsqj7SE75OgtrcIz5HqZDgCOfz4/vR1d3J3oCgjhCSe6uyWwqQNonX/ES1LG8gEg5izzeN6ZuUQDV3+Gph0ZvTvo/BYMHGZ5ej2KnumXlNgvGnvpCgAAhHQvivI5/HBcTaYhp6UH2rIjVSc9PjTtYUX5z6Gm0dbemZkiApZk+8F1KWTpO9TKZ8/PGZV1Hi8qmqT0jw/Y34+K/aSvDNx5bCkGvQZD9Bcnx6dC9ILMq6PBdZmHOrolsLMIap9OxlSUCUGD75+rN0zkigEk+hB5NdAU33tJgL8Pun/x6lRWWq6iP0qR+X4CcaStDv8OGNRxfDmGfUdDaCW3Os+TiaL7WwNyB4kBCSO1TJkAKki+PX57Hu393dicaTX6Z5TAI1zKqYBl12jub75ltsePupv0OZ26+qPi7BN7WUoCQh4CzD64+8BJPepPl8BIND/YPtX30Wf8HaohYR+IYqkG/RwAaFzGBMj7+cPIgwvaEFfMkZn4sHKu5K/Nh5UGC1Y/2Cl+H3BFTV90nwYypBasH0B6RzqXD58drcF2HRm7nMSDAwu79pBIlFWZcbocMDQxUMLcAoqiFLJSw7E3rjfnrs/zQZkmBoppbUoNCYzzWDLd+BtU8sTUqC9Vs2Yvv+uARJ+gMmJBjAyoefh8Vg4TorwTXOtp3FyQun2RsQPEgIyR7s8tACBOrooWPZNxi8jC+bDmsxI8EQSPSH/VDNdEhZOt5RYI9L8PElCHjLVdVHeuIS3EAluEszCVYWlWNF3XNUglbO0xL08d9H9qby/U+jh2uwi4MKkFrTCAkzmLakYfecOICeaI+WcxIMgLfQg1pXgHeMq9gLnKj/6eIkJBi+JkEtoBKsiktwzkJYjUKCI4E93zSCKArrchtiCQkOiDzEQjc9JrLuuuv4Pm2mIxiSmeW3I3t8Lu8Y/XCwSnDfTm0CxiXorsDy2UKCI4GWiy043dacSosfEUIGdN3gAozhbnpm+uWQaAQHmg5rOSPBAOTl6PGD8jsSP+iRBpME39+IbY07tAlIZ1YtJDhi2H18P/tiQqbRs3GgSwMK8IotH2Td74tThxGOhLSdkOAmJnurYDcV8o4xKElLkN5Ta7duQkPjn7UJKCQ4YthzfB/7YkmaSM+ugS7JgyyxgmAq635/OXFQy9kIBiBLljG7ZjqkLB3vKEOSrATDkSDWf7AZDV8ICWYS37Z8h+6udtblOij4wUAXBhZgD7yQ4Gfdbe9JIUDeeAo9mOwK8I6hirgE1zy+BH6PuryhMJXgh+/iI40l+BsqQYtBSJAHMUXB5999xd6A4D5CiO7GtwcWYBamD3rtFrS0nUNrxwU+UxJc5f7KuzBuQh7vGKqx5zuw7omlKPOoe+6Gwt14S2MJVlEJrqhbRCVo4TipzOWzbw+yLyaYQs/GG9++SXLUkjLV7T2s++yllqY9OI1IEMeYa8B9ZVN4x0gaG5XgWwuWwecuU1XPRYJF5VhZ9xwsejPHSWUmB059DaIobIslxJ+sthvflgcoNUKSp7KG3Nt0iNd8BFeo9VbBbrHzjsFEodWOt596BSVFPlX1fRJs0FCClVSCq+a+AJPexHFSmUd7VztOt55mXS4jhhlqBGi7YsukIUoMX505xnFEgiw5C3Mm0u9ZknlHYSbfYsMfnv4Vil2lqurjElz/4WY0NO7QJiCVYIUrgNfm/hLGPCPHSWUWiqJg/6mjqTS4hxDS74dx868khqn0rGPpf7q1Gd2hLr5TynDcBUWY5CzjHSNlrOZC/Pszr8LjLFZVHwoHsW7rJmzTVIJ+/Hbe38KQa+A4qcziQPMx9sWyXEvPuUMLkOAO1v4Hm48nLC3gx/1Vd0M3Ppd3jGHBbMrHO8/+Bm67R1V9OBLEmq3vYPu+ndoEpBIM0IfNP/94CfQ5eo6TyhwOnUlBgEA1PSZc/0Y/ARJCJkDCFNbuB84c5TqcTMeYa8DMsim8YwzvZzJa8d6i1aolGImEUL9lQ68ECUl/QCpBP5XgmvnLhAQ1oLO7E80XzrAuz0UUtde/Id9UQEg5U2t6s3199gTv+WQ0td4q2K1O3jGGnTyDGe8uWgW3w6uqPtIT7pXg/l3aSJDic5QKCWpATInh6Pkm9gYypgwlQAd9orlZ+nZcvoS2zjbe88lYdFk6zJk4g3eMtKE3WLDpb1bA6yxRVd9fgoomGYUE0w+hD7TDLd+m0AC30x5XvddfgDHUsvY9+n1Tws4CPrjyXZjsCvCOkVaMRiv+9MyrKHGVqqpPSPD9jVSCu6kDtZNg/WMvw5Br4DmqMc2RcyfYF0uopOcJff/2FyDBJNa+x843QdHoJhPczAOVd0POnsA7Rtoxmwvwh6d/jdIin6r6SCSUkOAnf9VOgmVUgm88ugTGPCPPUY1ZTredA6EPNyYI/PSc3ffvVQESQnRX7MhEXIACPhhyDZjpv413DM2wWgrx9lOvwOf2q6qPS/BNLSUoSQg4ffineS/BpDdxntbYI0Ll9x2VICNGRODt+0e+7kI2swAJwXEhQG5M9lbBbnXwjqEpBVY7fvfkMvg9AVX1fRL8mEqQWjD9AakEy51+vDb3RZj1Zs7TGltEY1GcvHCGvUEWavte9hegQrws/bq6O9BBD4H26LJ0eKhmOu8YXCikElz3xNKkJFhPJbh9f1yCJP0BqQQrXAGsevgFWAwWztMaOxD63Z1sbU6hAa7eMNcEGIGbfmF6ln6nLrUkrCzQHqfVganuSt4xuGHLd2Dt40sQ8Jarqk9IcMsGKsFdmkmwsiiAFXXPUQlaOU9r7HCy9Qz7YqKUU4km3HdNgFnws/Y7ffG8ECAnHqy6B9K48bxjcMVe4ET9Txerl2BP+JoEtYBKsKqonEpwEaxGIcHhoKm1mX2xJJXQsy7+8poAJXYBNl08x3seGYk+R49Zgdt5xxgROFgluG+nNgGvSHD57IVCgsNAV6gLwe4OtsU3CpAQIoMgwBrmVJsQIA+mFFej0GLnHWPEwCTB9zdiW+MObQJSCVa7K4QEh4GeWA/OdrSyLnfQwxh/IV95gwqQuFm7NV86z3seGYcuS4eHaqbzjjHiSFqCkRDWbt2EBiHBUUU0FkVLZxt7gx6UxP9cE6AsuVj6kGgEF7su8Z5HxmG32DDVU807xogkWQmGI0Gs/4BK8Is/axNQSDBlFEVBS0crewMZ7usFqAMBkwDbLrcnbCzQlh9V3wtJN453jBFLXIJrqAT9noCq+lCYSvDDd/ERBwlaDEKCLJztuMC+WOovwGx6FLD0udB1UQhQY3LG5+CBijt5xxjx2KkE1z2xNAkJduMtDhJcUbeIStDCcVKjk3PtKQgwpnjif/oE6LjudVK0dnUgJgSoKbeXToLFVMA7xqjAlu/A+gUvw+f2q6rnIcGqonKsrHtOSDBJzne2pbLcRgiRe6UXhZe1y6XuTiiE8J5FxiDTr2xOzb28Y4wqCq12/MuTy1Ba5FNV3yfBBg0lWEkluOrh52HWmzlOanTRTt1DFIVtsSQlnNcrQAkFrCG+v3yR9xwyCpvZhinFNbxjjDoKqAR//7NfocRVqqo+LsH1H25GQ6N2EqxwBbB67osw5Zk4Tmr0EI31IBjsZFssS3HnyX0CNLKGaO1q5z2HjGLOxJmQ5CzeMUYlVksh/vjzf4DXWaKqPhQOYt3WzdjWuEObgAkJ+vH6vJdgzDNynNToIBqLoSPUxbaYQB//0ytABcWsIdpZDSxImnG6cZhTfQ/vGKMas7kAG59dDrfDq6o+HAlizdZ3sH3fTm0CUgmWO8vwxqOLoc81cJzUyCemxNAZ7mZdHr8B5F4BAhNYu7QHL/OeQ8Zwl/825OnNvGOMeoxGK95duBJuu0dVfSQSQv2WDb0SJCT9AakE/Q4f6h9bCn2OnvO0Ri6KEsVl+oBKgQkyIUSGBBtrhw4hQM2YO2km7whjBr3BgvcWrVYvwZ5wrwT379JMgmVUgmvmLxMSHISYouByKCUB2uTEH0KymZbTGyFEbwxB+rFbbKgtruEdY0yRZzBj08KV8DiLVdX3l6CiSUafo1RIcBAI9U84GkmlRXavACXJwbK6J9xNLRzlPYeMYG7tLN4RxiRGoxUbfrEcxa5SVfW9EtxIJbibOlA7CdbPfxn6XAPPUY1ILgU72RdH4ZJT2TwSiyYsLEgvOp0Os6un846RAv/PXpmAR1GmefxfR5/pK+l00iQhBwkhyCEuKnjjseOJjvPM4TgeeM0+e6hzqchc7vqsIrqzjuuuo+M4s+7zzOMqyugaIAEnHCIhxkCHoMgZIAlJgJiEpO+q2q8qBAO5uquPr7upX55KV1d/71tvvfV9vy+1cdideHnJEygtnBbR+GDIj+Xvvop1nuRJsDy/DM/e/ihsWTaarUo5/GRDigVZgCyxmEVNsEjkp+kv8SysmA+zWZv4iSTH4cJL9zyGaUXlEY0PBv14NpkSZBhUuKfh6dsehj3LTrlbqYMgxWAgBhb2VHNz1MT3+U5CEMK0e5DxLJ5zhbIANBKLMzsPv7nrpyifWhHR+GEJ1iZRgpVTyvHPt/4jHBYH5W6lBscHemMJz2FjiRaJfWMRsMbkFDoLMKdoBu0yzhlcOfl44c4fo2Lq9IjGyxJcfkqCxIKJL5BIcEZBBZ5a/PfItmZT7hZ9pBh7ztJ+AI2JWTx3ERiWo13GOUWe040Vd/4I04srIxqvSHDl71CzQ5aglPgCFQlOx69u/jtNgjGiCTCFMRuzcF3VxcqE10gu+USCy+94JHIJhgKnJLg5aRKsKqzELxUJ5lDuVvqiCTCFWVA+D2aTlXYZ5yzu3CnqJZgMiARnEgn+4qaHkGPTJKgGTYApCkMm9+I5V4BhtVdEE3USfBU12zcmp0AyT84rmoGf36hJUA3a6kpRSvNLUJVfSrsMDaiRoB/Pvfsa1jYlV4LLbnwQ9iw7xU6lH5oAU5TrZ14KhtfTLkPjFNFKMBD04/lVv8eapg3JKZBIcFZRFZZcdjs4lqPYqfRCE2AKYjFZsKhyvjKpNVIHWYLPEQlWTJ0e0Xh/wIcXVr2O1Z8lT4LXVy1AVVElxS6lF5oAU5CLp50PW5addhkaY5BPJLjizh9FJcF/+8sfkiZBRm/Ety+4FiyrLe1IGO5SUE2wiTRba3R8YckufvOcy8GwHO1SNMYh3+nG80SC5UUVEY33B7xJleCsKeVw2pw0W5Q0LAZzLOFB2V4iObrVRJt1BnCaAONKmbsUM/JKaJehMQl5RIK/uesnKCssj2h8MiVozbLDbXfRblFSsBhM6oMldA/bS6T9IBpDXFe1EJzOSLsMjQjIzcnHb+/5GUoLpkU0fkiCr2NNU4IlyDBwWhy025MOiMMC9KqJNhst4Dme9kNkDDazFVdNn69MYI30wJmdh5eXPIGSKaURjfcHfHhh1etY27QxoXVlnSObaK4lW32whIFhAR5XE89wHFmr2mKNF/PL5sKh7dxpR7YjF6/cvwxT3SURjZcluOK911CzPXES7PH2025LUtCxfAzBOD4swKDaHDlZ2oKNByzL4pbZl4NhOdqlaKjAbnfi9w/9EkX5UyMaHwj6sXylLMFNgCTFtRYpHMTRvmO0W5JwOLJW7GZLLCn8LMMwIiR0q82g5/W0+5ARTMsvxYy8EtplaMSA1ZqNN374VMQSDIZkCf4ONTs2x1WCR3o60dWb+QIk7oKe42NJ0c0qHyxOqM3gIi9dI3auq1oIzmCiXYZGjGRZHXj9h7/GVHdkm1kwFBghQTH2AohIN+5rgi/gpd2KhMPzOmSbrWrDe+V/7Mgvasgx22n3Ie2xW+y4ouIC2mVoxAmrNRuvPvBzlEwpjWj81xL8mDgwNgl29hxFdfMG2i1ICjzLwWawqA2XnScOCVBCh9osDvUG1jjF/JLZyLbm0C5DI47Y7U78531PorRgWkTjFQm++ypqPeolGA548V+bV6JvoI/24ycFjuNgNZrUhnfK/4YEKKJbbRan2U67D2kNz/G4cdalYFiWdikacSbbkYuX7n0MZYXlEY0PBv2KBFc3bYAkhKO6V8g/iP/Y9DY+3bed9mMnDaPOCFavWoCy88ShVacbsqEasrOsZBFztHuRtpTmFWNmfintMjQShDM7Dy/e/VOUF1VENF6W4HMrX8XLNW9hcKAXkKSJAyQR7cfb8NTq11Dr2Uj7cZOK05KtPlgUuxmGEflTX4+Tw08OY7R5XKQIjuMRFgTa/UhLrq1aAM5gpl2GRgLJzcnHCz/4MR7784vYd2TvpOMFMYy3NqzCps8/xW0XXoNrZ1+M3CwbeIMJDMtCCofgD/hw6EQX/rqnAXVf1mPAN0D7MZPOFHuu+mCOPSJ/DAtQJEcHOaZFm8eVZQfP6RAgfxrR4bA4cHn5PNplaCSBPKcbK77/KB59cwWOdB6OKKajuw2vrH4Tb6x/BwWuAjitDnAcB1/AjxMnv0LnsaOQGBEmuxEcz9N+xKTjtjnVB0tokz9GClC+ELUAjWYbDDo9Bn2025F+XFAyC05bLu0yNBKMJIRx5FgHanduw6A/+oUSCPpwsH0/Do7zu6/Pf05K0B3L2pGg7ELDHQuTo0NtrimOPPT099DuR1qh43W44bxLwLAs7VI0EoEkwe8bwKcHP8f7n22EZ/8ueMn3RCAK4jknQZ7jkW/LiSHBCAEyDCNKIakVjLpcBUSAuw7vpt2TtMJhcaA8t4h2GRrxRhLR2dOJur2fYXXTZhw8dJC4UEz4bb+WoIlIkKPdhYTD8zrkW1UKUJL8xHodpwWowGCv2mLKnIW0+5F2nOg/gTe2foB/uuo7YHRG2uVoxEg44MWO9j1Y3bIFLW1f4qT3JHGhCN7AIeRPvABlZAkGBmQJmsEwDO2WJBQ9EaDLlqsymmkl/8Ly2dcClLAPKntW6HCBZVmIYnJedCYg92pt8wblXJNgmiKJ6Ozpwoa9jajb04j24+0QROH0zwxZE0aLQTkP+UNJKSkcFIiMw9AZdbS7k1Dy7S4wHK8umMEYAuSJAMPkjTIMG22+InseeFJMUAzS7ktacaYEv0skaKBdkkYESEE/mo58ibVffALP4S9w0nty3LE0JBj0BcEbeLKUGdqtShjFOQWxhB8gvTlLgEA/6VgH+SyKNtsURx4MZPEGQ5oAo0WRoKdOOdckmNp0nejE+pYGVDdtxNETR8GZWPD6yUWTbAmKIQGiIILjOdotSxgVriL1wQy+GD4dKUDZiAegQoAMr4ObSHCinVBjfERJUiQokfOHr/oekaCedkkap5DCITQe2IW/NNahce9ODAz2y1eV35gQA5PNCC7FJChXl8kC5FgOxTlu9QlE7Bk+HSnAIDnkH65Uk7PKXYa9Hftp9yal0OsMKCAvqrXr0KRjZQnWEAmGAiH85Bt3aRKkTF/fCaxp3ooPt2/CkaOHERZGS0si78zX54PJbko9CQoSveYlGJ7XoSJ3qtpwkVjv81ECJC9PJE3zQGXfytUXlHGU5BXjupkLcW3lRbAYzXhl80qs2VE3aVw4HMb7H69FMBDG0luXgOE1CSYTSRTgOfgFVjXWoX53EwYHT5LlMPGCkH9NRQlKahdyGmAzW2GzONQFi1IrOKZ3+Ct/lht3g1GXt8pdAp7jyU4Zpt0fKlhMFlwy/W+wePaVqHAVgdEZTv/28FXfUz4nk2CIiE8QBFTX1yrfl956H5GgjvajZTwB70lUez7Bym3r0NbVRt5BdGJKRQmy5B6Zygx3GWmiSlGxzAHyPzj89UwB8mhGWPKT5MZo8xbnTIGeLPpzSYDyJJs9dSZuOO9SXFZ+PvQGkzy7R42TZShLUCIrZa1nbAlK5MeQL3R69/5QkaCEpbc9AIY78zVpxIe9bfvxv6TPdc2fIOD3KX1Xy2kJOogEdRFK0Dq0zBIhQZZjEto7mszIL1UfLIg7GJ4bR4CAlxhyD3mbc6PNy/B6FOcWYnfbHtr9STguhwt/O/MSXE/E57K7hib7ZBOeSPCRRXeQM4lIcMOo34VgGKIgjrgyJEFZmk/e/iBZMBztx84Iwn4vqj1b8Dbp7aGOVtJfMW65ZQl6e30wO8zg9ZNvWvK8SYQEWY4lmyYb996lAhzZOGa6YxAgx24f+XW0AAWxBSw7V03uuYWVGStAljT+ovJ5+NYF12BOYRWZYNELidHp8cjV35fPiATrTl+XiOXGWwDV22QJSlj2rYc0CaqB9E7uX2vnYaxs+AhrGusQCPoSektvr5dI0AROxw9tjhOQCAnqjDplvmYiHKdDVV4sAkTjyK9nCJC8DFESpO1kK7tTTe7zi6bjnQZGmXCZxg8uuQ13LryFNCm2icXwRIKL7iBnEpHgBuWaKIgIB4VxY1Y3rJOdiWW3axKMGElEwO/F+l0NeK/hr/iy9UvScTEuqTl2SGzyPBfE8JhjfL0+mChIkGUZIkA+AQ1NDcryisEZTGrDD5Oje+SF0Z0SUY+J39e4nJdfSiYHh7AQptulOGMxWXB15fyY5TcMoxuWIFDTvJHILzzpprF62zryWhg8+c0HwHCZO8FjRQqHcPhYB95uWI+PPJ/g5EBv3HI77E58+5IbcFnlXLgsDnT1f4Utezx4d2sN+k72nFkHTknQTiSoT44E5TvIOVguczfJWQUVsYQ3k8M78sLolcRjHwTIbzMn2uxGsw1FuYVo7TpEuU3xxR/0o3vgK0xxFsYtJ6MzKBKUvbdq4+qIYqq31ZKFAiy99X4wvI52W1IHSYTPO4CP93qw6tM6tOzfBUGM7yZcVToTy+94GK6c/NPXsh25qCqejm/OvxKP/PcKtHYcHC3BvuRIUM5sILG8IXPnhdyfeUXT1SeQxCaG4YIjL7FjDOvHkClVMW9qFa3+JIywEMab9dUYHOyLa15Zgo8SCd4w/xryctmIYqrr12H5B3+EFArSbgt15B4cOnoIL1b/D77728fx1J9fhIdIMN7yy7bn4unv/MMZ8huJMzsP/373z2A2WUfXiCEJCsEw2eykSe81LEGjxTipMIdhORYmhwl6kz7+TU4heI7H3IIYBAh286grZ18gTfeSGdSg9hYLSmdTak9i+fzIbjxT80b8Jag3YOkt9+LmBdeBjUCCEvmrrq/F8v/707kpQSIR70Af6nZuxSNvPo97XnoC72z6AD29xxJ2y5vmX4UCV8GEY/Kcbtx44aJx3hmRYL8f4SgkqDfrkZVjVj5lwTFjjOF4VpFlVrYZvF6XsOdPFcrdpTCYLGrDe8Cj5eyL/JhDOXaL2rucX1gBvU6PYAYuzqYDzUSCf8SybyxBlsURt7yM3qhIUF4baxrWQ5TECccrEty2TjmX42SJZjpSOITDxzpQ7dmCj5q3ovN4R1LuyzIcFpTPimjsZZXn473N1eTtjH5/svj8RIImu4nIio/s3hwHo4WDlEUyCiIkURyyKZEfy8kHl5QepAoXlsyKJVyWX//ZF8cRIJohoJecRb3KGZ0RMwsr4WltodSmxNJ0wINnav+EJ4kELXGW4JOL71XOI5Ig+X01kSBDFsMTN9+txGci3sE+bNu/C+9/thGefTvJxhpI6v15nofZYIpobLHTrbwPeSM7G5ZjoTPqFHFFi5yT42XZnVvCO5uFpXPUB4tiI8Nz3rMvj7cVdZNjBzkWqbnXgtLZGStAGVmCzxIJLrv+PmRl2eOWd1iCZL6jett6RXITIUuyur5WOc8kCUpCGG3Hj2KN5xOs37kV7V1HqNUSCoXQ7xuMaKzbnqPICqcEqIhLz0FPxMfpODAsS+050h2b2YppecXqEzBszViX+XF2HK8UkurBqBPg5RXz8NqGt2j0KWnIEnym5g0iwfvjLsGlt9yrnFfXEwkiEgmuU84fJxJk01iCvsF+NLbuxqrGOuw88Dm8vgHaJSn97+w9HtFYRmdQpMdyLHREeryBB8dztB8hI5hfNgcMx6sN7yamaxnrh4kyysZ8nBxRb1u5NhcKcwvRfrw96Y1KJk0HmvGvRIK/IBI0J0yC68gilCYcL0rC1xK8iUjQkD4SlEQBbcc6sMazFR+1bEVHV5si9VQiLAgRjzU7TJAYEQzL0i47o7isfF4s4Y3k6Bnrh/EFyGMPBBwmZ6XR3k3eBReUzcV7GS5Ame1Egk+v/QN+deODMJltccs7JMEl8hk+rK8ln5klwYBvAI0Hv8AHTZuwY38LBgb7aZc0Lk5rZJubLHNezxFh0q44s9DxOlxUfJ76BAw2Eif5x/ppfAEC3eRoggoBEgPi6soL8d6na5LbKUrsOLgT/7LmdfyaSNAYVwkaiATvVc4/rK+ZdPwZEryZSFCfWhKURBFtx9qxvqUBtc1b0N7VDkEM0y5rQgwGEwocrpGX/p+98oCTqjr//u/ec9u0be7SpIUibQGRXpSiEhCwYMG/+oomeX0/dsVEIGKib4yJMYpd89HE2AVBDCgQRGyI9Baa1AUpC8LW2am3/J97ESJKmbllZln2t5+7c2buOc/5neec831OqKqaShiGkW3LdU5dWnSEJPvtDlehYcGJXp4QgERM1VANk2Cj7czauqgpGuU3Qml5aUaTlS2ZEHxo9st4eMQtkH0h1+JaEBxlD4LjCYJcLYCgkYhh6Y4NmL50AdZsX49wTVW2LaWsBvkN0KqoSUp991YeqAegBxpyTk/7gw1jHURu+4leCycdzLAQqlFBNMxLd16Oiejb+lzMWD43g6nKrtaUrMPvPvwb/jDqVkhKwLW4nGhC8CarfdpAkEBwoGw//v2fxZiz+kvs2b8HqpbMvA+HuqhzXwiKP6W+2w/updzXA9BNSaKEfj/rTJeAsxeA4xbS/4oTvT45AIFdFGAtfV5gY2IMadfjjAKgqbU712PSrBfwyKjbXIaghAkjx1rt1CE4z2qPH3EjQVDOyPoNNYll29bh/WULsIo+wzVVGZnXbfE8h8aNm+KKnoNSHrNmz+Zs265z6tK8I2QlaHe4Ts8cjuP0E3U4KQBpYMTQjDkwbACQ1LqwKZo3aIZdB77NWMKyLcMwsGrzGtz3zmT8dcw9kP1B12KbEDMhaBbDWV+nAkHdgqDpacKomyyIeqWKioP497olmLn8U+zevxuqlvRsLi/FRAbJJ0Lx+3DbxWNwVl5RSuMMNYG1327Ktv06p6Ed+pocsjt8OxhWn6yDcMoQGuaBx4PU8qc7OydIuKBtd7x5BgBQU1UkYyrUWBK6bmBl5Src+9aTmHz9ONchOH7EjVY7ZQgu+dhquw1BQ1OxtmQT3ifofb1xBSKRMNVKw+XMei/zgokEPVERwTMejDFc3XsEBrbvlXKMld9uRHWkOttLqVMK+kPo07KTuUH2Auj6YjC+9GRdTg1AEVsJguuo1SttA2T84va98fbXM8mLnpGkZVIGrSkZJ/BFkwRA7Sfv12xZ4xEElbQgaJgQXDzPak+49GYqTKKj+aM1VZi1aiE+WPYJ9hzYA1VLupnWjImJDBKBT5AE66yaIJSoQFzXZxSu6TkcHM9SjvXvDV/XyTOeTfVpfS5E2Wc/AM//i/b0pJtySgBSgCpDM2ZRYe9lx0NRzlno3Lwj1pSs8zpfGZFhGNCSGpKxJNR4kr6fvL+3EBxrzf/h4hQgSH+HIWhgwmW/BMeEtGG/afdWTF/6CT7/zxJEo2Er5ukmYhxERYLoE8AzZkHviFo1aok7Bl2LDme3pY58yjEPlO/H0m1rsr20OqeRxeentQ8/0nYwLD5Vp9RugYqZFOw31MpJ1wXHCxjaoc9pD0ATfIloAsloErqWXqU/CsEbCII+NyEoY8LIsVY7VQh+SBA0oTnxil/R3rBTjklGazBr9ULMWPYJSvbsgG7oLmc2M2IiI/CJ1mPl7nvwCVQIerfphiu7XYj2TdqklJMf67UlsxBPxLK9xDql5g2a45yiZvYD6PpnnMB2n6pbagCUsBUa1lJrQNpG6KD1b9UFr4QKUF5d5lnCvJQJv1h1DMlY0nYMC4JvPoknr78Xij/kmjcLgqNustqpQNDUR0vmWTD87RX/96cXntZqEOR27v8Wby2aiwWrv0IsHnE/qRmQiTiBgCf5RfCMHQO+RgWNMLx4AEYVnw+fL2SdUzvauHsTFn6zLNtLrXMa2rEvOFG2O1wHz89IpWNKAKRDEzE0YwrdmQF23MhKAOef0x0zV3zsTbY8lqHr0BKq4zgmBMe9NRlPXHcPfIEc1/xxokQQHGu1P1w8z3R8yjGzl3xsdfvt6O8hSOBLxGrw8fqlmLZkPrbs3GxB8nQUE3iIPgmCLNA94I/+Lgoiuv2sMy7vOgjdWnSixPGO5klEa/DYrNcQjUYJsPxRuNbLmXyyHxee09N2UaLKtwE8FqfSVUg5KI/5UI1SMtUofUM8Luk0ALNXfwpVcw6STIujS8TRAYeuOY5lQvA37zyDv1x7J/zBXBchKGPCyMMQ/IggmAq8Zi/92Lq0/9P355i6dD4+Wb0QNZFqV/zwHEN+3lno0KwNFPK287s92LFnB+1/0rU1H3vm6TArIiSfCF5gR2FkArAwpxDDOvXHsI79kJ9b6Mp8Bp2Fxz58HVt2bLbm9uX5wEShHoIuqHfrrsgN5tkPoOnzOMYOptI15d0yDEOAhinUHG3Hk6Em8btZL2D5ttUupysz0pIqIhVRMw+uxDuv3Xn48zW3IxDKc9WnkYjjzx/+kyA4nxCoZyNVCNHhvXfUWAzt3BecIB45QCgp3Ym/EDTWblmbEqBTEU+FSfRJEBXBgt0RKZKCLi06YmTxAPRoUfxfHy7I0HW8+cVMvDz3Hbprhwu6Cb5AgZ/8sMwlug7K3MMnx4xHu7PPsRsiTEfr55zILUqlc1rlylANE34mBAU7zpZuW4WHPnjWNYhkWmpCRbTSPQh2Jwg+es1tCIbyXfVpJGIEwdfw0RKCoJFZCCpKAH+75fdo07T1cd9r8Sjue/spLNu43PYcJmwEWSDoiWAis76bYnR5zsotxOB2vTGsQx80KmhsdnZ1fYamYebyBZj8r38gqSaOeSf5JcgB+aifeqWvjs3a4a+j7wUnynZDLAQjAHJcJJXO6YGM4TPo2ESELbbjrBtRvUWD5ijZv9PVpGVKgiTAl6sQBGOuQHDFNyvxwHsv4pGrb0XIRQhykoIJI8da7dkEQT2DELxx8BVoc3arE75nsg8TRt2EOw7uw77v9qQVm2c8RJ8IkeDHM3b0dz9Bt32T1rikeAD6tCi25vBCRjKBV7+Yhdc+mQb1R/AzpSU0IOBRYs8Qjeo80An8dHB4I1X4meLTiU6By6DrH9h1J9JBvaTTAHcylSUJkmhB0K0qv3zTCkwiCFZXlbvq8wgEh/e6CDzHZyQ3PMdwVc/B5kE5ab9GhY3x+6tuRWF+w1TOnAU8f54PgYIAZL9swY/neRTlFeHq3iPwzLUT8Mcr7kb/c3p6Az8qdofKD9A+vYB/zHv3uPAzpWuZKzR1UU3OaoK+Pyt2EmIXEW1eOgNYujM8NOnhQzTJGGraOmlNQgX4dMtKROIRR8nKpswLyEQealxzJd7eg/uw6cAeDGhdDNnFC8wxAQPaFKO0phrb9u6AQX9eKjenAGMHXQ6OP/WxaphfhH7kbX3pLhyqLKNfjvXGMx6SX4ISkqlwSmACs2Dok/0obt4ON/a9HLcPvAY9Wp+LHH/OKaFrV3oiirlrv8LD01/Cuu3rf+Lzx5IDsmvF8UzT1T2HoUtLRwCcyjHuzXQGpL1ThmHQzTem0y5fbssiVdO3F8/CG1/NwOkuNZFEtDJm5sSVeD3ad8cjV92KUE6+qz6NRAwPzXgZ81d85mk+ckL5mD3xRXCCmPKYRDSMD1Z8jhnLPsE+KgQGr0NSRDBJOAoSnj4Lc4swsF1PDDmnB1oUNSfI8t4thPYzQQV65bebMG3VfKzcsBbxSCyloaGiUD0AbagwtxDPXzuRzlCB3RBhqk3DOZFbmM4gId1ZaHN1QzVepeYwepS0bdLhuKh9b8xc8ykqwxUupC57EiQRvly4BsHlm1Zg0rQX8cjVBMGQexDkJAXtG7fEfKveuQPr4ykcrkQ57WlBXlHKYyRfENcMGIHLug/E+n07sGn/DpQc2gvN0K33zQsao0OjlujapA1kX8gz75ZoztKyUny2ZQU+p2fXgV3QaV91TfV23nphSPs+yAnmOQmxmGi2NN1BtkoVXfYgNHxKzR6wedD+/uV0TFs6G3VBakIlCEZdgaCpHu27448EwaBLEDRUFTe9OAlbv93ieS7+Z/Bo3DHsOqvQnS7S4lGsK92Gj9YtxKqdGxCOVB99Z+5ptCICNamlFCtUFKKlnz5rrw3Kp3P+9JgJKMprYDdEgp6bOYF7O92BvJ3ZaIPDhM5XqWmvNHI8RnW+AHnOiF9rJEgCfLk+1w7+8k0r8NupL6CmusKVeOsJfLtKd2UkF9O/mo0NuzZnZC6n2l9WiunL5uKOqY/hgfefwpcblxwDP1MmAHU9tcJWDz57urhjfxTlFtoPwGEzGObaGcrbnpTH+/Tf9q1qkFeECzv2Q12R2xBc8c1KTJj6PCLhSsex5qxdhEQynpE8JBIxPDjlOVRVHsrIfOnKoDys3bkej370Eu6a8ie88vkUlOzfCU3Tjt9f06HTk4p4Vg/AdJUfysfI4gEEMdsoMjfnNbp3ZXYG256VJiyFpr/7vQEbAXhc2vl85AXzUFfkNgRXEgTvf/dZRGuqbMeI1FTiq40rMpqH0oN7Me7tp1ATrsjovCfTocrv8MGKebhr6l8wacbT+HLjElSdIq+GYSAZU1OegxdYtpd52unijv1RlNfASYjdRLGpdgfzjtxL/Mv0/4Dd4Q3yGmJk10GoS3Ibgqs2r8J9BJNYpNrW+KUlG1BWeSjDOWDYWbYDD374ogXgbMlQE1i7cz0en/sK7njnUfzt03ewdd92JNVkSuN1TSMAJlKej4nMtX0/E9SAwHe5ef+d5MzAm5TzXXaHOwIgTVxCH9McBMCITgOoAhShLsm8CG5CcM2WNRj31mTEo+H0Bho6FmxZBtEveL5mnvGQ/BICBQFaux+CLGLj7s349YynsPu73Z7P/0NVh8vx0eoFuGvKY5g042ksWPcVKsIVaaZORzwch2GkPsYEf71Sk3k3RnS5APmhAidhDkDAa04CON6xhx5+aBtR+Hpq+uyMVyQCBX2upEpdV2RuLsdzFgjVuOpKzP1l+7F6zw5c1KE7BFFKacy+sr144+uZUA3VApSacMfLDyXQGuWgQo9MABCseX4IfhM8n29dgTwliFaFTSkvvOseTBmaim/2bcXri2fipc+nYOE3y1BWXQ6dQJZ2LKJejOCXzt6Zey35JNeKXl1X08Kzcc/g6yBKir0AVmXi/s4x7i0nPlzZLSOhPQ+ev83u+GSsBne99zhK9u90w06tkXmRtKSGaGXUaruhrm27YvL14yD7g6fsO3XpbLz6xXtHvSRjScSqY4498AR3QREhKibwWEqXnvEMnVt0xC/7XYY2jVu7kgtT0UgVFmxejjnrF2LXd3uQVBOO4pnAjFOOkmkWLl+OAkEW6wGYgngqguOG/gIXFvd3EuYgGPpSvrc6CeIOAA2jDTQso2ae3RhfbFqCx+e8DFXT3LBUa5QtCBrJOG6f8mfsKC05xosTCDKRQfJJYNJh6Nm57LIko1vLYlzaZSDObdYBHBNsJFXHtv0lBL1F+HLzMlRHqh3n1hyvEvTi4RhBML1YPOMRyPeDo4tdr1OrXdO2mDx6HDhJsR/EwDOcyN3t1Itr5cpQjefp4zbb4zUVk/71LFZuX+uWpVqjbEBww7ebMGH6k0iqyZ94SQeCJuRERYToE+iiM1vQO54YwSI/VIB+bbqhe7P26NS4NQKB3BP2V+MRbCjdgdW7N+PLLStRWl5KxVJ17EPXdSSjSXoSaYPviJSQYuXIrdzUZUmijEeuuBOdm3dyEqYCDN0p39ud+nEPgIbRChpWUTPHboxtpdvx6/f+ilg86patWqNMQ/DZ+W9g9uoFJ/RyKggygUHyixAkwaSgp5fbjG0CMeALojBYgKDiP/ouQZD7ruogKsKVBCgNuku5UxOqBT01rsJJRCYy+PP89fBLUYM69cP4Yb+yzpQDPccJ3J1u+HF114yE9jR4/i4HlMCLn72LmSvmuWmr1ihTEKyqLsft7z6Kg5UHT+pFjScRrfovBM3DICgigU8Cz/g6danN9R4BfzKahK7pjmOa2fEXBOpcrrxSbjAXz4yZiAb5DZ2EKQNDd8p3iRueeFdXKPJ/oP8VtsfTIRrb6xI0dJagWivzkgiSAF+uz7ULs2bLGtzz5hMIE/TMAmJoKt5f8+lJ4XfEi6hI8OUoYAIPJaQgWBik7z76zhz5OwIbLakiHkkgXhO3wKPrumvgT9dHrCqK8MEw4uG4K/AzJVPOnObqTBFPORrTY7hT+NGm4jm34GfK9Z0zNGMCmfyTkxhfbFqCx+e8DFXT3LZXa6QmVEQro64BIS/nLAzo2B2lFYewcd8m8BKftYupqRpi1TECz7H7Z/qRgzKBV/TU2xHwqTEViWjCNeD9UJJfghyQ6+GXoto1bYvJV94HTpTtBzGMvRC4TpTzCrd8uQ9AwwhCwxpqtrIdQ9fx8KznsWTLSrft1Sq5DcEfSgkqEH3eguZ40qloRcoj0PUTr0kJKa5D8EgOdYJvIpok+CXhflYPS/JJFsjr4ZeaZEnBY1eOQ7uz2zoLxOFWjnEvuemNd3uxdCjC9PF7OLjUHM/jzkHXIieQ47a9WiVBEuDL9XlykWLhGJIEAi/gejLFaxInhZ/VJxy3QOWGzPWZ0E0S8CLlNagh+CY9hJ8ckOvhl6ZGnTvEDfitI1r9w21vrgPQEsO7dEIWOQlxVm4R/k+fS8HX8YN2GIJKnYCgOY9K8EmlX7QqBk1VbXkzx5iPllQRq46hpixifWqq7tnaeJ6DL8cHyS/Vwy8NNStqiht6DncaRqeKNp7ynnDbnycAJKN0sjGemo4Mj+gyEF1aFnthsVZJkMQ6AUFDN89patI1HZHyKNR46hA0++mahmQ8iUhFBDXlESRj3q7N3BFREeHP91uf9fBLXZIo4Y5B10L2BZ2Gmg2GuV549ASAlgQsov//dBKCYwLuHXI9cgI5ntmsLfIcgh6Dwo5MP9GqKGL0qAkVugnQ43g0f9OSKuLhOEEzQv1j9F3z1Ju5DaIsWOBTQgp4xrKdrtNOl3Qdgi4tOjkNU0Xwu4/uhe6FR88AaBlm+CM19zqJ0yC/IW45/xrwZ0Dl9RKCuurJ+TlGHM/b8p6Mq4hURL6HWxTxmvjRxwRkpLwGNfQuEU0QJL2FOM94SH6ZwBeAkuMDEwVP9qOuq2XDFril32XOA3F4gv5v9cqnZwD8XrtpASYEHd2+IR37on/7Xh5brR3yCoKi4v1FNuNLfsn2eF3TLRj+EIDJWBKax/A2fQsSo7z7CHx+KEEZTGD14LMpWVIwYejN4OjTkTisI0I9R/vg2QHwFICWcR6vU/MzR3F4HuMGX4cG+Q28tFtrdBiCPtcuIBN48MzrWndYJgCZyDIyl1PxvAls2YKeL9cPURbpt8zkqS7r5v6j0aJhC6dhEjAwke5AmZdePd9tWkAYOiZSs8JJHMUXwv1Db4IkSl5brhUSJME1CAqKaBWRTMj068tRXIO36/6s3DLy6IO/IAAlKFOBYLXW7+mmXm3Pw6XnDnYj1JtgmOe138zcChEriebPOIpBB7RT0/a4rs+ojFiuDXIDguZYURYy6tuErT/PHXi7JZ48SX4Z/nw/5dQPkYoCn6GicKaoYX5D3DfkBnDM8XkrIfj9gc5PwmvPGTkBtBAVAp6l5iKnELzmvIvRm6rMmSKnEGQSs4CUSZleeYFlHYLmzAKt35ejwF/ghxyQwEShVoG5rkgSZfx66FjkBPOchjKh9wA9uzLhO2M3gw7dQRiYSM0qR3Eo0XcPvg6NCxpnynrW5QSCoiJm5cIfhWC+D0zILIB5noPkl2huP+XNTzmQ6De+Hnwe6sZ+l6O4aTtz452GmgaG92mv9Ez4zuzJFLCIIPiU0zD5oQL8hqqNT/Zn1H42xUSWNgTNS2+Oy5ZMr0wgeOf5IfmkzOQoR4G/IAA5INN3oR56GdDAjn0xutsQ2nDHOCkh+D1IexbLlPeMApAWphIEn6XmIoeB0OHstvh/A8eA8dm74JmUBZM0ISjItQMAJojloAw/gdBtIPM8Z8E1kO+34ouKZM1XG9Z9Jqhtk1a4e9C14ATHBS5BzwO0b9sz6T+jADRFCzwIHfdRs8xZIB5DqfKMOu+iTC8ha0oXgqJSOwB4xLsgCRak/ORfkJyB0MyDElLgLwhYcGVi7VnrmaL8UD7uv/gm+HwhN8K9DoZpmV5DVk6MYRg8QfAeGHgcDiGsJaL4/3NewdItK7OxlKyI8gctqSFaGbXaxxMTePjzA7UWCqZvQ9ehJjTaQxWaqkPX9BP253kOPK3JBJ0gC+AZX2vXdiZIlmQ8fNkd6Nq8k1ndnAXjsI4oMJz2c3em15G1E0QXwA/VeI2Sd5XTWOFwBe7/4GnsKC3J1nIyrlNBUAkqkPxStm2mvJYfQhE/Wg5nws5qcPXQqwViPMPtF96A4V0uoD3hnYaroP2+khO5BdlYi2P3dkUHOQKBG0/N7U5jBQO5mPjzX6AwtzBby8m4TBAwkcGX6/sJFKx3Esu2xbTWYj48z4MJAq3r2Mf8nTOfevjVCl3V6xIMKx7gBvx0qmxPQMBn2VpL1gBoig60Cb876Qk7DIRmhU0xniAY9AWzuaSM6kQQlPwSeJbVra3XaaL/Za9sgKsqzzz+f8+5nyFfQBKQjyQgAQwEUovLl2KaVkQqmsbuFGe7guK0o+I4u+yu7thMxzJduqPWdWu7nW7VFWl3l+LH+AmIK7LiUowIGiSJfIR4ydeGNCEhIffec979nyuZdRREzLn3vffm/c08nOeQmed9nve+7+9cLJWzF2PV/G9DmB43yr1EA/2cZ9dWNY/yWyI84hV+BR5kag9XgrMLL8Pd3/w+/L6A6rEShiM+j8+DUWNHIZgdREZuRkyAnxaiRuMG5VPKcE/FzRBevxvlDsPE3Tyn/SpnUi7As108BClfcKPUkpnzcftV34XHnS9UymAYBrwBb0yGWn4at5l2yVTULFsDX2CUG+W6IXEXz2mz6rmSQoCxr4BH3A2BOjfqXV/+Day6shqmYaoeTaNJeYoKCrF+xZ3IGJXjRrkw7/l64RXbVc/lkBQCdKAEQ7DxQ6Ydwy9m4KbLr0HVvGthGEkzokaTcowfMx4/uu525GaPdaOczdhE6zymeq4hhOoGPouMytv4+AUjY9i1rCge3/0snq/dBsu2VY+m0aQUjvx+suJOTM6fPPxiUtI24i2YuFEI0aV6tiEM1Q18DhP/Bol/ZhYdbilherBmcTWq5l0L00i+UTWaZOUT+d3hjvwchDjMu70mmeQXa0t1A+dCShmAhceZroQLkpZWFI/vfhbP126DZduqx9Nokpr/l1+hOwWl7KRqbhJesUv1bJ8lKQXoQAnmUYLPMF3iSr2zEnyOErS1BDWac+K6/IA+xl3CIzaqnu1cGKobOB9CiE6YWAWJRlfqmR6sWVyN78xbBsNI2rE1GmV8Ir873ZOflFEIbOA93qR6tvMhVDdwIWRYXg4hX6YRx7tSz4pi454XsWXvK4gy12g0QFFBIX503e2YlD/ZnYJS2ryzv6L81gkhwqrnOx9JL0AHGZHf4r/PcEOz3Slo47l3d+DJt7YgEo2oHk+jUcq0S6Zi/Yo7kZs91p2CUtIsYjPldyvl1696vi8iJQToIKNyJR9PMgJuSXDnoT/in157CoORQdXjaTRKKJ9Shppla5AxKsfNsjsov+9Rfl2q57sQKSNAB2nJtZB4mKnPrZr7jr2Pn736W/T296oeT6NJKJWzF+OeipvhC4xyr6iUtfCIFZRfm+r5vgypJUApDdhYC1s+AiEMl4riWEcTfvLyb9DWlRK/mUYzLEzDxE1XLMPqBSsgvH73CgvshwFHfiHVM6alAB0oQQ+ieICd38dX1yTY2fO/2LDtCXz4cYPqETWauOH3+fHDq1di2ewrIUyPe4Vt2QivuJHyq1c948WQcgJ0oAQDCNs1/JS5J0EyONCLX/73Frz2/i7VI2o0rjM6azTuXXYb5haW8ua7dm14IdEIGzcJv6hTPePFkpICdIhJMIoaTuCqBGVkEJv3vYbf/c8LiEQjqsfUaFyhZMJU/N01qzEpbxJvvYvXPoXl55CyAnSISTBs18A03JWgbWHv0QN4ZMdG9PT1qB5ToxkWV5cuxD0VKxEMZmn5fYaUFqBDTIJR1DC7jz+uaxJkYbScbMHPtj+Jj1qOqB5To7lofF4/bllUheqvVUJ4fO4Wt2UjpEhp+TmkvAAdYhK08df8Iv2Yr+790pRgeLAfv9n9HF498AZs21Y9qkbzpRg3ehz+ZukqzJ40g7fccLe4wH4YuFkIUa96Ti3As1CCBiW4lhJ8EG5K0KltW9h56I/41c5/R99An+pRNZov5M9KLse6yu8jOzOXN9zlKy5lLTziO5RfSPWcbpA2AnSISdDCLUwfZWS7XBwd3e34h61PoOHER6pH1Wg+h98XwK2Lq3FD+TcgTI+7xXn+KdMdMPGXlF+b6lndIq0EOISMymo+/oVR4HrtSBgb976MZ2u3Isxco0kGiscV4b6lt6KIT9eR0qb8tlB+d1B+XapndZO0FKCDjMglnO4ppsXxqF8fasBDO57Gic4TqkfVjGB8Xh+Wz63EDxbdCOELuL+AlFHK79eU372UX7/qed0mbQXoIKUshY2nIVHOV8Pt+uEzffjt2y9g2wdvIhwJqx5XM8KYnD8JaytWYk7RrHgt0UdDbODNeYjyS8sDntYCdKAEJ8DC40yXIg4SdNjfVIdf7/oDjnc0qx5XMwLw+wJYPqcCq+Z/G/5gZnwWkbITQqyDiU2Un6165niR9gJ0oAQzKcEHma5mBOKxhhUewO/e2Yrn9+3AwGC/6pE1aYghBEomTsMdV/05Zkwsic8iUtIK4jAk1giv2KV65ngzIgToQAn6YGMtf9gf8zU7Xus0dzTjl7s2o675EGw7bT+cmgSTk5mD7827DlVzKyC8/ngt4xzYt2FSfkI0qp45EYwYAQ4ho/IGPh5lFMdtDSuCN+r34uk9L6LtT+2qR9akMD7KbtH0r+PWBTegYPS4eC4VZmyi/P6W8utSPXeiGHECdJBSzoSFf2V6ZTzX6T/dg83v7cDL77+Jvv5e1WNrUgjDMFAy4VKsWXQjyiaX8qbG9ap20wTrYeAxyi+sevZEMiIF6EAJjqEENzBdzfDFcSGETp7A7995FW9/tA+D4TOqR9ckORPzJuLmedehcvrXIXyBeC93GBJ3Ca/YrnpuFYxYATpQgh5K8BYmG/iFLYjrWraFg6FG/Oe723Hg+EFEohHV42uSjILcAiwvuwo3lC1BMCM73svZjJdg4m4hRLPq2VUxogU4hAzLeTDwKNNFcV+L4qs9XkcRvob6E42wLEv1+BrFjM4ajWtKF6NqbgXzMbyVcb+W3bz5D/PM/5zy61c9v0q0AM8ipcxDFDXMbucBzIi/dM9gb/OH+MO+HagPNcCybdVboEkwQ+K7fvaVyM8tSIT4nBtfBxv3wIOdlN+IP3RagJ+CEvTAQhXTDYxpiVjTpghrPz6EZ957HYdCjYhEI6q3QRNn8nLyUDlzAa4vuwr5zCGMRCwbZmyEiQcovpDqPUgWtADPAUU4lSL8KdNqhi8ha0YGcbD1KJ478AZqjx5AOBJWvQ0al5kwdgKWzVqMay9bgOzM3ESJz6GJcT/lt4Xy0wfrU2gBngdKMEAJfpfpekZxwta1ogh1teLFD3bhzYa9OHX6lOqt0AwDwzAwc2IJVpRdjYVTZsMfzErk8o7stlB8NRTfUdV7kYxoAX4BlKDziS6mCH/KpyNDT+IWt3Gqrwf/1fgOth7cjeMdzaq3Q3MRBP0ZmH/pXFTNrcD0giIIrz/RLTQx7qf8nqX8zqjej2RFC/BLQBEGKMEqpusZ0xK9vhUewMHWo3ip7i3sPbIfg2F9npOVwoJCLC1diG9OvwI5mbkQhpnoFsKMTRSfc1abKT9b9Z4kM1qAFwFFOAER+34Yxm18DSR8fdtCT183dn70Ll6v34MjbU1OT6q3ZcQzKpiJhdO+huWzFmNGQREMr583S8HVEqiDxN9TftspvrDqfUkFtAAvEgrHgygWcec28HUBw1DQBKQVRUt3O7Z9uAdvHd6H1q5W1VszovB5fZhTWIqlly3E/OJZ8PmDvE2JPwpnOcXz+DBP4mMUX5fqvUkltAC/IhRhADZWw5YP8GtfoLCRmAyPdYbwesM72E0Ztv+pXfX2pCVejxdzikpROf0KLJpSBn8gkz89r5BQdo1sxiswsY7Pw+zFVr1HqYYW4DChCAtgoYbpDxg+xc1A2jZCXS3YRRG+fWQ/jnc0w7L1vfiqZGdkobxoFpaUXI4rCkvh82d88gd10hu6uXWQuJfy26rF99XRAnQJinAmRfiPTJczPKr7GaL/dA/2Hv8Qe5o+wPsf16OH77YW4jmhSOAxPbh0fDHmUXoLisswtaAQwkyan9M5aC0wxHoYeIL9hlW3k+poAbqMjMhK7uoDTBcgiUQY643iC50MYf/HjXgv1ID61iPo7e+FZVuOwFW3l3AMw6DwvCjKn4iyiTNQPqkEcyaUwB/MVN3aueiCxGM8UY9QfN2qm0kXtADjAGXigYVq7u69PLRzkGQi/DQtnSfQ2NGMQ+1NaGg7ilBXG8KRQUStaFpJ0TRMeDxeZGdkYcb4KZgxrhjTCwoxa/xUmP6g6va+CEd2m2DiYYqvSXUz6YYWYByhQHwxEQLrGI4Ifap7umDPlN9xSvAYxXjsZAvjBJr5PH3mNCJWJCZG27ZVt3lePKYnJjofY1xOPgrHTMC0/El8jse0vMnIzszlqU/yY+98eITo5Mfz9/x0PkrxHVXdUrqS5CchPaAIAxRhFdO/YpQjBUT4WQb6T6H11Em093ahnc/WU51o6+lEB997+nspRkeOFizboiCjfNrO3K6tbxomnSBicnNyj2ki4A1gbOZoXJKTh/HZYxl5GJc9BuOyxiCfuaAMU5AOiu8/KL5fcN7DqptJd7QAEwiFkEERLmN6F18W8EZnqO7Jlbkou4GBXnQP9OF0eAB9TpwZwGA0zP/rxZnIICzKsLOvm2PbF6yX6c9gBGN5HgXnNTzIyciEj0LLDWYhi3/PCY6C4QuqHt0tnE0JUXybKL6nKL5G1Q2NFLQAFUAR+hBFBXd/DV+/xRijuieNEsI8A47snoKBzRRfs+qGRhpagAqhCA1EUA7YfwHDqOZ/FTIM1X1p4k4fYw/jSZjYSvF1qW4oLfi/9sqvJYooDsPvnB1FFpGQWEpiEenCIhapvQkKIvpH0CeIPp3XEV1FXghRFyJSS4h1EbbsRcgiISEi4+6ZnlknCgTZTDs7u78HXn9zjqKvc2aePQEmwAEBGVbl9UipnrFEipoM3ck4VTxpkSXOeFGxVhFfErrUqGMCHDAQYVkd1TmZpywfkCpxoXsZJ2aXNDjPRU5xCek1QxcyfmMCHGCQYVVd3eEyk2GdTIfuZPSF583aUNcvKXLPFauB+PZClzKOYgIsAIgwVqJ5XqTHSvWEreukHLqXcYQmWSYvVNIK0tsOXcg4HhNgwUCGkzpAhqWeDO+zVSNToXuNKJ5syvsVOfeSM1lFeq3QpYz+MQEWGGRYVqI5xbqXy7BOKqF7DTkdbvy6ougd16+QXoO5hfh86GLG32MCHBKQ4ThjRt2eBB+yUeclvcZ1HLrbENAma7wtb7i/yxrTJusdk17xMQEOIcjQMabIjLzuKtVtssBpX2bPhe5XAL6TdXmP9NxrPkLWsz2Etx+6mHG6mABHAIQY61CIFXV1ixf7ppyrsb5KyqH7DQAt8lGpf8/nw9tceD8Q3l7oYsbZYgIcQRCi06H4JtRRTU4LSnWDp2GeOT/kUmySL+r6hkrug0paY90mewgvCV3O+L+YAI0euRQnekl0CTHUkMQVnpA5RdFsL9KF0D37/Gf2ebSbdM9kl81P8vqsWBusd0hisjMyTIDGseRijPNM6UCzcggyYnpdVOoryLEqF51XqnP8zMwZV9ol22SLtOV9W5Hbos9X/n6Lli32v5FOFkTXCX0PjcHFBGj8E7kgM/6cE6RCxtHQoRAjTfJ1us9fmyCzNtMzdzXWE94+aeff978mgvOh74FRXH4CWRJObWpLCNYAAAAASUVORK5CYII=" />
                                        </pattern>
                                    </defs>
                                    <defs>
                                        <clipPath id="clipPath7227361933">
                                            <rect
                                                width="29.106371"
                                                height="29.106371"
                                                transform="matrix(0.854002 0.52027 -0.52027 0.854002 15.1432 0)" />
                                        </clipPath>
                                    </defs>
                                    <g clip-path="url(#clipPath7227361933)">
                                        <rect
                                            transform="matrix(0.854002 0.52027 -0.52027 0.854002 15.1432 0)"
                                            width="29.106371"
                                            height="29.106371"
                                            fill="url(#pattern4185874665)" />
                                    </g>
                                </g>
                            </g>
                        </svg>

                        <span class="title-text">简讯通报</span>
                    </div>
                    <div class="expand-icon">
                        <svg
                            width="21.2"
                            height="12"
                            viewBox="0 0 21 12"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M1 1L10.5 11L20 1"
                                stroke="#A8A8A8"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round" />
                        </svg>
                    </div>
                </div>
            </div>
            <div class="card-content brief-content">
                <el-scrollbar height="30vh">
                    <div class="brief-item" v-for="(v, i) in newsLetterList" :key="v.id">
                        {{ v.title }}
                    </div>
                </el-scrollbar>
            </div>
        </div>

        <!-- 新闻动态 -->
        <div class="news-card">
            <div class="card-header">
                <div class="header-content">
                    <div class="title-section">
                        <svg width="40" height="40" viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M18.5204 0.000369235C28.7738 -0.0646081 37.0298 8.45522 36.9999 18.5553C36.9713 28.5046 28.6516 37.4143 17.6432 36.9517C7.88746 36.5397 -0.181432 28.285 0.00310372 18.0056C0.179842 8.15373 8.48135 -0.0347185 18.5204 0.000369235ZM26.5853 19.9276C26.5853 17.3727 26.5931 14.8178 26.5853 12.2603C26.5853 10.6307 25.7185 9.78466 24.0993 9.78466H10.6477C8.9284 9.78466 8.07849 10.6086 8.07719 12.3032C8.072 17.3913 8.072 22.4791 8.07719 27.5664C8.07719 29.0686 8.95568 29.9315 10.4658 29.9315H24.0408C25.7601 29.9315 26.5827 29.1141 26.584 27.3922C26.5866 24.9023 26.5871 22.4141 26.5853 19.9276ZM27.469 27.0634C28.8258 26.684 29.3352 25.9822 29.3365 24.5501C29.3365 19.5469 29.3365 14.544 29.3365 9.54164C29.3365 9.34671 29.33 9.15178 29.3118 8.95815C29.2247 8.01857 28.7023 7.39869 27.842 7.079C27.5361 6.97619 27.2147 6.92738 26.892 6.93475C22.3436 6.92522 17.7952 6.92522 13.2468 6.93475C11.9291 6.93475 11.0675 7.63261 10.8621 8.88407H11.5678C15.7926 8.88407 20.0148 8.91786 24.2397 8.86978C26.2007 8.84639 27.5262 10.2642 27.4976 12.0784C27.4197 16.7983 27.4703 21.5209 27.4703 26.2434L27.469 27.0634Z"
                                fill="#478CAE" />
                            <path
                                d="M17.2625 18.1498C15.0532 18.1498 12.844 18.1563 10.6426 18.1498C9.86284 18.1498 9.50806 17.8288 9.52496 17.2077C9.54055 16.6125 9.93822 16.2889 10.6842 16.2876C15.0766 16.2876 19.4691 16.2876 23.8603 16.2798C24.4464 16.2798 24.8258 16.502 24.9727 17.0491C25.1286 17.6235 24.6569 18.1381 23.9473 18.1459C22.1527 18.1654 20.3567 18.1563 18.5607 18.1589H17.2612L17.2625 18.1498Z"
                                fill="#478CAE" />
                            <path
                                d="M17.1883 21.4039C15.1091 21.4039 13.0328 21.4039 10.9596 21.4039C10.7427 21.4062 10.5257 21.3966 10.3098 21.3753C9.79781 21.322 9.5496 21.0075 9.5236 20.5137C9.49501 19.9679 9.8186 19.682 10.3033 19.5494C10.4528 19.525 10.6048 19.5202 10.7556 19.5351H23.7316C23.8614 19.528 23.9916 19.528 24.1214 19.5351C24.6153 19.591 25.0311 20.0303 24.9869 20.4916C24.9388 20.9958 24.6802 21.3571 24.1305 21.3688C23.0285 21.3922 21.9213 21.3935 20.8232 21.3974C19.6133 21.4065 18.4021 21.4026 17.1883 21.4039Z"
                                fill="#478CAE" />
                            <path
                                d="M17.2546 26.0355C19.4638 26.0355 21.6666 26.0446 23.8719 26.029C24.4229 26.029 24.7933 26.2447 24.9622 26.7567C25.113 27.2129 24.792 27.7379 24.3125 27.8588C24.1645 27.8917 24.0131 27.9065 23.8615 27.9029C19.4491 27.9029 15.0376 27.9029 10.6269 27.9029C9.88228 27.9029 9.5379 27.6027 9.5249 27.0101C9.51191 26.4176 9.91477 26.0407 10.6373 26.0394C12.8426 26.0355 15.048 26.0355 17.2546 26.0355Z"
                                fill="#478CAE" />
                            <path
                                d="M17.2611 24.6567C15.2026 24.6567 13.1446 24.6567 11.087 24.6567C10.8479 24.6567 10.61 24.6567 10.3722 24.6359C9.83422 24.5956 9.54182 24.2941 9.52882 23.7587C9.51453 23.1869 9.84721 22.8945 10.3787 22.7905C10.4431 22.7847 10.5079 22.7847 10.5724 22.7905C15.0142 22.7905 19.4556 22.7905 23.8966 22.7905C24.5464 22.7905 24.9947 23.1895 24.9895 23.7314C24.9843 24.2733 24.614 24.6099 23.9499 24.6528C23.7771 24.6632 23.6029 24.6619 23.4301 24.6619L17.2611 24.6567Z"
                                fill="#478CAE" />
                            <rect x="12.25" y="11.8704" width="10.0065" height="2.859" rx="1.4295" fill="#478CAE" />
                        </svg>
                        <span class="title-text">新闻动态</span>
                    </div>
                    <div class="expand-icon">
                        <svg
                            width="21.2"
                            height="12"
                            viewBox="0 0 21 12"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M1 1L10.5 11L20 1"
                                stroke="#A8A8A8"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round" />
                        </svg>
                    </div>
                </div>
            </div>
            <div class="card-content news-content">
                <el-scrollbar height="30vh">
                    <div class="news-item" v-for="(v, i) in newsList" :key="v.id">
                        <div class="news-header">
                            <div class="news-title">{{ v.title }}</div>
                            <div class="news-date">2024/07/04</div>
                        </div>
                        <div class="news-content-text">
                            {{ extractTextFromHtml(v.content) }}
                        </div>
                        <div class="news-actions">
                            <el-button class="detail-btn" @click="goNewsDetail(v.id)">查看详情</el-button>
                        </div>
                    </div>
                </el-scrollbar>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { nmsNewsListBrief, nmsNewsListPublish } from "@/apis/nmsNewsController";
import { useRouter } from "vue-router";
import { extractTextFromHtml } from "@/utils/parseHtml";

const router = useRouter();
const newsList = ref([]);
const newsLetterList = ref([]);

function fetchNews() {
    nmsNewsListPublish({
        params: {
            pageNum: 1,
            pageSize: 10,
        },
    }).then((res) => {
        newsList.value = res.data.records;
    });
}

function fetchNewsLetter() {
    nmsNewsListBrief({
        params: {
            pageNum: 1,
            pageSize: 10,
        },
    }).then((res) => {
        newsLetterList.value = res.data.records;
    });
}

function goNewsDetail(id: number) {
    router.push({
        path: "/newsDetail",
        query: { id },
    });
}

fetchNews();
fetchNewsLetter();
</script>

<style scoped lang="scss">
.wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding-top: 59px;
    min-height: 450px;
}

.news-card {
    border: 1px solid #d1dfe9;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    background: #ffffff;

    .card-header {
        background: linear-gradient(360deg, #fafcff 0%, #d7e1f4 100%);
        border-bottom: 8px solid #8ba0c9;
        padding: 12px 24px 20px 24px;

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .title-section {
                display: flex;
                align-items: center;
                gap: 14px;

                .title-text {
                    color: #23346d;
                    font-size: 20px;
                    font-weight: 700;
                    line-height: 26px;
                    letter-spacing: 2px;
                }
            }

            .expand-icon {
                display: flex;
                align-items: center;
                cursor: pointer;
            }
        }
    }

    .card-content {
        background: #ffffff;
    }
}

.brief-content {
    .brief-item {
        padding: 16px 40px;
        border-bottom: 1px solid #d2daeb;
        color: #23346d;
        font-size: 16px;
        line-height: 21px;
        letter-spacing: 1.4px;

        &:last-child {
            border-bottom: none;
        }
    }
}

.news-content {
    .news-item {
        padding: 24px 40px;
        border-bottom: 1px solid #d2daeb;

        &:last-child {
            border-bottom: none;
        }

        .news-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;

            .news-title {
                color: #23346d;
                font-size: 16px;
                font-weight: 500;
                line-height: 21px;
                letter-spacing: 1.4px;
                flex: 1;
                margin-right: 20px;
            }

            .news-date {
                color: #9da5b7;
                font-size: 14px;
                line-height: 21px;
                letter-spacing: 1.4px;
                white-space: nowrap;
            }
        }

        .news-content-text {
            color: #6c7cb2;
            font-size: 14px;
            line-height: 18px;
            letter-spacing: 1.4px;
            margin-bottom: 20px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 3;
            line-clamp: 3;
        }

        .news-actions {
            display: flex;
            align-items: center;

            .detail-btn {
                background: #1677ff;
                border: 1px solid rgba(0, 0, 0, 0);
                border-radius: 6px;
                color: #ffffff;
                font-size: 14px;
                line-height: 21px;
                padding: 5.5px 24.5px;
                box-shadow: 0px 2px 0px 0px rgba(5, 145, 255, 0.1);

                &:hover {
                    background: #4096ff;
                }
            }
        }
    }
}

// 覆盖Element Plus按钮样式
:deep(.el-button) {
    &.detail-btn {
        border: none !important;

        &:hover {
            background: #4096ff !important;
            border-color: transparent !important;
        }

        &:focus {
            background: #1677ff !important;
            border-color: transparent !important;
        }
    }
}

// 覆盖滚动条样式
:deep(.el-scrollbar__bar) {
    &.is-vertical .el-scrollbar__thumb {
        background-color: #d1dfe9;
        border-radius: 4px;
    }
}

:deep(.el-scrollbar__view) {
    height: 100%;
}
</style>
