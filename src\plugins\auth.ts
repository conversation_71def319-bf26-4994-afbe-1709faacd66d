import useUserStore from '@/store/modules/user'
import { PermissionEnum, PermissionIds } from '@/enums/roles/authorCards'

interface Permission {
  id: number;
  name: string;
  sort: number;
  description: null | string;
}

function authPermission(permission: PermissionEnum) {
  const allPermission = PermissionEnum.ALL;
  const permissions = useUserStore().permissions as Permission[]
  if (permission) {
    // 检查是否有全部权限
    if (permissions.some(p => p.name === allPermission)) {
      return true
    }
    // 通过权限名称匹配
    return permissions.some(p => p.name === permission)
  }
  return false
}

function authRole(role: string) {
  const superAdmin = "admin";
  const roles = useUserStore().roles
  if (role && role.length > 0) {
    return roles.some(v => {
      return superAdmin === v || v === role
    })
  }
  return false
}

export default {
  // 验证用户是否具备某权限
  hasPermi(permission: PermissionEnum) {
    return authPermission(permission);
  },
  // 验证用户是否含有指定权限，只需包含其中一个
  hasPermiOr(permissions: PermissionEnum[]) {
    return permissions.some(item => {
      return authPermission(item)
    })
  },
  // 验证用户是否含有指定权限，必须全部拥有
  hasPermiAnd(permissions: PermissionEnum[]) {
    return permissions.every(item => {
      return authPermission(item)
    })
  },
  // 验证用户是否具备某角色
  hasRole(role: string) {
    return authRole(role);
  },
  // 验证用户是否含有指定角色，只需包含其中一个
  hasRoleOr(roles: string[]) {
    return roles.some(item => {
      return authRole(item)
    })
  },
  // 验证用户是否含有指定角色，必须全部拥有
  hasRoleAnd(roles: string[]) {
    return roles.every(item => {
      return authRole(item)
    })
  }
}
