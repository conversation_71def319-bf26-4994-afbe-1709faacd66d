<template>
    <div v-if="selectedItem" v-loading="loading" class="detail-content">
        <div class="right-scroll" v-loading="detailLoading">
            <div class="header-bar">
                <div class="text">
                    {{ detailAndScore.templateName }}
                    <el-button
                        @click="showPerformanceRules(selectedItem.categoryTemplateId)"
                        type="primary"
                        color="#1677FF"
                        >查看已关联绩效规则</el-button
                    >
                </div>

                <div>
                    <!-- 判断是否是主审核人 -->
                    <el-button v-if="selectedItem.isPrincipal === 1" @click="assignDialogVisible = true" color="#1677FF"
                        >分发给其他审核人</el-button
                    >
                    <el-button v-if="selectedItem.isPrincipal === 0" @click="submitNotAssign" color="#1677FF"
                        >此表不归我审核</el-button
                    >
                </div>
            </div>

            <div class="right-content">
                <div class="repeat-info" v-if="selectedItem.isRepeat">
                    <el-icon class="warning-icon">
                        <WarningFilled />
                    </el-icon>
                    <div class="repeat-text">
                        当前待审核的数据与数据库中已存在的记录高度重复，可点击
                        <span
                            class="detail-link"
                            @click="
                                $router.push({ path: '/dataOverview/dataBase', query: { rowId: selectedItem.id } })
                            ">
                            查看详情
                        </span>
                    </div>
                </div>
                <div class="section-title">
                    <span>录入信息</span>
                </div>
                <div class="fields-container">
                    <div v-for="v in selectedItem.fieldsList" :key="v.id" class="field-card">
                        <div class="field-header" :class="{ star: v.isRequired === 1 }">
                            <div class="field-header-content">
                                <!-- 字段名称 -->
                                <div class="field-name">{{ v.value }}</div>
                                <!-- 特殊标记图标 -->
                                <div v-if="v.categoryPublicType" class="flag-icon"></div>
                                <!-- 信息图标 -->
                                <div v-if="v.description" class="info-icon">
                                    <a-tooltip placement="right" color="#fff">
                                        <template #title>
                                            <div
                                                style="
                                                    color: #000;
                                                    font-size: 12px;
                                                    padding: 5px;
                                                    letter-spacing: 0.1rem;
                                                ">
                                                {{ v.description }}
                                            </div>
                                        </template>
                                        <div class="info-circle"></div>
                                    </a-tooltip>
                                </div>
                            </div>
                        </div>
                        <div class="field-content">
                            <div class="inputs">
                                <div
                                    class="box"
                                    v-if="
                                        v.type === categoryTemplateValueType_NEW.STRING ||
                                        v.type === categoryTemplateValueType_NEW.INTEGER ||
                                        v.type === categoryTemplateValueType_NEW.DOUBLE ||
                                        v.type === categoryTemplateValueType_NEW.PROJECT_NAME ||
                                        v.type === categoryTemplateValueType_NEW.STUDENT
                                    ">
                                    <el-input
                                        style="width: 25vw"
                                        disabled
                                        placeholder="用户未填写此项"
                                        :model-value="fieldValueMap[v.id]?.fieldValue?.value" />
                                </div>
                                <div
                                    class="box"
                                    v-if="
                                        v.type === categoryTemplateValueType_NEW.DATE ||
                                        v.type === categoryTemplateValueType_NEW.PROJECT_DATE
                                    ">
                                    <el-date-picker
                                        disabled
                                        :model-value="fieldValueMap[v.id]?.fieldValue?.value"
                                        type="date"
                                        placeholder="用户未填写此项" />
                                </div>
                                <div v-if="v.type === categoryTemplateValueType_NEW.FILE">
                                    <el-upload :on-preview="handlePreview" disabled :file-list="getFileList(v.id)">
                                        <template #tip>
                                            <div
                                                v-if="!fieldValueMap[v.id]?.fieldValue?.value"
                                                style="font-size: 14px; color: #8a8886">
                                                用户未填写此项
                                            </div>

                                            <div v-else class="description">
                                                {{
                                                    isImageField(fieldValueMap[v.id]?.fieldValue?.value)
                                                        ? "图片列表 ( 点击图片可全屏预览，点击列表可下载图片 )"
                                                        : "文件列表 ( 点击文件列表即可下载文件 )"
                                                }}
                                            </div>
                                            <div v-if="isImageField(fieldValueMap[v.id]?.fieldValue?.value)">
                                                <el-image
                                                    v-for="(file, index) in getPreviewFileList(
                                                        fieldValueMap[v.id]?.fieldValue?.value,
                                                    )"
                                                    hide-on-click-modal
                                                    :key="file"
                                                    style="width: 50px; height: 50px; margin-right: 20px"
                                                    :src="file"
                                                    :zoom-rate="1.2"
                                                    :max-scale="7"
                                                    :min-scale="0.2"
                                                    :preview-src-list="
                                                        getPreviewFileList(fieldValueMap[v.id]?.fieldValue?.value)
                                                    "
                                                    :initial-index="index"
                                                    fit="cover" />
                                            </div>
                                        </template>
                                    </el-upload>
                                </div>
                                <div class="box" v-if="v.type === categoryTemplateValueType_NEW.PERSON_SINGLE">
                                    <el-input
                                        disabled
                                        placeholder="用户未填写此项"
                                        :model-value="getPersonName(fieldValueMap[v.id]?.fieldValue?.value)" />
                                    <div style="color: #23346d; margin-left: 30px; width: 120px">是否本院</div>
                                    <el-switch
                                        :model-value="isInternalPerson(fieldValueMap[v.id]?.fieldValue?.value)"
                                        disabled />
                                </div>
                                <div
                                    class="multi-person"
                                    v-if="
                                        v.type === categoryTemplateValueType_NEW.PERSON_MULTI ||
                                        v.type === categoryTemplateValueType_NEW.PROJECT_PARTICIPATE ||
                                        v.type === categoryTemplateValueType_NEW.PROJECT_MAIN
                                    ">
                                    <div v-for="(item, index) in fieldValueMap[v.id]?.fieldValue?.value.split(',')">
                                        <el-input
                                            disabled
                                            placeholder="用户未填写此项"
                                            :model-value="getPersonName(item)" />
                                        <div
                                            style="
                                                color: #23346d;
                                                margin-left: 30px;
                                                width: 120px;
                                                display: flex;
                                                align-items: center;
                                            ">
                                            是否本院
                                        </div>
                                        <el-switch :model-value="isInternalPerson(item)" disabled />
                                    </div>
                                </div>
                                <div
                                    class="box"
                                    v-if="
                                        v.type === categoryTemplateValueType_NEW.ENUM_SINGLE ||
                                        v.type === categoryTemplateValueType_NEW.LEVEL ||
                                        v.type === categoryTemplateValueType_NEW.PROJECT_STATUS
                                    ">
                                    <el-select
                                        disabled
                                        :model-value="
                                            getSingleSelectLabel(v.dictList, fieldValueMap[v.id]?.fieldValue?.value)
                                        "
                                        style="width: 300px"
                                        placeholder="用户未填写此项">
                                        <el-option
                                            v-for="item in v.dictList"
                                            :key="item.id"
                                            :label="item.name"
                                            :value="item.name" />
                                    </el-select>
                                </div>
                                <div class="box" v-if="v.type === categoryTemplateValueType_NEW.ENUM_MULTI">
                                    <el-select
                                        multiple
                                        disabled
                                        :model-value="
                                            getMultiSelectLabels(v.dictList, fieldValueMap[v.id]?.fieldValue?.value)
                                        "
                                        style="width: 300px"
                                        placeholder="用户未填写此项">
                                        <el-option
                                            v-for="item in v.dictList"
                                            :key="item.id"
                                            :label="item.name"
                                            :value="item.name" />
                                    </el-select>
                                </div>
                                <div class="box" v-if="v.type === categoryTemplateValueType_NEW.ISN">
                                    <el-radio-group
                                        disabled
                                        :model-value="
                                            fieldValueMap[v.id]?.fieldValue?.value === ''
                                                ? '-1'
                                                : Number(fieldValueMap[v.id]?.fieldValue?.value)
                                        ">
                                        <el-radio :value="1" size="large">是</el-radio>
                                        <el-radio :value="0" size="large">否</el-radio>
                                    </el-radio-group>
                                    <div
                                        v-if="!fieldValueMap[v.id]?.fieldValue?.value"
                                        style="font-size: 14px; color: #8a8886">
                                        用户未填写此项
                                    </div>
                                </div>
                                <div class="box" v-if="v.type === categoryTemplateValueType_NEW.MONEY">
                                    <el-input
                                        v-if="fieldValueMap[v.id]?.fieldValue?.value"
                                        disabled
                                        :model-value="fieldValueMap[v.id]?.fieldValue?.value"
                                        :formatter="(value) => `￥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                        :parser="(value) => value.replace(/\￥\s?|(,*)/g, '')" />
                                    <el-input v-else disabled placeholder="用户未填写此项" />
                                </div>
                                <div v-if="v.type === categoryTemplateValueType_NEW.DEPENDENT_MAJOR">
                                    <el-select
                                        multiple
                                        :model-value="
                                            getMultiSelectLabels(
                                                allDependentMajors,
                                                fieldValueMap[v.id]?.fieldValue?.value,
                                            )
                                        "
                                        disabled
                                        style="width: 100%; min-width: 300px"
                                        placeholder="用户未填写此项">
                                        <el-option
                                            v-for="item in allDependentMajors"
                                            :key="item.id"
                                            :label="item.name"
                                            :value="item.id" />
                                    </el-select>
                                </div>
                            </div>
                            <div>
                                <el-input
                                    v-model="errorDescription.find((item) => Number(item.fieldId) === v.id).description"
                                    type="textarea"
                                    class="approval-textarea"
                                    :autosize="{ minRows: 3, maxRows: 6 }"
                                    placeholder="如有错误请批注"></el-input>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="section-title">
                    <span>绩效评分</span>
                </div>
                <div class="performance-score" v-if="performanceRulesList.length > 0">
                    <PerformanceScore :detail-and-score="detailAndScore" v-model:reAssignScore="reAssignScore" />
                </div>
                <div style="display: flex; justify-content: center; margin-top: 20px">
                    <el-button @click="submitReject">不予通过</el-button>
                    <el-button color="#1677FF" style="color: #fff" @click="submitPass">审核通过</el-button>
                </div>
            </div>
        </div>
    </div>

    <div v-else class="detail-content">
        <el-empty description="请从左侧选择待审核录入查看详情。" />
    </div>

    <!-- =========================== 分发审核弹窗 =========================== -->
    <el-dialog v-model="assignDialogVisible" title="选择人员" width="500">
        <div>
            <!-- 下拉框 -->
            <el-select filterable v-model="assignValue.checkId" placeholder="请选择人员">
                <el-option v-for="item in assignOptions" :key="item.checkId" :label="item.label" :value="item.value" />
            </el-select>
        </div>
        <template #footer>
            <div>
                <el-button @click="assignDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitAssign">确定</el-button>
            </div>
        </template>
    </el-dialog>
    <!-- =========================== 分发审核弹窗 =========================== -->

    <!-- =========================== 图片预览弹窗 =========================== -->
    <el-dialog v-model="picturePreviewdialogVisible">
        <img w-full :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
    <!-- =========================== 图片预览弹窗 =========================== -->

    <!-- =========================== 查看已关联绩效规则弹窗 =========================== -->
    <PerformanceRulesDialog
        v-model="performanceRulesDialogVisible"
        :performance-rules-list="performanceRulesList"
        :show-change-rules-button="false" />
    <!-- =========================== 查看已关联绩效规则弹窗 =========================== -->
</template>

<script setup lang="ts">
import { tagScoringMethod } from "@/enums/tag/tagConfig";
import { categoryTemplateValueType_NEW } from "@/enums/categoryTemplate/categoryTemplateValueType";
import { Action, ElMessage, ElMessageBox, UploadUserFile } from "element-plus";
import { WarningFilled } from "@element-plus/icons-vue";
import { PermissionEnum, PermissionIds } from "@/enums/roles/authorCards";
import useUserStore from "@/store/modules/user";
import { cmsCheckReject, cmsCheckSend, cmsCheckSendBack, cmsCheckAgree, cmsCheckRead } from "@/apis/cmsCheckController";
import { umsPersonAuditList } from "@/apis/umsPersonController";
import { CmsTagDetailAndScoreDto, CmsCategoryRow0, CmsCategoryTemplateValue10 } from "@/apis/types";
import { filesBaseUrl } from "@/utils/filesBaseUrl";
import { cmsTagConfigGetDetailAndScoreId } from "@/apis/cmsTagConfigController";
import { cmsTagCategoryEntityGetTagTreeListByEntityId } from "@/apis/cmsTagCategoryEntityController";
import PerformanceScore from "@/components/PerformanceScore/index.vue";
import { useRouter } from "vue-router";
import { usePersons } from "@/hooks/usePersons";
import { useDependentMajors } from "@/hooks/useDependentMajors";
import { useCustomDialog } from "@/components/CustomDialog/useCustomDialog";
import PerformanceRulesDialog from "@/components/PerformanceRulesDialog/index.vue";

const customDialog = useCustomDialog();

const router = useRouter();

const loading = ref(false);

// 平台计算得分折叠面板
const activeNames = ref(["1"]);

// 图片预览弹窗
const picturePreviewdialogVisible = ref(false);

// 所有人员列表
const { allPersonListWithDisabled } = usePersons(true);

// 所有依托专业列表
const { allDependentMajors } = useDependentMajors(true);

// 图片预览弹窗的图片url
const dialogImageUrl = ref("");

// 分发给其他审核人弹窗
const assignDialogVisible = ref(false);

// 选中的项目
const { selectedItem, detailLoading } = defineProps<{
    selectedItem: CmsCategoryRow0 & {
        isPrincipal?: number;
        fieldsList?: CmsCategoryTemplateValue10[];
        isRepeat?: boolean;
        isRead?: number;
    };
    detailLoading: boolean;
}>();

// 绩效规则弹窗
const performanceRulesDialogVisible = ref(false);

// 绩效规则列表
const performanceRulesList = ref([]);

// 手动重新赋分分值
const reAssignScore = ref<number | null>(null);

// 错误批注
interface ErrorDescription {
    description: string;
    fieldId: number;
}

// 错误批注数组
const errorDescription = ref<ErrorDescription[]>([]);

// 父组件事件，刷新approvalList
const emit = defineEmits(["refresh"]);

// 分发给其他审核人弹窗数据接口
interface AssignValue {
    checkId: number;
    rowId: number;
    label: string;
    value: number;
}

// 分发给其他审核人弹窗数据
const assignValue = ref<AssignValue>({
    checkId: null,
    rowId: null,
    label: "",
    value: 0,
});

// 审核人员下拉框数据
const assignOptions = ref<AssignValue[]>([]);

// 获取审核人员下拉框数据
function getAssignOptions() {
    // 获取拥有审核权限的人员
    umsPersonAuditList({ params: { cards: [PermissionIds[PermissionEnum.CHECK_PERMISSION]] } }).then((res) => {
        const { userId } = useUserStore();
        // 生成el-options,格式为[{label: '', value: ''},...],且排除当前用户
        assignOptions.value = res.data
            .filter((item) => item.id !== Number(userId))
            .map((item) => ({
                checkId: item.id,
                rowId: assignValue.value.rowId,
                label: item.employeeName,
                value: item.id,
            }));
    });
}

// 提交分发审核
function submitAssign() {
    cmsCheckSend({
        body: {
            checkId: assignValue.value.checkId,
            rowId: assignValue.value.rowId,
        },
    }).then((res: any) => {
        console.log(res, "提交分发审核");
        if (res.code === 200) {
            ElMessage.success("分发审核成功");
            assignValue.value.checkId = null;
            assignDialogVisible.value = false;
            // 向父组件发送事件，来刷新approvalList
            emit("refresh");
        }
    });
}

// 不归自己审核
function submitNotAssign() {
    customDialog
        .confirm({
            title: "确认此表不在您的审核范围内吗？",
            message: "点击“确定”将此表返回负责人处，负责人将重新为该表分配审核人。",
            alignCenter: true,
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            icon: null,
        })
        .then((action: Action) => {
            if (action === "confirm") {
                cmsCheckSendBack({
                    body: {
                        rowId: assignValue.value.rowId,
                    },
                }).then((res: any) => {
                    if (res.code === 200) {
                        ElMessage.success("退回审核成功");
                        // 向父组件发送事件，来刷新approvalList
                        emit("refresh");
                    }
                });
            }
        });
}

// 驳回审核
function submitReject() {
    // 至少要填写一条错误批注
    if (
        !errorDescription.value.some((item) => {
            return item.description && item.description.trim() !== "";
        })
    ) {
        customDialog.warning({
            title: "请添加批注",
            message: "如确定审核不予通过，请至少填写一条批注，以便提交人进行相应修改。",
            alignCenter: true,
            confirmButtonText: "确定",
            icon: null,
            showCancelButton: false,
        });
        return;
    }
    // 弹窗确认是否驳回审核，confirmButton应该为红色danger类型button
    customDialog
        .confirm({
            title: "确认不予通过吗？",
            message: "此表将返回填表人处，填表人需要根据您的批注修改信息并重新提交。",
            alignCenter: true,
            confirmButtonText: "不予通过",
            cancelButtonText: "取消",
            icon: null,
        })
        .then((action: Action) => {
            if (action === "confirm") {
                cmsCheckReject({
                    body: {
                        rowId: selectedItem.id,
                        description: JSON.stringify(errorDescription.value),
                    },
                }).then((res: any) => {
                    if (res.code === 200) {
                        customDialog.info({
                            title: "审核未通过",
                            message: "当前审核未通过，提交人将收到通知。",
                            alignCenter: true,
                            showCancelButton: false,
                        });
                        // 向父组件发送事件，来刷新approvalList
                        emit("refresh");
                    }
                });
            }
        });
}

// 审核通过
function submitPass() {
    if (
        detailAndScore.value.scoreMethod == tagScoringMethod.MANUAL &&
        (!reAssignScore.value || reAssignScore.value <= 0)
    ) {
        ElMessage.error("请输入正确的分数！");
        return;
    }
    customDialog
        .confirm({
            title: "确认通过审核吗？",
            message: "请确保已仔细核对该表的全部内容。",
            confirmButtonText: "审核通过",
            cancelButtonText: "取消",
            icon: null,
            alignCenter: true,
        })
        .then((action: Action) => {
            if (action === "confirm") {
                cmsCheckAgree({
                    body: {
                        id: selectedItem.id,
                        score: reAssignScore.value,
                    },
                }).then((res: any) => {
                    if (res.code === 200) {
                        customDialog.success({
                            title: "审核已通过",
                            message: "当前审核已通过，提交人将收到通知。",
                            alignCenter: true,
                        });
                        emit("refresh");
                    }
                });
            }
        });
}

/**
 * 根据 fieldsList 和 values 生成映射表
 */
const createFieldValueMap = (fieldsList: any[], values: any[]) => {
    return fieldsList.reduce(
        (acc, field) => {
            const matchedValue = values.find((v) => v.categoryTemplateValueId === field.id);
            acc[field.id] = {
                fieldProperties: field,
                fieldValue: matchedValue ? matchedValue : null,
            };
            return acc;
        },
        {} as Record<string, any>,
    );
};

// 用户上传的文件列表映射表
const userFilesMap = ref<Map<number, UploadUserFile[]>>(new Map());

// 获取预览图片urls
const getPreviewFileList = (urls: string) => {
    const fileUrls = urls.split(",");
    return fileUrls.map((url) => `${filesBaseUrl}${url}`);
};

// 修改createUserFilesList函数
function createUserFilesList(fieldValueMap: any) {
    // 清空文件列表映射表
    userFilesMap.value.clear();

    Object.entries(fieldValueMap).forEach(([key, value]: [any, any]) => {
        if (value.fieldProperties.type === categoryTemplateValueType_NEW.FILE && value.fieldValue?.value) {
            const fieldId = Number(key);
            const fileList: UploadUserFile[] = [];

            const fileUrls = value.fieldValue.value.split(",");
            fileUrls.forEach((fileUrl) => {
                // 获取文件名（取路径最后一部分）
                const fileName = fileUrl.split("/").pop();
                // 从文件名中提取实际名称（去掉UUID前缀）
                const actualFileName = fileName.substring(32);
                fileList.push({
                    name: actualFileName,
                    url: `${filesBaseUrl}${fileUrl}`,
                });
            });

            // 将该数据项的文件列表存入映射表
            userFilesMap.value.set(fieldId, fileList);
        }
    });
}

// 点击文件列表事件
const handlePreview = (file) => {
    // 创建一个下载链接
    const link = document.createElement("a");
    link.download = file.name;
    // 判断是否为图片类型
    if (isImageField(file.url)) {
        // 如果是图片类型，创建 Blob URL
        fetch(file.url)
            .then((response) => response.blob())
            .then((blob) => {
                const blobUrl = window.URL.createObjectURL(blob);
                link.href = blobUrl;
                link.click();
                window.URL.revokeObjectURL(blobUrl); // 释放内存
            })
            .catch((error) => {
                console.error("图片下载失败：", error);
            });
    } else {
        // 如果是普通文件，直接使用链接地址
        link.href = file.url;
        link.click();
    }
};
// 根据选中的项目生成映射表
const fieldValueMap = computed(() => createFieldValueMap(selectedItem.fieldsList, selectedItem.values));

// 监听页面事件
watch(
    () => selectedItem,
    (newVal) => {
        if (newVal) {
            if (newVal.isRead === 0) {
                customDialog
                    .warning({
                        message: "当前待审核的数据与数据库中已存在的记录高度重复。点击“查看详情”进行后续操作",
                        alignCenter: true,
                        confirmButtonText: "查看详情",
                        showCancelButton: false,
                        iconColor: "#FAAD14",
                        title: "数据重复",
                    })
                    .then((action: Action) => {
                        if (action === "confirm") {
                            cmsCheckRead({ params: { rowId: newVal.id } }).then((res: any) => {
                                if (res.code === 200) {
                                    router.push(`/dataOverview/dataBase?rowId=${newVal.id}`);
                                }
                            });
                        }
                    });
            }

            activeNames.value = ["0"];
            assignValue.value.rowId = newVal.id;
            errorDescription.value = [];
            Object.entries(fieldValueMap.value).forEach(([key, value]: [any, any]) => {
                errorDescription.value.push({
                    description: "",
                    fieldId: key,
                });
            });
            createUserFilesList(fieldValueMap.value);
            getDetailAndScore(selectedItem.id);
            // 获取绩效规则
            cmsTagCategoryEntityGetTagTreeListByEntityId({
                params: { categoryEntityId: newVal.categoryTemplateId },
            }).then((res) => {
                performanceRulesList.value = res.data;
            });
            // 重置 reAssignScore
            reAssignScore.value = null;
        }
    },
);

onMounted(async () => {
    getAssignOptions();
});

// 获取多选下拉框的label
const getMultiSelectLabels = (dictList: any[], value: string) => {
    if (!value) return [];
    const ids = value.split(",");
    return ids.map((id) => {
        const item = dictList.find((dict) => dict.id.toString() === id);
        // 为依托专业情况设置特殊返回值
        if (item.hasOwnProperty("organizationName")) {
            return item.organizationName;
        } else {
            return item ? item.name : "";
        }
    });
};

// 获取单选下拉框的label
const getSingleSelectLabel = (dictList: any[], value: string) => {
    if (!value) return "";
    const item = dictList.find((dict) => dict.id.toString() === value);
    return item ? item.name : "";
};

// 获取人员姓名
const getPersonName = (value: string) => {
    if (!value) return "未填写";

    // 判断是否为数字（院内人员ID）
    const isId = /^\d+$/.test(value);

    if (isId) {
        // 在allPersonListWithDisabled中查找对应的人员
        const person = allPersonListWithDisabled.value.find((p) => p.id === Number(value));
        return person ? person.employeeName : value;
    }

    // 如果不是ID，则直接返回（院外人员）
    return value;
};

// 判断是否为院内人员
const isInternalPerson = (value: string) => {
    if (!value) return false;
    return /^\d+$/.test(value);
};

// 判断是否为图片数据项
const isImageField = (value: string) => {
    if (!value) return false;
    const fileUrls = value.split(",");
    const imageExtensions = [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp"];
    return fileUrls.every((url) => {
        const lowerUrl = url.toLowerCase();
        return imageExtensions.some((ext) => lowerUrl.endsWith(ext));
    });
};

// 配置信息和得分信息
const detailAndScore = ref<CmsTagDetailAndScoreDto>({});

// 获取配置信息以及得分信息
const getDetailAndScore = (rowId: number) => {
    cmsTagConfigGetDetailAndScoreId({ params: { id: rowId } }).then((res) => {
        detailAndScore.value = res.data;
    });
};

// 获取指定数据项的文件列表
const getFileList = (fieldId: number) => {
    return userFilesMap.value.get(fieldId) || [];
};

// 修改监听 detailAndScore 的 watch
watch(
    () => detailAndScore.value,
    (newVal) => {
        if (newVal && newVal.totalScore) {
            // 只在手动评分模式下初始化 reAssignScore
            if (newVal.scoreMethod === tagScoringMethod.MANUAL) {
                reAssignScore.value = newVal.totalScore;
            }
        }
    },
    { immediate: true },
);

// 显示绩效规则弹窗.value
function showPerformanceRules(templateId: number) {
    cmsTagCategoryEntityGetTagTreeListByEntityId({ params: { categoryEntityId: templateId } }).then((res) => {
        performanceRulesList.value = res.data;
        performanceRulesDialogVisible.value = true;
    });
}
</script>

<style scoped lang="scss">
@use "../../../../assets/styles/mixins.scss" as mix;
@use "../../../../assets/styles/templateFieldsDetail.scss";
$padding: 0 30px;

// 每行最低高度
$min-height: 70px;

// 禁用textarea拖拽缩放功能
:deep(.approval-textarea .el-textarea__inner) {
    resize: none !important;
    max-height: 80px !important;
    width: 320px !important;
}

.star {
    &::before {
        content: "*";
        color: #ff8061;
        font-size: 14px;
        margin-right: 5px;
    }
}

.reassign-input-wrapper {
    display: flex;
    align-items: center;
    .reassign-input-suffix {
        border-right: 1px solid #dcdfe6;
        border-top: 1px solid #dcdfe6;
        border-bottom: 1px solid #dcdfe6;
        height: 30px;
        line-height: 30px;
        padding: 0 8px;
        background-color: #e2e7f2;
    }
}

:deep(.reassign-input) {
    .el-input__wrapper {
        width: 20px !important;
        border-radius: 0 !important;
    }
}

.multi-person {
    display: flex;
    flex-direction: column;
    div {
        display: flex;
        margin: 5px 0;
    }
}

.description {
    font-size: 14px;
    color: #999;
    margin-bottom: 10px;
}

.detail-content {
    overflow: hidden;
    .right-scroll {
        .right-content {
            // height: calc(100vh - 350px);
            padding: 16px 16px 24px 16px;
        }
    }
}

.repeat-info {
    margin: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px;
    background: #fff7e6;
    border: 1px solid #ffd591;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    min-height: 20px;

    .warning-icon {
        color: #faad14;
        font-size: 16px;
        flex-shrink: 0;
        margin-top: 2px;
        margin-bottom: 2px;
    }

    .repeat-text {
        color: #d46b08;
        flex: 1;
    }

    .detail-link {
        color: #1677ff;
        text-decoration: underline;
        cursor: pointer;
        flex-shrink: 0;

        &:hover {
            color: #4096ff;
        }
    }
}
</style>
