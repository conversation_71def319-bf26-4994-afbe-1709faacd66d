import { ref, computed } from "vue";
import type { TableColumnCtx } from "element-plus";
import { gradeOptionsMap } from "@/enums/options/gradeOptions";
import { getFieldName, getPersonName } from "@/utils/getNames";
import { cmsCategoryTemplateValueGetAllDropdownValues } from "@/apis/cmsCategoryTemplateValueController";

/**
 * 创建项目状态筛选列表
 */
export function useProjectStatusFilter(dictList: any) {
    // 创建项目状态筛选列表
    // const projectStatusList = ref([
    //     { text: "申报", value: ProjectStatus.DECLARE },
    //     { text: "立项", value: ProjectStatus.APPROVAL },
    //     { text: "结项", value: ProjectStatus.END_ITEM },
    // ]);

    const projectStatusList = ref<any[]>([]);

    const getProjectStatusList = async () => {
        // 查询指定数据项的所有下拉值 项目状态：4，级别：5
        cmsCategoryTemplateValueGetAllDropdownValues({
            params: {
                type: 4,
            },
        }).then((res) => {
            projectStatusList.value = res.data.map((item: any, index: number) => ({
                text: item,
                value: index,
            }));
        });
    };

    // 筛选项目状态
    const projectStatusFilterHandler = (value: string | number, row: any, column: TableColumnCtx<any>) => {
        const targetFieldName = projectStatusList.value.find((item) => Number(item.value) === Number(value))?.text;

        const property = column["property"];
        // 获取行数据中的实际字典ID
        const rowValue = row[property];
        if (!rowValue) return false;

        // 使用字典列表获取实际显示文本
        const rowValueText = getFieldName(dictList.value, rowValue);

        // 比较两个文本是否相同
        return rowValueText === targetFieldName;
    };

    // 根据ID获取项目状态描述
    const getStatusDescriptionById = (id: string | number): string => {
        return getFieldName(dictList.value, String(id));
    };

    onMounted(() => {
        getProjectStatusList();
    });

    return {
        projectStatusList,
        projectStatusFilterHandler,
        getStatusDescriptionById,
    };
}
/**
 * 创建级别筛选功能
 */
export function useGradeFilter(dictList: any) {
    // 创建级别筛选列表
    const gradeFilterList = ref(
        Object.entries(gradeOptionsMap).map(([value, text]) => ({
            text,
            value: Number(value),
        }))
    );

    // 筛选级别
    const gradeFilterHandler = (value: string | number, row: any, column: TableColumnCtx<any>) => {
        const property = column["property"];
        // 获取行数据中的实际字典ID
        const rowValue = row[property];
        if (!rowValue) return false;

        // 根据选中的筛选值查找对应的文本
        const selectedFilterText = gradeFilterList.value.find((item) => Number(item.value) === Number(value))?.text;

        // 使用字典列表获取实际显示文本
        const rowValueText = getFieldName(dictList.value, rowValue);

        // 比较两个文本是否相同
        return rowValueText === selectedFilterText;
    };

    return {
        gradeFilterList,
        gradeFilterHandler,
    };
}

/**
 * 创建人员筛选功能
 */
export function usePersonFilter(tableData: any, allPersonListWithDisabled: any) {
    // 创建人员筛选列表
    const personFilterList = computed(() => {
        // 从表格数据中收集所有唯一的owner值
        const uniqueOwners = new Set<string>();
        tableData.value.forEach((row: any) => {
            if (row.owner) {
                uniqueOwners.add(String(row.owner));
            }
        });

        // 获取普通数组形式的人员列表
        const personList = allPersonListWithDisabled.value ? Array.from(allPersonListWithDisabled.value) : [];

        // 转换为筛选列表格式
        return Array.from(uniqueOwners).map((ownerId) => ({
            text: getPersonName(personList, ownerId),
            value: ownerId,
        }));
    });

    // 筛选人员
    const personFilterHandler = (value: string, row: any, column: TableColumnCtx<any>) => {
        const property = column["property"];
        const rowValue = String(row[property]);
        return rowValue === value;
    };

    return {
        personFilterList,
        personFilterHandler,
    };
}
