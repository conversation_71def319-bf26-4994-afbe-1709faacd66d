# 动态路由重定向系统

## 概述

该系统解决了基于用户权限的动态路由重定向问题。当用户访问一个分类路由（没有对应页面的父路由）时，系统会根据用户的权限自动重定向到用户有权限访问的第一个子页面。

## 问题背景

在我们的系统中：
1. 二级路由只作为分类，没有对应的页面
2. 用户权限不同，能访问的子页面也不同
3. 静态重定向会导致用户访问无权限页面时出现404错误

## 解决方案

### 核心文件

- `src/utils/routeRedirect.ts` - 动态重定向逻辑
- `src/router/index.js` - 路由配置
- `src/store/modules/user.ts` - 用户权限存储

### 工作原理

1. **权限映射**: 在 `ROUTE_GROUPS` 配置中定义每个页面对应的权限ID
2. **优先级排序**: 通过 `priority` 字段定义页面的优先级（数字越小优先级越高）
3. **动态检查**: 在重定向时检查用户权限，返回第一个有权限访问的页面
4. **兜底处理**: 如果没有任何可访问页面，重定向到404页面

## 使用方法

### 1. 添加新的路由组

当你需要为新的分类路由添加动态重定向时：

```typescript
import { addRouteGroup } from '@/utils/routeRedirect';

// 添加新的路由组配置
addRouteGroup('/your/parent/path', [
  {
    path: '/your/parent/path/child1',
    permissionId: PermissionIds[PermissionEnum.YOUR_PERMISSION],
    priority: 1  // 最高优先级，默认重定向到此页面
  },
  {
    path: '/your/parent/path/child2', 
    permissionId: PermissionIds[PermissionEnum.ANOTHER_PERMISSION],
    priority: 2  // 次优先级，当child1无权限时才重定向到此页面
  }
]);
```

### 1.1 优先级配置详解

**重要概念**：优先级数字越小，优先级越高！

- `priority: 1` = 最高优先级（第一选择）
- `priority: 2` = 次优先级（第二选择）
- `priority: 3` = 第三优先级，以此类推

**工作逻辑**：
1. 系统会检查用户对所有子页面的权限
2. 过滤出用户有权限访问的页面
3. 按 `priority` 数值从小到大排序
4. 重定向到排序后的第一个页面（优先级最高的页面）

**配置示例**：
```typescript
// 团队管理路由组配置示例
'/workspace/teamManagement': [
  {
    path: '/workspace/teamManagement/currentTeam',
    permissionId: PermissionIds[PermissionEnum.ALL],
    priority: 1  // 默认跳转到"当前团队"
  },
  {
    path: '/workspace/teamManagement/beVerifiedTeam', 
    permissionId: PermissionIds[PermissionEnum.BACKEND_MANAGEMENT],
    priority: 2  // 如果用户只有后台管理权限，跳转到"待确认申请"
  }
]
```

**常见场景**：

1. **所有用户默认页面**：如果某个页面所有人都能访问，设置为 `priority: 1`
2. **管理员专属页面**：需要特殊权限的页面设置较高的数字优先级
3. **权限分层**：按用户权限等级设置不同优先级

**修改优先级的步骤**：
1. 在 `src/utils/routeRedirect.ts` 中找到对应的路由组
2. 调整 `priority` 数值（记住：数字越小优先级越高）
3. 保存文件后刷新浏览器或重新登录以清除缓存
4. 测试不同权限用户的重定向效果

### 2. 在路由配置中使用

在 `src/router/index.js` 中添加动态重定向：

```javascript
{
  path: "/your/parent/path",
  redirect: () => {
    return getDynamicRedirectPath('/your/parent/path') || '/404';
  },
}
```

### 3. 权限配置

确保在 `src/enums/roles/authorCards.ts` 中定义了对应的权限枚举和ID：

```typescript
export const enum PermissionEnum {
  YOUR_PERMISSION = "你的权限名称",
}

export const PermissionIds: Record<PermissionEnum, number> = {
  [PermissionEnum.YOUR_PERMISSION]: 权限ID,
};
```

## 当前配置的路由组

### 学院绩效统计
- 路径: `/workspace/CollegePerformanceStatistics`
- 子页面:
  - 学院绩效统计页 (优先级1, 权限ID: 8)
  - 专业绩效统计页 (优先级2, 权限ID: 9)

### 后台管理-信息管理  
- 路径: `/backgroundManagement/infoManagement`
- 子页面:
  - 账户管理 (优先级1, 权限ID: 10)
  - 组织架构 (优先级2, 权限ID: 10)
  - 权限组 (优先级3, 权限ID: 10)

### 后台管理-中央设置
- 路径: `/backgroundManagement/centralSettings`
- 子页面:
  - 表格管理 (优先级1, 权限ID: 13)
  - 新闻通讯管理 (优先级2, 权限ID: 13)
  - 绩效规则管理 (优先级3, 权限ID: 13)

### 工作台-团队管理
- 路径: `/workspace/teamManagement`
- 子页面:
  - 当前团队 (优先级1, 权限ID: 1) - **默认跳转页面，所有人可访问**
  - 待确认申请 (优先级2, 权限ID: 10) - 需要后台管理权限

### 工作台-管理员审核
- 路径: `/workspace/adminApproval`
- 子页面:
  - 待审核 (优先级1, 权限ID: 12)
  - 审核记录 (优先级2, 权限ID: 12)
  - 待重新分配 (优先级3, 权限ID: 12)

### 工作台-管理员录入
- 路径: `/workspace/adminEntry`
- 子页面:
  - 录入首页 (优先级1, 权限ID: 2)
  - 录入记录 (优先级2, 权限ID: 2)
  - 管理员待更新 (优先级3, 权限ID: 2)
  - 待录入历史 (优先级4, 权限ID: 2)

### 工作台-教师录入
- 路径: `/workspace/teacherEntry`
- 子页面:
  - 录入首页 (优先级1, 权限ID: 7)
  - 录入记录 (优先级2, 权限ID: 7)
  - 待更新 (优先级3, 权限ID: 7)
  - 待录入历史 (优先级4, 权限ID: 7)

### 工作台-新闻管理
- 路径: `/workspace/newsManagement`
- 子页面:
  - 新闻草稿 (优先级1, 权限ID: 3)
  - 待发布新闻 (优先级2, 权限ID: 3)
  - 已发布新闻 (优先级3, 权限ID: 3)

### 后台管理-权限管理
- 路径: `/backgroundManagement/permissionManagement`
- 子页面:
  - 用户列表 (优先级1, 权限ID: 10)

## 注意事项

1. **权限ID映射**: 确保 `permissionId` 与后端返回的权限ID一致
2. **优先级设置**: 
   - ⚠️ **重要**：数字越小优先级越高！`priority: 1` 是最高优先级
   - 建议从1开始递增：1, 2, 3, 4...
   - 将最常用/默认的页面设置为 `priority: 1`
3. **兜底处理**: 确保所有动态重定向都有兜底的404处理
4. **权限更新**: 当用户权限发生变化时，可能需要重新加载页面或重新导航
5. **优先级修改后**: 修改优先级配置后需要刷新浏览器或重新登录以清除路由缓存

## 调试

如果重定向不正常，可以：

1. 检查浏览器控制台的警告信息
2. 确认用户权限数据格式是否正确
3. 验证权限ID映射是否准确
4. 检查路由配置是否正确

## 扩展性

该系统设计为可扩展的：
- 可以通过 `addRouteGroup` 动态添加新的路由组
- 支持复杂的权限逻辑（通过修改 `hasPermission` 函数）
- 可以轻松调整优先级顺序
- 支持自定义兜底处理逻辑 