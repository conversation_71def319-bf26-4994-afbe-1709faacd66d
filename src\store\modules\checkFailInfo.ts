import { defineStore } from "pinia";
import { RowImportErrorMessage } from "@/apis/";
import { UploadFile } from "element-plus";
export const useCheckFailInfoStore = defineStore("checkFailInfo", {
    state: () => ({
        checkFailInfo: null as RowImportErrorMessage | null,
        checkFailFile: null as UploadFile  | null,
    }),
    actions: {
        /**
         * 设置校验失败信息
         * @param checkFailInfo 校验失败信息
         */
        setCheckFailInfo(checkFailInfo: RowImportErrorMessage) {
            this.checkFailInfo = checkFailInfo;
        },
        /**
         * 缓存校验失败的文件
         */
        setCheckFailFile(file: UploadFile) {
            this.checkFailFile = file;
        },
        /**
         * 清除校验失败信息与文件缓存
         */
        clearCheckFailInfo() {
            this.checkFailInfo = null;
            this.checkFailFile = null;
        },
    },
});
