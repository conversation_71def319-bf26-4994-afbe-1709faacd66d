/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 文件删除 POST /fms/minio/delete */
export async function minioDelete({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.minioDeleteParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/fms/minio/delete', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 文件打包成zip下载 POST /fms/minio/download/zip */
export async function minioDownloadZip({
  body,
  options,
}: {
  body: API.MultifileDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.Resource>('/fms/minio/download/zip', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 通过文件链接下载文件 GET /fms/minio/downloadByFileUrl */
export async function minioDownloadByFileUrl({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.minioDownloadByFileUrlParams;
  options?: { [key: string]: unknown };
}) {
  return request<unknown>('/fms/minio/downloadByFileUrl', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 通过图片链接下载图片 GET /fms/minio/downloadByImageUrl */
export async function minioDownloadByImageUrl({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.minioDownloadByImageUrlParams;
  options?: { [key: string]: unknown };
}) {
  return request<unknown>('/fms/minio/downloadByImageUrl', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取文件信息 POST /fms/minio/getFileInfos */
export async function minioGetFileInfos({
  body,
  options,
}: {
  body: API.MultifileDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListFileInfoDto_>('/fms/minio/getFileInfos', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 文件上传(限制大小最大100M) POST /fms/minio/upload */
export async function minioUpload({
  body,
  file,
  options,
}: {
  body: {};
  file?: File;
  options?: { [key: string]: unknown };
}) {
  const formData = new FormData();

  if (file) {
    formData.append('file', file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as { [key: string]: any })[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (item instanceof Array) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request<API.CommonResultMinioUploadDto_>('/fms/minio/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data: formData,
    ...(options || {}),
  });
}

/** 多文件上传(限制大小最大100M) POST /fms/minio/uploads */
export async function minioUploads({
  body,
  options,
}: {
  body: {
    files?: unknown[];
  };
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListMinioUploadDto_>('/fms/minio/uploads', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: body,
    ...(options || {}),
  });
}
