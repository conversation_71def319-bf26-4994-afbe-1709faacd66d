import { ref, onMounted } from "vue";
import { getTemplateGroupsList } from "@/api/cms/categoryTemplateGroups"; 
import { CmsTemplateGroups } from "@/models/cms/categoryTemplateGroup";
export function useCategoryTemplateGroupsList() {
    const groupsList = ref<CmsTemplateGroups[]>([]);

    const fetchTemplateGroupsList = async () => {
        try {
            const res = await getTemplateGroupsList();
            groupsList.value = res.data;
        } catch (error) {
            console.error("加载大类分组列表失败:", error);
        }
    };

    // 自动加载数据
    onMounted(fetchTemplateGroupsList);

    return { groupsList, fetchTemplateGroupsList };
}
