import { umsAdminInfo, umsAdmin<PERSON>ogin, umsAdmin<PERSON>ogout, Ums<PERSON><PERSON>_, UmsRole_ } from "@/apis";
import { getToken, setToken, removeToken } from "@/utils/auth";
import { defineStore } from "pinia";
import usePermissionStore from "./permission";

export interface LoginForm {
    loginType?: number;
    username: string;
    password: string;
}

const useUserStore = defineStore("user", {
    state: () => ({
        token: getToken(),
        username: "",
        userId: null,
        avatar: "",
        roles: [] as UmsRole_[],
        permissions: [] as any,
    }),
    actions: {
        // 登录
        login(userInfo: LoginForm) {
            const username = userInfo.username.trim();
            const password = userInfo.password;
            const loginType = userInfo.loginType ? userInfo.loginType : 1;
            return new Promise((resolve, reject) => {
                umsAdminLogin({
                    body: {
                        username,
                        password,
                        loginType,
                    },
                })
                    .then((res: any) => {
                        setToken(res.data.token);
                        this.token = res.data.token;
                        resolve(res.data.message);
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },
        // 获取用户信息
        getInfo() {
            return new Promise((resolve, reject) => {
                umsAdminInfo({}).then((res: any) => {
                    console.log("用户信息：", res);
                    const user: UmsPerson_ = res.data.userInfo;
                    this.username = user.employeeName;
                    this.userId = user.id;
                    this.permissions = res.data.permission;

                    if (res.data.roles && res.data.roles.length > 0) {
                        // 验证返回的roles是否是一个非空数组
                        this.roles = res.data.roles;
                    } else {
                        this.roles = [{
                            id: 0,
                            name: "ROLE_DEFAULT",
                            description: "默认角色",
                            authorityCardList: []
                        } as UmsRole_];
                    }
                    // this.avatar = avatar;
                    resolve(res);
                });
            });
        },
        // 退出系统
        logOut() {
            usePermissionStore().clearAllRoutes();
            return new Promise<void>((resolve, reject) => {
                umsAdminLogout({})
                    .then(() => {
                        this.resetStore();
                        resolve();
                    })
                    .catch((error) => {
                        // 即使请求失败，也要清理本地状态
                        this.resetStore();
                        reject(error);
                    });
            });
        },
        resetStore() {
            removeToken();
            this.permissions = [];
            this.token = null;
            this.username = null;
            this.userId = null;
            this.avatar = null;
            this.roles = [];
        },
    },
});

export default useUserStore;
