<template>
    <div class="permission-group">
        <div class="permission-group-header">
            <el-button @click="switchRoleCards" type="primary">+ 新建权限组</el-button>
        </div>

        <div class="table">
            <el-table
                height="100%"
                :header-cell-style="{ backgroundColor: '#e4ecfc', color: 'rgba(0, 0, 0, 0.88)', fontWeight: '500' }"
                :data="rolesAndCardsList"
                border
                style="height: 100%">
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="name" label="权限身份" width="180" />
                <el-table-column prop="description" label="描述" width="350" />
                <el-table-column prop="authorityCardList" label="拥有权限卡" min-width="500">
                    <template #default="scope">
                        <div class="card-list">
                            <div
                                :style="{ backgroundColor: getColor(item.name) }"
                                class="card-item"
                                :key="i"
                                v-for="(item, i) in scope.row.authorityCardList">
                                <el-tooltip
                                    @show="showToolTip"
                                    effect="customized"
                                    placement="right"
                                    :content="getCardDescription(item.name)"
                                    raw-content
                                    popper-class="cards-tooltip">
                                    <span style="display: inline-block; width: 100%">
                                        {{ item.name }}
                                    </span>
                                </el-tooltip>
                            </div>
                            <el-button
                                v-if="scope.row.authorityCardList.length === 0"
                                @click="switchShowCards(scope.row)"
                                >+</el-button
                            >
                            <div v-else @click="switchShowCards(scope.row)" class="flex-center" style="cursor: pointer">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24">
                                    <defs>
                                        <clipPath id="clipPath6113273396">
                                            <path
                                                d="M0 0L24 0L24 24L0 24L0 0Z"
                                                fill-rule="nonzero"
                                                transform="matrix(1 0 0 1 0 0)" />
                                        </clipPath>
                                    </defs>
                                    <g clip-path="url(#clipPath6113273396)">
                                        <path
                                            d="M12.0964 1.96339L10.133 0L0.417857 9.7125L0 12.0964L2.38125 11.6759L12.0964 1.96339Z"
                                            fill-rule="nonzero"
                                            transform="matrix(1 0 0 1 6.57568 4.04517)"
                                            fill="rgb(230, 244, 255)" />
                                        <path
                                            d="M20.5714 19.3929L0.857143 19.3929C0.383036 19.3929 0 19.7759 0 20.25L0 21.2143C0 21.3321 0.0964286 21.4286 0.214286 21.4286L21.2143 21.4286C21.3321 21.4286 21.4286 21.3321 21.4286 21.2143L21.4286 20.25C21.4286 19.7759 21.0455 19.3929 20.5714 19.3929ZM3.90268 17.1429C3.95625 17.1429 4.00982 17.1375 4.06339 17.1295L8.56875 16.3393C8.62232 16.3286 8.67321 16.3045 8.71071 16.2643L20.0652 4.90982C20.09 4.88504 20.1097 4.85561 20.1232 4.8232C20.1366 4.7908 20.1435 4.75606 20.1435 4.72098C20.1435 4.6859 20.1366 4.65116 20.1232 4.61876C20.1097 4.58636 20.09 4.55692 20.0652 4.53214L15.6134 0.0776786C15.5625 0.0267858 15.4955 0 15.4232 0C15.3509 0 15.2839 0.0267858 15.233 0.0776786L3.87857 11.4321C3.83839 11.4723 3.81429 11.5205 3.80357 11.5741L3.01339 16.0795C2.98734 16.223 2.99665 16.3706 3.04052 16.5097C3.08439 16.6488 3.1615 16.7751 3.26518 16.8777C3.44196 17.0491 3.66429 17.1429 3.90268 17.1429ZM5.70804 12.4714L15.4232 2.75893L17.3866 4.72232L7.67143 14.4348L5.29018 14.8554L5.70804 12.4714Z"
                                            fill-rule="nonzero"
                                            transform="matrix(1 0 0 1 1.28467 1.28613)"
                                            fill="rgb(22, 119, 255)" />
                                    </g>
                                </svg>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="160">
                    <template #default="scope">
                        <div>
                            <el-button text type="primary" @click="handleEdit(scope.row)">编辑</el-button>
                            <el-button text type="primary" @click="handleDelete(scope.row)">删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分配权限卡弹窗 -->
        <CustomDrawer
            v-model="showCardsdialogVisible"
            :title="rolesAndCardsList.find((item: any) => item.id === selectedEditRoleId)?.name || '-'"
            confirm-text="保存"
            @confirm="saveRoleCards"
            @close="showCardsdialogVisible = false">
            <template #alert>
                <div class="custom-drawer-alert-icon">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24">
                        <defs>
                            <clipPath id="clipPath4089678495">
                                <path
                                    d="M0 0L24 0L24 24L0 24L0 0Z"
                                    fill-rule="nonzero"
                                    transform="matrix(1 0 0 1 0 0)" />
                            </clipPath>
                        </defs>
                        <g clip-path="url(#clipPath4089678495)">
                            <path
                                d="M12 0C5.37321 0 0 5.37321 0 12C0 18.6268 5.37321 24 12 24C18.6268 24 24 18.6268 24 12C24 5.37321 18.6268 0 12 0ZM12 21.9643C6.49821 21.9643 2.03571 17.5018 2.03571 12C2.03571 6.49821 6.49821 2.03571 12 2.03571C17.5018 2.03571 21.9643 6.49821 21.9643 12C21.9643 17.5018 17.5018 21.9643 12 21.9643Z"
                                fill-rule="nonzero"
                                transform="matrix(1 0 0 1 -5.96046e-08 -5.96046e-08)"
                                fill="rgb(22, 119, 255)" />
                            <path
                                d="M0 1.28571C1.52259e-15 1.62671 0.135459 1.95373 0.376577 2.19485C0.617695 2.43597 0.944722 2.57143 1.28571 2.57143C1.62671 2.57143 1.95373 2.43597 2.19485 2.19485C2.43597 1.95373 2.57143 1.62671 2.57143 1.28571C2.57143 0.944722 2.43597 0.617695 2.19485 0.376577C1.95373 0.135459 1.62671 1.52259e-15 1.28571 0C0.944722 1.52259e-15 0.617695 0.135459 0.376577 0.376577C0.135459 0.617695 1.52259e-15 0.944722 0 1.28571ZM1.92857 4.28571L0.642857 4.28571C0.525 4.28571 0.428571 4.38214 0.428571 4.5L0.428571 11.7857C0.428571 11.9036 0.525 12 0.642857 12L1.92857 12C2.04643 12 2.14286 11.9036 2.14286 11.7857L2.14286 4.5C2.14286 4.38214 2.04643 4.28571 1.92857 4.28571Z"
                                fill-rule="nonzero"
                                transform="matrix(1 0 0 1 10.7143 6)"
                                fill="rgb(22, 119, 255)" />
                        </g>
                    </svg>
                </div>
                <div class="custom-drawer-alert-content">
                    <div class="custom-drawer-alert-content-title">权限卡</div>
                    <div class="custom-drawer-alert-content-description">
                        本平台通过"权限卡"控制用户访问功能与数据的范围。为某岗位分配"权限卡"即赋予该岗位用户对应的操作权限。
                    </div>
                </div>
            </template>

            <div class="permission-cards-list">
                <el-checkbox-group v-model="selectedCardIds">
                    <div v-for="(item, i) in authorityCardList" :key="i" class="permission-card-item">
                        <div class="card-row">
                            <el-checkbox :value="item.id" />
                            <div class="card-name" :style="getDynamicStyle(item)">{{ item.name }}</div>
                        </div>
                        <div class="card-description" v-html="getCardDescription(item.name)"></div>
                    </div>
                </el-checkbox-group>
            </div>
        </CustomDrawer>

        <!-- 新增/编辑岗位弹窗 -->
        <CustomDrawer
            v-model="showRoleVisible"
            :title="addAndEditRoleForm.id ? '编辑权限身份' : '新建权限身份'"
            :confirm-text="addAndEditRoleForm.id ? '保存' : '确定'"
            @confirm="submitForm(addAndEditRoleFormRef)"
            @close="() => { showRoleVisible = false; clearRoleForm(addAndEditRoleFormRef); }">
            <el-form
                ref="addAndEditRoleFormRef"
                :model="addAndEditRoleForm"
                :rules="addAndEditRolerules"
                label-position="top">
                <el-form-item prop="name">
                    <template #label>
                        <span style="display: inline-flex; align-items: center">
                            权限身份
                            <el-tooltip content="请填写该权限身份在学院内的岗位名称" placement="top">
                                <template #default>
                                    <div class="flex-center">
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            xmlns:xlink="http://www.w3.org/1999/xlink"
                                            width="16"
                                            height="16"
                                            viewBox="0 0 16 16"
                                            style="margin-left: 4px">
                                            <defs>
                                                <clipPath id="clipPath0083516960">
                                                    <path
                                                        d="M0 0L16 0L16 16L0 16L0 0Z"
                                                        fill-rule="nonzero"
                                                        transform="matrix(1 0 0 1 0 0)" />
                                                </clipPath>
                                            </defs>
                                            <g clip-path="url(#clipPath0083516960)">
                                                <path
                                                    d="M8 0C3.58214 0 0 3.58214 0 8C0 12.4179 3.58214 16 8 16C12.4179 16 16 12.4179 16 8C16 3.58214 12.4179 0 8 0ZM8.57143 11.8571C8.57143 11.9357 8.50714 12 8.42857 12L7.57143 12C7.49286 12 7.42857 11.9357 7.42857 11.8571L7.42857 7C7.42857 6.92143 7.49286 6.85714 7.57143 6.85714L8.42857 6.85714C8.50714 6.85714 8.57143 6.92143 8.57143 7L8.57143 11.8571ZM8 5.71429C7.7757 5.70971 7.56213 5.61739 7.40513 5.45714C7.24812 5.2969 7.16018 5.08149 7.16018 4.85714C7.16018 4.6328 7.24812 4.41739 7.40513 4.25714C7.56213 4.0969 7.7757 4.00458 8 4C8.2243 4.00458 8.43787 4.0969 8.59488 4.25714C8.75189 4.41739 8.83982 4.6328 8.83982 4.85714C8.83982 5.08149 8.75189 5.2969 8.59488 5.45714C8.43787 5.61739 8.2243 5.70971 8 5.71429Z"
                                                    fill-rule="nonzero"
                                                    transform="matrix(1 0 0 1 0 0)"
                                                    fill="rgb(0, 0, 0)"
                                                    fill-opacity="0.25" />
                                            </g>
                                        </svg>
                                    </div>
                                </template>
                            </el-tooltip>
                        </span>
                    </template>
                    <el-input v-model="addAndEditRoleForm.name" />
                </el-form-item>
                <el-form-item label="描述" prop="description">
                    <el-input v-model="addAndEditRoleForm.description" type="textarea" />
                </el-form-item>
            </el-form>
        </CustomDrawer>
        <!-- <GuideStickyNote :steps="steps" /> -->
    </div>
</template>

<script setup lang="ts">
import GuideStickyNote from "@/components/GuideStickyNote/index.vue";
import CustomDrawer from "@/components/CustomDrawer";
import { EditPen } from "@element-plus/icons-vue";
import { ref, getCurrentInstance, onMounted } from "vue";
import { PermissionEnum, PermissionColors, PermissionDescription } from "@/enums/roles/authorCards";
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from "element-plus";
import { reactive } from "vue";
import { umsAuthorityCardGetAll } from "@/apis/umsAuthorityCardController";
import {
    UmsAuthorityCard_,
    umsRoleAdd,
    umsRoleAllocCard,
    umsRoleDelete,
    umsRoleEnableOrDisable,
    umsRoleGetAll,
    umsRoleGetById,
    umsRoleUpdate,
} from "@/apis";
import { useCustomDialog } from "@/components/CustomDialog";

const customDialog = useCustomDialog();

// 权限卡悬浮面板内，添加对应卡名，以及给卡名添加对应颜色
// 便签组件可关闭/打开，右下角图标切换

// // --------------------- 用户引导部分的函数、变量、规则声明，与业务逻辑分离 -------------------------

const IS_SHOW_TOOLTIP = ref(false);

function showToolTip() {
    IS_SHOW_TOOLTIP.value = true;
}

const steps = ref([
    {
        content: "鼠标悬浮在权限卡上，可以查阅不同“权限卡”的具体信息。",
        condition: () => IS_SHOW_TOOLTIP.value,
        isDone: false,
    },
    {
        content: "请点击每行岗位末尾的按钮以创建或编辑权限组。",
        condition: function () {
            let flag = false;
            if (showCardsdialogVisible.value) {
                flag = true;
            }
            return flag;
        },
        isDone: false,
    },
    {
        content:
            "勾选对应“权限卡”，使“教研室主任”在本平台的权限符合规则。依次为教研室主任添加”管理员权限卡“、“教师权限卡”、“专业数据权限卡“。",
        condition: () => selectedCardIds.value.length >= 3 && selectedEditRoleId.value !== -1,
        isDone: false,
    },
    {
        content: "点击保存以完成权限组的配置。",
        condition: () => selectedCardIds.value.length === 0,
        isDone: false,
    },
    {
        content: "点击 新增权限组 ，可以创建新的岗位",
        condition: () => showRoleVisible.value,
        isDone: false,
    },
    {
        content: "点击 岗位名称 文本框，可以修改岗位名称",
        condition: () => addAndEditRoleForm.value.name !== "",
        isDone: false,
    },
    {
        content: "点击 保存 ，可以确认添加岗位",
        condition: () => addAndEditRoleForm.value.name === "",
        isDone: false,
    },
    {
        content: "恭喜！您已完成“权限管理”部分的教程。",
        condition: () => true,
        isDone: true,
    },
]);

// // --------------------- 用户引导部分的函数、变量、规则声明，与业务逻辑分离 -------------------------

// 全部岗位列表，为表格提供数据
const rolesAndCardsList = ref([]);
// 编辑角色与编辑权限卡弹窗变量
const showCardsdialogVisible = ref(false);
const showRoleVisible = ref(false);
// 选中的权限卡 ID 列表
const selectedCardIds = ref<number[]>([]);
// 系统全部的权限卡列表
const authorityCardList = ref<UmsAuthorityCard_[]>([]);
// 当前编辑的角色id
const selectedEditRoleId = ref<number>(-1);

// 编辑/添加角色表单Ref
const addAndEditRoleFormRef = ref<FormInstance>();
const addAndEditRoleForm = ref({
    id: null,
    name: "",
    description: "",
    status: null,
});

// 添加角色表单验证规则
const addAndEditRolerules = reactive<FormRules>({
    name: [{ required: true, message: "请输入岗位名称", trigger: "blur" }],
});

/**
 * 动态生成样式
 * @param {Object} item 当前权限卡
 */
const getDynamicStyle = (item: UmsAuthorityCard_) => {
    // const isSelected = selectedCardIds.value.includes(item.id);
    return {
        backgroundColor: getColor(item.name),
    };
};

onMounted(() => {
    fetchData();
    umsAuthorityCardGetAll({}).then((res) => {
        authorityCardList.value = res.data;
    });
});

/**
 * 提交角色表单，包含新增或编辑
 * @param formEl 表单模型
 */
const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    await formEl.validate((valid, fields) => {
        if (valid) {
            if (addAndEditRoleForm.value.id) {
                // 修改
                umsRoleUpdate({
                    body: {
                        id: addAndEditRoleForm.value.id,
                        name: addAndEditRoleForm.value.name,
                        description: addAndEditRoleForm.value.description,
                    },
                }).then((res: any) => {
                    if (res.code === 200) {
                        ElMessage.success("修改岗位成功");
                    }
                    fetchData();
                });
            } else {
                // 新增
                umsRoleAdd({
                    body: {
                        name: addAndEditRoleForm.value.name,
                        description: addAndEditRoleForm.value.description,
                    },
                })
                    .then(() => {
                        ElMessage.success("添加岗位成功");
                        fetchData();
                    })
                    .catch((err) => {
                        console.log(err);
                    });
            }
            showRoleVisible.value = false;
        } else {
            ElMessage.error("请输入岗位名称");
        }
    });
};

/**
 * 修改角色拥有的权限卡
 */
const saveRoleCards = async () => {
    const cardsList = selectedCardIds.value.join(",");
    umsRoleAllocCard({
        params: {
            roleId: selectedEditRoleId.value,
            cardIds: cardsList,
        },
    })
        .then(() => {
            ElMessage.success("分配权限成功");
            showCardsdialogVisible.value = false;
            selectedCardIds.value = [];
            fetchData();
        })
        .catch((err) => {
            console.log(err);
        });
};

// 获取权限对应的颜色
const getColor = (permission: string) => PermissionColors[permission as PermissionEnum];

// 获取权限对应的描述
const getCardDescription = (permission: string) => PermissionDescription[permission as PermissionEnum];

// 获取表格数据
function fetchData() {
    umsRoleGetAll({}).then((res) => {
        rolesAndCardsList.value = res.data;
    });
}

function switchShowCards(row: any) {
    showCardsdialogVisible.value = true;
    // 遍历出该行角色所拥有的权限卡id列表
    const ownsCardIds = row.authorityCardList.map((card: any) => card.id);
    selectedCardIds.value = ownsCardIds;
    selectedEditRoleId.value = row.id;
}

function switchRoleCards() {
    showRoleVisible.value = true;
}

function handleEdit(row: any) {
    umsRoleGetById({
        params: {
            id: row.id,
        },
    }).then((res) => {
        const { id, name, description } = res.data;
        addAndEditRoleForm.value.name = name;
        addAndEditRoleForm.value.description = description;
        addAndEditRoleForm.value.id = id;
        showRoleVisible.value = true;
    });
}

const handleDelete = (row: any) => {
    if (row.useUserCount > 0) {
        customDialog.warning({
            title: "权限身份使用中",
            message: `当前有<span style="color: #1677FF;">${row.useUserCount}</span>名用户正在使用该权限身份。如要删除该权限身份，请先将该权限身份从对应用户的账号上移除。`,
            confirmButtonText: "我知道了",
            showCancelButton: false,
            type: "warning",
            dangerouslyUseHTMLString: true,
            alignCenter: true,
        });
    } else {
        customDialog.confirm({
            title: "确认删除该权限身份吗？",
            message: `该权限身份将被从平台中移除。`,
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            alignCenter: true,
            onConfirm: () => {
                umsRoleDelete({
                    params: {
                        id: row.id,
                    },
                }).then((res: any) => {
                    if (res.code === 200) {
                        ElMessage.success("删除岗位成功");
                        fetchData();
                    }
                });
            },
        });
    }
};

function clearRoleForm(formEl: FormInstance | undefined) {
    if (!formEl) return;
    addAndEditRoleForm.value.id = null;
    addAndEditRoleForm.value.name = "";
    addAndEditRoleForm.value.description = "";
}
</script>

<style scoped lang="scss">
// 覆盖el-tooltip样式
:global(.el-popper.is-customized.cards-tooltip) {
    /* Set padding to ensure the height is 32px */
    padding: 6px 12px;
    background: #f2f2f2;
    color: #6b7995;
    font-weight: 400;
    max-width: 200px; // 限制悬浮窗最大宽度
    box-shadow: rgba(0, 0, 0, 0.24) 0 3px 8px;
}

:global(.el-popper.is-customized.cards-tooltip .el-popper__arrow::before) {
    background: #f2f2f2;
    right: 0;
}

// 清楚无用边框线
:deep(.el-table--border .el-table__inner-wrapper:after) {
    width: 0;
}

.card-item {
    text-align: center;
    color: #fff;
    cursor: help;
    height: 32px;
    font-size: 14px;
    border-radius: 4px;
    font-weight: 500;
    display: flex;
    padding: 1px 7px;
    align-items: center;
    justify-content: center;
}

// 设置分配权限卡中列表css
.select-card-list {
    div {
        display: flex;
        flex-direction: column;
    }
}

// CustomDrawer alert 样式
.custom-drawer-alert-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-right: 12px;
}

.custom-drawer-alert-content {
    flex: 1;
    
    &-title {
        font-size: 16px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.88);
        line-height: 24px;
        margin-bottom: 4px;
    }
    
    &-description {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
    }
}

// 权限卡列表样式
.permission-cards-list {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .permission-card-item {
        .card-row {
            display: flex;
            align-items: center;
            gap: 10px;

            .card-name {
                color: #fff;
                font-weight: 500;
                font-size: 14px;
                line-height: 20px;
                border-radius: 4px;
                padding: 2px 7px;
                display: flex;
                align-items: center;
                justify-content: center;
                min-width: 111px;
                height: 24px;
            }
        }

        .card-description {
            color: rgba(0, 0, 0, 0.88);
            font-size: 14px;
            line-height: 22px;
            padding-left: 55px;
            margin-top: 4px;
            position: relative;

            :deep(li) {
                list-style-type: disc;
            }
        }
    }
}

.permission-group {
    height: 100%;
    overflow-y: hidden;
    display: flex;
    gap: 12px;
    flex-direction: column;

    &-header {
        display: flex;
        padding: 16px 32px;
        justify-content: flex-end;
        background: #fff;
    }

    .table {
        background: #fff;
        height: 100%;
        position: relative;
        :deep(.el-table) {
            position: absolute;
        }
    }

    .card-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .card-desc {
        width: 98%;
        margin: 20px auto;

        ul {
            li {
                display: block;
                margin: 10px 0;
                font-size: 14px;
                font-weight: 400;
                letter-spacing: 1px;
            }
        }
    }
}
</style>
