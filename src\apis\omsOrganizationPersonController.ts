/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 分页查询组织成员 GET /oms/omsOrganizationPerson/list */
export async function omsOrganizationPersonList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.omsOrganizationPersonListParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageUmsPerson_>(
    '/oms/omsOrganizationPerson/list',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 根据组织id和名字查询成员 GET /oms/omsOrganizationPerson/members */
export async function omsOrganizationPersonMembers({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.omsOrganizationPersonMembersParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageUmsPerson_>(
    '/oms/omsOrganizationPerson/members',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}
