/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 删除新闻草稿 POST /nms/nmsNews/deleteDraft */
export async function nmsNewsDeleteDraft({
  body,
  options,
}: {
  body: API.CommonDeleteDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/nms/nmsNews/deleteDraft', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除已发布的新闻 POST /nms/nmsNews/deletePublish */
export async function nmsNewsDeletePublish({
  body,
  options,
}: {
  body: API.CommonDeleteDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/nms/nmsNews/deletePublish', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除待审核新闻 POST /nms/nmsNews/deleteSubmit */
export async function nmsNewsDeleteSubmit({
  body,
  options,
}: {
  body: API.CommonDeleteDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/nms/nmsNews/deleteSubmit', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 编辑新闻草稿 POST /nms/nmsNews/editDraft */
export async function nmsNewsEditDraft({
  body,
  options,
}: {
  body: API.NmsNewsUpdateParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/nms/nmsNews/editDraft', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 编辑待审核新闻 POST /nms/nmsNews/editSubmit */
export async function nmsNewsEditSubmit({
  body,
  options,
}: {
  body: API.NmsNewsUpdateSubmitParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/nms/nmsNews/editSubmit', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据id获取自己的新闻草稿 GET /nms/nmsNews/getDraftById */
export async function nmsNewsGetDraftById({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.nmsNewsGetDraftByIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultNmsNews_>('/nms/nmsNews/getDraftById', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据id获取已发布新闻 GET /nms/nmsNews/getPublishById */
export async function nmsNewsGetPublishById({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.nmsNewsGetPublishByIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultNmsNews_>('/nms/nmsNews/getPublishById', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据id获取待审核新闻 GET /nms/nmsNews/getSubmitById */
export async function nmsNewsGetSubmitById({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.nmsNewsGetSubmitByIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultNmsNews_>('/nms/nmsNews/getSubmitById', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取已发布的新闻简讯列表 GET /nms/nmsNews/listBrief */
export async function nmsNewsListBrief({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.nmsNewsListBriefParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageNmsNews_>('/nms/nmsNews/listBrief', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取当前用户新闻草稿列表 GET /nms/nmsNews/listDraft */
export async function nmsNewsListDraft({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.nmsNewsListDraftParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageNmsNews_>('/nms/nmsNews/listDraft', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取已发布的新闻列表 GET /nms/nmsNews/listPublish */
export async function nmsNewsListPublish({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.nmsNewsListPublishParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageNmsNews_>(
    '/nms/nmsNews/listPublish',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取待审核新闻列表 GET /nms/nmsNews/listSubmit */
export async function nmsNewsListSubmit({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.nmsNewsListSubmitParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultCommonPageNmsNews_>(
    '/nms/nmsNews/listSubmit',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 发布待审核新闻 POST /nms/nmsNews/publishSubmit */
export async function nmsNewsPublishSubmit({
  body,
  options,
}: {
  body: API.NmsPublishSubmitDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/nms/nmsNews/publishSubmit', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 保存新闻草稿 POST /nms/nmsNews/saveDraft */
export async function nmsNewsSaveDraft({
  body,
  options,
}: {
  body: API.NmsNewsSaveParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/nms/nmsNews/saveDraft', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 提交新闻草稿 POST /nms/nmsNews/submitDraft */
export async function nmsNewsSubmitDraft({
  body,
  options,
}: {
  body: API.NmsNewsSubmitParam;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/nms/nmsNews/submitDraft', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
