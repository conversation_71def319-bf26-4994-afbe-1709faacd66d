<template>
    <div class="imported-records">
        <GeneralDataDisplay>
            <template #top>
                <div class="top">
                    <div>
                        <el-input
                            v-model="search"
                            style="width: 420px"
                            placeholder="请输入数据表名称、项目名称、审核人、参与人搜索"
                            suffix-icon="Search"
                            @keyup.enter="handleSearch" />
                    </div>
                    <div>
                        <el-button color="#586FBB" type="primary" @click="handleSearch">搜索</el-button>
                    </div>
                </div>
            </template>
            <template #content>
                <div class="content">
                    <DataTable
                        :search-keyword="search"
                        :is-local-search="false"
                        ref="dataTableRef"
                        :use-tab-style="true"
                        :tab-config="[
                            {
                                key: 'approvaling',
                                label: '审核中',
                                loadMoreFunc: loadMoreApprovalingFunc,
                            },
                            {
                                key: 'approved',
                                label: '审核通过',
                                loadMoreFunc: loadMoreApprovedFunc,
                            },
                        ]"
                        :is-load-first="true"
                        @itemClick="loadDetail"
                        @update:modelValue="handleTabChange">
                        <template #title="{ item }: { item: CmsRowValueResult }">
                            <div class="title">
                                {{ item.templateName }}
                            </div>
                        </template>
                        <template #desc="{ item }: { item: CmsRowValueResult }">
                            <div>项目名称：{{ item.projectName === null ? "-" : item.projectName }}</div>
                            <div v-if="currentActiveTab === 'approved'">
                                审核人：{{
                                    getPersonName(
                                        allPersonListWithDisabled,
                                        item.approvalBy === null ? "-" : String(item.approvalBy),
                                    )
                                }}
                            </div>
                        </template>
                        <template #detail="{ selectedItem }">
                            <EntryRecordDetail
                                :record-list="recordList"
                                :selected-item="detailData"
                                :current-active-tab="currentActiveTab"
                                @refresh="refreshAll"
                                :detailLoading="detailLoading"
                                :all-person-list="allPersonListWithDisabled" />
                        </template>
                    </DataTable>
                </div>
            </template>
        </GeneralDataDisplay>
    </div>
</template>

<script setup lang="ts">
import GeneralDataDisplay from "@/components/GeneralDataDisplay/index.vue";
import DataTable from "@/components/DataTable/index.vue";
import EntryRecordDetail from "./detail.vue";

import { cmsCategoryRowGetByApprovalAndRowType } from "@/apis/cmsCategoryRowController";
import { CmsCategoryRow_, CmsCheck_, CmsRowValueResult } from "@/apis/types";
import { cmsCheckGetRecord } from "@/apis/cmsCheckController";
import { getPersonName } from "@/utils/getNames";
import { usePersons } from "@/hooks/usePersons";

// 所有人员列表(包含已被禁用的人员)
const { allPersonListWithDisabled } = usePersons(true);

// 定义dataTableRef的类型
const dataTableRef = ref();

// 跟踪当前活动的标签页
const currentActiveTab = ref("pending");

// 详情加载状态
const detailLoading = ref<boolean>(false);

// 详情数据
const detailData = ref<CmsRowValueResult>();

// 搜索
const search = ref<string>("");

// 审核历史
const recordList = ref<CmsCheck_[]>([]);

// 处理标签页变化
function handleTabChange(tabKey: string) {
    currentActiveTab.value = tabKey;
    // 每次切换标签页时清空详情数据
    detailData.value = null;
    // 刷新
    refreshAll();
}

// 处理搜索
const handleSearch = () => {
    if (dataTableRef.value) {
        dataTableRef.value.search();
    }
};

// 刷新
const refreshAll = () => {
    // 清空detailData
    detailData.value = null;

    if (dataTableRef.value) {
        // 获取当前活动的标签页并传递
        dataTableRef.value.refreshAll(currentActiveTab.value);
    }
};

// 加载更多函数【审核中】
const loadMoreApprovalingFunc = (pageNum: number, pageSize: number, searchKeyword?: string) => {
    return cmsCategoryRowGetByApprovalAndRowType({
        params: {
            approvalStatus: [1],
            rowTypes: ["INSERT", "IMPORT"],
            pageNum,
            pageSize,
            name: searchKeyword.trim(),
        },
    });
};

// 加载更多函数【审核通过】
const loadMoreApprovedFunc = (pageNum: number, pageSize: number, searchKeyword?: string) => {
    return cmsCategoryRowGetByApprovalAndRowType({
        params: {
            approvalStatus: [3],
            rowTypes: ["INSERT", "IMPORT"],
            pageNum,
            pageSize,
            name: searchKeyword.trim(),
        },
    });
};

// 加载详情
const loadDetail = (item: CmsCategoryRow_) => {
    // 如果当前选中项和之前选中项相同，则不重新加载
    if (item.id === detailData.value?.id) return;

    detailLoading.value = true;
    detailData.value = null; // 清空之前的详情数据

    detailData.value = item;
    detailLoading.value = false;

    cmsCheckGetRecord({ params: { rowId: item.id } }).then((res) => {
        recordList.value = res.data;
    });
};
</script>

<style scoped lang="scss">
.imported-records {
    height: 100%;
    .top {
        padding: 20px;
        background: #fff;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .content {
        height: 100%;
        .title {
            font-weight: bold;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
</style>
