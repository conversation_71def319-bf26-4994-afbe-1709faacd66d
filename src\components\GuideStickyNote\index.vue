<template>
    <div class="tips-container" v-show="isClose">
        <div class="tips-header">
            <!-- 空div仅供布局使用 -->
            <div></div>
            <div>{{ currentStepIndex + 1 }}/{{ steps.length }}</div>
            <div class="close" @click="isClose = false">x</div>
        </div>
        <div class="tips-body">
            <p>{{ steps[currentStepIndex].content }}</p>
        </div>
        <div class="tips-footer">
            <el-button :disabled="isFirstStep" size="small" @click="prevStep">
                &lt;
            </el-button>
            <el-button :disabled="!(steps[currentStepIndex].isDone) || isLastStep" type="primary" size="small"
                @click="nextStep">
                &gt;
            </el-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const props = defineProps({
    steps: {
        type: Array,
        required: true,
        validator: (value: any[]) =>
            value.every(
                (step) =>
                    typeof step.content === 'string' &&
                    typeof step.condition === 'function'
            ),
    },
});
// 是否关闭
const isClose = ref(true)

const currentStepIndex = ref(0);
// 是否为第一步
const isFirstStep = computed(() => currentStepIndex.value === 0);
// 是否为最后一步
const isLastStep = computed(() => currentStepIndex.value === props.steps.length - 1);


// 监听条件变化，自动完成步骤
watch(
    () => props.steps[currentStepIndex.value].condition(),
    (canProceed) => {
        if (canProceed && !props.steps[currentStepIndex.value].isDone) {
            props.steps[currentStepIndex.value].isDone = true;
            nextStep()
        }
    }
);


// 上一步
const prevStep = () => {
    if (currentStepIndex.value >= 1) {
        currentStepIndex.value--;
    }
};

// 下一步
const nextStep = () => {
    if (currentStepIndex.value < props.steps.length - 1 && props.steps[currentStepIndex.value].isDone) {
        currentStepIndex.value++;
    }
};
</script>

<style scoped lang="scss">
.tips-container {
    z-index: 9999;
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 300px;
    background: #ecc424;
    border: 1px solid #ccc;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .tips-header {
        /* background: #f5f7fa; */
        padding: 10px;
        font-weight: bold;
        text-align: center;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .close {
            cursor: pointer;
            color: #878787;
            font-weight: normal;
        }

    }

    .tips-body {
        padding: 15px;
        font-size: 14px;
        line-height: 1.6;
    }

    .tips-footer {
        display: flex;
        justify-content: space-between;
        padding: 10px;
        /* background: #f5f7fa; */
    }
}
</style>