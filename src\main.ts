import 'mutation-events';

import { createApp } from 'vue';

import './assets/styles/reset.css';
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';

import 'element-plus/dist/index.css';
import App from './App.vue';

import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/reset.css';

//quill 配置
import { QuillEditor } from '@vueup/vue-quill';
import '@vueup/vue-quill/dist/vue-quill.snow.css';
// 导入自定义样式覆盖默认行为
import './views/backgroundManagement/centralSettings/newsletterManagement/components/quill-custom.css';
import Quill from 'quill';
// 添加全局标记追踪格式注册状态
window.QuillFormatsRegistered = window.QuillFormatsRegistered || {};
// 全局定义selectData，避免重复初始化
Quill.selectData = Quill.selectData || {};
// 重新定义Block格式以避免过度格式化
const Block = Quill.import('blots/block');
Block.tagName = 'DIV'; // 默认是P，改成DIV可以减少自动换行
Quill.register(Block, true);

// 导入全局样式
import './style.css';

// 导入阿里巴巴字体
import './assets/fonts/fonts.scss';

// 导入自定义级联框样式覆盖默认行为
import './assets/styles/customCascader.scss';

// 导入自定义按钮样式
import './assets/styles/customButton.scss';

// 导入自定义tooltip样式
import './assets/styles/customTooltip.scss';

// 导入自定义抽屉样式
import './assets/styles/customDrawer.scss';

import store from './store';
import router from './router';

// 导入自定义指令
import directives from './directives';

import './permission'; // 权限控制
import { setupDataEntryWatcher } from './store/modules/dataEntry';

//注册wangeditor附件上传插件
import { Boot } from '@wangeditor/editor';
import attachmentModule from '@wangeditor/plugin-upload-attachment';
// 注册插件
Boot.registerModule(attachmentModule);

import zhCn from 'element-plus/es/locale/lang/zh-cn';

const app = createApp(App);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
// 全局注册 QuillEditor 组件，并提供全局配置
app.component('QuillEditor', QuillEditor);
app.use(ElementPlus, {
  locale: zhCn,
});
app.use(router);
app.use(store);
app.use(Antd);
// 注册自定义指令
app.use(directives);

// 设置数据录入状态监听
setupDataEntryWatcher();

app.mount('#app');
