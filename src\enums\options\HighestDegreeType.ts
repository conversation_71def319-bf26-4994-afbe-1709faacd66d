/**
 * 最高学位：学士学位、副学士学位/高级文凭、硕士学位、博士学位
 */
export enum HighestDegreeType {
    /** 学士学位 */
    BACHELOR = 0,
    /** 副学士学位/高级文凭 */
    ASSOCIATE = 1,
    /** 硕士学位 */
    MASTER = 2,
    /** 博士学位 */
    DOCTOR = 3,
}

// 生成对应Map
export const HighestDegreeTypeMap: Record<HighestDegreeType, string> = {
    [HighestDegreeType.BACHELOR]: "学士学位",
    [HighestDegreeType.ASSOCIATE]: "副学士学位/高级文凭",
    [HighestDegreeType.MASTER]: "硕士学位",
    [HighestDegreeType.DOCTOR]: "博士学位",
} 