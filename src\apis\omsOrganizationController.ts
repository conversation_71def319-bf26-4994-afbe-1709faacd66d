/* eslint-disable */
// @ts-ignore
import request from '@/utils/request.ts';

import * as API from './types';

/** 修改组织团队指定人员的职位 POST /oms/omsOrganization/changePosition */
export async function omsOrganizationChangePosition({
  body,
  options,
}: {
  body: API.OmsChangePositionDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/oms/omsOrganization/changePosition', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除组织团队 POST /oms/omsOrganization/delete */
export async function omsOrganizationDelete({
  body,
  options,
}: {
  body: API.CommonDeleteDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/oms/omsOrganization/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除项目人员 GET /oms/omsOrganization/deleteMember */
export async function omsOrganizationDeleteMember({
  body,
  options,
}: {
  body: API.OmsPersonDeleteDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/oms/omsOrganization/deleteMember', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询组织团队信息 GET /oms/omsOrganization/getOrgById */
export async function omsOrganizationGetOrgById({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.omsOrganizationGetOrgByIdParams;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultOmsOrganization_>(
    '/oms/omsOrganization/getOrgById',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 查询组织团队列表 GET /oms/omsOrganization/getOrgList */
export async function omsOrganizationGetOrgList({
  options,
}: {
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultListOmsOrganization_>(
    '/oms/omsOrganization/getOrgList',
    {
      method: 'GET',
      ...(options || {}),
    }
  );
}

/** 修改组织团队成员 POST /oms/omsOrganization/member/update */
export async function omsOrganizationMemberUpdate({
  body,
  options,
}: {
  body: API.OmsOrganizationMemberUpdateDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/oms/omsOrganization/member/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增组织团队 POST /oms/omsOrganization/save */
export async function omsOrganizationSave({
  body,
  options,
}: {
  body: API.OmsOrganizationDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/oms/omsOrganization/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改组织团队 POST /oms/omsOrganization/update */
export async function omsOrganizationUpdate({
  body,
  options,
}: {
  body: API.OmsOrganizationUpdateDto;
  options?: { [key: string]: unknown };
}) {
  return request<API.CommonResultVoid_>('/oms/omsOrganization/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
