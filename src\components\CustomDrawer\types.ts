export type DrawerDirection = 'rtl' | 'ltr' | 'ttb' | 'btt'

export interface CustomDrawerProps {
    /**
     * 控制抽屉显示隐藏
     */
    modelValue: boolean
    
    /**
     * 抽屉标题
     */
    title?: string
    
    /**
     * 抽屉尺寸
     */
    size?: string | number
    
    /**
     * 抽屉打开方向
     */
    direction?: DrawerDirection
    
    /**
     * 是否显示遮罩层
     */
    modal?: boolean
    
    /**
     * 是否锁定 body 滚动
     */
    lockScroll?: boolean
    
    /**
     * 是否将 drawer 插入到 body 元素上
     */
    appendToBody?: boolean
    
    /**
     * 是否可以通过点击 modal 关闭
     */
    closeOnClickModal?: boolean
    
    /**
     * 是否可以通过按下 ESC 关闭
     */
    closeOnPressEscape?: boolean
    
    /**
     * 取消按钮文本
     */
    cancelText?: string
    
    /**
     * 确定按钮文本
     */
    confirmText?: string
    
    /**
     * 自定义 drawer 类名
     */
    drawerClass?: string
}

export interface CustomDrawerEmits {
    (e: 'update:modelValue', value: boolean): void
    (e: 'close'): void
    (e: 'confirm'): void
    (e: 'before-close', done: () => void): void
}

export interface CustomDrawerSlots {
    /**
     * 关闭图标插槽
     */
    'close-icon'?: () => any
    
    /**
     * 标题插槽
     */
    title?: () => any
    
    /**
     * 操作按钮插槽
     */
    actions?: (props: { close: () => void }) => any
    
    /**
     * 警告提示插槽
     */
    alert?: () => any
    
    /**
     * 默认内容插槽
     */
    default?: () => any
} 