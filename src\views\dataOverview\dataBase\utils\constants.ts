import { categoryTemplateType } from "@/enums/categoryTemplate/categoryTemplateType";
import { categoryTemplateValueType_NEW } from "@/enums/categoryTemplate/categoryTemplateValueType";
import { createFilterConfigs } from "@/utils/enumToFilterList";

/**
 * 数据加载相关常量
 */
export const DATA_CONSTANTS = {
    DEFAULT_PAGE_SIZE: 50,
    SCROLL_THRESHOLD: 5,
    PAGINATION_HIDE_DELAY: 3000,
    THROTTLE_DELAY: 16, // 约60fps的频率
} as const;

/**
 * 过滤器映射配置
 */
export const filterConfigs = createFilterConfigs({
    // 可以在这里添加更多的过滤器配置
});

/**
 * 判断是否是院情数据类型
 */
export const isCollegeDataType = (templateType: number) => {
    return templateType === categoryTemplateType['院情数据'];
};

/**
 * 生成表格选项的工具函数
 */
export const buildTableOptions = (classifyList: any[], templateList: any[]) => {
    return classifyList.map((item) => {
        const children = templateList
            .map((template) => {
                if (item.id === template.classify) {
                    return {
                        value: template.id,
                        label: template.templateName,
                    };
                }
                return undefined;
            })
            .filter((item) => item !== undefined);

        return {
            value: item.id,
            label: item.name,
            children,
            disabled: children.length === 0,
        };
    });
};

/**
 * 字段值映射生成器
 */
export const createFieldValueMap = (fieldsList: any[], values: any[]) => {
    return fieldsList.reduce(
        (acc, field) => {
            const matchedValue = values.find((v) => v.categoryTemplateValueId === field.id);
            acc[field.id] = {
                fieldProperties: field,
                fieldValue: matchedValue ? matchedValue : null,
            };
            return acc;
        },
        {} as Record<string, any>
    );
};

/**
 * 判断人员是否为内部人员
 */
export const isInternalPerson = (value: string) => {
    if (!value) return false;
    return /^\d+$/.test(value);
};

/**
 * 获取多选下拉框的标签
 */
export const getMultiSelectLabels = (dictList: any[], value: string) => {
    if (!value) return [];
    const ids = value.split(",");
    if (Number.isNaN(Number(ids[0]))) {
        return value;
    }
    return ids.map((id) => {
        const item = dictList.find((dict) => dict.id.toString() === id);
        // 为依托专业情况设置特殊返回值
        if (item?.hasOwnProperty("organizationName")) {
            return item.organizationName;
        } else {
            return item ? item.name : "";
        }
    });
};

/**
 * 获取单选下拉框的标签
 */
export const getSingleSelectLabel = (dictList: any[], value: string) => {
    if (!value) return "";
    const item = dictList.find((dict) => dict.id.toString() === value);
    return item ? item.name : "";
};

/**
 * 生成佐证材料列表
 */
export const generateSupportingMaterialsList = (fieldValueMap: Record<string, any>, fieldList: any[]) => {
    const fileList: string[] = [];

    fieldList.forEach((field) => {
        if (field.type === categoryTemplateValueType_NEW.FILE) {
            const fieldData = fieldValueMap[field.id];
            if (fieldData?.fieldValue?.value) {
                const fileUrls = fieldData.fieldValue.value.split(",");
                fileUrls.forEach((fileUrl: string) => {
                    if (fileUrl.trim()) {
                        fileList.push(fileUrl);
                    }
                });
            }
        }
    });

    return fileList.join(",");
};